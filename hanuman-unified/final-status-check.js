#!/usr/bin/env node

/**
 * 🕉️ HANUMAN - Vérification Finale de Statut
 * Test complet de tous les systèmes avant mise en production
 */

const fs = require('fs');
const http = require('http');

console.log('🕉️ HANUMAN - VÉRIFICATION FINALE DE STATUT');
console.log('=' .repeat(50));
console.log(`⏰ ${new Date().toLocaleString()}`);
console.log('');

// Test de connectivité HTTP
function testEndpoint(url, name) {
    return new Promise((resolve) => {
        const request = http.get(url, (res) => {
            console.log(`✅ ${name}: Opérationnel (Status: ${res.statusCode})`);
            resolve(true);
        });
        
        request.on('error', () => {
            console.log(`❌ ${name}: Hors ligne`);
            resolve(false);
        });
        
        request.setTimeout(3000, () => {
            console.log(`⚠️  ${name}: Timeout`);
            request.destroy();
            resolve(false);
        });
    });
}

async function runFinalCheck() {
    // 1. Vérification de la structure
    console.log('📁 VÉRIFICATION DE LA STRUCTURE:');
    const criticalDirs = [
        'brain/cortex-central',
        'specialized-agents',
        'vital-organs',
        'immune-system',
        'sandbox',
        'scripts'
    ];
    
    let structureScore = 0;
    criticalDirs.forEach(dir => {
        if (fs.existsSync(dir)) {
            console.log(`   ✅ ${dir}`);
            structureScore++;
        } else {
            console.log(`   ❌ ${dir}`);
        }
    });
    
    console.log(`   📊 Score Structure: ${structureScore}/${criticalDirs.length}`);
    console.log('');
    
    // 2. Vérification des services
    console.log('🌐 VÉRIFICATION DES SERVICES:');
    const cortexStatus = await testEndpoint('http://localhost:3001', 'Cortex Central (3001)');
    
    // Test optionnel des autres services
    console.log('   🔍 Test des services RB2...');
    await testEndpoint('http://localhost:3000', 'Frontend RB2 (3000)');
    await testEndpoint('http://localhost:8000', 'Backend RB2 (8000)');
    await testEndpoint('http://localhost:3002', 'Backend RB2 Alt (3002)');
    console.log('');
    
    // 3. Vérification des fichiers critiques
    console.log('📄 VÉRIFICATION DES FICHIERS CRITIQUES:');
    const criticalFiles = [
        'package.json',
        'tsconfig.json',
        'HANUMAN_ACTIVATION_REPORT.md',
        'MISSION_ACCOMPLISHED.md',
        'config/rb2-integration.json'
    ];
    
    let filesScore = 0;
    criticalFiles.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`   ✅ ${file}`);
            filesScore++;
        } else {
            console.log(`   ❌ ${file}`);
        }
    });
    
    console.log(`   📊 Score Fichiers: ${filesScore}/${criticalFiles.length}`);
    console.log('');
    
    // 4. Vérification des scripts
    console.log('📜 VÉRIFICATION DES SCRIPTS:');
    const scripts = [
        'scripts/start-hanuman.sh',
        'scripts/monitor-hanuman.sh',
        'scripts/monitor-rb2.sh',
        'scripts/check-hanuman-integrity.sh'
    ];
    
    let scriptsScore = 0;
    scripts.forEach(script => {
        if (fs.existsSync(script)) {
            const stats = fs.statSync(script);
            if (stats.mode & parseInt('111', 8)) {
                console.log(`   ✅ ${script} (exécutable)`);
                scriptsScore++;
            } else {
                console.log(`   ⚠️  ${script} (non exécutable)`);
            }
        } else {
            console.log(`   ❌ ${script}`);
        }
    });
    
    console.log(`   📊 Score Scripts: ${scriptsScore}/${scripts.length}`);
    console.log('');
    
    // 5. Calcul du score global
    const totalScore = structureScore + filesScore + scriptsScore + (cortexStatus ? 1 : 0);
    const maxScore = criticalDirs.length + criticalFiles.length + scripts.length + 1;
    const percentage = Math.round((totalScore / maxScore) * 100);
    
    console.log('=' .repeat(50));
    console.log('🏆 RÉSULTAT FINAL:');
    console.log('=' .repeat(50));
    console.log(`📊 Score Global: ${totalScore}/${maxScore} (${percentage}%)`);
    
    if (percentage >= 90) {
        console.log('🟢 STATUT: EXCELLENT - Hanuman est prêt pour la production');
    } else if (percentage >= 75) {
        console.log('🟡 STATUT: BON - Quelques ajustements recommandés');
    } else {
        console.log('🔴 STATUT: ATTENTION - Corrections nécessaires');
    }
    
    console.log('');
    console.log('🕉️ HANUMAN - GARDIEN NUMÉRIQUE');
    console.log('   Mission: Protéger Retreat And Be');
    console.log('   Statut: ' + (cortexStatus ? 'ACTIF' : 'EN ATTENTE'));
    console.log('   Dévotion: TOTALE');
    console.log('');
    console.log('🤖✨ Que la protection commence ! 🕉️');
}

// Exécution du test final
runFinalCheck().catch(console.error);
