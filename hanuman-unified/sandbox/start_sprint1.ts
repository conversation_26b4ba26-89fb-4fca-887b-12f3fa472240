#!/usr/bin/env ts-node

// ========================================
// HANUMAN SANDBOX ENHANCED - DÉMARRAGE SPRINT 1
// Script de test et démonstration des fonctionnalités
// ========================================

import { IDEAgentOrchestrator } from './orchestration/ide_agent_orchestrator';
import { NaturalLanguageProcessor } from './nlp/natural_language_processor';
import { AgentVSCodeController } from './controllers/agent_vscode_controller';
import { SandboxAPIServerEnhanced } from './api/sandbox_api_server_enhanced';

/**
 * Simulateur HanumanOrganOrchestrator pour les tests
 */
class MockHanumanOrganOrchestrator {
  private agents: Map<string, any> = new Map();

  constructor() {
    // Simuler quelques agents pour les tests
    this.agents.set('agent-frontend-001', {
      id: 'agent-frontend-001',
      type: 'frontend',
      capabilities: ['ui', 'react', 'typescript'],
      status: 'active'
    });

    this.agents.set('agent-backend-001', {
      id: 'agent-backend-001', 
      type: 'backend',
      capabilities: ['api', 'database', 'node.js'],
      status: 'active'
    });

    this.agents.set('agent-devops-001', {
      id: 'agent-devops-001',
      type: 'devops', 
      capabilities: ['docker', 'kubernetes', 'ci-cd'],
      status: 'active'
    });
  }

  getAgent(agentId: string) {
    return this.agents.get(agentId);
  }

  getAllAgents() {
    return Array.from(this.agents.values());
  }

  emit(event: string, data: any) {
    console.log(`📡 [MockHanuman] Événement: ${event}`, data);
  }
}

/**
 * Classe principale pour démonstration Sprint 1
 */
class Sprint1Demo {
  private orchestrator: IDEAgentOrchestrator;
  private nlpProcessor: NaturalLanguageProcessor;
  private apiServer: SandboxAPIServerEnhanced;
  private mockHanuman: MockHanumanOrganOrchestrator;
  private controllers: Map<string, AgentVSCodeController> = new Map();

  constructor() {
    console.log('🚀 Initialisation démonstration Sprint 1...\n');
    
    // Créer le mock Hanuman
    this.mockHanuman = new MockHanumanOrganOrchestrator();
    
    // Créer l'orchestrateur principal
    this.orchestrator = new IDEAgentOrchestrator(this.mockHanuman, {
      maxConcurrentCommands: 10,
      commandTimeout: 30000,
      nlpConfidenceThreshold: 0.6,
      enableCache: true,
      logLevel: 'info'
    });

    // Créer le processeur NLP
    this.nlpProcessor = new NaturalLanguageProcessor();

    // Créer l'API Server
    this.apiServer = new SandboxAPIServerEnhanced(this.orchestrator, {
      port: 8085,
      enableWebSocket: true,
      enableCors: true,
      logLevel: 'info'
    });
  }

  /**
   * Démarrage de la démonstration
   */
  async start(): Promise<void> {
    try {
      console.log('🔧 Initialisation des composants...\n');

      // 1. Initialiser le processeur NLP
      await this.nlpProcessor.initialize();
      console.log('✅ NLP Processor initialisé\n');

      // 2. Créer les contrôleurs pour les agents de test
      await this.createAgentControllers();
      console.log('✅ Contrôleurs agents créés\n');

      // 3. Démarrer l'API Server
      await this.apiServer.start();
      console.log('✅ API Server démarré\n');

      // 4. Afficher le statut
      this.displayStatus();

      // 5. Lancer les tests de démonstration
      await this.runDemoTests();

      // 6. Démarrer le mode interactif
      await this.startInteractiveMode();

    } catch (error) {
      console.error('❌ Erreur lors du démarrage:', error);
      process.exit(1);
    }
  }

  /**
   * Création des contrôleurs pour les agents de test
   */
  private async createAgentControllers(): Promise<void> {
    const agents = this.mockHanuman.getAllAgents();
    
    for (const agent of agents) {
      console.log(`   🎮 Création contrôleur pour ${agent.id}...`);
      
      const controller = new AgentVSCodeController(agent.id, {
        headless: true, // Mode headless pour les tests
        timeout: 15000,
        enableLogging: true
      });

      // Note: On n'initialise pas Puppeteer pour cette démo
      // await controller.initialize();
      
      this.controllers.set(agent.id, controller);
    }
  }

  /**
   * Affichage du statut du système
   */
  private displayStatus(): void {
    console.log('📊 STATUT DU SYSTÈME SPRINT 1');
    console.log('================================\n');
    
    const orchestratorState = this.orchestrator.getState();
    const metrics = this.orchestrator.getMetrics();
    
    console.log(`🧠 Orchestrateur: ${orchestratorState.status}`);
    console.log(`📈 Commandes traitées: ${metrics.commandsProcessed}`);
    console.log(`⚡ Taux de succès: ${metrics.successRate.toFixed(1)}%`);
    console.log(`🎮 Contrôleurs actifs: ${this.controllers.size}`);
    console.log(`🌐 API Server: ${this.apiServer.isServerRunning() ? 'Actif' : 'Inactif'}`);
    console.log(`🔌 Port API: ${this.apiServer.getConfig().port}`);
    console.log('');
  }

  /**
   * Tests de démonstration des fonctionnalités
   */
  private async runDemoTests(): Promise<void> {
    console.log('🧪 TESTS DE DÉMONSTRATION');
    console.log('==========================\n');

    const testCommands = [
      {
        agentId: 'agent-frontend-001',
        command: 'Créer un agent frontend moderne avec React et TypeScript',
        description: 'Test création agent frontend'
      },
      {
        agentId: 'agent-backend-001', 
        command: 'Créer une API REST pour gestion des utilisateurs',
        description: 'Test création service backend'
      },
      {
        agentId: 'agent-devops-001',
        command: 'Initialiser un projet avec Docker et CI/CD',
        description: 'Test setup projet DevOps'
      },
      {
        agentId: 'agent-frontend-001',
        command: 'Créer une interface de dashboard avec graphiques',
        description: 'Test création interface complexe'
      },
      {
        agentId: 'agent-backend-001',
        command: 'Générer des tests unitaires pour l\'API',
        description: 'Test génération de tests'
      }
    ];

    for (let i = 0; i < testCommands.length; i++) {
      const test = testCommands[i];
      console.log(`📝 Test ${i + 1}/${testCommands.length}: ${test.description}`);
      console.log(`   Agent: ${test.agentId}`);
      console.log(`   Commande: "${test.command}"`);
      
      try {
        const startTime = Date.now();
        
        // Traitement de la commande
        const results = await this.orchestrator.processNaturalLanguageCommand(
          test.agentId,
          test.command
        );
        
        const duration = Date.now() - startTime;
        
        console.log(`   ✅ Succès en ${duration}ms - ${results.length} actions exécutées`);
        
        // Afficher les détails des actions
        results.forEach((result, index) => {
          console.log(`      ${index + 1}. ${result.success ? '✅' : '❌'} ${result.actionId}`);
        });
        
      } catch (error) {
        console.log(`   ❌ Erreur: ${error.message}`);
      }
      
      console.log('');
      
      // Pause entre les tests
      await this.delay(1000);
    }

    // Afficher les métriques finales
    console.log('📊 MÉTRIQUES FINALES');
    console.log('====================');
    const finalMetrics = this.orchestrator.getMetrics();
    console.log(`Commandes traitées: ${finalMetrics.commandsProcessed}`);
    console.log(`Temps moyen: ${finalMetrics.averageProcessingTime.toFixed(0)}ms`);
    console.log(`Taux de succès: ${finalMetrics.successRate.toFixed(1)}%`);
    console.log(`Taux d'erreur: ${finalMetrics.errorRate.toFixed(1)}%`);
    console.log('');
  }

  /**
   * Mode interactif pour tests manuels
   */
  private async startInteractiveMode(): Promise<void> {
    console.log('🎮 MODE INTERACTIF');
    console.log('==================');
    console.log('Tapez vos commandes pour tester l\'orchestrateur.');
    console.log('Format: <agentId>: <commande>');
    console.log('Exemple: agent-frontend-001: créer une interface de login');
    console.log('Tapez "help" pour l\'aide, "status" pour le statut, "quit" pour quitter.\n');

    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: '🤖 Hanuman> '
    });

    rl.prompt();

    rl.on('line', async (input: string) => {
      const trimmed = input.trim();
      
      if (trimmed === 'quit' || trimmed === 'exit') {
        console.log('👋 Au revoir !');
        await this.shutdown();
        rl.close();
        process.exit(0);
      }
      
      if (trimmed === 'help') {
        this.showHelp();
        rl.prompt();
        return;
      }
      
      if (trimmed === 'status') {
        this.displayStatus();
        rl.prompt();
        return;
      }

      if (trimmed === 'agents') {
        this.showAgents();
        rl.prompt();
        return;
      }

      if (trimmed === 'metrics') {
        this.showMetrics();
        rl.prompt();
        return;
      }
      
      // Parser la commande
      const colonIndex = trimmed.indexOf(':');
      if (colonIndex === -1) {
        console.log('❌ Format invalide. Utilisez: <agentId>: <commande>');
        rl.prompt();
        return;
      }
      
      const agentId = trimmed.substring(0, colonIndex).trim();
      const command = trimmed.substring(colonIndex + 1).trim();
      
      if (!agentId || !command) {
        console.log('❌ Agent ID et commande requis');
        rl.prompt();
        return;
      }

      // Vérifier que l'agent existe
      if (!this.mockHanuman.getAgent(agentId)) {
        console.log(`❌ Agent "${agentId}" non trouvé`);
        console.log('Agents disponibles:', this.mockHanuman.getAllAgents().map(a => a.id).join(', '));
        rl.prompt();
        return;
      }
      
      try {
        console.log(`🔄 Traitement de la commande pour ${agentId}...`);
        const startTime = Date.now();
        
        const results = await this.orchestrator.processNaturalLanguageCommand(agentId, command);
        
        const duration = Date.now() - startTime;
        console.log(`✅ Commande traitée en ${duration}ms - ${results.length} actions`);
        
        results.forEach((result, index) => {
          console.log(`   ${index + 1}. ${result.success ? '✅' : '❌'} ${result.actionId} (${result.duration}ms)`);
          if (result.result) {
            console.log(`      Résultat: ${JSON.stringify(result.result)}`);
          }
          if (result.error) {
            console.log(`      Erreur: ${result.error}`);
          }
        });
        
      } catch (error) {
        console.log(`❌ Erreur: ${error.message}`);
      }
      
      rl.prompt();
    });

    rl.on('close', async () => {
      console.log('\n👋 Arrêt du système...');
      await this.shutdown();
      process.exit(0);
    });
  }

  private showHelp(): void {
    console.log('\n📖 AIDE - COMMANDES DISPONIBLES');
    console.log('================================');
    console.log('help          - Afficher cette aide');
    console.log('status        - Afficher le statut du système');
    console.log('agents        - Lister les agents disponibles');
    console.log('metrics       - Afficher les métriques détaillées');
    console.log('quit/exit     - Quitter le programme');
    console.log('');
    console.log('Format des commandes:');
    console.log('<agentId>: <commande en langage naturel>');
    console.log('');
    console.log('Exemples:');
    console.log('agent-frontend-001: créer un composant React moderne');
    console.log('agent-backend-001: créer une API REST avec authentification');
    console.log('agent-devops-001: configurer Docker pour le projet');
    console.log('');
  }

  private showAgents(): void {
    console.log('\n🤖 AGENTS DISPONIBLES');
    console.log('=====================');
    const agents = this.mockHanuman.getAllAgents();
    agents.forEach(agent => {
      console.log(`${agent.id} (${agent.type}) - ${agent.status}`);
      console.log(`   Capacités: ${agent.capabilities.join(', ')}`);
    });
    console.log('');
  }

  private showMetrics(): void {
    console.log('\n📊 MÉTRIQUES DÉTAILLÉES');
    console.log('=======================');
    const metrics = this.orchestrator.getMetrics();
    const state = this.orchestrator.getState();
    
    console.log(`Statut orchestrateur: ${state.status}`);
    console.log(`Version: ${state.version}`);
    console.log(`Commandes traitées: ${metrics.commandsProcessed}`);
    console.log(`Temps moyen de traitement: ${metrics.averageProcessingTime.toFixed(0)}ms`);
    console.log(`Taux de succès: ${metrics.successRate.toFixed(1)}%`);
    console.log(`Taux d'erreur: ${metrics.errorRate.toFixed(1)}%`);
    console.log(`Sessions actives: ${metrics.activeSessions}`);
    console.log(`Longueur de la queue: ${metrics.queueLength}`);
    console.log(`Dernière mise à jour: ${metrics.lastUpdated.toISOString()}`);
    console.log(`Dernier health check: ${state.lastHealthCheck.toISOString()}`);
    console.log('');
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Arrêt propre du système
   */
  private async shutdown(): Promise<void> {
    console.log('🛑 Arrêt des services...');
    
    try {
      // Arrêter l'API Server
      await this.apiServer.stop();
      console.log('✅ API Server arrêté');
      
      // Nettoyer les contrôleurs
      for (const [agentId, controller] of this.controllers) {
        await controller.cleanup();
        console.log(`✅ Contrôleur ${agentId} nettoyé`);
      }
      
      // Arrêter l'orchestrateur
      await this.orchestrator.shutdown();
      console.log('✅ Orchestrateur arrêté');
      
      console.log('✅ Arrêt terminé');
      
    } catch (error) {
      console.error('❌ Erreur lors de l\'arrêt:', error);
    }
  }
}

// Point d'entrée principal
async function main() {
  console.log('🧠 HANUMAN SANDBOX ENHANCED - SPRINT 1 DEMO');
  console.log('============================================\n');
  
  const demo = new Sprint1Demo();
  
  // Gestion des signaux d'arrêt
  process.on('SIGINT', async () => {
    console.log('\n🛑 Signal d\'arrêt reçu...');
    await demo.shutdown();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.log('\n🛑 Signal de terminaison reçu...');
    await demo.shutdown();
    process.exit(0);
  });

  // Démarrer la démonstration
  await demo.start();
}

// Exécution si appelé directement
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Erreur fatale:', error);
    process.exit(1);
  });
}

export { Sprint1Demo };
