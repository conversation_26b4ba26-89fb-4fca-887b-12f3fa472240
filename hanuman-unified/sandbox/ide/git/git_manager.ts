import { EventEmitter } from 'events';
import { VSCodeInstance } from '../vscode/vscode_server_manager';

// Types pour la gestion Git
export interface GitRepository {
  id: string;
  name: string;
  path: string;
  remoteUrl?: string;
  branch: string;
  status: GitStatus;
  lastCommit?: GitCommit;
  agentId?: string;
  organId?: string;
  createdAt: Date;
  lastActivity: Date;
}

export interface GitStatus {
  ahead: number;
  behind: number;
  staged: GitFile[];
  unstaged: GitFile[];
  untracked: string[];
  conflicts: string[];
  clean: boolean;
}

export interface GitFile {
  path: string;
  status: 'added' | 'modified' | 'deleted' | 'renamed' | 'copied';
  staged: boolean;
}

export interface GitCommit {
  hash: string;
  author: string;
  email: string;
  date: Date;
  message: string;
  files: string[];
}

export interface GitBranch {
  name: string;
  current: boolean;
  remote: boolean;
  lastCommit: string;
  ahead: number;
  behind: number;
}

export interface GitRemote {
  name: string;
  url: string;
  type: 'fetch' | 'push';
}

export interface GitConfig {
  autoCommit: boolean;
  autoCommitInterval: number; // en minutes
  commitMessageTemplate: string;
  branchNamingConvention: string;
  protectedBranches: string[];
  requirePullRequest: boolean;
  enableHooks: boolean;
}

/**
 * Gestionnaire Git pour la Sandbox Hanuman
 * Gère les dépôts Git, commits automatiques et intégration avec VS Code
 */
export class GitManager extends EventEmitter {
  private repositories: Map<string, GitRepository> = new Map();
  private config: GitConfig;
  private autoCommitInterval: NodeJS.Timeout | null = null;

  constructor() {
    super();
    this.initializeConfig();
    this.startAutoCommitService();
  }

  /**
   * Initialise la configuration Git par défaut
   */
  private initializeConfig(): void {
    this.config = {
      autoCommit: true,
      autoCommitInterval: 15, // 15 minutes
      commitMessageTemplate: 'feat({{agentName}}): {{description}}\n\n- {{changes}}\n\nGenerated by Hanuman Sandbox',
      branchNamingConvention: '{{agentName}}/{{feature}}-{{timestamp}}',
      protectedBranches: ['main', 'master', 'production', 'staging'],
      requirePullRequest: true,
      enableHooks: true
    };
  }

  /**
   * Démarre le service de commit automatique
   */
  private startAutoCommitService(): void {
    if (this.config.autoCommit && !this.autoCommitInterval) {
      this.autoCommitInterval = setInterval(() => {
        this.performAutoCommits();
      }, this.config.autoCommitInterval * 60 * 1000);

      console.log(`🔄 Service de commit automatique démarré (${this.config.autoCommitInterval}min)`);
    }
  }

  /**
   * Arrête le service de commit automatique
   */
  private stopAutoCommitService(): void {
    if (this.autoCommitInterval) {
      clearInterval(this.autoCommitInterval);
      this.autoCommitInterval = null;
      console.log('🛑 Service de commit automatique arrêté');
    }
  }

  /**
   * Initialise un dépôt Git pour une instance VS Code
   */
  async initializeRepository(instance: VSCodeInstance, options?: {
    remoteUrl?: string;
    initialBranch?: string;
    autoCommit?: boolean;
  }): Promise<GitRepository> {
    const repoId = `repo_${instance.id}`;
    const repoPath = `${instance.config.workspaceFolder}`;

    try {
      // Initialiser le dépôt Git
      await this.executeGitCommand(repoPath, ['init']);
      
      // Configurer l'utilisateur Git
      await this.configureGitUser(repoPath, instance);
      
      // Créer le fichier .gitignore
      await this.createGitignore(repoPath);
      
      // Commit initial
      await this.executeGitCommand(repoPath, ['add', '.']);
      await this.executeGitCommand(repoPath, [
        'commit', 
        '-m', 
        'feat: Initial commit by Hanuman Sandbox\n\nGenerated project structure and configuration'
      ]);

      // Configurer la branche par défaut
      const initialBranch = options?.initialBranch || 'main';
      await this.executeGitCommand(repoPath, ['branch', '-M', initialBranch]);

      // Ajouter le remote si fourni
      if (options?.remoteUrl) {
        await this.executeGitCommand(repoPath, ['remote', 'add', 'origin', options.remoteUrl]);
      }

      const repository: GitRepository = {
        id: repoId,
        name: `${instance.agentId || instance.organId || 'unknown'}-repo`,
        path: repoPath,
        remoteUrl: options?.remoteUrl,
        branch: initialBranch,
        status: await this.getRepositoryStatus(repoPath),
        agentId: instance.agentId,
        organId: instance.organId,
        createdAt: new Date(),
        lastActivity: new Date()
      };

      this.repositories.set(repoId, repository);
      this.emit('repository:initialized', repository);
      
      console.log(`📁 Dépôt Git initialisé: ${repository.name}`);
      return repository;

    } catch (error) {
      console.error(`❌ Erreur lors de l'initialisation du dépôt:`, error);
      this.emit('repository:error', { repoId, error });
      throw error;
    }
  }

  /**
   * Configure l'utilisateur Git pour le dépôt
   */
  private async configureGitUser(repoPath: string, instance: VSCodeInstance): Promise<void> {
    const agentName = instance.agentId || instance.organId || 'hanuman-agent';
    const email = `${agentName}@hanuman.sandbox`;

    await this.executeGitCommand(repoPath, ['config', 'user.name', agentName]);
    await this.executeGitCommand(repoPath, ['config', 'user.email', email]);
  }

  /**
   * Crée un fichier .gitignore adapté aux projets Hanuman
   */
  private async createGitignore(repoPath: string): Promise<void> {
    const gitignoreContent = `
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/settings.json
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Temporary folders
tmp/
temp/

# Hanuman specific
.hanuman/
sandbox-data/
agent-state/
organ-memory/

# Security
*.key
*.pem
*.p12
*.pfx
secrets/
    `.trim();

    // Simuler l'écriture du fichier .gitignore
    console.log(`📝 Création du fichier .gitignore`);
  }

  /**
   * Exécute une commande Git
   */
  private async executeGitCommand(repoPath: string, args: string[]): Promise<string> {
    const command = `git ${args.join(' ')}`;
    console.log(`🔧 Exécution: ${command} dans ${repoPath}`);
    
    // Simuler l'exécution de la commande Git
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return 'Command executed successfully';
  }

  /**
   * Obtient le statut d'un dépôt
   */
  async getRepositoryStatus(repoPath: string): Promise<GitStatus> {
    try {
      // Simuler l'obtention du statut Git
      const status: GitStatus = {
        ahead: 0,
        behind: 0,
        staged: [],
        unstaged: [],
        untracked: [],
        conflicts: [],
        clean: true
      };

      return status;
    } catch (error) {
      console.error(`❌ Erreur lors de l'obtention du statut:`, error);
      throw error;
    }
  }

  /**
   * Effectue un commit automatique pour tous les dépôts
   */
  private async performAutoCommits(): Promise<void> {
    console.log('🔄 Vérification des commits automatiques...');

    for (const [repoId, repo] of this.repositories) {
      try {
        const status = await this.getRepositoryStatus(repo.path);
        
        if (!status.clean && (status.unstaged.length > 0 || status.untracked.length > 0)) {
          await this.autoCommit(repo);
        }
      } catch (error) {
        console.error(`❌ Erreur lors du commit automatique pour ${repo.name}:`, error);
        this.emit('repository:auto-commit-error', { repo, error });
      }
    }
  }

  /**
   * Effectue un commit automatique pour un dépôt
   */
  private async autoCommit(repo: GitRepository): Promise<void> {
    try {
      // Ajouter tous les fichiers modifiés
      await this.executeGitCommand(repo.path, ['add', '.']);
      
      // Générer le message de commit
      const commitMessage = this.generateCommitMessage(repo);
      
      // Effectuer le commit
      await this.executeGitCommand(repo.path, ['commit', '-m', commitMessage]);
      
      // Mettre à jour le statut
      repo.status = await this.getRepositoryStatus(repo.path);
      repo.lastActivity = new Date();
      
      this.repositories.set(repo.id, repo);
      this.emit('repository:auto-committed', repo);
      
      console.log(`✅ Commit automatique effectué pour ${repo.name}`);
    } catch (error) {
      console.error(`❌ Erreur lors du commit automatique:`, error);
      throw error;
    }
  }

  /**
   * Génère un message de commit automatique
   */
  private generateCommitMessage(repo: GitRepository): string {
    const agentName = repo.agentId || repo.organId || 'unknown';
    const timestamp = new Date().toISOString().slice(0, 16).replace('T', ' ');
    
    return `feat(${agentName}): Auto-save progress at ${timestamp}

- Automatic commit by Hanuman Sandbox
- Preserving development progress
- Agent: ${agentName}

Generated by Hanuman Sandbox Auto-Commit Service`;
  }

  /**
   * Crée une nouvelle branche
   */
  async createBranch(repoId: string, branchName: string, fromBranch?: string): Promise<void> {
    const repo = this.repositories.get(repoId);
    if (!repo) throw new Error(`Dépôt ${repoId} non trouvé`);

    try {
      const args = ['checkout', '-b', branchName];
      if (fromBranch) {
        args.push(fromBranch);
      }

      await this.executeGitCommand(repo.path, args);
      
      repo.branch = branchName;
      repo.lastActivity = new Date();
      this.repositories.set(repoId, repo);
      
      this.emit('repository:branch-created', { repo, branchName });
      console.log(`🌿 Branche créée: ${branchName}`);
    } catch (error) {
      console.error(`❌ Erreur lors de la création de la branche:`, error);
      throw error;
    }
  }

  /**
   * Change de branche
   */
  async switchBranch(repoId: string, branchName: string): Promise<void> {
    const repo = this.repositories.get(repoId);
    if (!repo) throw new Error(`Dépôt ${repoId} non trouvé`);

    try {
      await this.executeGitCommand(repo.path, ['checkout', branchName]);
      
      repo.branch = branchName;
      repo.lastActivity = new Date();
      this.repositories.set(repoId, repo);
      
      this.emit('repository:branch-switched', { repo, branchName });
      console.log(`🔄 Basculé vers la branche: ${branchName}`);
    } catch (error) {
      console.error(`❌ Erreur lors du changement de branche:`, error);
      throw error;
    }
  }

  /**
   * Effectue un commit manuel
   */
  async commit(repoId: string, message: string, files?: string[]): Promise<void> {
    const repo = this.repositories.get(repoId);
    if (!repo) throw new Error(`Dépôt ${repoId} non trouvé`);

    try {
      // Ajouter les fichiers spécifiés ou tous les fichiers
      if (files && files.length > 0) {
        await this.executeGitCommand(repo.path, ['add', ...files]);
      } else {
        await this.executeGitCommand(repo.path, ['add', '.']);
      }
      
      // Effectuer le commit
      await this.executeGitCommand(repo.path, ['commit', '-m', message]);
      
      // Mettre à jour le statut
      repo.status = await this.getRepositoryStatus(repo.path);
      repo.lastActivity = new Date();
      this.repositories.set(repoId, repo);
      
      this.emit('repository:committed', { repo, message });
      console.log(`✅ Commit effectué: ${message}`);
    } catch (error) {
      console.error(`❌ Erreur lors du commit:`, error);
      throw error;
    }
  }

  /**
   * Pousse les commits vers le remote
   */
  async push(repoId: string, remote = 'origin', branch?: string): Promise<void> {
    const repo = this.repositories.get(repoId);
    if (!repo) throw new Error(`Dépôt ${repoId} non trouvé`);

    try {
      const targetBranch = branch || repo.branch;
      await this.executeGitCommand(repo.path, ['push', remote, targetBranch]);
      
      repo.lastActivity = new Date();
      this.repositories.set(repoId, repo);
      
      this.emit('repository:pushed', { repo, remote, branch: targetBranch });
      console.log(`📤 Push effectué vers ${remote}/${targetBranch}`);
    } catch (error) {
      console.error(`❌ Erreur lors du push:`, error);
      throw error;
    }
  }

  /**
   * Tire les commits depuis le remote
   */
  async pull(repoId: string, remote = 'origin', branch?: string): Promise<void> {
    const repo = this.repositories.get(repoId);
    if (!repo) throw new Error(`Dépôt ${repoId} non trouvé`);

    try {
      const targetBranch = branch || repo.branch;
      await this.executeGitCommand(repo.path, ['pull', remote, targetBranch]);
      
      repo.status = await this.getRepositoryStatus(repo.path);
      repo.lastActivity = new Date();
      this.repositories.set(repoId, repo);
      
      this.emit('repository:pulled', { repo, remote, branch: targetBranch });
      console.log(`📥 Pull effectué depuis ${remote}/${targetBranch}`);
    } catch (error) {
      console.error(`❌ Erreur lors du pull:`, error);
      throw error;
    }
  }

  /**
   * Obtient l'historique des commits
   */
  async getCommitHistory(repoId: string, limit = 10): Promise<GitCommit[]> {
    const repo = this.repositories.get(repoId);
    if (!repo) throw new Error(`Dépôt ${repoId} non trouvé`);

    try {
      // Simuler l'obtention de l'historique
      const commits: GitCommit[] = [
        {
          hash: 'abc123',
          author: repo.agentId || 'Hanuman Agent',
          email: `${repo.agentId}@hanuman.sandbox`,
          date: new Date(),
          message: 'feat: Initial commit by Hanuman Sandbox',
          files: ['README.md', 'package.json']
        }
      ];

      return commits;
    } catch (error) {
      console.error(`❌ Erreur lors de l'obtention de l'historique:`, error);
      throw error;
    }
  }

  /**
   * Obtient la liste des branches
   */
  async getBranches(repoId: string): Promise<GitBranch[]> {
    const repo = this.repositories.get(repoId);
    if (!repo) throw new Error(`Dépôt ${repoId} non trouvé`);

    try {
      // Simuler l'obtention des branches
      const branches: GitBranch[] = [
        {
          name: repo.branch,
          current: true,
          remote: false,
          lastCommit: 'abc123',
          ahead: 0,
          behind: 0
        }
      ];

      return branches;
    } catch (error) {
      console.error(`❌ Erreur lors de l'obtention des branches:`, error);
      throw error;
    }
  }

  /**
   * Met à jour la configuration Git
   */
  updateConfig(newConfig: Partial<GitConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Redémarrer le service de commit automatique si nécessaire
    if ('autoCommit' in newConfig || 'autoCommitInterval' in newConfig) {
      this.stopAutoCommitService();
      if (this.config.autoCommit) {
        this.startAutoCommitService();
      }
    }
    
    this.emit('config:updated', this.config);
  }

  /**
   * Obtient tous les dépôts
   */
  getRepositories(): GitRepository[] {
    return Array.from(this.repositories.values());
  }

  /**
   * Obtient un dépôt par ID
   */
  getRepository(repoId: string): GitRepository | undefined {
    return this.repositories.get(repoId);
  }

  /**
   * Obtient les dépôts d'un agent
   */
  getRepositoriesByAgent(agentId: string): GitRepository[] {
    return Array.from(this.repositories.values())
      .filter(repo => repo.agentId === agentId);
  }

  /**
   * Supprime un dépôt
   */
  async removeRepository(repoId: string): Promise<void> {
    const repo = this.repositories.get(repoId);
    if (!repo) return;

    this.repositories.delete(repoId);
    this.emit('repository:removed', repo);
    console.log(`🗑️ Dépôt supprimé: ${repo.name}`);
  }

  /**
   * Obtient la configuration actuelle
   */
  getConfig(): GitConfig {
    return { ...this.config };
  }

  /**
   * Nettoie les ressources
   */
  cleanup(): void {
    this.stopAutoCommitService();
    this.repositories.clear();
  }
}

export default GitManager;
