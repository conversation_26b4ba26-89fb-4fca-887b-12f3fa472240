// ========================================
// HANUMAN-VIMANA INTEGRATION - TYPES & INTERFACES
// Types pour l'intégration du framework spirituel Vimana
// ========================================

import { HanumanContext } from '../orchestration/types';

/**
 * Configuration divine de Vimana
 */
export interface VimanaConfig {
  identity: {
    name: string;
    subtitle: string;
    mission: string;
    mantras: {
      creation: string;
      preservation: string;
      transformation: string;
    };
  };
  cosmicPrinciples: {
    goldenRatio: number;
    sacredFrequency: number;
    omFrequency: number;
    fibonacciSequence: number[];
    sacredNumbers: number[];
  };
  trigunaBalance: {
    sattva: { weight: number; focus: string };
    rajas: { weight: number; focus: string };
    tamas: { weight: number; focus: string };
  };
  divineStandards: {
    codeQuality: {
      minCoverage: number;
      maxComplexity: number;
      goldenRatioLayout: boolean;
      sacredNaming: boolean;
    };
    performance: {
      maxResponseTime: number;
      cosmicFrequency: number;
      omInterval: number;
      fibonacciChunking: boolean;
    };
    spiritual: {
      mantrasInCode: boolean;
      blessedCommits: boolean;
      cosmicTiming: boolean;
      chakraValidation: boolean;
    };
  };
  divineAgents: {
    brahmaCreator: DivineAgentConfig;
    vishnuPreserver: DivineAgentConfig;
    shivaTransformer: DivineAgentConfig;
  };
}

/**
 * Configuration d'un agent divin
 */
export interface DivineAgentConfig {
  model: string;
  temperature: number;
  mantra: string;
  focus: string[];
}

/**
 * Types d'agents divins
 */
export type DivineAgentType = 'brahma' | 'vishnu' | 'shiva';

/**
 * Commande pour génération divine
 */
export interface DivineGenerationRequest {
  agentId: string;
  command: string;
  divineAgent: DivineAgentType;
  context: HanumanContext;
  cosmicPrinciples: {
    goldenRatio: boolean;
    mantras: boolean;
    sacredGeometry: boolean;
    fibonacciStructure: boolean;
    cosmicFrequency: boolean;
  };
  trigunaPreference?: {
    sattva?: number;
    rajas?: number;
    tamas?: number;
  };
}

/**
 * Résultat de génération divine
 */
export interface DivineGenerationResult {
  success: boolean;
  code: string;
  mantras: string[];
  spiritualQuality: number; // 0-108
  goldenRatioCompliance: number; // 0-100%
  fibonacciAlignment: boolean;
  cosmicFrequency: number; // Hz
  trigunaBalance: TriGunaBalance;
  divineAgent: DivineAgentType;
  blessings: string[];
  metadata: {
    generationTime: number;
    codeLength: number;
    mantrasCount: number;
    sacredNumbers: number[];
    cosmicTimestamp: Date;
  };
}

/**
 * Balance Tri-Guna
 */
export interface TriGunaBalance {
  sattva: number; // % pureté, stabilité, harmonie
  rajas: number;  // % passion, action, création
  tamas: number;  // % inertie, transformation, destruction nécessaire
  harmony: number; // Score d'harmonie 0-100
  recommendation: string;
}

/**
 * Validation de géométrie sacrée
 */
export interface SacredGeometryValidation {
  goldenRatioCompliance: number; // 0-100%
  fibonacciAlignment: boolean;
  sacredProportions: {
    phi: number; // 1.618...
    fibonacci: number[];
    sacredNumbers: number[];
  };
  cosmicResonance: {
    frequency: number; // Hz
    harmony: number; // 0-100
    omAlignment: boolean; // 136.1 Hz
    sacredAlignment: boolean; // 432 Hz
  };
  geometricPatterns: {
    spirals: number;
    symmetries: number;
    fractals: number;
  };
  spiritualScore: number; // 0-108
}

/**
 * Enhancement avec mantras
 */
export interface MantraEnhancement {
  originalCode: string;
  enhancedCode: string;
  mantrasAdded: {
    creation: string[];
    preservation: string[];
    transformation: string[];
  };
  spiritualComments: string[];
  sacredVariableNames: Record<string, string>;
  blessedFunctions: string[];
  divineMetadata: {
    totalMantras: number;
    spiritualDensity: number; // mantras per line
    cosmicAlignment: number; // 0-100
  };
}

/**
 * Contexte spirituel pour Vimana
 */
export interface SpiritualContext {
  intention: string;
  cosmicPhase: 'creation' | 'preservation' | 'transformation';
  lunarPhase?: 'new' | 'waxing' | 'full' | 'waning';
  chakraFocus?: 'root' | 'sacral' | 'solar' | 'heart' | 'throat' | 'third-eye' | 'crown';
  elementalBalance?: {
    earth: number;
    water: number;
    fire: number;
    air: number;
    space: number;
  };
  vedicTiming?: {
    auspicious: boolean;
    muhurta: string;
    nakshatra: string;
  };
}

/**
 * Métriques spirituelles
 */
export interface SpiritualMetrics {
  totalGenerations: number;
  divineAgentUsage: Record<DivineAgentType, number>;
  averageSpiritualQuality: number;
  goldenRatioCompliance: number;
  mantrasGenerated: number;
  cosmicHarmony: number;
  trigunaBalance: TriGunaBalance;
  sacredGeometryScore: number;
  lastBlessedGeneration: Date;
  cosmicCycles: {
    creationCycle: number;
    preservationCycle: number;
    transformationCycle: number;
  };
}

/**
 * Configuration du pont Vimana
 */
export interface VimanaBridgeConfig {
  vimanaPath: string;
  enableSpiritualEnhancement: boolean;
  defaultDivineAgent: DivineAgentType;
  cosmicValidation: boolean;
  mantrasRequired: boolean;
  goldenRatioThreshold: number;
  spiritualQualityThreshold: number;
  trigunaBalanceRequired: boolean;
  sacredGeometryValidation: boolean;
  cosmicTimingOptimization: boolean;
}

/**
 * Événement spirituel
 */
export interface SpiritualEvent {
  type: 'divine-generation' | 'cosmic-validation' | 'triguna-balance' | 'mantra-enhancement' | 'sacred-geometry';
  timestamp: Date;
  agentId: string;
  divineAgent?: DivineAgentType;
  data: any;
  spiritualSignificance: number; // 0-108
  cosmicAlignment: number; // 0-100
  mantras: string[];
}

/**
 * Workflow spirituel
 */
export interface SpiritualWorkflow {
  id: string;
  name: string;
  phases: SpiritualWorkflowPhase[];
  currentPhase: number;
  trigunaBalance: TriGunaBalance;
  cosmicAlignment: number;
  spiritualGoals: string[];
  mantras: string[];
  sacredGeometry: boolean;
  divineAgentSequence: DivineAgentType[];
}

/**
 * Phase de workflow spirituel
 */
export interface SpiritualWorkflowPhase {
  name: string;
  divineAgent: DivineAgentType;
  mantra: string;
  cosmicPrinciples: string[];
  expectedOutcome: string;
  spiritualValidation: {
    required: boolean;
    threshold: number;
  };
  trigunaFocus: keyof TriGunaBalance;
}

/**
 * Template divin
 */
export interface DivineTemplate {
  id: string;
  name: string;
  description: string;
  divineAgent: DivineAgentType;
  mantra: string;
  template: string;
  variables: DivineTemplateVariable[];
  cosmicPrinciples: string[];
  sacredGeometry: boolean;
  goldenRatioLayout: boolean;
  spiritualEnhancements: string[];
  trigunaBalance: TriGunaBalance;
  examples: string[];
  blessings: string[];
}

/**
 * Variable de template divin
 */
export interface DivineTemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'sacred-number' | 'mantra' | 'cosmic-frequency';
  description: string;
  required: boolean;
  defaultValue?: any;
  sacredValidation?: {
    goldenRatio?: boolean;
    fibonacci?: boolean;
    sacredNumber?: boolean;
    cosmicFrequency?: boolean;
  };
  spiritualSignificance?: string;
}

/**
 * Erreur spirituelle
 */
export class SpiritualError extends Error {
  constructor(
    message: string,
    public code: string,
    public spiritualContext?: SpiritualContext,
    public cosmicAlignment?: number,
    public recommendedMantras?: string[]
  ) {
    super(message);
    this.name = 'SpiritualError';
  }
}

/**
 * Réponse de l'API Vimana
 */
export interface VimanaAPIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    spiritualGuidance?: string;
    recommendedMantras?: string[];
  };
  spiritualMetadata?: {
    divineAgent: DivineAgentType;
    mantra: string;
    cosmicAlignment: number;
    spiritualQuality: number;
    trigunaBalance: TriGunaBalance;
    blessings: string[];
  };
  cosmicTimestamp: Date;
}

/**
 * État du système Vimana
 */
export interface VimanaSystemState {
  status: 'initializing' | 'blessed' | 'active' | 'meditating' | 'transforming' | 'error';
  divineAgents: {
    brahma: { active: boolean; lastInvocation: Date; creationsCount: number };
    vishnu: { active: boolean; lastInvocation: Date; preservationsCount: number };
    shiva: { active: boolean; lastInvocation: Date; transformationsCount: number };
  };
  cosmicAlignment: number;
  spiritualQuality: number;
  trigunaBalance: TriGunaBalance;
  sacredGeometry: SacredGeometryValidation;
  mantrasActive: string[];
  lastBlessing: Date;
  cosmicCycles: {
    creation: number;
    preservation: number;
    transformation: number;
  };
}

/**
 * Configuration de l'intégration Hanuman-Vimana
 */
export interface HanumanVimanaIntegration {
  enabled: boolean;
  bridgeMode: 'full' | 'selective' | 'spiritual-only';
  defaultRouting: {
    creation: 'vimana' | 'roo-code' | 'auto';
    preservation: 'vimana' | 'roo-code' | 'auto';
    transformation: 'vimana' | 'roo-code' | 'auto';
  };
  spiritualEnhancement: {
    mantras: boolean;
    sacredGeometry: boolean;
    cosmicTiming: boolean;
    trigunaBalance: boolean;
  };
  qualityThresholds: {
    spiritualQuality: number;
    cosmicAlignment: number;
    goldenRatioCompliance: number;
  };
  fallbackStrategy: 'roo-code' | 'error' | 'manual';
}

/**
 * Callback pour événements spirituels
 */
export type SpiritualEventCallback<T = any> = (event: SpiritualEvent, data: T) => void | Promise<void>;

/**
 * Interface pour le pont Vimana
 */
export interface VimanaIntegrationBridge {
  initialize(): Promise<void>;
  generateDivineCode(request: DivineGenerationRequest): Promise<DivineGenerationResult>;
  validateSacredGeometry(code: string): Promise<SacredGeometryValidation>;
  enhanceWithMantras(code: string, context: SpiritualContext): Promise<MantraEnhancement>;
  balanceTriGuna(workload: any[]): Promise<TriGunaBalance>;
  getSystemState(): VimanaSystemState;
  invokeBlessing(mantra: string): Promise<void>;
  onSpiritualEvent(callback: SpiritualEventCallback): void;
  shutdown(): Promise<void>;
}

/**
 * Types d'événements spirituels
 */
export type SpiritualEventType = 
  | 'vimana:divine-generation-started'
  | 'vimana:divine-generation-completed'
  | 'vimana:cosmic-validation-performed'
  | 'vimana:triguna-balance-updated'
  | 'vimana:mantra-enhancement-applied'
  | 'vimana:sacred-geometry-validated'
  | 'vimana:blessing-invoked'
  | 'vimana:cosmic-alignment-changed'
  | 'vimana:spiritual-error-occurred'
  | 'vimana:divine-agent-activated'
  | 'vimana:workflow-phase-completed';
