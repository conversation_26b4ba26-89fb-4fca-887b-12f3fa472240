# 🐳 Configuration Docker et Déploiement Complet
## Hanuman Sandbox Enhanced - VS Code & Roo Code

---

## 📁 Structure des Fichiers de Configuration

```
hanuman-unified/
├── docker-enhanced/
│   ├── sandbox/
│   │   ├── Dockerfile.enhanced
│   │   ├── entrypoint.sh
│   │   └── config/
│   │       ├── vscode-server.json
│   │       ├── roo-code.config.json
│   │       └── hanuman-templates/
│   ├── roo-agents/
│   │   ├── Dockerfile
│   │   └── config/
│   └── monitoring/
│       ├── prometheus.yml
│       └── grafana/
├── docker-compose.enhanced.yml
├── scripts/
│   ├── start-enhanced-sandbox.sh
│   ├── setup-development-environment.sh
│   ├── test-enhanced-features.sh
│   └── deploy-production.sh
├── nginx/
│   ├── nginx.conf
│   └── ssl/
└── config/
    ├── enhanced-sandbox.config.js
    └── environment-templates/
```

---

## 🐳 Dockerfile Enhanced Principal

### docker-enhanced/sandbox/Dockerfile.enhanced

```dockerfile
# ========================================
# HANUMAN SANDBOX ENHANCED - DOCKERFILE
# VS Code Server + Roo Code + API Sandbox
# ========================================

FROM ubuntu:22.04

# Maintainer et métadonnées
LABEL maintainer="Hanuman Enhanced Team <<EMAIL>>"
LABEL description="Sandbox Enhanced avec VS Code, Roo Code et Neural API"
LABEL version="2.0.0"
LABEL architecture="biomimetic-enhanced"

# Variables d'environnement
ENV DEBIAN_FRONTEND=noninteractive \
    NODE_VERSION=18 \
    CODE_SERVER_VERSION=4.20.0 \
    ROO_CODE_VERSION=latest \
    WORKSPACE_DIR=/workspace \
    PROJECTS_DIR=/projects \
    API_PORT=8080 \
    VSCODE_PORT=8081 \
    ROO_CODE_PORT=8082 \
    WEBSOCKET_PORT=8083 \
    TZ=UTC

# Arguments de build
ARG OPENAI_API_KEY=""
ARG HANUMAN_ENV=development
ARG DEBUG_MODE=false

# Installation des dépendances système de base
RUN apt-get update && apt-get install -y \
    # Utilitaires de base
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    tree \
    jq \
    unzip \
    zip \
    sudo \
    # Build tools
    build-essential \
    python3 \
    python3-pip \
    # Outils développement
    nodejs \
    npm \
    # Docker pour agents
    docker.io \
    # Outils réseau
    net-tools \
    iputils-ping \
    telnet \
    # Outils monitoring
    procps \
    lsof \
    # Nettoyage
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Installation de Node.js 18 LTS
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g npm@latest yarn@latest

# Création de l'utilisateur hanuman avec privilèges sudo
RUN useradd -m -s /bin/bash hanuman \
    && echo "hanuman ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers \
    && usermod -aG docker hanuman \
    && mkdir -p ${WORKSPACE_DIR} ${PROJECTS_DIR} \
    && chown -R hanuman:hanuman ${WORKSPACE_DIR} ${PROJECTS_DIR}

# Installation de code-server avec configuration personnalisée
RUN curl -fsSL https://code-server.dev/install.sh | sh -s -- --version=${CODE_SERVER_VERSION} \
    && mkdir -p /home/<USER>/.config/code-server \
    && mkdir -p /home/<USER>/.local/share/code-server/User

# Configuration de code-server
COPY config/vscode-server.json /home/<USER>/.config/code-server/config.yaml
COPY config/vscode-settings.json /home/<USER>/.local/share/code-server/User/settings.json

# Installation des extensions VS Code essentielles + Hanuman spécialisées
USER hanuman
RUN code-server --install-extension ms-vscode.vscode-typescript-next \
    && code-server --install-extension bradlc.vscode-tailwindcss \
    && code-server --install-extension esbenp.prettier-vscode \
    && code-server --install-extension ms-vscode.vscode-json \
    && code-server --install-extension ms-python.python \
    && code-server --install-extension ms-vscode.vscode-eslint \
    && code-server --install-extension GitLab.gitlab-workflow \
    && code-server --install-extension ms-vscode.vscode-docker \
    && code-server --install-extension ms-kubernetes-tools.vscode-kubernetes-tools \
    && code-server --install-extension GitHub.copilot \
    && code-server --install-extension ms-vscode.vscode-theme-hanuman

# Retour en root pour installations globales
USER root

# Installation de Roo Code avec configuration Enhanced
RUN npm install -g roo-code@${ROO_CODE_VERSION} \
    && mkdir -p /home/<USER>/.roo-code/templates \
    && mkdir -p /home/<USER>/.roo-code/prompts

# Copie des templates et configuration Hanuman
COPY config/roo-code.config.json /home/<USER>/.roo-code/config.json
COPY config/hanuman-templates/ /home/<USER>/.roo-code/templates/
COPY config/hanuman-prompts/ /home/<USER>/.roo-code/prompts/

# Configuration du workspace de développement
WORKDIR ${WORKSPACE_DIR}

# Copie du code source de l'application Enhanced
COPY package*.json ./
COPY tsconfig.json ./
COPY .eslintrc.js ./

# Installation des dépendances Node.js
RUN npm ci --only=production \
    && npm cache clean --force

# Copie des sources application
COPY src/ ./src/
COPY config/ ./config/
COPY templates/ ./templates/
COPY scripts/ ./scripts/

# Build de l'application TypeScript
RUN npm run build

# Installation des outils de développement globaux
RUN npm install -g \
    typescript \
    ts-node \
    nodemon \
    jest \
    @types/node \
    eslint \
    prettier \
    concurrently

# Configuration des permissions
RUN chown -R hanuman:hanuman ${WORKSPACE_DIR} \
    && chown -R hanuman:hanuman /home/<USER>/.roo-code \
    && chown -R hanuman:hanuman /home/<USER>/.config \
    && chown -R hanuman:hanuman /home/<USER>/.local

# Installation des outils Hanuman spécialisés
COPY tools/ ./tools/
RUN chmod +x ./tools/*.sh \
    && ./tools/install-hanuman-tools.sh

# Configuration de l'environnement utilisateur
USER hanuman

# Initialisation du workspace avec projets exemple
RUN mkdir -p ${WORKSPACE_DIR}/agents \
    && mkdir -p ${WORKSPACE_DIR}/organs \
    && mkdir -p ${WORKSPACE_DIR}/interfaces \
    && mkdir -p ${WORKSPACE_DIR}/services \
    && mkdir -p ${WORKSPACE_DIR}/tests \
    && mkdir -p ${WORKSPACE_DIR}/docs

# Copie des projets exemple et templates
COPY --chown=hanuman:hanuman examples/ ${WORKSPACE_DIR}/examples/
COPY --chown=hanuman:hanuman templates/ ${WORKSPACE_DIR}/templates/

# Configuration Git par défaut
RUN git config --global user.name "Hanuman Enhanced Agent" \
    && git config --global user.email "<EMAIL>" \
    && git config --global init.defaultBranch main

# Exposition des ports
EXPOSE ${API_PORT} ${VSCODE_PORT} ${ROO_CODE_PORT} ${WEBSOCKET_PORT}

# Variables d'environnement runtime
ENV NODE_ENV=production \
    LOG_LEVEL=info \
    HANUMAN_ENHANCED=true \
    ROO_CODE_ENABLED=true \
    NEURAL_COMMUNICATION=true

# Health check personnalisé
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${API_PORT}/health || exit 1

# Point d'entrée avec script d'initialisation
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
CMD ["start-enhanced"]
```

### docker-enhanced/sandbox/entrypoint.sh

```bash
#!/bin/bash

# ========================================
# HANUMAN SANDBOX ENHANCED - ENTRYPOINT
# Script de démarrage avec initialisation complète
# ========================================

set -e

# Configuration des variables
export WORKSPACE_DIR=${WORKSPACE_DIR:-/workspace}
export API_PORT=${API_PORT:-8080}
export VSCODE_PORT=${VSCODE_PORT:-8081}
export ROO_CODE_PORT=${ROO_CODE_PORT:-8082}
export WEBSOCKET_PORT=${WEBSOCKET_PORT:-8083}

# Couleurs pour logging
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Fonction de logging avec couleurs
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] HANUMAN ENHANCED:${NC} $1"
}

error() {
    echo -e "${RED}[ERROR] HANUMAN ENHANCED:${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING] HANUMAN ENHANCED:${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO] HANUMAN ENHANCED:${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS] HANUMAN ENHANCED:${NC} $1"
}

# Banner de démarrage
print_banner() {
    echo -e "${PURPLE}"
    cat << 'EOF'
 ╔══════════════════════════════════════════════════════════════╗
 ║                    HANUMAN ENHANCED                          ║
 ║              Sandbox Biomimétique Intelligent               ║
 ║                                                              ║
 ║  🧠 Neural Architecture  🤖 Autonomous Agents              ║
 ║  💻 VS Code Enhanced    🎨 Roo Code Integration            ║
 ║  🔗 Neural Communication 📡 Real-time WebSocket            ║
 ╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# Initialisation de l'environnement
initialize_environment() {
    log "Initialisation de l'environnement Enhanced..."
    
    # Créer les répertoires nécessaires
    mkdir -p ${WORKSPACE_DIR}/{agents,organs,interfaces,services,tests,docs,logs}
    mkdir -p /tmp/hanuman/{logs,cache,temp}
    
    # Permissions
    chown -R hanuman:hanuman ${WORKSPACE_DIR}
    chown -R hanuman:hanuman /tmp/hanuman
    
    # Variables d'environnement runtime
    export HANUMAN_WORKSPACE=${WORKSPACE_DIR}
    export HANUMAN_LOGS_DIR=/tmp/hanuman/logs
    export HANUMAN_CACHE_DIR=/tmp/hanuman/cache
    
    success "Environnement initialisé"
}

# Configuration de Roo Code
configure_roo_code() {
    log "Configuration de Roo Code Enhanced..."
    
    # Vérifier la configuration Roo Code
    if [ -f "/home/<USER>/.roo-code/config.json" ]; then
        info "Configuration Roo Code trouvée"
    else
        warning "Configuration Roo Code manquante, création par défaut..."
        cat > /home/<USER>/.roo-code/config.json << 'EOF'
{
  "model": "gpt-4",
  "temperature": 0.7,
  "maxTokens": 4000,
  "hanumanIntegration": true,
  "templates": {
    "hanuman-agent": "/home/<USER>/.roo-code/templates/hanuman-agent.template.ts",
    "hanuman-organ": "/home/<USER>/.roo-code/templates/hanuman-organ.template.ts",
    "hanuman-interface": "/home/<USER>/.roo-code/templates/hanuman-interface.template.tsx"
  },
  "prompts": {
    "create-agent": "Créer un agent Hanuman biomimétique avec architecture neurale",
    "enhance-organ": "Améliorer un organe existant avec nouvelles capacités",
    "generate-interface": "Générer interface React moderne pour agents Hanuman"
  }
}
EOF
        chown hanuman:hanuman /home/<USER>/.roo-code/config.json
    fi
    
    # Vérifier les templates
    if [ ! -d "/home/<USER>/.roo-code/templates" ]; then
        mkdir -p /home/<USER>/.roo-code/templates
        chown -R hanuman:hanuman /home/<USER>/.roo-code/templates
    fi
    
    success "Roo Code configuré"
}

# Configuration de VS Code Server
configure_vscode() {
    log "Configuration de VS Code Server..."
    
    # Configuration par défaut si manquante
    if [ ! -f "/home/<USER>/.config/code-server/config.yaml" ]; then
        warning "Configuration VS Code manquante, création par défaut..."
        mkdir -p /home/<USER>/.config/code-server
        cat > /home/<USER>/.config/code-server/config.yaml << EOF
bind-addr: 0.0.0.0:${VSCODE_PORT}
auth: password
password: ${VSCODE_PASSWORD:-hanuman123}
cert: false
disable-telemetry: true
disable-update-check: true
EOF
        chown -R hanuman:hanuman /home/<USER>/.config/code-server
    fi
    
    # Settings VS Code personnalisés Hanuman
    if [ ! -f "/home/<USER>/.local/share/code-server/User/settings.json" ]; then
        mkdir -p /home/<USER>/.local/share/code-server/User
        cat > /home/<USER>/.local/share/code-server/User/settings.json << 'EOF'
{
  "workbench.colorTheme": "Dark+ (default dark)",
  "workbench.iconTheme": "vscode-icons",
  "editor.fontSize": 14,
  "editor.fontFamily": "'Fira Code', 'Cascadia Code', monospace",
  "editor.fontLigatures": true,
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.updateImportsOnFileMove.enabled": "always",
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.autofetch": true,
  "terminal.integrated.defaultProfile.linux": "bash",
  "terminal.integrated.fontSize": 13,
  "files.associations": {
    "*.hanuman": "typescript",
    "*.neural": "json"
  },
  "emmet.includeLanguages": {
    "typescript": "html"
  },
  "workbench.startupEditor": "welcomePage",
  "explorer.confirmDelete": false,
  "explorer.confirmDragAndDrop": false,
  "window.zoomLevel": 0,
  "breadcrumbs.enabled": true,
  "editor.minimap.enabled": true,
  "editor.renderWhitespace": "boundary",
  "editor.rulers": [80, 120],
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "extensions.autoUpdate": false,
  "telemetry.telemetryLevel": "off",
  "hanuman.enhanced": true,
  "rooCode.enabled": true,
  "rooCode.autoComplete": true,
  "rooCode.contextAware": true,
  "rooCode.hanumanIntegration": true
}
EOF
        chown -R hanuman:hanuman /home/<USER>/.local/share/code-server
    fi
    
    success "VS Code Server configuré"
}

# Initialisation des projets exemple
initialize_sample_projects() {
    log "Initialisation des projets exemple..."
    
    # Projet Agent exemple
    if [ ! -d "${WORKSPACE_DIR}/examples/sample-agent" ]; then
        mkdir -p ${WORKSPACE_DIR}/examples/sample-agent
        cat > ${WORKSPACE_DIR}/examples/sample-agent/SampleAgent.ts << 'EOF'
import { EventEmitter } from 'events';

/**
 * Exemple d'Agent Hanuman Enhanced
 * Démonstration des capacités biomimétiques
 */
export class SampleAgent extends EventEmitter {
  private isActive = false;

  constructor() {
    super();
    this.initialize();
  }

  private async initialize(): Promise<void> {
    console.log('🤖 Initialisation SampleAgent...');
    this.emit('agent:initialized', { agent: 'SampleAgent' });
  }

  async activate(): Promise<void> {
    this.isActive = true;
    this.emit('agent:activated', { agent: 'SampleAgent' });
    console.log('✅ SampleAgent activé');
  }

  getStatus(): { active: boolean } {
    return { active: this.isActive };
  }
}

export default SampleAgent;
EOF

        # Package.json pour l'exemple
        cat > ${WORKSPACE_DIR}/examples/sample-agent/package.json << 'EOF'
{
  "name": "hanuman-sample-agent",
  "version": "1.0.0",
  "description": "Exemple d'agent Hanuman Enhanced",
  "main": "SampleAgent.ts",
  "scripts": {
    "build": "tsc",
    "start": "node dist/SampleAgent.js",
    "dev": "ts-node SampleAgent.ts"
  },
  "keywords": ["hanuman", "agent", "biomimetic", "ai"],
  "author": "Hanuman Enhanced System",
  "license": "MIT"
}
EOF
        chown -R hanuman:hanuman ${WORKSPACE_DIR}/examples
    fi
    
    success "Projets exemple initialisés"
}

# Démarrage des services
start_services() {
    log "Démarrage des services Enhanced..."
    
    # Démarrer les services en arrière-plan avec supervision
    (
        # API Sandbox Enhanced
        info "Démarrage API Sandbox Enhanced sur port ${API_PORT}..."
        cd ${WORKSPACE_DIR}
        node dist/src/index.js --port=${API_PORT} > /tmp/hanuman/logs/api.log 2>&1 &
        echo $! > /tmp/hanuman/api.pid
        
        # Attendre que l'API soit prête
        sleep 5
        
        # VS Code Server
        info "Démarrage VS Code Server sur port ${VSCODE_PORT}..."
        su - hanuman -c "code-server --config /home/<USER>/.config/code-server/config.yaml ${WORKSPACE_DIR}" > /tmp/hanuman/logs/vscode.log 2>&1 &
        echo $! > /tmp/hanuman/vscode.pid
        
        # Roo Code Service (si disponible)
        if command -v roo-code-server &> /dev/null; then
            info "Démarrage Roo Code Service sur port ${ROO_CODE_PORT}..."
            roo-code-server --port=${ROO_CODE_PORT} > /tmp/hanuman/logs/roo-code.log 2>&1 &
            echo $! > /tmp/hanuman/roo-code.pid
        fi
        
    ) &
    
    success "Services démarrés en arrière-plan"
}

# Vérification de santé des services
health_check() {
    log "Vérification de santé des services..."
    
    local all_healthy=true
    
    # Vérifier API Sandbox
    if curl -sf http://localhost:${API_PORT}/health > /dev/null 2>&1; then
        success "✅ API Sandbox: Healthy"
    else
        error "❌ API Sandbox: Non accessible"
        all_healthy=false
    fi
    
    # Vérifier VS Code Server
    if curl -sf http://localhost:${VSCODE_PORT} > /dev/null 2>&1; then
        success "✅ VS Code Server: Healthy"
    else
        warning "⚠️ VS Code Server: En cours de démarrage..."
    fi
    
    if [ "$all_healthy" = true ]; then
        success "Tous les services sont opérationnels"
    else
        warning "Certains services nécessitent une attention"
    fi
}

# Affichage des informations de connexion
display_connection_info() {
    log "Informations de connexion Hanuman Enhanced:"
    echo ""
    echo -e "${BLUE}🌐 Services Accessibles:${NC}"
    echo -e "  ${GREEN}📡 API Sandbox Enhanced:${NC}  http://localhost:${API_PORT}"
    echo -e "  ${GREEN}💻 VS Code Server:${NC}        http://localhost:${VSCODE_PORT}"
    echo -e "  ${GREEN}🤖 Roo Code Service:${NC}      http://localhost:${ROO_CODE_PORT}"
    echo -e "  ${GREEN}🔌 WebSocket:${NC}             ws://localhost:${WEBSOCKET_PORT}"
    echo ""
    echo -e "${BLUE}🔑 Identifiants par défaut:${NC}"
    echo -e "  ${YELLOW}VS Code:${NC} Password = ${VSCODE_PASSWORD:-hanuman123}"
    echo ""
    echo -e "${BLUE}📁 Répertoires importants:${NC}"
    echo -e "  ${YELLOW}Workspace:${NC}     ${WORKSPACE_DIR}"
    echo -e "  ${YELLOW}Projects:${NC}      ${WORKSPACE_DIR}/examples"
    echo -e "  ${YELLOW}Templates:${NC}     ${WORKSPACE_DIR}/templates"
    echo -e "  ${YELLOW}Logs:${NC}          /tmp/hanuman/logs"
    echo ""
    echo -e "${BLUE}🚀 Commandes utiles:${NC}"
    echo -e "  ${YELLOW}Logs API:${NC}      tail -f /tmp/hanuman/logs/api.log"
    echo -e "  ${YELLOW}Logs VS Code:${NC}  tail -f /tmp/hanuman/logs/vscode.log"
    echo -e "  ${YELLOW}Health Check:${NC}  curl http://localhost:${API_PORT}/health"
    echo ""
}

# Surveillance continue des services
monitor_services() {
    log "Démarrage surveillance des services..."
    
    while true; do
        sleep 30
        
        # Vérifier les PIDs des services
        if [ -f /tmp/hanuman/api.pid ]; then
            if ! kill -0 $(cat /tmp/hanuman/api.pid) 2>/dev/null; then
                error "API Sandbox arrêtée, redémarrage..."
                # Logique de redémarrage ici
            fi
        fi
        
        if [ -f /tmp/hanuman/vscode.pid ]; then
            if ! kill -0 $(cat /tmp/hanuman/vscode.pid) 2>/dev/null; then
                warning "VS Code Server arrêté, redémarrage..."
                # Logique de redémarrage ici
            fi
        fi
    done
}

# Gestion des signaux pour arrêt propre
cleanup_and_exit() {
    log "Signal reçu, arrêt des services..."
    
    # Arrêter les services
    if [ -f /tmp/hanuman/api.pid ]; then
        kill $(cat /tmp/hanuman/api.pid) 2>/dev/null || true
        rm -f /tmp/hanuman/api.pid
    fi
    
    if [ -f /tmp/hanuman/vscode.pid ]; then
        kill $(cat /tmp/hanuman/vscode.pid) 2>/dev/null || true
        rm -f /tmp/hanuman/vscode.pid
    fi
    
    if [ -f /tmp/hanuman/roo-code.pid ]; then
        kill $(cat /tmp/hanuman/roo-code.pid) 2>/dev/null || true
        rm -f /tmp/hanuman/roo-code.pid
    fi
    
    success "Arrêt des services terminé"
    exit 0
}

# Gestion des signaux
trap cleanup_and_exit SIGTERM SIGINT

# ========================================
# FONCTION PRINCIPALE
# ========================================

main() {
    print_banner
    
    log "Démarrage Hanuman Sandbox Enhanced..."
    
    # Mode de démarrage
    case "${1:-start-enhanced}" in
        "start-enhanced")
            log "Mode: Enhanced Full Stack"
            initialize_environment
            configure_roo_code
            configure_vscode
            initialize_sample_projects
            start_services
            
            # Attendre que les services soient prêts
            sleep 10
            health_check
            display_connection_info
            
            # Surveiller les services
            monitor_services
            ;;
            
        "start-api-only")
            log "Mode: API Sandbox seulement"
            initialize_environment
            cd ${WORKSPACE_DIR}
            node dist/src/index.js --port=${API_PORT}
            ;;
            
        "start-vscode-only")
            log "Mode: VS Code Server seulement"
            configure_vscode
            su - hanuman -c "code-server --config /home/<USER>/.config/code-server/config.yaml ${WORKSPACE_DIR}"
            ;;
            
        "shell")
            log "Mode: Shell interactif"
            initialize_environment
            exec /bin/bash
            ;;
            
        "health-check")
            health_check
            ;;
            
        *)
            error "Mode non reconnu: $1"
            echo "Modes disponibles:"
            echo "  start-enhanced   - Démarrage complet (défaut)"
            echo "  start-api-only   - API Sandbox seulement"
            echo "  start-vscode-only - VS Code Server seulement"
            echo "  shell            - Shell interactif"
            echo "  health-check     - Vérification santé"
            exit 1
            ;;
    esac
}

# Exécution de la fonction principale
main "$@"
```

---

## 📋 Docker Compose Enhanced Complet

### docker-compose.enhanced.yml

```yaml
version: '3.8'

# ========================================
# HANUMAN SANDBOX ENHANCED - DOCKER COMPOSE
# Architecture complète avec monitoring
# ========================================

services:
  # 🧠 HANUMAN SANDBOX ENHANCED - Conteneur Principal
  hanuman-sandbox-enhanced:
    build:
      context: ./docker-enhanced/sandbox
      dockerfile: Dockerfile.enhanced
      args:
        - OPENAI_API_KEY=${OPENAI_API_KEY:-}
        - HANUMAN_ENV=${HANUMAN_ENV:-development}
        - DEBUG_MODE=${DEBUG_MODE:-false}
    container_name: hanuman-sandbox-enhanced
    hostname: hanuman-sandbox
    restart: unless-stopped
    
    ports:
      - "${API_PORT:-8080}:8080"           # API Sandbox Enhanced
      - "${VSCODE_PORT:-8081}:8081"        # VS Code Server
      - "${ROO_CODE_PORT:-8082}:8082"      # Roo Code Service  
      - "${WEBSOCKET_PORT:-8083}:8083"     # WebSocket Server
    
    environment:
      # Configuration générale
      - NODE_ENV=${NODE_ENV:-production}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - DEBUG_MODE=${DEBUG_MODE:-false}
      - TZ=UTC
      
      # Ports de service
      - API_PORT=8080
      - VSCODE_PORT=8081
      - ROO_CODE_PORT=8082
      - WEBSOCKET_PORT=8083
      
      # Configuration VS Code
      - VSCODE_PASSWORD=${VSCODE_PASSWORD:-hanuman123}
      - VSCODE_WORKSPACE=/workspace
      - VSCODE_EXTENSIONS_AUTO_INSTALL=true
      
      # Configuration Roo Code
      - ROO_CODE_API_KEY=${OPENAI_API_KEY:-}
      - ROO_CODE_MODEL=${ROO_CODE_MODEL:-gpt-4}
      - ROO_CODE_TEMPERATURE=${ROO_CODE_TEMPERATURE:-0.7}
      - ROO_CODE_MAX_TOKENS=${ROO_CODE_MAX_TOKENS:-4000}
      
      # Intégration Hanuman Neural
      - HANUMAN_NEURAL_ENABLED=true
      - HANUMAN_ORCHESTRATOR_URL=http://cortex-central:8080
      - KAFKA_BROKERS=kafka:9092
      - REDIS_URL=redis://redis:6379
      - WEAVIATE_URL=http://weaviate:8080
      
      # Configuration Sandbox
      - SANDBOX_SECURITY_LEVEL=${SANDBOX_SECURITY_LEVEL:-medium}
      - CONTAINER_ISOLATION=enabled
      - NETWORK_MONITORING=enabled
      - FILE_SYSTEM_ENCRYPTION=false
      
      # Monitoring et Performance
      - PROMETHEUS_ENABLED=true
      - PROMETHEUS_PORT=9090
      - METRICS_COLLECTION=true
      - HEALTH_CHECK_INTERVAL=30s
      
      # Limites de ressources
      - MEMORY_LIMIT=6G
      - CPU_LIMIT=4
      - MAX_CONCURRENT_OPERATIONS=20
      - MAX_AGENTS_PER_SANDBOX=10
    
    volumes:
      # Workspace et projets persistants
      - hanuman_workspace:/workspace
      - hanuman_projects:/projects
      - hanuman_templates:/workspace/templates:ro
      
      # Configuration et données
      - hanuman_config:/home/<USER>/.config
      - hanuman_roo_code:/home/<USER>/.roo-code
      - hanuman_vscode_extensions:/home/<USER>/.local/share/code-server/extensions
      
      # Logs et cache
      - hanuman_logs:/tmp/hanuman/logs
      - hanuman_cache:/tmp/hanuman/cache
      
      # Docker socket pour gestion conteneurs (sécurisé)
      - /var/run/docker.sock:/var/run/docker.sock:ro
      
      # Configuration externe
      - ./config/enhanced-sandbox.config.js:/workspace/config/sandbox.config.js:ro
      - ./templates:/workspace/templates:ro
    
    depends_on:
      - cortex-central
      - kafka
      - redis
      - weaviate
      - prometheus
    
    networks:
      - neural-network
      - sandbox-network
      - monitoring-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 120s
    
    deploy:
      resources:
        limits:
          memory: 6G
          cpus: '4'
        reservations:
          memory: 3G
          cpus: '2'
    
    labels:
      # Traefik
      - "traefik.enable=true"
      - "traefik.http.routers.hanuman-sandbox.rule=Host(`sandbox.hanuman.local`)"
      - "traefik.http.routers.hanuman-sandbox.entrypoints=websecure"
      - "traefik.http.routers.hanuman-sandbox.tls.certresolver=letsencrypt"
      
      # Prometheus
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=8080"
      - "prometheus.io/path=/metrics"
      
      # Metadata
      - "hanuman.component=sandbox-enhanced"
      - "hanuman.version=2.0.0"
      - "hanuman.environment=${HANUMAN_ENV:-development}"

  # 🤖 ROO AGENT RUNTIME - Service dédié pour agents Roo Code
  roo-agent-runtime:
    build:
      context: ./docker-enhanced/roo-agents
      dockerfile: Dockerfile
    container_name: roo-agent-runtime
    hostname: roo-agents
    restart: unless-stopped
    
    ports:
      - "${ROO_AGENTS_PORT:-9000}:9000"    # Roo Agent API
      - "${ROO_AGENTS_WS_PORT:-9001}:9001" # Roo Agent WebSocket
    
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=9000
      - WEBSOCKET_PORT=9001
      - LOG_LEVEL=${LOG_LEVEL:-info}
      
      # Intégration Hanuman
      - HANUMAN_NEURAL_URL=http://cortex-central:8080
      - SANDBOX_API_URL=http://hanuman-sandbox-enhanced:8080
      - KAFKA_BROKERS=kafka:9092
      - REDIS_URL=redis://redis:6379
      
      # Configuration Roo Code
      - ROO_CODE_API_KEY=${OPENAI_API_KEY:-}
      - ROO_CODE_MODEL=${ROO_CODE_MODEL:-gpt-4}
      - ROO_TEMPLATES_PATH=/app/templates
      - ROO_CONTEXT_WINDOW=8000
      - ROO_MAX_TOKENS=4000
      
      # Performance et limites
      - MAX_CONCURRENT_AGENTS=10
      - AGENT_TIMEOUT=300000
      - MEMORY_CLEANUP_INTERVAL=60000
      - REQUEST_RATE_LIMIT=100
    
    volumes:
      - roo_agents_data:/app/data
      - roo_templates:/app/templates
      - roo_agents_logs:/app/logs
      - ./config/roo-agents.config.js:/app/config/config.js:ro
    
    depends_on:
      - hanuman-sandbox-enhanced
      - kafka
      - redis
    
    networks:
      - neural-network
      - sandbox-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '2'
        reservations:
          memory: 1G
          cpus: '1'

  # 📊 PROMETHEUS - Monitoring et métriques
  prometheus:
    image: prom/prometheus:latest
    container_name: hanuman-prometheus
    restart: unless-stopped
    
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    
    volumes:
      - ./docker-enhanced/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    
    networks:
      - monitoring-network
      - neural-network
    
    labels:
      - "prometheus.io/scrape=false"

  # 📈 GRAFANA - Dashboards et visualisation
  grafana:
    image: grafana/grafana:latest
    container_name: hanuman-grafana
    restart: unless-stopped
    
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-hanuman123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
      - GF_DASHBOARDS_DEFAULT_HOME_DASHBOARD_PATH=/var/lib/grafana/dashboards/hanuman-overview.json
      - GF_FEATURE_TOGGLES_ENABLE=ngalert
    
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker-enhanced/monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
      - ./docker-enhanced/monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    
    depends_on:
      - prometheus
    
    networks:
      - monitoring-network
    
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.hanuman.local`)"

  # 🔄 NGINX - Reverse Proxy et Load Balancer
  nginx:
    image: nginx:alpine
    container_name: hanuman-nginx
    restart: unless-stopped
    
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    
    depends_on:
      - hanuman-sandbox-enhanced
      - roo-agent-runtime
      - grafana
    
    networks:
      - sandbox-network
      - monitoring-network
    
    labels:
      - "traefik.enable=false"

  # 🗃️ REDIS - Cache et session store
  redis:
    image: redis:7-alpine
    container_name: hanuman-redis
    restart: unless-stopped
    
    ports:
      - "${REDIS_PORT:-6379}:6379"
    
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    
    networks:
      - neural-network
    
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 📨 KAFKA - Messaging neural
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: hanuman-kafka
    restart: unless-stopped
    
    ports:
      - "${KAFKA_PORT:-9092}:9092"
    
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 5
      KAFKA_DEFAULT_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
    
    volumes:
      - kafka_data:/var/lib/kafka/data
    
    depends_on:
      - zookeeper
    
    networks:
      - neural-network

  # 🔗 ZOOKEEPER - Coordination Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: hanuman-zookeeper
    restart: unless-stopped
    
    ports:
      - "${ZOOKEEPER_PORT:-2181}:2181"
    
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_INIT_LIMIT: 5
      ZOOKEEPER_SYNC_LIMIT: 2
    
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    
    networks:
      - neural-network

  # 🧠 WEAVIATE - Base de données vectorielle
  weaviate:
    image: semitechnologies/weaviate:latest
    container_name: hanuman-weaviate
    restart: unless-stopped
    
    ports:
      - "${WEAVIATE_PORT:-8080}:8080"
    
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai,text2vec-cohere,text2vec-huggingface,ref2vec-centroid,generative-openai,qna-openai'
      CLUSTER_HOSTNAME: 'node1'
      LOG_LEVEL: 'info'
    
    volumes:
      - weaviate_data:/var/lib/weaviate
    
    networks:
      - neural-network

  # 🧠 CORTEX CENTRAL - Orchestrateur Hanuman (dépendance)
  cortex-central:
    image: hanuman/cortex-central:latest
    container_name: hanuman-cortex-central
    restart: unless-stopped
    
    ports:
      - "${CORTEX_PORT:-8080}:8080"
    
    environment:
      - KAFKA_BROKERS=kafka:9092
      - REDIS_URL=redis:6379
      - WEAVIATE_URL=weaviate:8080
      - NEURAL_NETWORK_MODE=active
    
    depends_on:
      - kafka
      - redis
      - weaviate
    
    networks:
      - neural-network

# ========================================
# VOLUMES PERSISTANTS
# ========================================

volumes:
  # Workspace et projets
  hanuman_workspace:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/workspace
  
  hanuman_projects:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/projects
  
  hanuman_templates:
    driver: local
  
  # Configuration utilisateur
  hanuman_config:
    driver: local
  
  hanuman_roo_code:
    driver: local
  
  hanuman_vscode_extensions:
    driver: local
  
  # Logs et cache
  hanuman_logs:
    driver: local
  
  hanuman_cache:
    driver: local
  
  # Services Roo Agents
  roo_agents_data:
    driver: local
  
  roo_templates:
    driver: local
  
  roo_agents_logs:
    driver: local
  
  # Monitoring
  prometheus_data:
    driver: local
  
  grafana_data:
    driver: local
  
  # Infrastructure
  redis_data:
    driver: local
  
  kafka_data:
    driver: local
  
  zookeeper_data:
    driver: local
  
  zookeeper_logs:
    driver: local
  
  weaviate_data:
    driver: local
  
  # Nginx
  nginx_logs:
    driver: local

# ========================================
# RÉSEAUX
# ========================================

networks:
  # Réseau neural pour communication Hanuman
  neural-network:
    external: true
    
  # Réseau sandbox pour développement
  sandbox-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    labels:
      - "project=hanuman-sandbox-enhanced"
      - "environment=${HANUMAN_ENV:-development}"
  
  # Réseau monitoring
  monitoring-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
```

---

## 🚀 Scripts de Déploiement

### scripts/start-enhanced-sandbox.sh

```bash
#!/bin/bash

# ========================================
# HANUMAN ENHANCED - SCRIPT DE DÉMARRAGE COMPLET
# Déploiement et initialisation de l'environnement
# ========================================

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
ENV_FILE="${PROJECT_DIR}/.env"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Fonctions utilitaires
log() { echo -e "${GREEN}[$(date +'%H:%M:%S')] ✅${NC} $1"; }
info() { echo -e "${BLUE}[$(date +'%H:%M:%S')] ℹ️${NC} $1"; }
warn() { echo -e "${YELLOW}[$(date +'%H:%M:%S')] ⚠️${NC} $1"; }
error() { echo -e "${RED}[$(date +'%H:%M:%S')] ❌${NC} $1" >&2; }
success() { echo -e "${CYAN}[$(date +'%H:%M:%S')] 🎉${NC} $1"; }

# Banner
print_banner() {
    echo -e "${PURPLE}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════════════╗
║                        HANUMAN ENHANCED SANDBOX                      ║
║                     Déploiement Automatisé Complet                  ║
║                                                                      ║
║  🧠 Neural Architecture    🤖 Autonomous Agents                     ║
║  💻 VS Code + Roo Code     📡 Real-time Communication               ║
║  🐳 Docker Orchestration  📊 Complete Monitoring                    ║
╚══════════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}\n"
}

# Vérification des prérequis
check_prerequisites() {
    log "Vérification des prérequis système..."
    
    local missing_tools=()
    
    # Docker
    if ! command -v docker &> /dev/null; then
        missing_tools+=("docker")
    else
        info "Docker: $(docker --version)"
    fi
    
    # Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        missing_tools+=("docker-compose")
    else
        info "Docker Compose: $(docker-compose --version)"
    fi
    
    # Node.js (optionnel pour développement local)
    if command -v node &> /dev/null; then
        info "Node.js: $(node --version)"
    else
        warn "Node.js non installé (optionnel)"
    fi
    
    # Curl pour tests
    if ! command -v curl &> /dev/null; then
        missing_tools+=("curl")
    fi
    
    # jq pour traitement JSON
    if ! command -v jq &> /dev/null; then
        warn "jq non installé (recommandé pour les tests)"
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        error "Outils manquants: ${missing_tools[*]}"
        echo "Veuillez installer les outils manquants et réessayer."
        exit 1
    fi
    
    # Vérifier que Docker est démarré
    if ! docker info &> /dev/null; then
        error "Docker n'est pas démarré. Veuillez démarrer Docker et réessayer."
        exit 1
    fi
    
    success "Tous les prérequis sont satisfaits"
}

# Création des répertoires et fichiers nécessaires
setup_project_structure() {
    log "Configuration de la structure du projet..."
    
    # Répertoires de données
    mkdir -p "${PROJECT_DIR}/data/workspace"
    mkdir -p "${PROJECT_DIR}/data/projects"
    mkdir -p "${PROJECT_DIR}/data/logs"
    mkdir -p "${PROJECT_DIR}/data/cache"
    
    # Répertoires de configuration
    mkdir -p "${PROJECT_DIR}/config"
    mkdir -p "${PROJECT_DIR}/templates"
    mkdir -p "${PROJECT_DIR}/docker-enhanced/sandbox/config"
    mkdir -p "${PROJECT_DIR}/docker-enhanced/roo-agents/config"
    mkdir -p "${PROJECT_DIR}/docker-enhanced/monitoring/grafana/dashboards"
    mkdir -p "${PROJECT_DIR}/docker-enhanced/monitoring/grafana/provisioning"
    
    # Répertoires Nginx
    mkdir -p "${PROJECT_DIR}/nginx/ssl"
    
    # Permissions
    chmod -R 755 "${PROJECT_DIR}/data"
    chmod -R 755 "${PROJECT_DIR}/config"
    
    success "Structure du projet configurée"
}

# Configuration de l'environnement
setup_environment() {
    log "Configuration de l'environnement..."
    
    # Créer .env s'il n'existe pas
    if [ ! -f "$ENV_FILE" ]; then
        info "Création du fichier .env..."
        cat > "$ENV_FILE" << 'EOF'
# ========================================
# HANUMAN ENHANCED - CONFIGURATION
# ========================================

# Environnement général
HANUMAN_ENV=development
NODE_ENV=production
DEBUG_MODE=false
LOG_LEVEL=info

# API Keys (À CONFIGURER)
OPENAI_API_KEY=your_openai_api_key_here
ROO_CODE_API_KEY=your_openai_api_key_here

# Ports des services
API_PORT=8080
VSCODE_PORT=8081
ROO_CODE_PORT=8082
WEBSOCKET_PORT=8083
ROO_AGENTS_PORT=9000
ROO_AGENTS_WS_PORT=9001

# Services infrastructure
REDIS_PORT=6379
KAFKA_PORT=9092
ZOOKEEPER_PORT=2181
WEAVIATE_PORT=8080
CORTEX_PORT=8080

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# Nginx
HTTP_PORT=80
HTTPS_PORT=443

# Authentification
VSCODE_PASSWORD=hanuman123
GRAFANA_USER=admin
GRAFANA_PASSWORD=hanuman123

# Configuration Roo Code
ROO_CODE_MODEL=gpt-4
ROO_CODE_TEMPERATURE=0.7
ROO_CODE_MAX_TOKENS=4000

# Sandbox
SANDBOX_SECURITY_LEVEL=medium

# Ressources
MEMORY_LIMIT=6G
CPU_LIMIT=4
EOF
        warn "⚠️ Fichier .env créé. Veuillez configurer vos clés API avant de continuer !"
        echo ""
        echo "Éditez le fichier .env pour configurer :"
        echo "  - OPENAI_API_KEY: Votre clé API OpenAI pour Roo Code"
        echo "  - Autres paramètres selon vos besoins"
        echo ""
        read -p "Appuyez sur Entrée après avoir configuré .env..."
    else
        info "Fichier .env existant trouvé"
    fi
    
    # Charger les variables d'environnement
    source "$ENV_FILE"
    
    # Vérifier les variables critiques
    if [ -z "$OPENAI_API_KEY" ] || [ "$OPENAI_API_KEY" = "your_openai_api_key_here" ]; then
        warn "OPENAI_API_KEY non configurée - Roo Code fonctionnera en mode dégradé"
    fi
    
    success "Environnement configuré"
}

# Création des réseaux Docker
create_networks() {
    log "Création des réseaux Docker..."
    
    # Réseau neural (externe, peut déjà exister)
    if ! docker network ls | grep -q neural-network; then
        info "Création du réseau neural-network..."
        docker network create neural-network \
            --driver bridge \
            --subnet=172.20.0.0/16 \
            --label="project=hanuman-enhanced" \
            --label="network=neural"
    else
        info "Réseau neural-network existe déjà"
    fi
    
    success "Réseaux Docker configurés"
}

# Construction des images Docker
build_images() {
    log "Construction des images Docker..."
    
    # Image sandbox enhanced
    info "Construction de l'image Hanuman Sandbox Enhanced..."
    docker-compose -f "${PROJECT_DIR}/docker-compose.enhanced.yml" build hanuman-sandbox-enhanced
    
    # Image Roo Agents
    info "Construction de l'image Roo Agent Runtime..."
    docker-compose -f "${PROJECT_DIR}/docker-compose.enhanced.yml" build roo-agent-runtime
    
    success "Images Docker construites"
}

# Démarrage des services
start_services() {
    log "Démarrage des services Hanuman Enhanced..."
    
    # Démarrer les services de base d'abord
    info "Démarrage de l'infrastructure de base..."
    docker-compose -f "${PROJECT_DIR}/docker-compose.enhanced.yml" up -d \
        zookeeper kafka redis weaviate cortex-central prometheus grafana
    
    # Attendre que les services de base soient prêts
    info "Attente de l'initialisation des services de base..."
    wait_for_service "http://localhost:${REDIS_PORT:-6379}" "Redis" 30
    wait_for_service "http://localhost:${PROMETHEUS_PORT:-9090}" "Prometheus" 60
    
    # Démarrer les services principaux
    info "Démarrage des services principaux..."
    docker-compose -f "${PROJECT_DIR}/docker-compose.enhanced.yml" up -d \
        hanuman-sandbox-enhanced roo-agent-runtime nginx
    
    success "Services démarrés"
}

# Attendre qu'un service soit prêt
wait_for_service() {
    local url=$1
    local name=$2
    local timeout=${3:-30}
    local count=0
    
    info "Attente de $name ($url)..."
    
    while [ $count -lt $timeout ]; do
        if curl -sf "$url" > /dev/null 2>&1; then
            success "$name est prêt"
            return 0
        fi
        
        echo -n "."
        sleep 2
        count=$((count + 1))
    done
    
    warn "$name n'est pas encore prêt après ${timeout} tentatives"
    return 1
}

# Vérification de l'état des services
check_services_health() {
    log "Vérification de l'état des services..."
    
    local services=(
        "http://localhost:${API_PORT:-8080}/health:API Sandbox Enhanced"
        "http://localhost:${VSCODE_PORT:-8081}:VS Code Server"
        "http://localhost:${ROO_AGENTS_PORT:-9000}/health:Roo Agent Runtime"
        "http://localhost:${PROMETHEUS_PORT:-9090}/-/ready:Prometheus"
        "http://localhost:${GRAFANA_PORT:-3000}/api/health:Grafana"
    )
    
    local all_healthy=true
    
    for service in "${services[@]}"; do
        local url="${service%:*}"
        local name="${service#*:}"
        
        if curl -sf "$url" > /dev/null 2>&1; then
            success "✅ $name: Opérationnel"
        else
            error "❌ $name: Non accessible ($url)"
            all_healthy=false
        fi
    done
    
    if [ "$all_healthy" = true ]; then
        success "🎉 Tous les services sont opérationnels !"
    else
        warn "⚠️ Certains services nécessitent une vérification"
    fi
    
    return $all_healthy
}

# Tests de fonctionnalité
run_functionality_tests() {
    log "Exécution des tests de fonctionnalité..."
    
    # Test API Sandbox
    info "Test de l'API Sandbox..."
    local api_response=$(curl -s "http://localhost:${API_PORT:-8080}/status" | jq -r '.server.running // false' 2>/dev/null)
    if [ "$api_response" = "true" ]; then
        success "✅ API Sandbox: Fonctionnelle"
    else
        warn "⚠️ API Sandbox: Réponse inattendue"
    fi
    
    # Test templates Roo Code
    info "Test des templates Roo Code..."
    local templates_response=$(curl -s "http://localhost:${API_PORT:-8080}/api/roo/templates" | jq -r '.count // 0' 2>/dev/null)
    if [ "$templates_response" -gt 0 ]; then
        success "✅ Templates Roo Code: $templates_response templates disponibles"
    else
        warn "⚠️ Templates Roo Code: Aucun template trouvé"
    fi
    
    # Test commande en langage naturel
    info "Test de commande en langage naturel..."
    local cmd_payload='{"agentId":"test-agent","command":"Créer un agent de test simple"}'
    local cmd_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$cmd_payload" \
        "http://localhost:${API_PORT:-8080}/api/vscode/command" | jq -r '.status // "error"' 2>/dev/null)
    
    if [ "$cmd_response" = "success" ]; then
        success "✅ Commande langage naturel: Fonctionnelle"
    else
        warn "⚠️ Commande langage naturel: Test échoué"
    fi
    
    success "Tests de fonctionnalité terminés"
}

# Affichage des informations de connexion
display_connection_info() {
    log "Informations de connexion Hanuman Enhanced"
    echo ""
    echo -e "${CYAN}🌟 === HANUMAN ENHANCED SANDBOX DÉPLOYÉ === 🌟${NC}"
    echo ""
    echo -e "${BLUE}🌐 Services Principaux:${NC}"
    echo -e "  ${GREEN}📡 API Sandbox Enhanced:${NC}     http://localhost:${API_PORT:-8080}"
    echo -e "  ${GREEN}💻 VS Code Server:${NC}           http://localhost:${VSCODE_PORT:-8081}"
    echo -e "  ${GREEN}🤖 Roo Agent Runtime:${NC}        http://localhost:${ROO_AGENTS_PORT:-9000}"
    echo -e "  ${GREEN}🔌 WebSocket:${NC}                ws://localhost:${WEBSOCKET_PORT:-8083}"
    echo ""
    echo -e "${BLUE}📊 Monitoring et Gestion:${NC}"
    echo -e "  ${GREEN}📈 Grafana Dashboard:${NC}        http://localhost:${GRAFANA_PORT:-3000}"
    echo -e "  ${GREEN}📊 Prometheus:${NC}               http://localhost:${PROMETHEUS_PORT:-9090}"
    echo -e "  ${GREEN}🔄 Nginx Proxy:${NC}              http://localhost:${HTTP_PORT:-80}"
    echo ""
    echo -e "${BLUE}🔑 Identifiants:${NC}"
    echo -e "  ${YELLOW}VS Code:${NC}     Password = ${VSCODE_PASSWORD:-hanuman123}"
    echo -e "  ${YELLOW}Grafana:${NC}     ${GRAFANA_USER:-admin} / ${GRAFANA_PASSWORD:-hanuman123}"
    echo ""
    echo -e "${BLUE}📁 Points d'accès importants:${NC}"
    echo -e "  ${YELLOW}Health Check:${NC}     http://localhost:${API_PORT:-8080}/health"
    echo -e "  ${YELLOW}API Status:${NC}       http://localhost:${API_PORT:-8080}/status"
    echo -e "  ${YELLOW}Templates:${NC}        http://localhost:${API_PORT:-8080}/api/roo/templates"
    echo ""
    echo -e "${BLUE}🚀 Commandes utiles:${NC}"
    echo -e "  ${YELLOW}Logs services:${NC}    docker-compose -f docker-compose.enhanced.yml logs -f"
    echo -e "  ${YELLOW}Arrêt propre:${NC}     docker-compose -f docker-compose.enhanced.yml down"
    echo -e "  ${YELLOW}Redémarrage:${NC}      docker-compose -f docker-compose.enhanced.yml restart"
    echo -e "  ${YELLOW}Shell sandbox:${NC}     docker exec -it hanuman-sandbox-enhanced /bin/bash"
    echo ""
    echo -e "${BLUE}📚 Documentation:${NC}"
    echo -e "  ${YELLOW}README:${NC}           Consultez le README.md pour plus d'informations"
    echo -e "  ${YELLOW}Tests:${NC}            Exécutez ./scripts/test-enhanced-features.sh"
    echo ""
}

# Gestion des erreurs et nettoyage
cleanup_on_error() {
    error "Erreur détectée, nettoyage en cours..."
    
    # Arrêter les services si démarrés
    if docker-compose -f "${PROJECT_DIR}/docker-compose.enhanced.yml" ps | grep -q "Up"; then
        warn "Arrêt des services..."
        docker-compose -f "${PROJECT_DIR}/docker-compose.enhanced.yml" down
    fi
    
    exit 1
}

# Gestion des signaux
trap cleanup_on_error ERR
trap 'warn "Script interrompu"; exit 130' INT

# ========================================
# FONCTION PRINCIPALE
# ========================================

main() {
    print_banner
    
    local start_time=$(date +%s)
    
    log "🚀 Début du déploiement Hanuman Enhanced Sandbox"
    echo ""
    
    # Étapes de déploiement
    check_prerequisites
    setup_project_structure
    setup_environment
    create_networks
    build_images
    start_services
    
    # Attente et vérifications
    info "⏳ Attente de l'initialisation complète des services..."
    sleep 30
    
    check_services_health
    run_functionality_tests
    
    # Informations finales
    display_connection_info
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    success "🎉 Déploiement Hanuman Enhanced terminé avec succès !"
    success "⏱️ Durée totale: ${duration}s"
    echo ""
    echo -e "${PURPLE}🧠 Hanuman Enhanced Sandbox est maintenant prêt à l'usage !${NC}"
    echo -e "${PURPLE}🤖 Commencez par créer votre premier agent avec des commandes en langage naturel.${NC}"
    echo ""
}

# Point d'entrée avec gestion des arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "status")
        check_services_health
        ;;
    "test")
        run_functionality_tests
        ;;
    "info")
        display_connection_info
        ;;
    "cleanup")
        log "Nettoyage de l'environnement..."
        docker-compose -f "${PROJECT_DIR}/docker-compose.enhanced.yml" down -v
        docker system prune -f
        success "Nettoyage terminé"
        ;;
    *)
        echo "Usage: $0 [deploy|status|test|info|cleanup]"
        echo ""
        echo "Commandes disponibles:"
        echo "  deploy  - Déploiement complet (défaut)"
        echo "  status  - Vérification état des services"
        echo "  test    - Tests de fonctionnalité"
        echo "  info    - Affichage des informations de connexion"
        echo "  cleanup - Nettoyage complet de l'environnement"
        exit 1
        ;;
esac
```

### scripts/test-enhanced-features.sh

```bash
#!/bin/bash

# ========================================
# HANUMAN ENHANCED - TESTS DE FONCTIONNALITÉ
# Suite complète de tests pour validation
# ========================================

set -e

# Configuration
API_URL="http://localhost:8080"
VSCODE_URL="http://localhost:8081"
ROO_AGENTS_URL="http://localhost:9000"
GRAFANA_URL="http://localhost:3000"

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Compteurs
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Fonctions utilitaires
log() { echo -e "${BLUE}[TEST]${NC} $1"; }
pass() { echo -e "${GREEN}[PASS]${NC} $1"; ((TESTS_PASSED++)); }
fail() { echo -e "${RED}[FAIL]${NC} $1"; ((TESTS_FAILED++)); }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }

# Fonction de test générique
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TESTS_TOTAL++))
    log "Test: $test_name"
    
    if eval "$test_command"; then
        pass "$test_name"
        return 0
    else
        fail "$test_name"
        return 1
    fi
}

# Test de santé API Sandbox
test_api_health() {
    local response=$(curl -s -w "%{http_code}" -o /tmp/health_response "$API_URL/health")
    
    if [ "$response" = "200" ]; then
        local status=$(cat /tmp/health_response | jq -r '.status // "unknown"' 2>/dev/null)
        [ "$status" = "healthy" ]
    else
        false
    fi
}

# Test des templates Roo Code
test_roo_code_templates() {
    local response=$(curl -s "$API_URL/api/roo/templates")
    local count=$(echo "$response" | jq -r '.count // 0' 2>/dev/null)
    
    [ "$count" -gt 0 ]
}

# Test de commande en langage naturel
test_natural_language_command() {
    local payload='{"agentId":"test-agent","command":"Créer un agent TestAgent simple avec capacités de base"