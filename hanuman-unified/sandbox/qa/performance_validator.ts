import { EventEmitter } from 'events';

/**
 * Validateur de Performance QA
 * Métriques de performance, temps de réponse, utilisation des ressources et optimisations
 */

// Types pour la validation de performance
export interface PerformanceMetrics {
  id: string;
  timestamp: Date;
  url: string;
  environment: string;
  metrics: {
    // Core Web Vitals
    lcp: number; // Largest Contentful Paint
    fid: number; // First Input Delay
    cls: number; // Cumulative Layout Shift
    
    // Loading Performance
    ttfb: number; // Time to First Byte
    fcp: number; // First Contentful Paint
    si: number; // Speed Index
    tti: number; // Time to Interactive
    tbt: number; // Total Blocking Time
    
    // Resource Usage
    memoryUsage: number;
    cpuUsage: number;
    networkUsage: number;
    
    // Custom Metrics
    domSize: number;
    resourceCount: number;
    jsHeapSize: number;
    renderTime: number;
  };
  scores: PerformanceScores;
  issues: PerformanceIssue[];
  recommendations: PerformanceRecommendation[];
}

export interface PerformanceScores {
  overall: number; // 0-100
  loading: number;
  interactivity: number;
  visualStability: number;
  resourceEfficiency: number;
  coreWebVitals: number;
}

export interface PerformanceIssue {
  id: string;
  type: 'loading' | 'rendering' | 'scripting' | 'memory' | 'network';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  metric: string;
  actualValue: number;
  expectedValue: number;
  suggestion: string;
  priority: number;
}

export interface PerformanceRecommendation {
  id: string;
  category: 'optimization' | 'caching' | 'compression' | 'minification' | 'lazy_loading';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  estimatedImprovement: string;
  implementation: string;
  priority: number;
}

export interface PerformanceTest {
  id: string;
  name: string;
  description: string;
  type: 'load_time' | 'stress' | 'endurance' | 'spike' | 'volume';
  target: PerformanceTestTarget;
  config: PerformanceTestConfig;
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: PerformanceTestResult;
  createdAt: Date;
  updatedAt: Date;
}

export interface PerformanceTestTarget {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  timeout: number;
}

export interface PerformanceTestConfig {
  duration: number; // en secondes
  concurrency: number;
  rampUpTime: number;
  thresholds: PerformanceThresholds;
  scenarios: PerformanceScenario[];
}

export interface PerformanceThresholds {
  responseTime: {
    p50: number;
    p95: number;
    p99: number;
  };
  errorRate: number;
  throughput: number;
  resourceUsage: {
    cpu: number;
    memory: number;
    network: number;
  };
}

export interface PerformanceScenario {
  name: string;
  weight: number;
  steps: PerformanceStep[];
}

export interface PerformanceStep {
  name: string;
  action: 'request' | 'wait' | 'think_time';
  target?: string;
  duration?: number;
  data?: any;
}

export interface PerformanceTestResult {
  summary: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    errorRate: number;
    averageResponseTime: number;
    throughput: number;
    duration: number;
  };
  metrics: PerformanceMetrics;
  timeline: PerformanceDataPoint[];
  errors: PerformanceError[];
  recommendations: PerformanceRecommendation[];
}

export interface PerformanceDataPoint {
  timestamp: Date;
  responseTime: number;
  throughput: number;
  errorRate: number;
  activeUsers: number;
  cpuUsage: number;
  memoryUsage: number;
}

export interface PerformanceError {
  timestamp: Date;
  type: string;
  message: string;
  url: string;
  statusCode?: number;
  count: number;
}

export class PerformanceValidator extends EventEmitter {
  private tests: Map<string, PerformanceTest> = new Map();
  private activeTests: Set<string> = new Set();
  private metricsHistory: Map<string, PerformanceMetrics[]> = new Map();
  private config: PerformanceValidatorConfig;

  constructor(config: PerformanceValidatorConfig) {
    super();
    this.config = config;
    this.initializeDefaultTests();
  }

  /**
   * Initialise les tests de performance par défaut
   */
  private initializeDefaultTests(): void {
    const defaultTests: PerformanceTest[] = [
      {
        id: 'perf_load_homepage',
        name: 'Test de Charge Page d\'Accueil',
        description: 'Test de charge sur la page d\'accueil',
        type: 'load_time',
        target: {
          url: '/',
          method: 'GET',
          timeout: 30000
        },
        config: {
          duration: 300, // 5 minutes
          concurrency: 50,
          rampUpTime: 60,
          thresholds: {
            responseTime: { p50: 1000, p95: 2000, p99: 3000 },
            errorRate: 0.01,
            throughput: 100,
            resourceUsage: { cpu: 80, memory: 80, network: 80 }
          },
          scenarios: [
            {
              name: 'Navigation normale',
              weight: 80,
              steps: [
                { name: 'Charger page d\'accueil', action: 'request', target: '/' },
                { name: 'Temps de réflexion', action: 'think_time', duration: 2000 }
              ]
            }
          ]
        },
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'perf_stress_api',
        name: 'Test de Stress API',
        description: 'Test de stress sur les endpoints API',
        type: 'stress',
        target: {
          url: '/api/data',
          method: 'GET',
          timeout: 10000
        },
        config: {
          duration: 600, // 10 minutes
          concurrency: 200,
          rampUpTime: 120,
          thresholds: {
            responseTime: { p50: 500, p95: 1000, p99: 2000 },
            errorRate: 0.05,
            throughput: 500,
            resourceUsage: { cpu: 90, memory: 85, network: 90 }
          },
          scenarios: [
            {
              name: 'Requêtes API intensives',
              weight: 100,
              steps: [
                { name: 'Appel API', action: 'request', target: '/api/data' },
                { name: 'Pause courte', action: 'think_time', duration: 500 }
              ]
            }
          ]
        },
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    defaultTests.forEach(test => {
      this.tests.set(test.id, test);
    });
  }

  /**
   * Lance un test de performance
   */
  async runPerformanceTest(testId: string): Promise<PerformanceTestResult> {
    const test = this.tests.get(testId);
    if (!test) {
      throw new Error(`Test de performance non trouvé: ${testId}`);
    }

    test.status = 'running';
    this.activeTests.add(testId);

    try {
      this.emit('test:started', test);

      // Simulation d'exécution du test
      const result = await this.executeTest(test);
      
      test.result = result;
      test.status = 'completed';
      test.updatedAt = new Date();

      this.emit('test:completed', { test, result });
      return result;

    } catch (error) {
      test.status = 'failed';
      test.updatedAt = new Date();
      this.emit('test:error', { test, error });
      throw error;

    } finally {
      this.activeTests.delete(testId);
    }
  }

  /**
   * Exécute un test de performance
   */
  private async executeTest(test: PerformanceTest): Promise<PerformanceTestResult> {
    const startTime = Date.now();
    const timeline: PerformanceDataPoint[] = [];
    const errors: PerformanceError[] = [];

    // Simulation de l'exécution du test
    const duration = Math.min(test.config.duration * 1000, 10000); // Max 10s pour la démo
    const intervals = 20;
    const intervalDuration = duration / intervals;

    for (let i = 0; i < intervals; i++) {
      await new Promise(resolve => setTimeout(resolve, intervalDuration));
      
      // Générer des données simulées
      const progress = i / intervals;
      const responseTime = this.generateResponseTime(test, progress);
      const throughput = this.generateThroughput(test, progress);
      const errorRate = this.generateErrorRate(test, progress);

      timeline.push({
        timestamp: new Date(startTime + i * intervalDuration),
        responseTime,
        throughput,
        errorRate,
        activeUsers: Math.floor(test.config.concurrency * progress),
        cpuUsage: 20 + Math.random() * 60,
        memoryUsage: 30 + Math.random() * 50
      });

      // Générer des erreurs occasionnelles
      if (Math.random() < errorRate) {
        errors.push({
          timestamp: new Date(),
          type: 'HTTP_ERROR',
          message: 'Request timeout',
          url: test.target.url,
          statusCode: 500,
          count: 1
        });
      }
    }

    // Calculer les métriques finales
    const avgResponseTime = timeline.reduce((sum, point) => sum + point.responseTime, 0) / timeline.length;
    const avgThroughput = timeline.reduce((sum, point) => sum + point.throughput, 0) / timeline.length;
    const totalErrors = errors.reduce((sum, error) => sum + error.count, 0);
    const totalRequests = Math.floor(avgThroughput * (duration / 1000));
    const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;

    // Générer les métriques de performance
    const metrics = this.generatePerformanceMetrics(test, avgResponseTime);

    // Générer les recommandations
    const recommendations = this.generateRecommendations(metrics, test);

    return {
      summary: {
        totalRequests,
        successfulRequests: totalRequests - totalErrors,
        failedRequests: totalErrors,
        errorRate,
        averageResponseTime: avgResponseTime,
        throughput: avgThroughput,
        duration: duration / 1000
      },
      metrics,
      timeline,
      errors,
      recommendations
    };
  }

  /**
   * Génère un temps de réponse simulé
   */
  private generateResponseTime(test: PerformanceTest, progress: number): number {
    const baseTime = 200;
    const loadFactor = Math.pow(progress, 2) * 500; // Augmentation quadratique avec la charge
    const randomVariation = (Math.random() - 0.5) * 100;
    return Math.max(50, baseTime + loadFactor + randomVariation);
  }

  /**
   * Génère un débit simulé
   */
  private generateThroughput(test: PerformanceTest, progress: number): number {
    const maxThroughput = test.config.thresholds.throughput;
    const efficiency = Math.max(0.3, 1 - progress * 0.4); // Diminution avec la charge
    const randomVariation = (Math.random() - 0.5) * 0.2;
    return Math.max(10, maxThroughput * efficiency * (1 + randomVariation));
  }

  /**
   * Génère un taux d'erreur simulé
   */
  private generateErrorRate(test: PerformanceTest, progress: number): number {
    const baseErrorRate = 0.001;
    const stressFactor = Math.pow(progress, 3) * 0.05; // Augmentation cubique sous stress
    return Math.min(0.1, baseErrorRate + stressFactor);
  }

  /**
   * Génère les métriques de performance
   */
  private generatePerformanceMetrics(test: PerformanceTest, avgResponseTime: number): PerformanceMetrics {
    const lcp = avgResponseTime * 1.5;
    const fid = Math.random() * 100;
    const cls = Math.random() * 0.1;

    const scores: PerformanceScores = {
      overall: this.calculateOverallScore(lcp, fid, cls, avgResponseTime),
      loading: this.calculateLoadingScore(lcp, avgResponseTime),
      interactivity: this.calculateInteractivityScore(fid),
      visualStability: this.calculateVisualStabilityScore(cls),
      resourceEfficiency: 75 + Math.random() * 20,
      coreWebVitals: this.calculateCoreWebVitalsScore(lcp, fid, cls)
    };

    const issues = this.generatePerformanceIssues(lcp, fid, cls, avgResponseTime);

    return {
      id: `metrics_${Date.now()}`,
      timestamp: new Date(),
      url: test.target.url,
      environment: 'test',
      metrics: {
        lcp,
        fid,
        cls,
        ttfb: avgResponseTime * 0.3,
        fcp: avgResponseTime * 0.6,
        si: avgResponseTime * 1.2,
        tti: avgResponseTime * 2,
        tbt: avgResponseTime * 0.4,
        memoryUsage: 50 + Math.random() * 30,
        cpuUsage: 40 + Math.random() * 40,
        networkUsage: 30 + Math.random() * 50,
        domSize: 1000 + Math.random() * 2000,
        resourceCount: 50 + Math.random() * 100,
        jsHeapSize: 10 + Math.random() * 20,
        renderTime: avgResponseTime * 0.8
      },
      scores,
      issues,
      recommendations: []
    };
  }

  /**
   * Calcule le score global de performance
   */
  private calculateOverallScore(lcp: number, fid: number, cls: number, responseTime: number): number {
    const lcpScore = Math.max(0, 100 - (lcp - 2500) / 25);
    const fidScore = Math.max(0, 100 - fid);
    const clsScore = Math.max(0, 100 - cls * 1000);
    const responseScore = Math.max(0, 100 - (responseTime - 1000) / 20);

    return Math.round((lcpScore + fidScore + clsScore + responseScore) / 4);
  }

  /**
   * Calcule le score de chargement
   */
  private calculateLoadingScore(lcp: number, responseTime: number): number {
    const lcpScore = Math.max(0, 100 - (lcp - 2500) / 25);
    const responseScore = Math.max(0, 100 - (responseTime - 1000) / 20);
    return Math.round((lcpScore + responseScore) / 2);
  }

  /**
   * Calcule le score d'interactivité
   */
  private calculateInteractivityScore(fid: number): number {
    return Math.max(0, Math.round(100 - fid));
  }

  /**
   * Calcule le score de stabilité visuelle
   */
  private calculateVisualStabilityScore(cls: number): number {
    return Math.max(0, Math.round(100 - cls * 1000));
  }

  /**
   * Calcule le score Core Web Vitals
   */
  private calculateCoreWebVitalsScore(lcp: number, fid: number, cls: number): number {
    const lcpGood = lcp <= 2500;
    const fidGood = fid <= 100;
    const clsGood = cls <= 0.1;

    const goodMetrics = [lcpGood, fidGood, clsGood].filter(Boolean).length;
    return Math.round((goodMetrics / 3) * 100);
  }

  /**
   * Génère les problèmes de performance
   */
  private generatePerformanceIssues(lcp: number, fid: number, cls: number, responseTime: number): PerformanceIssue[] {
    const issues: PerformanceIssue[] = [];

    if (lcp > 2500) {
      issues.push({
        id: `issue_lcp_${Date.now()}`,
        type: 'loading',
        severity: lcp > 4000 ? 'critical' : 'high',
        title: 'Largest Contentful Paint trop lent',
        description: `LCP de ${lcp.toFixed(0)}ms dépasse le seuil recommandé`,
        impact: 'Expérience de chargement dégradée',
        metric: 'LCP',
        actualValue: lcp,
        expectedValue: 2500,
        suggestion: 'Optimiser les ressources critiques et réduire le temps de chargement',
        priority: lcp > 4000 ? 1 : 2
      });
    }

    if (fid > 100) {
      issues.push({
        id: `issue_fid_${Date.now()}`,
        type: 'scripting',
        severity: fid > 300 ? 'critical' : 'medium',
        title: 'First Input Delay élevé',
        description: `FID de ${fid.toFixed(0)}ms affecte la réactivité`,
        impact: 'Interactions utilisateur ralenties',
        metric: 'FID',
        actualValue: fid,
        expectedValue: 100,
        suggestion: 'Réduire le JavaScript bloquant et optimiser les tâches longues',
        priority: fid > 300 ? 1 : 3
      });
    }

    if (cls > 0.1) {
      issues.push({
        id: `issue_cls_${Date.now()}`,
        type: 'rendering',
        severity: cls > 0.25 ? 'high' : 'medium',
        title: 'Cumulative Layout Shift élevé',
        description: `CLS de ${cls.toFixed(3)} cause des décalages visuels`,
        impact: 'Expérience utilisateur frustrante',
        metric: 'CLS',
        actualValue: cls,
        expectedValue: 0.1,
        suggestion: 'Définir des dimensions pour les images et éviter les insertions dynamiques',
        priority: cls > 0.25 ? 2 : 4
      });
    }

    return issues.sort((a, b) => a.priority - b.priority);
  }

  /**
   * Génère les recommandations d'optimisation
   */
  private generateRecommendations(metrics: PerformanceMetrics, test: PerformanceTest): PerformanceRecommendation[] {
    const recommendations: PerformanceRecommendation[] = [];

    if (metrics.scores.loading < 80) {
      recommendations.push({
        id: `rec_loading_${Date.now()}`,
        category: 'optimization',
        title: 'Optimiser le temps de chargement',
        description: 'Améliorer les performances de chargement des pages',
        impact: 'high',
        effort: 'medium',
        estimatedImprovement: '20-30% d\'amélioration du temps de chargement',
        implementation: 'Compresser les images, minifier CSS/JS, utiliser un CDN',
        priority: 1
      });
    }

    if (metrics.scores.interactivity < 80) {
      recommendations.push({
        id: `rec_interactivity_${Date.now()}`,
        category: 'optimization',
        title: 'Améliorer la réactivité',
        description: 'Réduire les délais d\'interaction utilisateur',
        impact: 'high',
        effort: 'high',
        estimatedImprovement: '15-25% d\'amélioration de la réactivité',
        implementation: 'Code splitting, lazy loading, optimisation du JavaScript',
        priority: 2
      });
    }

    return recommendations.sort((a, b) => a.priority - b.priority);
  }

  // Getters
  getTests(): PerformanceTest[] {
    return Array.from(this.tests.values());
  }

  getActiveTests(): PerformanceTest[] {
    return Array.from(this.tests.values()).filter(test => 
      this.activeTests.has(test.id)
    );
  }

  getMetricsHistory(url: string): PerformanceMetrics[] {
    return this.metricsHistory.get(url) || [];
  }
}

export interface PerformanceValidatorConfig {
  maxConcurrentTests: number;
  defaultTimeout: number;
  enableRealUserMonitoring: boolean;
  enableSyntheticMonitoring: boolean;
  reportingInterval: number;
}
