# 🚀 Hanuman Sandbox Enhanced - Sprint 1
## Architecture Agent-IDE Orchestrator

---

## 📋 APERÇU DU SPRINT 1

Le Sprint 1 implémente l'architecture fondamentale pour le contrôle intelligent des IDE par les agents Hanuman. Cette première phase établit les bases pour permettre aux agents de contrôler VS Code et Roo Code via des commandes en langage naturel.

### 🎯 Objectifs Atteints

✅ **IDEAgentOrchestrator** - Cerveau central pour orchestration  
✅ **NaturalLanguageProcessor** - Analyse des commandes vocales/textuelles  
✅ **AgentVSCodeController** - Automation intelligente VS Code  
✅ **SandboxAPIServerEnhanced** - API REST + WebSocket  
✅ **Démonstration interactive** - Tests et validation  

---

## 🏗️ ARCHITECTURE IMPLÉMENTÉE

### 🧠 Composants Principaux

```
┌─────────────────────────────────────────────────────────────┐
│                    AGENT HANUMAN                           │
└─────────────────┬───────────────────────────────────────────┘
                  │ Commande Langage Naturel
                  ▼
┌─────────────────────────────────────────────────────────────┐
│              IDEAgentOrchestrator                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ NLP Processor   │  │ Action Planner  │  │ Neural Comm  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────┬───────────────────────────────────────────┘
                  │ Actions IDE Structurées
                  ▼
┌─────────────────────────────────────────────────────────────┐
│            AgentVSCodeController                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Puppeteer UI    │  │ VS Code API     │  │ Roo Code API │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────┬───────────────────────────────────────────┘
                  │ Exécution Concrète
                  ▼
┌─────────────────────────────────────────────────────────────┐
│                VS CODE + ROO CODE                           │
└─────────────────────────────────────────────────────────────┘
```

### 📁 Structure des Fichiers

```
hanuman-unified/sandbox/
├── orchestration/
│   ├── types.ts                        # Interfaces TypeScript complètes
│   └── ide_agent_orchestrator.ts       # Orchestrateur principal
├── nlp/
│   └── natural_language_processor.ts   # Processeur langage naturel
├── controllers/
│   └── agent_vscode_controller.ts      # Contrôleur VS Code spécialisé
├── api/
│   └── sandbox_api_server_enhanced.ts  # API REST + WebSocket
├── start_sprint1.ts                    # Script de démonstration
├── package.json                        # Dépendances mises à jour
└── README_SPRINT1.md                   # Cette documentation
```

---

## 🚀 DÉMARRAGE RAPIDE

### 1. Installation des Dépendances

```bash
cd hanuman-unified/sandbox
npm install
```

### 2. Lancement de la Démonstration

```bash
# Démarrage du Sprint 1 avec démonstration interactive
npm run sprint1

# Ou directement avec ts-node
ts-node start_sprint1.ts
```

### 3. Test de l'API

```bash
# Démarrage de l'API Server seul
npm run api

# L'API sera disponible sur http://localhost:8085
```

---

## 🎮 UTILISATION

### Mode Démonstration Interactive

Le script `start_sprint1.ts` lance une démonstration complète avec :

1. **Tests automatisés** - 5 scénarios de test prédéfinis
2. **Mode interactif** - Interface en ligne de commande
3. **Monitoring temps réel** - Métriques et statuts

### Commandes Supportées

```bash
# Format: <agentId>: <commande en langage naturel>

agent-frontend-001: créer un agent frontend moderne avec React
agent-backend-001: créer une API REST pour gestion des utilisateurs  
agent-devops-001: initialiser un projet avec Docker et CI/CD
agent-frontend-001: créer une interface de dashboard avec graphiques
agent-backend-001: générer des tests unitaires pour l'API
```

### Commandes Système

```bash
help          # Afficher l'aide
status        # Statut du système
agents        # Lister les agents disponibles
metrics       # Métriques détaillées
quit/exit     # Quitter
```

---

## 🔌 API REST

### Endpoints Principaux

#### Commandes Langage Naturel
```http
POST /api/ide/command
Content-Type: application/json
X-Agent-ID: agent-frontend-001

{
  "command": "créer un agent frontend moderne",
  "sessionId": "session-123"
}
```

#### Actions Directes
```http
POST /api/ide/action
Content-Type: application/json
X-Agent-ID: agent-frontend-001

{
  "type": "open_file",
  "target": "vscode",
  "params": {
    "path": "/workspace/agents/sample/Sample.ts",
    "focus": true
  }
}
```

#### Génération Roo Code
```http
POST /api/roo/generate
Content-Type: application/json
X-Agent-ID: agent-frontend-001

{
  "template": "hanuman-agent",
  "variables": {
    "agentName": "SampleAgent",
    "capabilities": ["ui", "api"]
  }
}
```

### WebSocket

```javascript
const socket = io('http://localhost:8085');

// Authentification
socket.emit('authenticate', { 
  agentId: 'agent-frontend-001', 
  token: 'your-token' 
});

// Commandes temps réel
socket.emit('ide_command', {
  command: 'créer un composant React',
  sessionId: 'session-123'
});

// Écouter les résultats
socket.on('command_result', (data) => {
  console.log('Résultat:', data);
});
```

---

## 🧪 TESTS ET VALIDATION

### Tests Automatisés

Le Sprint 1 inclut 5 tests automatisés :

1. **Création Agent Frontend** - React + TypeScript
2. **Création Service Backend** - API REST + Node.js  
3. **Setup Projet DevOps** - Docker + CI/CD
4. **Interface Dashboard** - Composants complexes
5. **Génération Tests** - Tests unitaires automatiques

### Métriques Surveillées

- ⚡ **Temps de traitement** - < 3s par commande
- 📊 **Taux de succès** - > 90% des commandes
- 🎯 **Précision NLP** - > 70% de confiance
- 🔄 **Throughput** - 10 commandes simultanées

### Validation Manuelle

```bash
# Lancer les tests interactifs
npm run sprint1

# Tester des commandes spécifiques
agent-frontend-001: créer un agent de test simple
agent-backend-001: créer une API avec authentification
agent-devops-001: configurer Docker pour le projet
```

---

## 🔧 CONFIGURATION

### Variables d'Environnement

```bash
# Configuration Orchestrateur
IDE_ORCHESTRATOR_ENABLED=true
IDE_ORCHESTRATOR_PORT=8084
IDE_ORCHESTRATOR_MAX_CONCURRENT_COMMANDS=20

# Configuration Puppeteer  
PUPPETEER_HEADLESS=true
PUPPETEER_TIMEOUT=30000
PUPPETEER_MAX_SESSIONS=10

# Configuration NLP
NLP_CONFIDENCE_THRESHOLD=0.7
NLP_CACHE_TTL=3600

# Configuration API
API_ENHANCED_PORT=8085
WEBSOCKET_ENABLED=true
API_RATE_LIMIT=100
```

### Personnalisation

```typescript
// Configuration de l'orchestrateur
const orchestrator = new IDEAgentOrchestrator(hanumanOrchestrator, {
  maxConcurrentCommands: 20,
  commandTimeout: 30000,
  nlpConfidenceThreshold: 0.7,
  enableCache: true,
  logLevel: 'info'
});

// Configuration du contrôleur VS Code
const controller = new AgentVSCodeController(agentId, {
  headless: true,
  timeout: 15000,
  enableLogging: true
});
```

---

## 🐛 DÉPANNAGE

### Problèmes Courants

**1. Erreur "Session Puppeteer non disponible"**
```bash
# Solution: Installer Chromium
sudo apt-get install chromium-browser
# Ou définir PUPPETEER_EXECUTABLE_PATH
```

**2. Timeout des commandes**
```bash
# Augmenter le timeout dans la configuration
IDE_ORCHESTRATOR_TIMEOUT=60000
```

**3. Erreur de confiance NLP faible**
```bash
# Réduire le seuil de confiance
NLP_CONFIDENCE_THRESHOLD=0.5
```

### Logs et Debugging

```bash
# Activer les logs détaillés
export LOG_LEVEL=debug

# Voir les logs en temps réel
tail -f logs/orchestrator.log
```

---

## 🔮 PROCHAINES ÉTAPES

### Sprint 2 - Interface Unifiée (Semaine 2)

- 🎨 **EnhancedIDEControlInterface** - Dashboard React moderne
- 🗣️ **Commandes vocales** - Interface microphone
- 📊 **Monitoring temps réel** - Métriques live
- 🎮 **Contrôle multi-agents** - Orchestration avancée

### Sprint 3 - Optimisation (Semaine 3)

- 🚀 **Performance** - Cache intelligent et optimisations
- 🤖 **Templates avancés** - Génération contextuelle
- 🔄 **Workflows complexes** - Pipelines automatisés
- 📈 **Analytics** - Métriques avancées

### Sprint 4 - Production (Semaine 4)

- 🏭 **Déploiement production** - Configuration sécurisée
- 📚 **Documentation complète** - Guides et tutoriels
- 🧪 **Tests E2E** - Validation exhaustive
- 🛡️ **Sécurité renforcée** - Audit et compliance

---

## 🤝 CONTRIBUTION

### Structure du Code

- **Types** - Toutes les interfaces dans `orchestration/types.ts`
- **Logging** - Utiliser les méthodes `log()` intégrées
- **Erreurs** - Utiliser `OrchestrationError` pour les erreurs métier
- **Tests** - Ajouter des tests pour chaque nouvelle fonctionnalité

### Standards de Code

- ✅ TypeScript strict avec interfaces complètes
- ✅ Gestion d'erreurs avec try/catch et logs
- ✅ Communication via EventEmitter
- ✅ Documentation TSDoc pour les méthodes publiques
- ✅ Tests unitaires avec Jest

---

## 📞 SUPPORT

Pour toute question ou problème :

1. 📖 Consulter cette documentation
2. 🔍 Vérifier les logs dans `logs/`
3. 🧪 Tester avec `npm run sprint1`
4. 📊 Vérifier les métriques avec la commande `metrics`

---

## 🎉 RÉSULTAT SPRINT 1

✅ **Architecture solide** - Fondations pour contrôle IDE  
✅ **Commandes naturelles** - Interface vocale/textuelle  
✅ **Automation VS Code** - Contrôle programmatique complet  
✅ **API moderne** - REST + WebSocket temps réel  
✅ **Tests validés** - 5 scénarios fonctionnels  

**Le Sprint 1 établit les bases pour transformer la pensée en code via l'intelligence biomimétique !** 🧠✨
