// ========================================
// HANUMAN SANDBOX ENHANCED - IMPLÉMENTATION TECHNIQUE COMPLÈTE
// Guide complet pour contrôle intelligent VS Code & Roo Code
// ========================================

// ==========================================
// 1. IDE AGENT ORCHESTRATOR - CŒUR DU SYSTÈME
// ==========================================

import { EventEmitter } from 'events';
import { HanumanOrganOrchestrator } from '../hanuman-working/services/HanumanOrganOrchestrator';
import { VSCodeServerManager } from '../hanuman_sandbox/ide/vscode/vscode_server_manager';
import { RooCodeIntegration } from './roo_code/roo_code_integration';

export interface IDEAction {
  type: 'open_file' | 'generate_code' | 'run_command' | 'install_extension' | 'create_project';
  target: string;
  params: Record<string, any>;
  context?: HanumanContext;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

export interface HanumanContext {
  agentId?: string;
  organId?: string;
  projectType: 'agent' | 'organ' | 'interface' | 'service' | 'full-project';
  architecture: string;
  currentTask?: string;
  developmentPhase?: 'planning' | 'development' | 'testing' | 'deployment';
  technologies?: string[];
  requirements?: string[];
}

export interface NaturalLanguageCommand {
  input: string;
  intent: string;
  entities: Record<string, any>;
  confidence: number;
  context: HanumanContext;
  timestamp: Date;
  sessionId?: string;
}

/**
 * Orchestrateur principal pour le contrôle intelligent des IDE
 * Point central pour toutes les interactions développeur ↔ IDE
 */
export class IDEAgentOrchestrator extends EventEmitter {
  private vscodeManager: VSCodeServerManager;
  private rooCodeIntegration: RooCodeIntegration;
  private hanumanOrchestrator: HanumanOrganOrchestrator;
  private agentControllers: Map<string, AgentVSCodeController>;
  private nlpEngine: NaturalLanguageProcessor;
  private sandboxAPI: SandboxAPIServer;
  private activeCommands: Map<string, NaturalLanguageCommand>;
  private commandHistory: NaturalLanguageCommand[];

  constructor(hanumanOrchestrator: HanumanOrganOrchestrator) {
    super();
    this.hanumanOrchestrator = hanumanOrchestrator;
    this.agentControllers = new Map();
    this.activeCommands = new Map();
    this.commandHistory = [];
    this.initializeComponents();
    this.setupNeuralCommunication();
    this.setupEventHandlers();
  }

  private async initializeComponents(): Promise<void> {
    console.log('🧠 Initialisation IDE Agent Orchestrator...');
    
    // Initialiser le processeur de langage naturel
    this.nlpEngine = new NaturalLanguageProcessor();
    await this.nlpEngine.initialize();
    
    // Initialiser l'API Sandbox
    this.sandboxAPI = new SandboxAPIServer(this);
    await this.sandboxAPI.start();
    
    // Initialiser l'intégration Roo Code
    this.rooCodeIntegration = new RooCodeIntegration();
    await this.rooCodeIntegration.loadHanumanTemplates();
    
    console.log('✅ IDE Agent Orchestrator initialisé');
  }

  /**
   * Point d'entrée principal pour traitement des commandes
   */
  async processNaturalLanguageCommand(
    agentId: string, 
    command: string,
    sessionId?: string
  ): Promise<IDEAction[]> {
    const commandId = `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      console.log(`🗣️ [${agentId}] Traitement commande: "${command}"`);
      
      // 1. Analyser la commande avec NLP
      const parsedCommand = await this.nlpEngine.parse(command);
      parsedCommand.sessionId = sessionId;
      
      // Ajouter à l'historique
      this.commandHistory.push(parsedCommand);
      this.activeCommands.set(commandId, parsedCommand);
      
      // 2. Obtenir le contexte de l'agent Hanuman
      const context = await this.getEnhancedAgentContext(agentId, parsedCommand);
      parsedCommand.context = context;
      
      // 3. Valider la faisabilité de la commande
      await this.validateCommand(parsedCommand, context);
      
      // 4. Traduire en actions IDE concrètes
      const actions = await this.translateToIDEActions(parsedCommand, context);
      
      // 5. Optimiser et prioriser les actions
      const optimizedActions = await this.optimizeActions(actions, context);
      
      // 6. Exécuter les actions via les contrôleurs
      const results = await this.executeIDEActions(agentId, optimizedActions, commandId);
      
      // 7. Post-traitement et feedback
      await this.postProcessCommand(commandId, results, context);
      
      // 8. Envoyer signal neural à Hanuman
      this.sendNeuralSignal('ide:command-completed', {
        agentId,
        commandId,
        command,
        actions: results,
        success: true,
        context
      });

      this.activeCommands.delete(commandId);
      return results;

    } catch (error) {
      console.error(`❌ [${agentId}] Erreur traitement commande "${command}":`, error);
      
      // Signal d'erreur vers Hanuman
      this.sendNeuralSignal('ide:command-failed', {
        agentId,
        commandId,
        command,
        error: error.message,
        context: this.activeCommands.get(commandId)?.context
      });

      this.emit('ide:error', { agentId, command, error, commandId });
      this.activeCommands.delete(commandId);
      throw error;
    }
  }

  /**
   * Traduction avancée des commandes en actions IDE
   */
  private async translateToIDEActions(
    parsedCommand: NaturalLanguageCommand,
    context: HanumanContext
  ): Promise<IDEAction[]> {
    const actions: IDEAction[] = [];
    const { intent, entities } = parsedCommand;

    console.log(`🎯 Traduction intent "${intent}" avec entités:`, entities);

    switch (intent) {
      case 'create_agent':
        actions.push(...await this.createAgentActions(entities, context));
        break;

      case 'create_organ':
        actions.push(...await this.createOrganActions(entities, context));
        break;

      case 'create_interface':
        actions.push(...await this.createInterfaceActions(entities, context));
        break;

      case 'create_service':
        actions.push(...await this.createServiceActions(entities, context));
        break;

      case 'setup_project':
        actions.push(...await this.setupProjectActions(entities, context));
        break;

      case 'run_tests':
        actions.push(...await this.runTestsActions(entities, context));
        break;

      case 'deploy_project':
        actions.push(...await this.deployActions(entities, context));
        break;

      case 'debug_issue':
        actions.push(...await this.debugActions(entities, context));
        break;

      case 'optimize_code':
        actions.push(...await this.optimizeCodeActions(entities, context));
        break;

      case 'generate_documentation':
        actions.push(...await this.generateDocsActions(entities, context));
        break;

      default:
        // Tentative de génération générique avec Roo Code
        actions.push({
          type: 'generate_code',
          target: 'roo_code',
          params: {
            prompt: parsedCommand.input,
            context: context,
            mode: 'generic'
          },
          context,
          priority: 'medium'
        });
    }

    return actions;
  }

  /**
   * Actions spécialisées pour création d'agent Hanuman
   */
  private async createAgentActions(entities: Record<string, any>, context: HanumanContext): Promise<IDEAction[]> {
    const agentName = entities.agentName || entities.name;
    const responsibilities = entities.responsibilities || entities.description;
    const capabilities = entities.capabilities || [];

    if (!agentName) {
      throw new Error('Nom d\'agent requis pour la création');
    }

    return [
      // 1. Créer la structure de fichiers
      {
        type: 'run_command',
        target: 'terminal',
        params: {
          command: `mkdir -p agents/${agentName.toLowerCase()}`,
          workingDir: context.currentTask || '/workspace'
        },
        context,
        priority: 'high'
      },
      
      // 2. Générer le code de l'agent
      {
        type: 'generate_code',
        target: 'roo_code',
        params: {
          template: 'hanuman-agent',
          variables: {
            agentName,
            description: responsibilities,
            responsibilities,
            capabilities,
            organId: context.organId,
            configFields: this.generateConfigFields(capabilities),
            stateFields: this.generateStateFields(capabilities)
          },
          outputPath: `agents/${agentName.toLowerCase()}/${agentName}.ts`
        },
        context,
        priority: 'high'
      },

      // 3. Générer les tests
      {
        type: 'generate_code',
        target: 'roo_code',
        params: {
          template: 'hanuman-agent-test',
          variables: { agentName, capabilities },
          outputPath: `agents/${agentName.toLowerCase()}/${agentName}.test.ts`
        },
        context,
        priority: 'medium'
      },

      // 4. Générer la documentation
      {
        type: 'generate_code',
        target: 'roo_code',
        params: {
          template: 'hanuman-agent-docs',
          variables: { agentName, responsibilities, capabilities },
          outputPath: `agents/${agentName.toLowerCase()}/README.md`
        },
        context,
        priority: 'low'
      },

      // 5. Ouvrir l'agent dans VS Code
      {
        type: 'open_file',
        target: 'vscode',
        params: {
          path: `agents/${agentName.toLowerCase()}/${agentName}.ts`,
          focus: true
        },
        context,
        priority: 'medium'
      }
    ];
  }

  /**
   * Actions pour création d'interface React Hanuman
   */
  private async createInterfaceActions(entities: Record<string, any>, context: HanumanContext): Promise<IDEAction[]> {
    const componentName = entities.componentName || entities.name;
    const description = entities.description || '';
    const features = entities.features || [];
    const sections = entities.sections || [];

    return [
      // 1. Créer le dossier d'interface
      {
        type: 'run_command',
        target: 'terminal',
        params: {
          command: `mkdir -p interfaces/${componentName}`,
          workingDir: '/workspace'
        },
        context,
        priority: 'high'
      },

      // 2. Générer le composant React
      {
        type: 'generate_code',
        target: 'roo_code',
        params: {
          template: 'hanuman-interface',
          variables: {
            componentName,
            description,
            displayName: componentName.replace(/([A-Z])/g, ' $1').trim(),
            purpose: description,
            features,
            sections: sections.map(s => ({ title: s, sectionClass: 'interface-section' })),
            props: this.generateInterfaceProps(features),
            stateFields: this.generateInterfaceState(features)
          },
          outputPath: `interfaces/${componentName}/${componentName}.tsx`
        },
        context,
        priority: 'high'
      },

      // 3. Générer les styles Tailwind
      {
        type: 'generate_code',
        target: 'roo_code',
        params: {
          template: 'hanuman-styles',
          variables: { componentName, features },
          outputPath: `interfaces/${componentName}/${componentName}.styles.ts`
        },
        context,
        priority: 'medium'
      },

      // 4. Générer les tests d'interface
      {
        type: 'generate_code',
        target: 'roo_code',
        params: {
          template: 'hanuman-interface-test',
          variables: { componentName, features },
          outputPath: `interfaces/${componentName}/${componentName}.test.tsx`
        },
        context,
        priority: 'medium'
      }
    ];
  }

  /**
   * Exécution orchestrée des actions IDE
   */
  private async executeIDEActions(
    agentId: string,
    actions: IDEAction[],
    commandId: string
  ): Promise<IDEAction[]> {
    const controller = this.getOrCreateAgentController(agentId);
    const executedActions: IDEAction[] = [];

    // Trier par priorité
    const sortedActions = actions.sort((a, b) => {
      const priorities = { urgent: 4, high: 3, medium: 2, low: 1 };
      return (priorities[b.priority || 'medium'] || 2) - (priorities[a.priority || 'medium'] || 2);
    });

    // Exécuter séquentiellement avec gestion d'erreurs
    for (const [index, action] of sortedActions.entries()) {
      try {
        console.log(`⚡ [${agentId}] Exécution action ${index + 1}/${sortedActions.length}: ${action.type}`);
        
        await controller.executeAction(action);
        executedActions.push(action);
        
        this.emit('ide:action-executed', { 
          agentId, 
          action, 
          commandId,
          progress: { current: index + 1, total: sortedActions.length }
        });
        
        // Délai entre actions pour éviter la surcharge
        if (index < sortedActions.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        
      } catch (error) {
        console.error(`❌ [${agentId}] Erreur action ${action.type}:`, error);
        
        // Continuer avec les autres actions sauf si c'est critique
        if (action.priority === 'urgent' || action.priority === 'high') {
          this.emit('ide:action-failed', { agentId, action, error, commandId });
          throw new Error(`Action critique échouée: ${action.type} - ${error.message}`);
        } else {
          this.emit('ide:action-warning', { agentId, action, error, commandId });
        }
      }
    }

    return executedActions;
  }

  /**
   * Communication bidirectionnelle avec le système nerveux Hanuman
   */
  private setupNeuralCommunication(): void {
    console.log('🔗 Configuration communication neurale...');

    // Écoute des signaux entrants du système Hanuman
    this.hanumanOrchestrator.on('neural:signal-received', this.handleNeuralSignal.bind(this));
    this.hanumanOrchestrator.on('organ:request-ide', this.handleIDERequest.bind(this));
    this.hanumanOrchestrator.on('agent:created', this.handleAgentCreated.bind(this));
    this.hanumanOrchestrator.on('organ:evolved', this.handleOrganEvolved.bind(this));
    
    console.log('✅ Communication neurale configurée');
  }

  private async handleNeuralSignal(signal: any): Promise<void> {
    console.log(`🧠 Signal neural reçu: ${signal.type}`, signal.data);

    switch (signal.type) {
      case 'ide:request':
        await this.processNeuralIDERequest(signal);
        break;
      case 'agent:needs-development':
        await this.processAgentDevelopmentRequest(signal);
        break;
      case 'organ:needs-interface':
        await this.processOrganInterfaceRequest(signal);
        break;
      case 'system:optimization-required':
        await this.processOptimizationRequest(signal);
        break;
    }
  }

  private sendNeuralSignal(type: string, data: any): void {
    const signal = {
      type,
      source: 'ide_orchestrator',
      data,
      timestamp: new Date(),
      metadata: {
        version: '1.0.0',
        priority: data.priority || 'medium'
      }
    };

    console.log(`📡 Envoi signal neural: ${type}`);
    this.hanumanOrchestrator.emit('neural:signal-sent', signal);
  }

  /**
   * Gestion et création des contrôleurs d'agents
   */
  private getOrCreateAgentController(agentId: string): AgentVSCodeController {
    if (!this.agentControllers.has(agentId)) {
      console.log(`🎮 Création contrôleur pour agent: ${agentId}`);
      const controller = new AgentVSCodeController(agentId, this);
      this.agentControllers.set(agentId, controller);
    }
    return this.agentControllers.get(agentId)!;
  }

  /**
   * Obtention du contexte enrichi de l'agent
   */
  private async getEnhancedAgentContext(
    agentId: string, 
    command: NaturalLanguageCommand
  ): Promise<HanumanContext> {
    try {
      // Récupérer les infos de base depuis l'orchestrateur Hanuman
      const agent = await this.hanumanOrchestrator.getAgent(agentId);
      const organ = agent?.organId ? await this.hanumanOrchestrator.getOrgan(agent.organId) : null;

      // Analyser le contexte de la commande
      const projectType = this.inferProjectType(command);
      const technologies = this.extractTechnologies(command);
      const requirements = this.extractRequirements(command);

      return {
        agentId,
        organId: agent?.organId,
        projectType,
        architecture: 'hanuman-biomimetic-enhanced',
        currentTask: agent?.currentTask || '/workspace',
        developmentPhase: this.inferDevelopmentPhase(command),
        technologies,
        requirements
      };
    } catch (error) {
      console.warn(`⚠️ Erreur récupération contexte agent ${agentId}:`, error);
      
      // Contexte par défaut
      return {
        agentId,
        projectType: 'agent',
        architecture: 'hanuman-biomimetic-enhanced',
        currentTask: '/workspace',
        developmentPhase: 'development',
        technologies: ['typescript', 'react', 'node.js'],
        requirements: []
      };
    }
  }

  // Méthodes utilitaires pour génération de code
  private generateConfigFields(capabilities: string[]): Array<{name: string, type: string}> {
    return capabilities.map(cap => ({
      name: `${cap.replace(/[^a-zA-Z0-9]/g, '')}Config`,
      type: 'any'
    }));
  }

  private generateStateFields(capabilities: string[]): Array<{name: string, type: string, defaultValue: string}> {
    return capabilities.map(cap => ({
      name: `${cap.replace(/[^a-zA-Z0-9]/g, '')}State`,
      type: 'boolean',
      defaultValue: 'false'
    }));
  }

  private generateInterfaceProps(features: string[]): Array<{name: string, type: string}> {
    return features.map(feature => ({
      name: `${feature.replace(/[^a-zA-Z0-9]/g, '')}Config`,
      type: 'any'
    }));
  }

  private generateInterfaceState(features: string[]): Array<{name: string, type: string, defaultValue: string}> {
    return features.map(feature => ({
      name: `${feature.replace(/[^a-zA-Z0-9]/g, '')}Data`,
      type: 'any[]',
      defaultValue: '[]'
    }));
  }

  // Méthodes d'inférence de contexte
  private inferProjectType(command: NaturalLanguageCommand): HanumanContext['projectType'] {
    const input = command.input.toLowerCase();
    if (input.includes('agent')) return 'agent';
    if (input.includes('organ')) return 'organ';
    if (input.includes('interface') || input.includes('ui')) return 'interface';
    if (input.includes('service') || input.includes('api')) return 'service';
    if (input.includes('projet') || input.includes('app')) return 'full-project';
    return 'agent';
  }

  private extractTechnologies(command: NaturalLanguageCommand): string[] {
    const technologies = ['typescript', 'react', 'node.js'];
    const input = command.input.toLowerCase();
    
    if (input.includes('python')) technologies.push('python');
    if (input.includes('docker')) technologies.push('docker');
    if (input.includes('kubernetes')) technologies.push('kubernetes');
    if (input.includes('graphql')) technologies.push('graphql');
    if (input.includes('rest')) technologies.push('rest-api');
    
    return [...new Set(technologies)];
  }

  private extractRequirements(command: NaturalLanguageCommand): string[] {
    const requirements: string[] = [];
    const input = command.input.toLowerCase();
    
    if (input.includes('sécuri')) requirements.push('security');
    if (input.includes('performance')) requirements.push('performance');
    if (input.includes('test')) requirements.push('testing');
    if (input.includes('accessib')) requirements.push('accessibility');
    if (input.includes('responsive')) requirements.push('responsive');
    
    return requirements;
  }

  private inferDevelopmentPhase(command: NaturalLanguageCommand): HanumanContext['developmentPhase'] {
    const input = command.input.toLowerCase();
    if (input.includes('plan') || input.includes('design')) return 'planning';
    if (input.includes('test')) return 'testing';
    if (input.includes('deploy') || input.includes('publish')) return 'deployment';
    return 'development';
  }

  // Méthodes publiques pour accès externe
  public getVSCodeManager(): VSCodeServerManager {
    return this.vscodeManager;
  }

  public getRooCodeIntegration(): RooCodeIntegration {
    return this.rooCodeIntegration;
  }

  public getCommandHistory(): NaturalLanguageCommand[] {
    return [...this.commandHistory];
  }

  public getActiveCommands(): Map<string, NaturalLanguageCommand> {
    return new Map(this.activeCommands);
  }
}

// ==========================================
// 2. AGENT VSCODE CONTROLLER - CONTRÔLE DIRECT
// ========================================== 

import { Browser, Page } from 'puppeteer';

export interface VSCodeCommand {
  type: 'open_file' | 'generate_code' | 'run_terminal' | 'install_extension' | 'navigate' | 'edit';
  params: Record<string, any>;
  timeout?: number;
}

/**
 * Contrôleur spécialisé pour interaction directe avec VS Code et Roo Code
 * Utilise Puppeteer pour automatisation UI et API pour actions backend
 */
export class AgentVSCodeController {
  private agentId: string;
  private orchestrator: IDEAgentOrchestrator;
  private puppeteerSession?: Browser;
  private vscodeAPI: VSCodeAPI;
  private rooCodeAPI: RooCodeAPI;
  private currentPage?: Page;
  private isInitialized = false;

  constructor(agentId: string, orchestrator: IDEAgentOrchestrator) {
    this.agentId = agentId;
    this.orchestrator = orchestrator;
    this.vscodeAPI = new VSCodeAPI(agentId);
    this.rooCodeAPI = new RooCodeAPI(agentId);
  }

  /**
   * Initialisation du contrôleur avec session Puppeteer
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log(`🎮 Initialisation contrôleur agent: ${this.agentId}`);
    
    try {
      await this.ensurePuppeteerSession();
      await this.vscodeAPI.initialize();
      await this.rooCodeAPI.initialize();
      
      this.isInitialized = true;
      console.log(`✅ Contrôleur ${this.agentId} initialisé`);
      
    } catch (error) {
      console.error(`❌ Erreur initialisation contrôleur ${this.agentId}:`, error);
      throw error;
    }
  }

  /**
   * Exécuter une action IDE spécifique
   */
  async executeAction(action: IDEAction): Promise<void> {
    await this.initialize();

    console.log(`⚡ [${this.agentId}] Exécution action: ${action.type}`);

    try {
      switch (action.type) {
        case 'open_file':
          await this.openFile(action.params.path, action.params.focus);
          break;
        
        case 'generate_code':
          await this.generateCode(action.params, action.context);
          break;
        
        case 'run_command':
          await this.runTerminalCommand(action.params.command, action.params.workingDir);
          break;
        
        case 'install_extension':
          await this.installExtension(action.params.extensionId);
          break;

        case 'create_project':
          await this.createProject(action.params, action.context);
          break;

        default:
          throw new Error(`Type d'action non supporté: ${action.type}`);
      }

      console.log(`✅ [${this.agentId}] Action ${action.type} terminée`);

    } catch (error) {
      console.error(`❌ [${this.agentId}] Erreur action ${action.type}:`, error);
      throw error;
    }
  }

  /**
   * Ouverture de fichier avec navigation intelligente
   */
  private async openFile(filePath: string, focus: boolean = false): Promise<void> {
    try {
      console.log(`📁 [${this.agentId}] Ouverture fichier: ${filePath}`);

      // Méthode 1: Via API VS Code (plus rapide)
      await this.vscodeAPI.openFile(filePath);
      
      if (focus) {
        // Méthode 2: Via Puppeteer pour focus UI
        await this.ensurePuppeteerSession();
        
        // Attendre que le fichier soit chargé
        await this.currentPage?.waitForSelector('.monaco-editor', { timeout: 10000 });
        
        // Trouver et cliquer sur l'onglet du fichier
        const fileName = filePath.split('/').pop();
        const tabSelector = `[aria-label*="${fileName}"]`;
        
        try {
          await this.currentPage?.waitForSelector(tabSelector, { timeout: 5000 });
          await this.currentPage?.click(tabSelector);
          
          // Focus sur l'éditeur
          await this.currentPage?.click('.monaco-editor .view-lines');
          
        } catch (tabError) {
          console.warn(`⚠️ Impossible de focuser sur l'onglet ${fileName}, focus général`);
          await this.currentPage?.click('.monaco-editor');
        }
      }

      console.log(`✅ Fichier ${filePath} ouvert${focus ? ' et focalisé' : ''}`);
      
    } catch (error) {
      console.error(`❌ Erreur ouverture fichier ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Génération de code contextuelle avec Roo Code
   */
  private async generateCode(params: any, context?: HanumanContext): Promise<void> {
    try {
      console.log(`🤖 [${this.agentId}] Génération de code:`, params);

      let generatedCode: string;

      if (params.template) {
        // Génération basée sur template Hanuman
        const template = await this.rooCodeAPI.getTemplate(params.template);
        const hanumanContext = this.buildEnhancedHanumanContext(params.variables || {}, context);
        
        generatedCode = await this.rooCodeAPI.generateFromTemplate(template, {
          ...params.variables,
          ...hanumanContext
        });
        
      } else if (params.prompt) {
        // Génération basée sur prompt libre
        const enhancedPrompt = this.enhancePromptWithContext(params.prompt, context);
        generatedCode = await this.rooCodeAPI.generateFromPrompt(enhancedPrompt, context);
        
      } else {
        throw new Error('Template ou prompt requis pour génération de code');
      }

      // Traitement du code généré
      generatedCode = this.postProcessGeneratedCode(generatedCode, params, context);

      // Insertion dans l'éditeur
      if (params.outputPath) {
        await this.writeCodeToFile(params.outputPath, generatedCode);
        await this.openFile(params.outputPath, true);
      } else {
        await this.insertCodeInEditor(generatedCode);
      }
      
      console.log(`✅ Code généré avec succès (${generatedCode.length} caractères)`);
      
    } catch (error) {
      console.error(`❌ Erreur génération code:`, error);
      throw error;
    }
  }

  /**
   * Exécution de commandes terminal avec monitoring
   */
  private async runTerminalCommand(command: string, workingDir?: string): Promise<void> {
    try {
      console.log(`⚡ [${this.agentId}] Exécution commande: ${command}`);
      
      await this.ensurePuppeteerSession();
      
      // Ouvrir/Activer le terminal intégré
      await this.currentPage?.keyboard.press('Control+Shift+`');
      await this.currentPage?.waitForSelector('.terminal-wrapper', { timeout: 8000 });
      
      // Attendre que le terminal soit prêt
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Changer de répertoire si nécessaire
      if (workingDir) {
        const cdCommand = `cd "${workingDir}"`;
        await this.currentPage?.type('.terminal-wrapper .xterm-screen', cdCommand);
        await this.currentPage?.keyboard.press('Enter');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      // Exécuter la commande principale
      await this.currentPage?.type('.terminal-wrapper .xterm-screen', command);
      await this.currentPage?.keyboard.press('Enter');
      
      // Attendre l'exécution (timeout adaptatif selon la commande)
      const timeout = this.getCommandTimeout(command);
      await new Promise(resolve => setTimeout(resolve, timeout));
      
      console.log(`✅ Commande "${command}" exécutée`);
      
    } catch (error) {
      console.error(`❌ Erreur exécution commande "${command}":`, error);
      throw error;
    }
  }

  /**
   * Installation d'extensions VS Code
   */
  private async installExtension(extensionId: string): Promise<void> {
    try {
      console.log(`🔌 [${this.agentId}] Installation extension: ${extensionId}`);
      
      // Via API VS Code si disponible
      await this.vscodeAPI.installExtension(extensionId);
      
      // Sinon via interface utilisateur
      await this.ensurePuppeteerSession();
      
      // Ouvrir la palette de commandes
      await this.currentPage?.keyboard.press('Control+Shift+P');
      await this.currentPage?.waitForSelector('.quick-input-widget', { timeout: 5000 });
      
      // Taper la commande d'installation
      await this.currentPage?.type('.quick-input-widget input', `Extensions: Install Extensions`);
      await this.currentPage?.keyboard.press('Enter');
      
      // Attendre la vue des extensions
      await this.currentPage?.waitForSelector('.extensions-viewlet', { timeout: 8000 });
      
      // Chercher l'extension
      await this.currentPage?.type('.extensions-viewlet input[placeholder*="Search"]', extensionId);
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Cliquer sur installer (premier résultat)
      const installButton = '.extensions-viewlet .extension .extension-action-bar .action-item .action-label[title*="Install"]';
      await this.currentPage?.waitForSelector(installButton, { timeout: 5000 });
      await this.currentPage?.click(installButton);
      
      console.log(`✅ Extension ${extensionId} installée`);
      
    } catch (error) {
      console.error(`❌ Erreur installation extension ${extensionId}:`, error);
      throw error;
    }
  }

  /**
   * Création de projet complet
   */
  private async createProject(params: any, context?: HanumanContext): Promise<void> {
    const projectName = params.projectName || params.name;
    const projectType = params.projectType || context?.projectType || 'agent';
    
    console.log(`🚀 [${this.agentId}] Création projet: ${projectName} (${projectType})`);

    try {
      // 1. Créer la structure de dossiers
      await this.runTerminalCommand(`mkdir -p ${projectName}/{src,tests,docs,config}`, '/workspace');
      
      // 2. Initialiser le projet Node.js
      await this.runTerminalCommand('npm init -y', `/workspace/${projectName}`);
      
      // 3. Installer les dépendances Hanuman
      const dependencies = this.getProjectDependencies(projectType);
      await this.runTerminalCommand(`npm install ${dependencies.join(' ')}`, `/workspace/${projectName}`);
      
      // 4. Générer les fichiers de base
      await this.generateProjectFiles(projectName, projectType, context);
      
      // 5. Ouvrir le projet dans VS Code
      await this.openFile(`${projectName}/src/index.ts`, true);
      
      console.log(`✅ Projet ${projectName} créé avec succès`);
      
    } catch (error) {
      console.error(`❌ Erreur création projet ${projectName}:`, error);
      throw error;
    }
  }

  /**
   * Construction du contexte Hanuman enrichi pour Roo Code
   */
  private buildEnhancedHanumanContext(
    variables: Record<string, any>,
    context?: HanumanContext
  ): Record<string, any> {
    return {
      // Métadonnées Hanuman
      hanumanVersion: '2.0.0-enhanced',
      architecture: 'biomimetic-organs-agents-enhanced',
      neuralCommunication: 'kafka-redis-websocket',
      memorySystem: 'weaviate-vectorial-enhanced',
      
      // Contexte de l'agent
      agentId: this.agentId,
      organId: context?.organId,
      projectType: context?.projectType || 'agent',
      developmentPhase: context?.developmentPhase || 'development',
      
      // Technologies et standards
      technologies: context?.technologies || ['typescript', 'react', 'node.js'],
      requirements: context?.requirements || [],
      
      // Structure de projet Hanuman
      projectStructure: {
        src: 'Code source principal avec agents et organes',
        agents: 'Agents spécialisés biomimétiques',
        organs: 'Organes système (cortex, cœur, poumons, etc.)',
        interfaces: 'Interfaces React pour interaction utilisateur',
        services: 'Services et API pour communication neurale',
        tests: 'Tests automatisés (unitaires, intégration, E2E)',
        docs: 'Documentation technique et fonctionnelle',
        config: 'Configuration environnements et déploiement'
      },
      
      // Standards de code Hanuman
      codeStandards: {
        language: 'TypeScript strict avec ESLint',
        framework: 'React functional components avec hooks',
        architecture: 'Event-driven avec communication neurale',
        patterns: 'Observer pattern pour agents autonomes',
        communication: 'Neural signals via EventEmitter et Kafka',
        security: 'Security-first design avec validation complète',
        testing: 'TDD avec Jest, Cypress, et tests d\'intégration',
        documentation: 'TSDoc pour code et Markdown pour guides'
      },
      
      // Intégration système
      systemIntegration: {
        orchestrator: 'HanumanOrganOrchestrator pour coordination',
        neural: 'Communication via signaux neuraux',
        memory: 'Stockage vectoriel avec Weaviate',
        events: 'EventEmitter pour communication locale',
        messaging: 'Kafka pour messages distribués',
        cache: 'Redis pour cache haute performance',
        monitoring: 'Prometheus + Grafana pour observabilité'
      },
      
      // Variables spécifiques
      ...variables
    };
  }

  /**
   * Amélioration des prompts avec contexte Hanuman
   */
  private enhancePromptWithContext(prompt: string, context?: HanumanContext): string {
    const contextInfo = `
Contexte Hanuman Enhanced:
- Agent: ${this.agentId}
- Organe: ${context?.organId || 'Non spécifié'}
- Type de projet: ${context?.projectType || 'agent'}
- Phase: ${context?.developmentPhase || 'development'}
- Technologies: ${context?.technologies?.join(', ') || 'TypeScript, React, Node.js'}
- Architecture: Biomimétique avec organes spécialisés
- Communication: Signaux neuraux via EventEmitter et Kafka

Standards de code à respecter:
- TypeScript strict avec interfaces complètes
- Gestion d'erreurs avec try/catch et logs
- Communication via EventEmitter pour agents
- Tests unitaires avec Jest
- Documentation TSDoc pour toutes les fonctions publiques
- Patterns Hanuman pour intégration système

Demande originale: ${prompt}

Génère le code en respectant ces standards et l'architecture Hanuman.`;

    return contextInfo;
  }

  /**
   * Post-traitement du code généré
   */
  private postProcessGeneratedCode(
    code: string, 
    params: any, 
    context?: HanumanContext
  ): string {
    let processedCode = code;

    // Ajouter les imports Hanuman si nécessaire
    if (!processedCode.includes('HanumanOrganOrchestrator') && context?.projectType === 'agent') {
      const hanumanImport = "import { HanumanOrganOrchestrator } from '../services/HanumanOrganOrchestrator';\n";
      processedCode = hanumanImport + processedCode;
    }

    // Ajouter les imports React si nécessaire
    if (params.template === 'hanuman-interface' && !processedCode.includes('import React')) {
      const reactImport = "import React, { useState, useEffect, useCallback } from 'react';\n";
      processedCode = reactImport + processedCode;
    }

    // Nettoyer les espaces et formatage
    processedCode = processedCode
      .replace(/\n\n\n+/g, '\n\n')  // Réduire les lignes vides multiples
      .replace(/^\s+$/gm, '')        // Supprimer les espaces en fin de ligne
      .trim();                       // Nettoyer début et fin

    // Ajouter l'en-tête de fichier Hanuman
    const header = `// Generated by Hanuman Enhanced IDE
// Agent: ${this.agentId}
// Template: ${params.template || 'custom'}
// Generated at: ${new Date().toISOString()}

`;

    return header + processedCode;
  }

  /**
   * Gestion de la session Puppeteer
   */
  private async ensurePuppeteerSession(): Promise<void> {
    if (!this.puppeteerSession || !this.currentPage) {
      console.log(`🌐 [${this.agentId}] Création session Puppeteer...`);
      
      const puppeteer = await import('puppeteer');
      this.puppeteerSession = await puppeteer.launch({
        headless: process.env.NODE_ENV === 'production',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-web-security',
          '--allow-running-insecure-content'
        ]
      });
      
      this.currentPage = await this.puppeteerSession.newPage();
      await this.currentPage.setViewport({ width: 1920, height: 1080 });
      
      // Naviguer vers l'instance VS Code de l'agent
      const vscodeUrl = await this.getAgentVSCodeURL();
      console.log(`🔗 [${this.agentId}] Navigation vers VS Code: ${vscodeUrl}`);
      
      await this.currentPage.goto(vscodeUrl, { waitUntil: 'networkidle2' });
      await this.currentPage.waitForSelector('.monaco-workbench', { timeout: 15000 });
      
      console.log(`✅ [${this.agentId}] Session Puppeteer créée`);
    }
  }

  private async getAgentVSCodeURL(): Promise<string> {
    try {
      const vscodeManager = this.orchestrator.getVSCodeManager();
      const instances = vscodeManager.getInstancesByAgent(this.agentId);
      
      if (instances.length > 0) {
        return instances[0].url;
      }
      
      // Créer une nouvelle instance si nécessaire
      console.log(`🆕 [${this.agentId}] Création nouvelle instance VS Code...`);
      // ... logique de création d'instance
      
      return 'http://localhost:8081'; // URL par défaut
      
    } catch (error) {
      console.warn(`⚠️ [${this.agentId}] Erreur récupération URL VS Code:`, error);
      return 'http://localhost:8081';
    }
  }

  // Méthodes utilitaires
  private async writeCodeToFile(filePath: string, code: string): Promise<void> {
    // Utiliser l'API Sandbox pour écrire le fichier
    const response = await fetch('http://localhost:8080/api/files/write', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ path: filePath, content: code })
    });
    
    if (!response.ok) {
      throw new Error(`Erreur écriture fichier ${filePath}: ${response.statusText}`);
    }
  }

  private async insertCodeInEditor(code: string): Promise<void> {
    await this.ensurePuppeteerSession();
    
    // Sélectionner tout et remplacer
    await this.currentPage?.click('.monaco-editor .view-lines');
    await this.currentPage?.keyboard.press('Control+A');
    await this.currentPage?.keyboard.type(code);
    
    // Formater le code
    await this.currentPage?.keyboard.press('Shift+Alt+F');
  }

  private getCommandTimeout(command: string): number {
    // Timeouts adaptatifs selon le type de commande
    if (command.includes('npm install') || command.includes('yarn install')) return 30000;
    if (command.includes('npm run build') || command.includes('yarn build')) return 20000;
    if (command.includes('git clone')) return 15000;
    if (command.includes('docker')) return 25000;
    return 5000; // Timeout par défaut
  }

  private getProjectDependencies(projectType: string): string[] {
    const baseDeps = ['typescript', '@types/node', 'ts-node', 'nodemon'];
    
    switch (projectType) {
      case 'agent':
        return [...baseDeps, 'events', 'kafkajs', 'redis', 'weaviate-ts-client'];
      case 'interface':
        return [...baseDeps, 'react', 'react-dom', '@types/react', '@types/react-dom', 'tailwindcss'];
      case 'service':
        return [...baseDeps, 'express', 'cors', 'helmet', 'socket.io'];
      case 'full-project':
        return [...baseDeps, 'react', 'react-dom', 'express', 'kafkajs', 'redis', 'weaviate-ts-client'];
      default:
        return baseDeps;
    }
  }

  private async generateProjectFiles(projectName: string, projectType: string, context?: HanumanContext): Promise<void> {
    // Générer package.json personnalisé
    const packageJson = {
      name: projectName.toLowerCase(),
      version: '1.0.0',
      description: `Projet Hanuman ${projectType}`,
      main: 'dist/index.js',
      scripts: {
        build: 'tsc',
        start: 'node dist/index.js',
        dev: 'nodemon src/index.ts',
        test: 'jest',
        'test:watch': 'jest --watch'
      },
      keywords: ['hanuman', projectType, 'biomimetic', 'ai'],
      author: 'Hanuman Enhanced System',
      license: 'MIT'
    };

    await this.writeCodeToFile(`${projectName}/package.json`, JSON.stringify(packageJson, null, 2));

    // Générer tsconfig.json
    const tsConfig = {
      compilerOptions: {
        target: 'ES2020',
        module: 'commonjs',
        lib: ['ES2020'],
        outDir: './dist',
        rootDir: './src',
        strict: true,
        esModuleInterop: true,
        skipLibCheck: true,
        forceConsistentCasingInFileNames: true,
        declaration: true,
        declarationMap: true,
        sourceMap: true
      },
      include: ['src/**/*'],
      exclude: ['node_modules', 'dist', 'tests']
    };

    await this.writeCodeToFile(`${projectName}/tsconfig.json`, JSON.stringify(tsConfig, null, 2));

    // Générer le fichier principal selon le type
    let mainFileContent = '';
    switch (projectType) {
      case 'agent':
        mainFileContent = await this.generateAgentMainFile(projectName);
        break;
      case 'interface':
        mainFileContent = await this.generateInterfaceMainFile(projectName);
        break;
      case 'service':
        mainFileContent = await this.generateServiceMainFile(projectName);
        break;
      default:
        mainFileContent = `console.log('Hello from ${projectName}!');`;
    }

    await this.writeCodeToFile(`${projectName}/src/index.ts`, mainFileContent);
  }

  private async generateAgentMainFile(projectName: string): Promise<string> {
    return `import { EventEmitter } from 'events';

/**
 * Agent ${projectName} - Généré par Hanuman Enhanced
 */
export class ${projectName}Agent extends EventEmitter {
  private isActive = false;

  constructor() {
    super();
    this.initialize();
  }

  private async initialize(): Promise<void> {
    console.log('🤖 Initialisation agent ${projectName}...');
    // Logique d'initialisation
    this.emit('agent:initialized', { agent: '${projectName}' });
  }

  async activate(): Promise<void> {
    this.isActive = true;
    this.emit('agent:activated', { agent: '${projectName}' });
    console.log('✅ Agent ${projectName} activé');
  }

  async deactivate(): Promise<void> {
    this.isActive = false;
    this.emit('agent:deactivated', { agent: '${projectName}' });
    console.log('😴 Agent ${projectName} désactivé');
  }

  getStatus(): { active: boolean } {
    return { active: this.isActive };
  }
}

// Démarrage de l'agent
const agent = new ${projectName}Agent();
agent.activate();

export default ${projectName}Agent;`;
  }

  private async generateInterfaceMainFile(projectName: string): Promise<string> {
    return `import React from 'react';
import ReactDOM from 'react-dom/client';

interface ${projectName}Props {
  title?: string;
}

/**
 * Interface ${projectName} - Générée par Hanuman Enhanced
 */
const ${projectName}: React.FC<${projectName}Props> = ({ title = '${projectName}' }) => {
  return (
    <div className="hanuman-interface">
      <h1 className="text-2xl font-bold text-center mb-4">
        {title}
      </h1>
      <p className="text-center text-gray-600">
        Interface générée par Hanuman Enhanced System
      </p>
    </div>
  );
};

// Rendu de l'application
const root = ReactDOM.createRoot(document.getElementById('root')!);
root.render(<${projectName} />);

export default ${projectName};`;
  }

  private async generateServiceMainFile(projectName: string): Promise<string> {
    return `import express from 'express';
import cors from 'cors';

/**
 * Service ${projectName} - Généré par Hanuman Enhanced
 */
class ${projectName}Service {
  private app = express();
  private port = process.env.PORT || 3000;

  constructor() {
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    this.app.use(cors());
    this.app.use(express.json());
  }

  private setupRoutes(): void {
    this.app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        service: '${projectName}',
        timestamp: new Date().toISOString()
      });
    });

    this.app.get('/', (req, res) => {
      res.json({ 
        message: 'Service ${projectName} - Hanuman Enhanced',
        version: '1.0.0'
      });
    });
  }

  async start(): Promise<void> {
    this.app.listen(this.port, () => {
      console.log(\`🚀 Service ${projectName} démarré sur le port \${this.port}\`);
    });
  }
}

// Démarrage du service
const service = new ${projectName}Service();
service.start();

export default ${projectName}Service;`;
  }

  // Nettoyage des ressources
  async cleanup(): Promise<void> {
    if (this.puppeteerSession) {
      await this.puppeteerSession.close();
    }
    this.isInitialized = false;
    console.log(`🧹 [${this.agentId}] Contrôleur nettoyé`);
  }
}

// ==========================================
// 3. ROO CODE INTEGRATION - TEMPLATES HANUMAN
// ==========================================

export interface RooCodeTemplate {
  id: string;
  name: string;
  description: string;
  category: 'agent' | 'organ' | 'interface' | 'service' | 'test' | 'utility' | 'documentation';
  language: 'typescript' | 'javascript' | 'react' | 'python' | 'markdown' | 'json' | 'yaml';
  template: string;
  variables: RooCodeVariable[];
  dependencies?: string[];
  examples?: string[];
  version: string;
  lastModified: Date;
}

export interface RooCodeVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'enum';
  description: string;
  required: boolean;
  defaultValue?: any;
  validation?: string;
  enumValues?: string[];
}

export interface RooCodePrompt {
  id: string;
  name: string;
  description: string;
  context: 'hanuman' | 'agent' | 'organ' | 'interface' | 'service' | 'general';
  prompt: string;
  parameters: string[];
  examples: RooCodeExample[];
  category: string;
}

export interface RooCodeExample {
  input: string;
  output: string;
  explanation: string;
  context?: Record<string, any>;
}

/**
 * Intégration avancée Roo Code spécialisée pour Hanuman Enhanced
 */
export class RooCodeIntegration extends EventEmitter {
  private templates: Map<string, RooCodeTemplate> = new Map();
  private prompts: Map<string, RooCodePrompt> = new Map();
  private config: RooCodeConfig;
  private isInitialized = false;

  constructor() {
    super();
    this.initializeConfig();
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('🤖 Initialisation Roo Code Integration...');
    
    await this.loadHanumanTemplates();
    await this.loadHanumanPrompts();
    await this.setupCustomCommands();
    
    this.isInitialized = true;
    console.log('✅ Roo Code Integration initialisée');
  }

  /**
   * Chargement des templates spécialisés Hanuman Enhanced
   */
  async loadHanumanTemplates(): Promise<void> {
    console.log('📋 Chargement templates Hanuman Enhanced...');

    const templates: RooCodeTemplate[] = [
      // Template Agent Hanuman Avancé
      {
        id: 'hanuman-agent-enhanced',
        name: 'Agent Hanuman Enhanced',
        description: 'Template avancé pour créer un agent Hanuman avec capacités étendues',
        category: 'agent',
        language: 'typescript',
        version: '2.0.0',
        lastModified: new Date(),
        template: `import { EventEmitter } from 'events';
import { HanumanOrganOrchestrator } from '../services/HanumanOrganOrchestrator';
{{#if useWeaviate}}
import { WeaviateMemoryStore } from '../memory/WeaviateMemoryStore';
{{/if}}
{{#if useKafka}}
import { KafkaNeuralCommunication } from '../communication/KafkaNeuralCommunication';
{{/if}}

export interface {{agentName}}Config {
  // Configuration principale
  name: string;
  version: string;
  debug?: boolean;
  
  // Capacités spécialisées
  {{#each capabilities}}
  {{this.name}}Config?: {{this.type}};
  {{/each}}
  
  // Configuration neurale
  neuralCommunication?: {
    enabled: boolean;
    topics: string[];
    priority: 'low' | 'medium' | 'high';
  };
  
  // Configuration mémoire
  memoryConfig?: {
    shortTermSize: number;
    longTermPersistence: boolean;
    vectorDimensions?: number;
  };
}

export interface {{agentName}}State {
  // État général
  isActive: boolean;
  lastActivity: Date;
  currentTasks: string[];
  
  // États spécialisés
  {{#each stateFields}}
  {{this.name}}: {{this.type}};
  {{/each}}
  
  // Métriques de performance
  performance: {
    tasksCompleted: number;
    averageResponseTime: number;
    errorRate: number;
    uptime: number;
  };
}

export interface {{agentName}}Capabilities {
  {{#each capabilities}}
  {{this.name}}: {
    enabled: boolean;
    level: 'basic' | 'intermediate' | 'advanced' | 'expert';
    lastUsed?: Date;
    successRate?: number;
  };
  {{/each}}
}

/**
 * Agent {{agentName}} - {{description}}
 * 
 * Responsabilités:
 * {{#each responsibilities}}
 * - {{this}}
 * {{/each}}
 * 
 * Capacités:
 * {{#each capabilities}}
 * - {{this.name}}: {{this.description}}
 * {{/each}}
 * 
 * <AUTHOR> Enhanced System
 * @version {{version}}
 * @generated {{timestamp}}
 */
export class {{agentName}} extends EventEmitter {
  private config: {{agentName}}Config;
  private state: {{agentName}}State;
  private capabilities: {{agentName}}Capabilities;
  private orchestrator: HanumanOrganOrchestrator;
  {{#if useWeaviate}}
  private memoryStore: WeaviateMemoryStore;
  {{/if}}
  {{#if useKafka}}
  private neuralComm: KafkaNeuralCommunication;
  {{/if}}

  constructor(orchestrator: HanumanOrganOrchestrator, config: {{agentName}}Config) {
    super();
    this.orchestrator = orchestrator;
    this.config = { version: '1.0.0', ...config };
    this.initializeState();
    this.initializeCapabilities();
    this.setupEventHandlers();
  }

  private initializeState(): void {
    this.state = {
      isActive: false,
      lastActivity: new Date(),
      currentTasks: [],
      {{#each stateFields}}
      {{this.name}}: {{this.defaultValue}},
      {{/each}}
      performance: {
        tasksCompleted: 0,
        averageResponseTime: 0,
        errorRate: 0,
        uptime: 0
      }
    };
  }

  private initializeCapabilities(): void {
    this.capabilities = {
      {{#each capabilities}}
      {{this.name}}: {
        enabled: true,
        level: '{{this.level}}',
        successRate: 0
      }{{#unless @last}},{{/unless}}
      {{/each}}
    };
  }

  private setupEventHandlers(): void {
    // Communication neurale avec l'orchestrateur
    this.orchestrator.on('neural:signal-received', this.handleNeuralSignal.bind(this));
    this.orchestrator.on('task:assigned', this.handleTaskAssigned.bind(this));
    
    // Auto-monitoring des performances
    setInterval(() => this.updatePerformanceMetrics(), 60000); // 1 minute
  }

  /**
   * Activation de l'agent avec initialisation complète
   */
  async activate(): Promise<void> {
    try {
      console.log(\`🤖 Activation agent {{agentName}}...\`);
      
      // Initialiser la mémoire si configurée
      {{#if useWeaviate}}
      if (this.config.memoryConfig) {
        await this.initializeMemoryStore();
      }
      {{/if}}
      
      // Initialiser la communication neurale
      {{#if useKafka}}
      if (this.config.neuralCommunication?.enabled) {
        await this.initializeNeuralCommunication();
      }
      {{/if}}
      
      // Initialiser les capacités spécialisées
      {{#each capabilities}}
      if (this.capabilities.{{this.name}}.enabled) {
        await this.initialize{{pascalCase this.name}}();
      }
      {{/each}}
      
      this.state.isActive = true;
      this.state.lastActivity = new Date();
      
      // Signaler l'activation
      this.emit('agent:activated', { 
        agent: '{{agentName}}', 
        config: this.config,
        capabilities: Object.keys(this.capabilities).filter(c => this.capabilities[c].enabled)
      });
      
      // Signal neural vers l'orchestrateur
      this.sendNeuralSignal('agent:ready', {
        agentId: '{{agentName}}',
        capabilities: this.capabilities,
        timestamp: new Date()
      });
      
      console.log(\`✅ Agent {{agentName}} activé avec succès\`);
      
    } catch (error) {
      console.error(\`❌ Erreur activation agent {{agentName}}:\`, error);
      this.emit('agent:activation-failed', { agent: '{{agentName}}', error });
      throw error;
    }
  }

  /**
   * Désactivation propre de l'agent
   */
  async deactivate(): Promise<void> {
    try {
      console.log(\`😴 Désactivation agent {{agentName}}...\`);
      
      // Terminer les tâches en cours
      if (this.state.currentTasks.length > 0) {
        console.log(\`⏳ Attente fin des tâches: \${this.state.currentTasks.join(', ')}\`);
        await this.waitForTasksCompletion();
      }
      
      // Sauvegarder l'état si nécessaire
      {{#if useWeaviate}}
      if (this.memoryStore) {
        await this.memoryStore.persistState(this.state);
      }
      {{/if}}
      
      this.state.isActive = false;
      
      this.emit('agent:deactivated', { agent: '{{agentName}}' });
      console.log(\`✅ Agent {{agentName}} désactivé\`);
      
    } catch (error) {
      console.error(\`❌ Erreur désactivation agent {{agentName}}:\`, error);
      throw error;
    }
  }

  {{#each capabilities}}
  /**
   * Initialisation de la capacité {{this.name}}
   */
  private async initialize{{pascalCase this.name}}(): Promise<void> {
    try {
      console.log(\`🔧 Initialisation capacité {{this.name}}...\`);
      
      // TODO: Implémenter l'initialisation de {{this.name}}
      // Configuration spécifique: this.config.{{this.name}}Config
      
      this.capabilities.{{this.name}}.lastUsed = new Date();
      console.log(\`✅ Capacité {{this.name}} initialisée\`);
      
    } catch (error) {
      console.error(\`❌ Erreur initialisation {{this.name}}:\`, error);
      this.capabilities.{{this.name}}.enabled = false;
      throw error;
    }
  }

  /**
   * Utilisation de la capacité {{this.name}}
   */
  async use{{pascalCase this.name}}(params: any): Promise<any> {
    if (!this.capabilities.{{this.name}}.enabled) {
      throw new Error('Capacité {{this.name}} non disponible');
    }

    const startTime = Date.now();
    
    try {
      console.log(\`🎯 Utilisation capacité {{this.name}}\`);
      
      // TODO: Implémenter la logique de {{this.name}}
      const result = await this.process{{pascalCase this.name}}(params);
      
      // Mise à jour des métriques
      const duration = Date.now() - startTime;
      this.updateCapabilityMetrics('{{this.name}}', true, duration);
      
      this.emit('capability:used', { 
        capability: '{{this.name}}', 
        success: true, 
        duration,
        result 
      });
      
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateCapabilityMetrics('{{this.name}}', false, duration);
      
      this.emit('capability:error', { 
        capability: '{{this.name}}', 
        error, 
        duration 
      });
      
      throw error;
    }
  }

  private async process{{pascalCase this.name}}(params: any): Promise<any> {
    // TODO: Implémenter la logique spécifique de {{this.name}}
    return { message: 'Capacité {{this.name}} exécutée', params };
  }
  {{/each}}

  /**
   * Gestion des signaux neuraux
   */
  private async handleNeuralSignal(signal: any): Promise<void> {
    if (!this.state.isActive) return;

    console.log(\`🧠 Signal neural reçu: \${signal.type}\`);
    
    try {
      switch (signal.type) {
        case 'task:request':
          await this.handleTaskRequest(signal.data);
          break;
        case 'capability:invoke':
          await this.handleCapabilityInvocation(signal.data);
          break;
        case 'state:query':
          await this.handleStateQuery(signal.data);
          break;
        case 'config:update':
          await this.handleConfigUpdate(signal.data);
          break;
        default:
          console.log(\`ℹ️ Signal non géré: \${signal.type}\`);
      }
      
    } catch (error) {
      console.error(\`❌ Erreur traitement signal \${signal.type}:\`, error);
      this.sendNeuralSignal('signal:error', {
        originalSignal: signal,
        error: error.message,
        agentId: '{{agentName}}'
      });
    }
  }

  private async handleTaskRequest(taskData: any): Promise<void> {
    const taskId = taskData.id || \`task_\${Date.now()}\`;
    
    this.state.currentTasks.push(taskId);
    
    try {
      // Traitement de la tâche selon son type
      let result;
      
      switch (taskData.type) {
        {{#each capabilities}}
        case '{{this.name}}':
          result = await this.use{{pascalCase this.name}}(taskData.params);
          break;
        {{/each}}
        default:
          throw new Error(\`Type de tâche non supporté: \${taskData.type}\`);
      }
      
      // Signaler la completion
      this.sendNeuralSignal('task:completed', {
        taskId,
        agentId: '{{agentName}}',
        result,
        duration: Date.now() - taskData.startTime
      });
      
      this.state.performance.tasksCompleted++;
      
    } catch (error) {
      this.sendNeuralSignal('task:failed', {
        taskId,
        agentId: '{{agentName}}',
        error: error.message
      });
      
    } finally {
      this.state.currentTasks = this.state.currentTasks.filter(id => id !== taskId);
      this.state.lastActivity = new Date();
    }
  }

  private sendNeuralSignal(type: string, data: any): void {
    this.orchestrator.emit('neural:signal-sent', {
      type,
      source: '{{agentName}}',
      data,
      timestamp: new Date()
    });
  }

  private updateCapabilityMetrics(capability: string, success: boolean, duration: number): void {
    const cap = this.capabilities[capability];
    if (!cap) return;

    // Mise à jour du taux de succès
    const currentRate = cap.successRate || 0;
    cap.successRate = (currentRate + (success ? 1 : 0)) / 2;
    
    cap.lastUsed = new Date();
  }

  private updatePerformanceMetrics(): void {
    if (!this.state.isActive) return;

    // Mise à jour de l'uptime
    this.state.performance.uptime += 1; // minutes
    
    // Calcul du taux d'erreur moyen
    const totalCapabilities = Object.keys(this.capabilities).length;
    const errorRates = Object.values(this.capabilities)
      .map(c => 1 - (c.successRate || 0));
    
    this.state.performance.errorRate = 
      errorRates.reduce((sum, rate) => sum + rate, 0) / totalCapabilities;
  }

  private async waitForTasksCompletion(): Promise<void> {
    const maxWait = 30000; // 30 secondes max
    const startTime = Date.now();
    
    while (this.state.currentTasks.length > 0 && (Date.now() - startTime) < maxWait) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    if (this.state.currentTasks.length > 0) {
      console.warn(\`⚠️ Arrêt forcé avec \${this.state.currentTasks.length} tâches en cours\`);
    }
  }

  // Getters publics
  getState(): {{agentName}}State {
    return { ...this.state };
  }

  getCapabilities(): {{agentName}}Capabilities {
    return { ...this.capabilities };
  }

  getConfig(): {{agentName}}Config {
    return { ...this.config };
  }

  isReady(): boolean {
    return this.state.isActive && Object.values(this.capabilities).some(c => c.enabled);
  }

  getPerformanceReport(): any {
    return {
      agent: '{{agentName}}',
      state: this.state,
      capabilities: this.capabilities,
      uptime: \`\${this.state.performance.uptime} minutes\`,
      tasksCompleted: this.state.performance.tasksCompleted,
      averageSuccessRate: Object.values(this.capabilities)
        .reduce((sum, c) => sum + (c.successRate || 0), 0) / Object.keys(this.capabilities).length
    };
  }
}

export default {{agentName}};`,
        variables: [
          {
            name: 'agentName',
            type: 'string',
            description: 'Nom de l\'agent (PascalCase)',
            required: true
          },
          {
            name: 'description',
            type: 'string',
            description: 'Description détaillée de l\'agent',
            required: true
          },
          {
            name: 'responsibilities',
            type: 'array',
            description: 'Liste des responsabilités de l\'agent',
            required: true,
            defaultValue: []
          },
          {
            name: 'capabilities',
            type: 'array',
            description: 'Capacités spécialisées de l\'agent',
            required: true,
            defaultValue: []
          },
          {
            name: 'version',
            type: 'string',
            description: 'Version de l\'agent',
            required: false,
            defaultValue: '1.0.0'
          },
          {
            name: 'useWeaviate',
            type: 'boolean',
            description: 'Utiliser Weaviate pour la mémoire',
            required: false,
            defaultValue: false
          },
          {
            name: 'useKafka',
            type: 'boolean',
            description: 'Utiliser Kafka pour communication',
            required: false,
            defaultValue: false
          }
        ],
        dependencies: ['events', '../services/HanumanOrganOrchestrator'],
        examples: [
          'Agent SecurityValidator avec capacités de scan automatique',
          'Agent DataProcessor avec analyse temps réel',
          'Agent UIGenerator avec génération d\'interfaces'
        ]
      },

      // Autres templates...
      // (Interface, Organ, Service, Tests, etc.)
    ];

    // Charger tous les templates
    templates.forEach(template => {
      this.templates.set(template.id, template);
    });

    console.log(`✅ ${templates.length} templates Hanuman chargés`);
  }

  // Méthodes publiques pour accès aux templates
  getTemplate(templateId: string): RooCodeTemplate | undefined {
    return this.templates.get(templateId);
  }

  getTemplates(): RooCodeTemplate[] {
    return Array.from(this.templates.values());
  }

  async generateFromTemplate(template: RooCodeTemplate, variables: Record<string, any>): Promise<string> {
    // Compilation du template avec Handlebars (simulé ici)
    let compiledTemplate = template.template;
    
    // Remplacement simple des variables (à remplacer par Handlebars en vrai)
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      compiledTemplate = compiledTemplate.replace(regex, String(value));
    });

    return compiledTemplate;
  }

  async generateFromPrompt(prompt: string, context?: any): Promise<string> {
    // Simulation d'appel à l'API Roo Code
    console.log('🤖 Génération via Roo Code API:', prompt);
    
    // En réalité, ici on appellerait l'API Roo Code
    return `// Code généré par Roo Code
// Prompt: ${prompt}
// Context: ${JSON.stringify(context, null, 2)}

// TODO: Implémenter la génération réelle via Roo Code API
console.log('Code généré automatiquement');`;
  }

  private initializeConfig(): void {
    this.config = {
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 4000,
      contextWindow: 8000,
      hanumanContext: {
        architecture: 'biomimetic-organs-agents-enhanced',
        patterns: [
          'Event-driven architecture with neural signals',
          'Organ-based modularity with specialized functions',
          'Agent autonomy with coordinated behavior',
          'Memory-enhanced processing with Weaviate',
          'Real-time communication via Kafka/WebSocket',
          'Security-first design with validation layers'
        ],
        conventions: [
          'TypeScript strict mode with comprehensive typing',
          'React functional components with modern hooks',
          'Event emitter patterns for agent communication',
          'Async/await for all asynchronous operations',
          'Descriptive naming following Hanuman conventions',
          'Comprehensive error handling with logging'
        ],
        bestPractices: [
          'Test-driven development with Jest and Cypress',
          'Security by design with OWASP compliance',
          'Performance optimization with monitoring',
          'Accessibility compliance (WCAG 2.1 AA)',
          'Documentation completeness with TSDoc',
          'Code reusability through modular architecture'
        ]
      }
    };
  }

  private async loadHanumanPrompts(): Promise<void> {
    // Chargement des prompts spécialisés Hanuman
    // (Implementation similaire aux templates)
    console.log('💬 Chargement prompts Hanuman Enhanced...');
  }

  private async setupCustomCommands(): Promise<void> {
    // Configuration des commandes personnalisées Roo Code
    console.log('⚙️ Configuration commandes personnalisées...');
  }
}

export default RooCodeIntegration;