// ========================================
// HANUMAN SANDBOX ENHANCED - API SERVER
// API REST et WebSocket pour contrôle IDE
// ========================================

import express, { Express, Request, Response, NextFunction } from 'express';
import { createServer, Server } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { 
  SandboxAPIResponse, 
  NaturalLanguageCommand, 
  IDEAction,
  IDEActionResult,
  HanumanContext,
  OrchestrationError 
} from '../orchestration/types';
import { IDEAgentOrchestrator } from '../orchestration/ide_agent_orchestrator';

/**
 * Configuration pour l'API Server Enhanced
 */
interface APIServerConfig {
  port: number;
  enableCors: boolean;
  enableRateLimit: boolean;
  enableWebSocket: boolean;
  enableSwagger: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  maxRequestSize: string;
  requestTimeout: number;
}

/**
 * Middleware d'authentification pour les agents
 */
interface AuthenticatedRequest extends Request {
  agentId?: string;
  organId?: string;
  context?: HanumanContext;
}

/**
 * API Server Enhanced pour la sandbox Hanuman
 * Expose les fonctionnalités d'orchestration IDE via REST et WebSocket
 */
export class SandboxAPIServerEnhanced {
  private app: Express;
  private server: Server;
  private io?: SocketIOServer;
  private config: APIServerConfig;
  private orchestrator: IDEAgentOrchestrator;
  private isRunning = false;

  constructor(orchestrator: IDEAgentOrchestrator, config?: Partial<APIServerConfig>) {
    this.orchestrator = orchestrator;
    
    this.config = {
      port: 8085,
      enableCors: true,
      enableRateLimit: true,
      enableWebSocket: true,
      enableSwagger: true,
      logLevel: 'info',
      maxRequestSize: '10mb',
      requestTimeout: 30000,
      ...config
    };

    this.app = express();
    this.server = createServer(this.app);
    
    this.setupMiddleware();
    this.setupRoutes();
    
    if (this.config.enableWebSocket) {
      this.setupWebSocket();
    }
  }

  /**
   * Configuration des middlewares
   */
  private setupMiddleware(): void {
    // Sécurité
    this.app.use(helmet());
    
    // CORS
    if (this.config.enableCors) {
      this.app.use(cors({
        origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
        credentials: true
      }));
    }

    // Rate limiting
    if (this.config.enableRateLimit) {
      const limiter = rateLimit({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100, // limite chaque IP à 100 requêtes par fenêtre
        message: 'Trop de requêtes depuis cette IP'
      });
      this.app.use('/api/', limiter);
    }

    // Parsing
    this.app.use(express.json({ limit: this.config.maxRequestSize }));
    this.app.use(express.urlencoded({ extended: true, limit: this.config.maxRequestSize }));

    // Timeout
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      req.setTimeout(this.config.requestTimeout);
      next();
    });

    // Logging
    this.app.use(this.requestLogger.bind(this));

    // Authentification
    this.app.use('/api/', this.authenticateAgent.bind(this));
  }

  /**
   * Configuration des routes REST
   */
  private setupRoutes(): void {
    // Health check
    this.app.get('/health', this.handleHealthCheck.bind(this));
    this.app.get('/status', this.handleStatus.bind(this));

    // Routes principales IDE
    this.app.post('/api/ide/command', this.handleNaturalLanguageCommand.bind(this));
    this.app.post('/api/ide/action', this.handleDirectAction.bind(this));
    this.app.get('/api/ide/sessions', this.handleGetSessions.bind(this));
    this.app.get('/api/ide/history/:agentId', this.handleGetHistory.bind(this));

    // Routes VS Code
    this.app.post('/api/vscode/open', this.handleVSCodeOpen.bind(this));
    this.app.post('/api/vscode/command', this.handleVSCodeCommand.bind(this));
    this.app.post('/api/vscode/generate', this.handleVSCodeGenerate.bind(this));

    // Routes Roo Code
    this.app.post('/api/roo/generate', this.handleRooGenerate.bind(this));
    this.app.get('/api/roo/templates', this.handleRooTemplates.bind(this));
    this.app.post('/api/roo/template/:templateId', this.handleRooTemplate.bind(this));

    // Routes fichiers
    this.app.post('/api/files/read', this.handleFileRead.bind(this));
    this.app.post('/api/files/write', this.handleFileWrite.bind(this));
    this.app.post('/api/files/create', this.handleFileCreate.bind(this));

    // Routes projets
    this.app.post('/api/projects/create', this.handleProjectCreate.bind(this));
    this.app.get('/api/projects/list', this.handleProjectList.bind(this));

    // Routes métriques
    this.app.get('/api/metrics', this.handleMetrics.bind(this));
    this.app.get('/api/metrics/orchestrator', this.handleOrchestratorMetrics.bind(this));

    // Gestion des erreurs
    this.app.use(this.errorHandler.bind(this));
  }

  /**
   * Configuration WebSocket
   */
  private setupWebSocket(): void {
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
        credentials: true
      }
    });

    this.io.on('connection', (socket) => {
      this.log('info', `🔌 Nouvelle connexion WebSocket: ${socket.id}`);

      // Authentification WebSocket
      socket.on('authenticate', async (data) => {
        try {
          const { agentId, token } = data;
          // TODO: Valider le token
          socket.data.agentId = agentId;
          socket.join(`agent:${agentId}`);
          socket.emit('authenticated', { success: true, agentId });
          this.log('info', `✅ Agent ${agentId} authentifié via WebSocket`);
        } catch (error) {
          socket.emit('authentication_error', { error: error.message });
        }
      });

      // Commandes en temps réel
      socket.on('ide_command', async (data) => {
        try {
          const { command, sessionId } = data;
          const agentId = socket.data.agentId;
          
          if (!agentId) {
            socket.emit('error', { message: 'Agent non authentifié' });
            return;
          }

          const results = await this.orchestrator.processNaturalLanguageCommand(
            agentId, 
            command, 
            sessionId
          );

          socket.emit('command_result', { success: true, results });
          
          // Diffuser aux autres clients de l'agent
          socket.to(`agent:${agentId}`).emit('agent_activity', {
            type: 'command_executed',
            command,
            results
          });

        } catch (error) {
          socket.emit('command_error', { error: error.message });
        }
      });

      // Monitoring en temps réel
      socket.on('subscribe_metrics', () => {
        socket.join('metrics');
        this.log('debug', `📊 Client ${socket.id} abonné aux métriques`);
      });

      socket.on('disconnect', () => {
        this.log('info', `🔌 Déconnexion WebSocket: ${socket.id}`);
      });
    });

    // Diffusion périodique des métriques
    setInterval(() => {
      if (this.io) {
        const metrics = this.orchestrator.getMetrics();
        this.io.to('metrics').emit('metrics_update', metrics);
      }
    }, 5000);
  }

  /**
   * Handlers des routes REST
   */

  private async handleHealthCheck(req: Request, res: Response): Promise<void> {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      orchestrator: this.orchestrator.getState().status,
      uptime: process.uptime()
    };

    res.json(this.createResponse(health));
  }

  private async handleStatus(req: Request, res: Response): Promise<void> {
    const status = {
      server: {
        running: this.isRunning,
        port: this.config.port,
        websocket: this.config.enableWebSocket
      },
      orchestrator: this.orchestrator.getState(),
      metrics: this.orchestrator.getMetrics()
    };

    res.json(this.createResponse(status));
  }

  private async handleNaturalLanguageCommand(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { command, sessionId } = req.body;
      const agentId = req.agentId!;

      this.log('info', `🗣️ Commande reçue de ${agentId}: "${command}"`);

      const results = await this.orchestrator.processNaturalLanguageCommand(
        agentId, 
        command, 
        sessionId
      );

      // Notifier via WebSocket si disponible
      if (this.io) {
        this.io.to(`agent:${agentId}`).emit('command_completed', {
          command,
          results,
          timestamp: new Date()
        });
      }

      res.json(this.createResponse(results));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleDirectAction(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const action: IDEAction = req.body;
      const agentId = req.agentId!;

      this.log('info', `⚡ Action directe de ${agentId}: ${action.type}`);

      // TODO: Exécuter l'action directement via le contrôleur
      const result: IDEActionResult = {
        actionId: `direct_${Date.now()}`,
        success: true,
        result: `Action ${action.type} simulée`,
        duration: 1000,
        timestamp: new Date()
      };

      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleGetSessions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const sessions = this.orchestrator.getState().activeSessions;
      const sessionArray = Array.from(sessions.values());
      
      res.json(this.createResponse(sessionArray));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleGetHistory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { agentId } = req.params;
      const history = this.orchestrator.getCommandHistory(agentId);
      
      res.json(this.createResponse(history));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleVSCodeOpen(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { filePath, focus } = req.body;
      
      // TODO: Implémenter ouverture VS Code
      const result = { opened: true, filePath, focus };
      
      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleVSCodeCommand(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { command, params } = req.body;
      
      // TODO: Implémenter commande VS Code
      const result = { executed: true, command, params };
      
      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleVSCodeGenerate(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { template, variables, outputPath } = req.body;
      
      // TODO: Implémenter génération VS Code
      const result = { generated: true, template, outputPath };
      
      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleRooGenerate(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { prompt, context, template } = req.body;
      
      // TODO: Implémenter génération Roo Code
      const result = { 
        generated: true, 
        code: `// Code généré pour: ${prompt}`,
        length: 100 
      };
      
      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleRooTemplates(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // TODO: Récupérer les templates Roo Code
      const templates = [
        { id: 'hanuman-agent', name: 'Agent Hanuman', category: 'agent' },
        { id: 'hanuman-interface', name: 'Interface React', category: 'interface' },
        { id: 'hanuman-service', name: 'Service API', category: 'service' }
      ];
      
      res.json(this.createResponse({ templates, count: templates.length }));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleRooTemplate(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { templateId } = req.params;
      const variables = req.body;
      
      // TODO: Utiliser template spécifique
      const result = { 
        templateId, 
        variables,
        generated: true 
      };
      
      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleFileRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { path } = req.body;
      
      // TODO: Lire fichier via système sécurisé
      const content = `// Contenu simulé du fichier: ${path}`;
      
      res.json(this.createResponse({ path, content, size: content.length }));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleFileWrite(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { path, content } = req.body;
      
      // TODO: Écrire fichier via système sécurisé
      const result = { path, written: true, size: content.length };
      
      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleFileCreate(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { path, content, type } = req.body;
      
      // TODO: Créer fichier
      const result = { path, created: true, type };
      
      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleProjectCreate(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { projectName, projectType, technologies } = req.body;
      
      // TODO: Créer projet via orchestrateur
      const result = { 
        projectName, 
        projectType, 
        technologies,
        created: true 
      };
      
      res.json(this.createResponse(result));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleProjectList(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // TODO: Lister projets
      const projects = [
        { name: 'SampleAgent', type: 'agent', status: 'active' },
        { name: 'TestInterface', type: 'interface', status: 'development' }
      ];
      
      res.json(this.createResponse({ projects, count: projects.length }));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleMetrics(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const metrics = {
        orchestrator: this.orchestrator.getMetrics(),
        server: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          connections: this.io?.engine.clientsCount || 0
        }
      };
      
      res.json(this.createResponse(metrics));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  private async handleOrchestratorMetrics(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const metrics = this.orchestrator.getMetrics();
      res.json(this.createResponse(metrics));

    } catch (error) {
      this.handleError(error, res);
    }
  }

  // Méthodes utilitaires

  private async authenticateAgent(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const authHeader = req.headers.authorization;
      const agentId = req.headers['x-agent-id'] as string;
      
      if (!agentId) {
        res.status(401).json(this.createResponse(null, 'Agent ID requis', 'MISSING_AGENT_ID'));
        return;
      }

      // TODO: Valider le token d'authentification
      // if (!authHeader || !this.validateToken(authHeader)) {
      //   res.status(401).json(this.createResponse(null, 'Token invalide', 'INVALID_TOKEN'));
      //   return;
      // }

      req.agentId = agentId;
      req.context = await this.getAgentContext(agentId);
      
      next();

    } catch (error) {
      res.status(401).json(this.createResponse(null, 'Erreur authentification', 'AUTH_ERROR'));
    }
  }

  private async getAgentContext(agentId: string): Promise<HanumanContext> {
    // TODO: Récupérer contexte depuis l'orchestrateur
    return {
      agentId,
      projectType: 'agent',
      architecture: 'hanuman-biomimetic-enhanced',
      workspaceDir: '/workspace',
      developmentPhase: 'development',
      technologies: ['typescript', 'react', 'node.js'],
      requirements: []
    };
  }

  private requestLogger(req: Request, res: Response, next: NextFunction): void {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      this.log('info', `${req.method} ${req.path} - ${res.statusCode} (${duration}ms)`);
    });
    
    next();
  }

  private errorHandler(error: Error, req: Request, res: Response, next: NextFunction): void {
    this.log('error', 'Erreur API:', error);

    if (error instanceof OrchestrationError) {
      res.status(400).json(this.createResponse(null, error.message, error.code));
    } else {
      res.status(500).json(this.createResponse(null, 'Erreur interne du serveur', 'INTERNAL_ERROR'));
    }
  }

  private handleError(error: any, res: Response): void {
    this.log('error', 'Erreur handler:', error);

    if (error instanceof OrchestrationError) {
      res.status(400).json(this.createResponse(null, error.message, error.code));
    } else {
      res.status(500).json(this.createResponse(null, error.message || 'Erreur interne', 'HANDLER_ERROR'));
    }
  }

  private createResponse<T>(data: T, error?: string, code?: string): SandboxAPIResponse<T> {
    return {
      success: !error,
      data: error ? undefined : data,
      error: error ? { code: code || 'UNKNOWN_ERROR', message: error } : undefined,
      metadata: {
        requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
        processingTime: 0 // TODO: Calculer le temps réel
      }
    };
  }

  private log(level: string, message: string, data?: any): void {
    const levels = { debug: 0, info: 1, warn: 2, error: 3 };
    if (levels[level] >= levels[this.config.logLevel]) {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] [API] [${level.toUpperCase()}] ${message}`, data || '');
    }
  }

  // Méthodes publiques

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server.listen(this.config.port, () => {
          this.isRunning = true;
          this.log('info', `🚀 API Server Enhanced démarré sur le port ${this.config.port}`);
          
          if (this.config.enableWebSocket) {
            this.log('info', `🔌 WebSocket activé sur le port ${this.config.port}`);
          }
          
          resolve();
        });

        this.server.on('error', (error) => {
          this.log('error', 'Erreur serveur:', error);
          reject(error);
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.io) {
        this.io.close();
      }
      
      this.server.close(() => {
        this.isRunning = false;
        this.log('info', '🛑 API Server Enhanced arrêté');
        resolve();
      });
    });
  }

  getConfig(): APIServerConfig {
    return { ...this.config };
  }

  isServerRunning(): boolean {
    return this.isRunning;
  }
}
