// ========================================
// HANUMAN SANDBOX ENHANCED - TYPES & INTERFACES
// Définitions TypeScript pour l'orchestration IDE
// ========================================

/**
 * Action IDE à exécuter par un contrôleur
 */
export interface IDEAction {
  type: 'open_file' | 'generate_code' | 'run_command' | 'install_extension' | 'create_project' | 'navigate' | 'edit';
  target: string;
  params: Record<string, any>;
  context?: HanumanContext;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  timeout?: number;
  retries?: number;
}

/**
 * Commande en langage naturel analysée
 */
export interface NaturalLanguageCommand {
  id: string;
  input: string;
  intent: string;
  entities: Record<string, any>;
  confidence: number;
  context: HanumanContext;
  timestamp: Date;
  sessionId?: string;
  agentId: string;
}

/**
 * Contexte Hanuman pour une action
 */
export interface HanumanContext {
  agentId?: string;
  organId?: string;
  projectType: 'agent' | 'organ' | 'interface' | 'service' | 'full-project';
  architecture: string;
  currentTask?: string;
  developmentPhase?: 'planning' | 'development' | 'testing' | 'deployment';
  technologies?: string[];
  requirements?: string[];
  workspaceDir?: string;
  projectName?: string;
}

/**
 * Résultat d'exécution d'une action IDE
 */
export interface IDEActionResult {
  actionId: string;
  success: boolean;
  result?: any;
  error?: string;
  duration: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Configuration pour l'orchestrateur IDE
 */
export interface IDEOrchestratorConfig {
  maxConcurrentCommands: number;
  commandTimeout: number;
  retryAttempts: number;
  nlpConfidenceThreshold: number;
  enableCache: boolean;
  cacheTimeout: number;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  enableMetrics: boolean;
}

/**
 * Événement neural pour communication avec Hanuman
 */
export interface NeuralSignal {
  type: string;
  source: string;
  target?: string;
  data: any;
  timestamp: Date;
  metadata?: {
    version: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    correlationId?: string;
  };
}

/**
 * Session de contrôle IDE pour un agent
 */
export interface IDESession {
  id: string;
  agentId: string;
  organId?: string;
  vscodeInstanceId?: string;
  puppeteerSessionId?: string;
  status: 'initializing' | 'active' | 'idle' | 'error' | 'terminated';
  createdAt: Date;
  lastActivity: Date;
  activeCommands: string[];
  context: HanumanContext;
}

/**
 * Commande VS Code spécifique
 */
export interface VSCodeCommand {
  type: 'open_file' | 'generate_code' | 'run_terminal' | 'install_extension' | 'navigate' | 'edit' | 'search' | 'refactor';
  params: Record<string, any>;
  timeout?: number;
  requiresFocus?: boolean;
  async?: boolean;
}

/**
 * Template Roo Code avec contexte Hanuman
 */
export interface RooCodeTemplate {
  id: string;
  name: string;
  description: string;
  category: 'agent' | 'organ' | 'interface' | 'service' | 'test' | 'utility' | 'documentation';
  language: 'typescript' | 'javascript' | 'react' | 'python' | 'markdown' | 'json' | 'yaml';
  template: string;
  variables: RooCodeVariable[];
  dependencies?: string[];
  examples?: string[];
  version: string;
  lastModified: Date;
  hanumanSpecific: boolean;
}

/**
 * Variable pour template Roo Code
 */
export interface RooCodeVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'enum';
  description: string;
  required: boolean;
  defaultValue?: any;
  validation?: string;
  enumValues?: string[];
}

/**
 * Intention NLP détectée
 */
export interface DetectedIntent {
  intent: string;
  confidence: number;
  entities: Record<string, any>;
  parameters: Record<string, any>;
  context?: Record<string, any>;
}

/**
 * Métriques de performance de l'orchestrateur
 */
export interface OrchestratorMetrics {
  commandsProcessed: number;
  averageProcessingTime: number;
  successRate: number;
  errorRate: number;
  activeSessions: number;
  queueLength: number;
  lastUpdated: Date;
}

/**
 * Configuration d'un agent pour l'IDE
 */
export interface AgentIDEConfig {
  agentId: string;
  organId?: string;
  preferredIDE: 'vscode' | 'intellij' | 'vim';
  extensions: string[];
  settings: Record<string, any>;
  templates: string[];
  workspacePreferences: {
    theme: string;
    fontSize: number;
    tabSize: number;
    autoSave: boolean;
  };
  rooCodeConfig: {
    model: string;
    temperature: number;
    maxTokens: number;
    customPrompts: Record<string, string>;
  };
}

/**
 * Événement d'orchestration
 */
export interface OrchestrationEvent {
  type: 'command-received' | 'command-processed' | 'action-executed' | 'session-created' | 'session-terminated' | 'error-occurred';
  data: any;
  timestamp: Date;
  source: string;
  correlationId?: string;
}

/**
 * Réponse de l'API Sandbox Enhanced
 */
export interface SandboxAPIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    requestId: string;
    timestamp: Date;
    processingTime: number;
  };
}

/**
 * Configuration WebSocket
 */
export interface WebSocketConfig {
  enabled: boolean;
  port: number;
  path: string;
  cors: {
    origin: string[];
    credentials: boolean;
  };
  authentication: {
    required: boolean;
    tokenValidation: boolean;
  };
}

/**
 * État global de l'orchestrateur
 */
export interface OrchestratorState {
  status: 'initializing' | 'running' | 'paused' | 'error' | 'shutdown';
  activeSessions: Map<string, IDESession>;
  commandQueue: NaturalLanguageCommand[];
  metrics: OrchestratorMetrics;
  lastHealthCheck: Date;
  version: string;
}

/**
 * Erreur spécifique à l'orchestration
 */
export class OrchestrationError extends Error {
  constructor(
    message: string,
    public code: string,
    public context?: any,
    public recoverable: boolean = true
  ) {
    super(message);
    this.name = 'OrchestrationError';
  }
}

/**
 * Types d'événements supportés
 */
export type EventType = 
  | 'ide:command-received'
  | 'ide:command-processed' 
  | 'ide:action-executed'
  | 'ide:action-failed'
  | 'ide:session-created'
  | 'ide:session-terminated'
  | 'ide:error'
  | 'neural:signal-received'
  | 'neural:signal-sent'
  | 'agent:created'
  | 'agent:updated'
  | 'organ:evolved'
  | 'system:health-check'
  | 'system:metrics-updated';

/**
 * Callback pour gestion d'événements
 */
export type EventCallback<T = any> = (data: T) => void | Promise<void>;

/**
 * Interface pour communication neurale
 */
export interface NeuralCommunicationBridge {
  sendSignal(signal: NeuralSignal): Promise<void>;
  onSignalReceived(callback: EventCallback<NeuralSignal>): void;
  isConnected(): boolean;
  connect(): Promise<void>;
  disconnect(): Promise<void>;
}

/**
 * Interface pour le processeur NLP
 */
export interface NaturalLanguageProcessor {
  parse(input: string, context?: HanumanContext): Promise<DetectedIntent>;
  extractEntities(input: string): Record<string, any>;
  classifyIntent(input: string): Promise<{ intent: string; confidence: number }>;
  validateCommand(command: NaturalLanguageCommand): Promise<boolean>;
  getCommandHistory(agentId: string): NaturalLanguageCommand[];
}

/**
 * Interface pour le gestionnaire de sessions
 */
export interface SessionManager {
  createSession(agentId: string, context: HanumanContext): Promise<IDESession>;
  getSession(sessionId: string): IDESession | null;
  updateSession(sessionId: string, updates: Partial<IDESession>): Promise<void>;
  terminateSession(sessionId: string): Promise<void>;
  getActiveSessions(): IDESession[];
  cleanupInactiveSessions(): Promise<void>;
}
