# 🏗️ Sandbox Hanuman - Environnement de Développement et Test

## 🎯 Vue d'ensemble

La Sandbox Hanuman est un environnement sécurisé et isolé permettant aux agents d'Hanuman de développer, tester et valider leurs évolutions avant déploiement en production. Elle fait partie intégrante de l'écosystème Hanuman et s'intègre parfaitement avec l'orchestrateur d'organes existant.

## ✨ Fonctionnalités Principales

### 🏗️ Infrastructure
- **Conteneurisation avancée** avec Docker et Kubernetes
- **Isolation réseau complète** avec réseaux virtuels sécurisés
- **Gestion des ressources** avec limites et monitoring
- **Stockage chiffré** pour les environnements sensibles
- **Namespaces dédiés** par type d'environnement

### 🛡️ Sécurité
- **Politiques de sécurité configurables** par niveau
- **Détection d'anomalies en temps réel**
- **Isolation maximale** pour les environnements critiques
- **Audit trail complet** de toutes les activités
- **Actions automatiques** en cas d'incident

### 🧪 Environnements
- **Templates prédéfinis** pour différents types de développement
- **Gestion automatique des ressources**
- **Monitoring des performances**
- **Nettoyage automatique** des ressources inutilisées

### 📊 Monitoring
- **Métriques en temps réel** de performance et sécurité
- **Alertes automatiques** pour les incidents
- **Dashboard de gestion** intuitif
- **Rapports détaillés** d'utilisation

## 🚀 Installation et Configuration

### Prérequis
- Node.js 18+
- Docker
- Kubernetes (optionnel pour production)
- Orchestrateur Hanuman

### Installation
```bash
# Cloner le projet
git clone <repository>
cd hanuman_sandbox

# Installer les dépendances
npm install

# Construire le projet
npm run build
```

### Configuration
```typescript
import { HanumanSandbox } from './hanuman_sandbox';
import { HanumanOrganOrchestrator } from '../hanuman-working/services/HanumanOrganOrchestrator';

// Créer l'orchestrateur
const orchestrator = new HanumanOrganOrchestrator();

// Configurer la sandbox
const sandbox = new HanumanSandbox(orchestrator, {
  autoStart: true,
  enableSecurity: true,
  enableMonitoring: true,
  logLevel: 'info',
  maxContainers: 50,
  defaultSecurityLevel: 'medium'
});
```

## 📖 Guide d'utilisation

### Démarrage de la Sandbox
```typescript
// Démarrer la sandbox
await sandbox.start();

// Vérifier le statut
const status = sandbox.getStatus();
console.log('Sandbox status:', status);
```

### Création d'un Environnement de Développement
```typescript
// Créer un environnement pour un agent
const environment = await sandbox.createDevelopmentEnvironment({
  name: 'agent-frontend-dev',
  agentId: 'agent-frontend',
  organId: 'cortex-creatif',
  securityLevel: 'medium'
});
```

### Utilisation de l'Interface de Gestion
```tsx
import { SandboxManagementInterface } from './interfaces/sandbox_management_interface';

// Intégrer l'interface dans votre application React
<SandboxManagementInterface 
  infrastructure={sandbox.getComponents().infrastructure}
  security={sandbox.getComponents().security}
  onError={(error) => console.error(error)}
/>
```

### Lancement des Tests
```typescript
// Lancer les tests d'infrastructure
await sandbox.runTests();

// Ou directement
import { runSandboxTests } from './tests/infrastructure_tests';
await runSandboxTests();
```

## 🏛️ Architecture

### Structure des Dossiers
```
hanuman_sandbox/
├── infrastructure/          # Infrastructure de base
│   └── sandbox_infrastructure.ts
├── environments/           # Gestion des environnements
│   └── environment_manager.tsx
├── security/              # Système de sécurité
│   └── sandbox_security.ts
├── interfaces/            # Interfaces utilisateur
│   └── sandbox_management_interface.tsx
├── tests/                 # Tests et validation
│   └── infrastructure_tests.ts
├── index.ts              # Point d'entrée principal
└── README.md             # Documentation
```

### Composants Principaux

#### SandboxInfrastructure
Gère la création, l'isolation et la destruction des conteneurs.
- Conteneurisation avec Docker
- Réseaux virtuels isolés
- Stockage chiffré
- Monitoring des ressources

#### SandboxSecurity
Système de sécurité complet avec détection d'anomalies.
- Politiques de sécurité configurables
- Détection d'incidents en temps réel
- Actions automatiques
- Audit et logging

#### EnvironmentManager
Interface React pour la gestion des environnements.
- Templates prédéfinis
- Création d'environnements
- Monitoring des performances
- Gestion des ressources

#### SandboxManagementInterface
Dashboard principal de gestion de la sandbox.
- Vue d'ensemble des statistiques
- Gestion des environnements
- Monitoring de sécurité
- Alertes système

## 🔧 Configuration Avancée

### Niveaux de Sécurité

#### Low (Bas)
- Isolation de base
- Monitoring standard
- Accès réseau limité

#### Medium (Moyen)
- Isolation renforcée
- Monitoring avancé
- Restrictions réseau strictes

#### High (Élevé)
- Isolation maximale
- Stockage chiffré
- Monitoring en temps réel

#### Maximum
- Isolation complète
- Chiffrement obligatoire
- Audit complet

### Types d'Environnements

#### Development
- Ressources: 1-2 CPU, 1-4GB RAM
- Réseau: Surveillé
- Sécurité: Medium par défaut

#### Testing
- Ressources: 2-8 CPU, 2-16GB RAM
- Réseau: Restreint
- Sécurité: High par défaut

#### Security
- Ressources: 1-2 CPU, 0.5-4GB RAM
- Réseau: Isolé
- Sécurité: Maximum obligatoire

#### QA
- Ressources: 2-6 CPU, 2-12GB RAM
- Réseau: Restreint
- Sécurité: High par défaut

#### Staging
- Ressources: 3-8 CPU, 4-16GB RAM
- Réseau: Surveillé
- Sécurité: High par défaut

## 📊 Monitoring et Métriques

### Métriques d'Infrastructure
- Nombre de conteneurs actifs
- Utilisation des ressources (CPU, RAM, stockage)
- Statut des réseaux
- Performance des namespaces

### Métriques de Sécurité
- Score de sécurité global
- Nombre d'incidents
- Politiques actives
- Menaces bloquées

### Métriques de Performance
- Temps de création des conteneurs
- Temps de réponse
- Uptime
- Débit

## 🧪 Tests et Validation

### Tests d'Infrastructure
```bash
npm run test:infrastructure
```

### Tests de Sécurité
```bash
npm run test:security
```

### Tests de Performance
```bash
npm run test:performance
```

### Tests Complets
```bash
npm run test:all
```

## 🔍 Dépannage

### Problèmes Courants

#### Conteneur ne démarre pas
1. Vérifier les ressources disponibles
2. Contrôler les logs de sécurité
3. Valider la configuration réseau

#### Score de sécurité bas
1. Vérifier les incidents ouverts
2. Mettre à jour les politiques
3. Résoudre les violations

#### Performance dégradée
1. Monitorer l'utilisation des ressources
2. Optimiser les allocations
3. Nettoyer les conteneurs inutilisés

### Logs et Debugging
```typescript
// Activer le mode debug
const sandbox = new HanumanSandbox(orchestrator, {
  logLevel: 'debug'
});

// Écouter les événements
sandbox.on('sandbox:error', (error) => {
  console.error('Erreur sandbox:', error);
});

sandbox.on('sandbox:security-incident', (incident) => {
  console.warn('Incident sécurité:', incident);
});
```

## 🤝 Contribution

### Développement
1. Fork le projet
2. Créer une branche feature
3. Implémenter les changements
4. Lancer les tests
5. Créer une pull request

### Standards de Code
- TypeScript strict
- Tests unitaires obligatoires
- Documentation complète
- Respect des patterns existants

## 📄 Licence

Ce projet fait partie de l'écosystème Hanuman et suit la même licence que le projet principal.

## 🙏 Remerciements

Développé avec ❤️ par l'équipe Hanuman pour créer un environnement de développement sécurisé et efficace.

---

**🏗️✨ "Dans la sandbox d'Hanuman, l'innovation rencontre la sécurité pour créer l'évolution parfaite." ✨🏗️**
