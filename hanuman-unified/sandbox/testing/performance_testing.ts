import { SandboxInfrastructure, SandboxContainer } from '../infrastructure/sandbox_infrastructure';
import { TestCase, TestResult } from './automated_testing_framework';

/**
 * Système de Tests de Performance pour la Sandbox Hanuman
 * Gère les tests de charge, profiling des ressources et métriques de latence
 */

export interface PerformanceMetrics {
  responseTime: {
    min: number;
    max: number;
    avg: number;
    p50: number;
    p95: number;
    p99: number;
  };
  throughput: {
    requestsPerSecond: number;
    operationsPerSecond: number;
    dataTransferRate: number; // MB/s
  };
  resources: {
    cpu: {
      usage: number; // percentage
      cores: number;
      loadAverage: number[];
    };
    memory: {
      used: number; // bytes
      available: number; // bytes
      usage: number; // percentage
      heapUsed: number; // bytes
      heapTotal: number; // bytes
    };
    network: {
      bytesIn: number;
      bytesOut: number;
      packetsIn: number;
      packetsOut: number;
      latency: number; // ms
    };
    storage: {
      readOps: number;
      writeOps: number;
      readBytes: number;
      writeBytes: number;
      iops: number;
    };
  };
  errors: {
    total: number;
    rate: number; // percentage
    types: { [errorType: string]: number };
  };
  concurrency: {
    activeConnections: number;
    maxConnections: number;
    queueLength: number;
  };
}

export interface PerformanceTest {
  id: string;
  name: string;
  description: string;
  type: 'load' | 'stress' | 'spike' | 'volume' | 'endurance' | 'scalability';
  configuration: {
    duration: number; // seconds
    rampUpTime: number; // seconds
    rampDownTime: number; // seconds
    virtualUsers: number;
    requestsPerSecond?: number;
    dataSize?: number; // bytes
    iterations?: number;
  };
  target: {
    endpoint?: string;
    function?: string;
    component?: string;
    container?: string;
  };
  thresholds: {
    maxResponseTime: number; // ms
    minThroughput: number; // req/s
    maxErrorRate: number; // percentage
    maxCpuUsage: number; // percentage
    maxMemoryUsage: number; // percentage
  };
  scenarios: PerformanceScenario[];
}

export interface PerformanceScenario {
  id: string;
  name: string;
  weight: number; // percentage of total load
  steps: PerformanceStep[];
}

export interface PerformanceStep {
  id: string;
  name: string;
  action: 'request' | 'wait' | 'think' | 'validate';
  parameters: {
    url?: string;
    method?: string;
    payload?: any;
    headers?: { [key: string]: string };
    duration?: number; // ms
    validation?: (response: any) => boolean;
  };
}

export interface PerformanceTestResult {
  testId: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  status: 'passed' | 'failed' | 'warning';
  metrics: PerformanceMetrics;
  thresholdViolations: {
    threshold: string;
    expected: number;
    actual: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }[];
  recommendations: string[];
  rawData: {
    timestamps: number[];
    responseTimes: number[];
    throughput: number[];
    errors: any[];
    resourceUsage: any[];
  };
}

export class PerformanceTesting {
  private infrastructure: SandboxInfrastructure;
  private activeTests: Map<string, PerformanceTest> = new Map();
  private testResults: Map<string, PerformanceTestResult> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(infrastructure: SandboxInfrastructure) {
    this.infrastructure = infrastructure;
  }

  /**
   * Exécute un test de performance
   */
  async executePerformanceTest(test: PerformanceTest): Promise<PerformanceTestResult> {
    console.log(`🚀 Démarrage du test de performance: ${test.name}`);

    const result: PerformanceTestResult = {
      testId: test.id,
      startTime: new Date(),
      endTime: new Date(),
      duration: 0,
      status: 'passed',
      metrics: this.initializeMetrics(),
      thresholdViolations: [],
      recommendations: [],
      rawData: {
        timestamps: [],
        responseTimes: [],
        throughput: [],
        errors: [],
        resourceUsage: []
      }
    };

    this.activeTests.set(test.id, test);

    try {
      // Démarrer le monitoring des ressources
      await this.startResourceMonitoring(test.id, result);

      // Phase de montée en charge
      if (test.configuration.rampUpTime > 0) {
        await this.executeRampUp(test, result);
      }

      // Phase de test principal
      await this.executeMainTest(test, result);

      // Phase de descente en charge
      if (test.configuration.rampDownTime > 0) {
        await this.executeRampDown(test, result);
      }

      // Arrêter le monitoring
      await this.stopResourceMonitoring();

      // Calculer les métriques finales
      this.calculateFinalMetrics(result);

      // Vérifier les seuils
      this.checkThresholds(test, result);

      // Générer les recommandations
      this.generateRecommendations(test, result);

      result.status = result.thresholdViolations.length === 0 ? 'passed' :
                     result.thresholdViolations.some(v => v.severity === 'critical') ? 'failed' : 'warning';

    } catch (error) {
      console.error(`❌ Erreur lors du test de performance:`, error);
      result.status = 'failed';
      result.recommendations.push(`Erreur d'exécution: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    } finally {
      result.endTime = new Date();
      result.duration = result.endTime.getTime() - result.startTime.getTime();

      this.activeTests.delete(test.id);
      this.testResults.set(test.id, result);

      console.log(`✅ Test de performance terminé: ${test.name} (${result.status})`);
    }

    return result;
  }

  /**
   * Initialise les métriques de performance
   */
  private initializeMetrics(): PerformanceMetrics {
    return {
      responseTime: { min: 0, max: 0, avg: 0, p50: 0, p95: 0, p99: 0 },
      throughput: { requestsPerSecond: 0, operationsPerSecond: 0, dataTransferRate: 0 },
      resources: {
        cpu: { usage: 0, cores: 0, loadAverage: [] },
        memory: { used: 0, available: 0, usage: 0, heapUsed: 0, heapTotal: 0 },
        network: { bytesIn: 0, bytesOut: 0, packetsIn: 0, packetsOut: 0, latency: 0 },
        storage: { readOps: 0, writeOps: 0, readBytes: 0, writeBytes: 0, iops: 0 }
      },
      errors: { total: 0, rate: 0, types: {} },
      concurrency: { activeConnections: 0, maxConnections: 0, queueLength: 0 }
    };
  }

  /**
   * Démarre le monitoring des ressources
   */
  private async startResourceMonitoring(testId: string, result: PerformanceTestResult): Promise<void> {
    this.monitoringInterval = setInterval(async () => {
      try {
        const timestamp = Date.now();
        const resourceUsage = await this.collectResourceMetrics();

        result.rawData.timestamps.push(timestamp);
        result.rawData.resourceUsage.push(resourceUsage);

        // Mettre à jour les métriques en temps réel
        this.updateResourceMetrics(result.metrics, resourceUsage);

      } catch (error) {
        console.error('Erreur lors de la collecte des métriques:', error);
      }
    }, 1000); // Collecte toutes les secondes
  }

  /**
   * Arrête le monitoring des ressources
   */
  private async stopResourceMonitoring(): Promise<void> {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  /**
   * Collecte les métriques de ressources système
   */
  private async collectResourceMetrics(): Promise<any> {
    // Simulation de collecte de métriques (à remplacer par de vraies métriques système)
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    return {
      timestamp: Date.now(),
      cpu: {
        usage: Math.random() * 100,
        cores: require('os').cpus().length,
        loadAverage: require('os').loadavg()
      },
      memory: {
        used: memoryUsage.heapUsed,
        available: memoryUsage.heapTotal,
        usage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal
      },
      network: {
        bytesIn: Math.floor(Math.random() * 1000000),
        bytesOut: Math.floor(Math.random() * 1000000),
        packetsIn: Math.floor(Math.random() * 1000),
        packetsOut: Math.floor(Math.random() * 1000),
        latency: Math.random() * 100
      },
      storage: {
        readOps: Math.floor(Math.random() * 100),
        writeOps: Math.floor(Math.random() * 100),
        readBytes: Math.floor(Math.random() * 1000000),
        writeBytes: Math.floor(Math.random() * 1000000),
        iops: Math.floor(Math.random() * 1000)
      }
    };
  }

  /**
   * Met à jour les métriques de ressources
   */
  private updateResourceMetrics(metrics: PerformanceMetrics, resourceUsage: any): void {
    // Mettre à jour les métriques CPU
    metrics.resources.cpu.usage = Math.max(metrics.resources.cpu.usage, resourceUsage.cpu.usage);
    metrics.resources.cpu.cores = resourceUsage.cpu.cores;
    metrics.resources.cpu.loadAverage = resourceUsage.cpu.loadAverage;

    // Mettre à jour les métriques mémoire
    metrics.resources.memory.used = Math.max(metrics.resources.memory.used, resourceUsage.memory.used);
    metrics.resources.memory.available = resourceUsage.memory.available;
    metrics.resources.memory.usage = Math.max(metrics.resources.memory.usage, resourceUsage.memory.usage);
    metrics.resources.memory.heapUsed = resourceUsage.memory.heapUsed;
    metrics.resources.memory.heapTotal = resourceUsage.memory.heapTotal;

    // Mettre à jour les métriques réseau
    metrics.resources.network.bytesIn += resourceUsage.network.bytesIn;
    metrics.resources.network.bytesOut += resourceUsage.network.bytesOut;
    metrics.resources.network.packetsIn += resourceUsage.network.packetsIn;
    metrics.resources.network.packetsOut += resourceUsage.network.packetsOut;
    metrics.resources.network.latency = Math.max(metrics.resources.network.latency, resourceUsage.network.latency);

    // Mettre à jour les métriques stockage
    metrics.resources.storage.readOps += resourceUsage.storage.readOps;
    metrics.resources.storage.writeOps += resourceUsage.storage.writeOps;
    metrics.resources.storage.readBytes += resourceUsage.storage.readBytes;
    metrics.resources.storage.writeBytes += resourceUsage.storage.writeBytes;
    metrics.resources.storage.iops = Math.max(metrics.resources.storage.iops, resourceUsage.storage.iops);
  }

  /**
   * Exécute la phase de montée en charge
   */
  private async executeRampUp(test: PerformanceTest, result: PerformanceTestResult): Promise<void> {
    console.log(`📈 Phase de montée en charge: ${test.configuration.rampUpTime}s`);

    const rampUpSteps = 10;
    const stepDuration = test.configuration.rampUpTime * 1000 / rampUpSteps;
    const usersPerStep = test.configuration.virtualUsers / rampUpSteps;

    for (let step = 1; step <= rampUpSteps; step++) {
      const currentUsers = Math.floor(usersPerStep * step);
      console.log(`  Étape ${step}/${rampUpSteps}: ${currentUsers} utilisateurs virtuels`);

      // Simuler la charge progressive
      await this.simulateLoad(currentUsers, stepDuration, result);

      await this.sleep(stepDuration);
    }
  }

  /**
   * Exécute le test principal
   */
  private async executeMainTest(test: PerformanceTest, result: PerformanceTestResult): Promise<void> {
    console.log(`🎯 Phase de test principal: ${test.configuration.duration}s avec ${test.configuration.virtualUsers} utilisateurs`);

    const testDuration = test.configuration.duration * 1000;
    await this.simulateLoad(test.configuration.virtualUsers, testDuration, result);
  }

  /**
   * Exécute la phase de descente en charge
   */
  private async executeRampDown(test: PerformanceTest, result: PerformanceTestResult): Promise<void> {
    console.log(`📉 Phase de descente en charge: ${test.configuration.rampDownTime}s`);

    const rampDownSteps = 10;
    const stepDuration = test.configuration.rampDownTime * 1000 / rampDownSteps;
    const usersPerStep = test.configuration.virtualUsers / rampDownSteps;

    for (let step = rampDownSteps; step >= 1; step--) {
      const currentUsers = Math.floor(usersPerStep * step);
      console.log(`  Étape ${rampDownSteps - step + 1}/${rampDownSteps}: ${currentUsers} utilisateurs virtuels`);

      await this.simulateLoad(currentUsers, stepDuration, result);
      await this.sleep(stepDuration);
    }
  }

  /**
   * Simule une charge de travail
   */
  private async simulateLoad(virtualUsers: number, duration: number, result: PerformanceTestResult): Promise<void> {
    const startTime = Date.now();
    const endTime = startTime + duration;
    const promises: Promise<void>[] = [];

    // Créer les utilisateurs virtuels
    for (let i = 0; i < virtualUsers; i++) {
      promises.push(this.simulateVirtualUser(i, endTime, result));
    }

    // Attendre que tous les utilisateurs virtuels terminent
    await Promise.allSettled(promises);
  }

  /**
   * Simule un utilisateur virtuel
   */
  private async simulateVirtualUser(userId: number, endTime: number, result: PerformanceTestResult): Promise<void> {
    while (Date.now() < endTime) {
      const requestStart = Date.now();

      try {
        // Simuler une requête (à remplacer par de vraies requêtes)
        await this.simulateRequest();

        const responseTime = Date.now() - requestStart;
        result.rawData.responseTimes.push(responseTime);

        // Mettre à jour les métriques de débit
        result.metrics.throughput.requestsPerSecond++;

      } catch (error) {
        result.metrics.errors.total++;
        result.rawData.errors.push({
          timestamp: Date.now(),
          userId,
          error: error instanceof Error ? error.message : 'Erreur inconnue'
        });
      }

      // Temps de réflexion entre les requêtes
      await this.sleep(Math.random() * 1000 + 500);
    }
  }

  /**
   * Simule une requête
   */
  private async simulateRequest(): Promise<void> {
    // Simulation d'une requête avec latence variable
    const latency = Math.random() * 200 + 50; // 50-250ms
    await this.sleep(latency);

    // Simuler des erreurs occasionnelles
    if (Math.random() < 0.05) { // 5% d'erreurs
      throw new Error('Erreur simulée');
    }
  }

  /**
   * Calcule les métriques finales
   */
  private calculateFinalMetrics(result: PerformanceTestResult): void {
    const responseTimes = result.rawData.responseTimes.sort((a, b) => a - b);

    if (responseTimes.length > 0) {
      result.metrics.responseTime.min = responseTimes[0];
      result.metrics.responseTime.max = responseTimes[responseTimes.length - 1];
      result.metrics.responseTime.avg = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
      result.metrics.responseTime.p50 = responseTimes[Math.floor(responseTimes.length * 0.5)];
      result.metrics.responseTime.p95 = responseTimes[Math.floor(responseTimes.length * 0.95)];
      result.metrics.responseTime.p99 = responseTimes[Math.floor(responseTimes.length * 0.99)];
    }

    // Calculer le débit moyen
    const durationSeconds = result.duration / 1000;
    if (durationSeconds > 0) {
      result.metrics.throughput.requestsPerSecond = responseTimes.length / durationSeconds;
      result.metrics.throughput.operationsPerSecond = result.metrics.throughput.requestsPerSecond;
    }

    // Calculer le taux d'erreur
    const totalRequests = responseTimes.length + result.metrics.errors.total;
    if (totalRequests > 0) {
      result.metrics.errors.rate = (result.metrics.errors.total / totalRequests) * 100;
    }
  }

  /**
   * Vérifie les seuils de performance
   */
  private checkThresholds(test: PerformanceTest, result: PerformanceTestResult): void {
    const thresholds = test.thresholds;
    const metrics = result.metrics;

    // Vérifier le temps de réponse
    if (metrics.responseTime.avg > thresholds.maxResponseTime) {
      result.thresholdViolations.push({
        threshold: 'Temps de réponse moyen',
        expected: thresholds.maxResponseTime,
        actual: metrics.responseTime.avg,
        severity: metrics.responseTime.avg > thresholds.maxResponseTime * 2 ? 'critical' : 'high'
      });
    }

    // Vérifier le débit
    if (metrics.throughput.requestsPerSecond < thresholds.minThroughput) {
      result.thresholdViolations.push({
        threshold: 'Débit minimum',
        expected: thresholds.minThroughput,
        actual: metrics.throughput.requestsPerSecond,
        severity: metrics.throughput.requestsPerSecond < thresholds.minThroughput * 0.5 ? 'critical' : 'high'
      });
    }

    // Vérifier le taux d'erreur
    if (metrics.errors.rate > thresholds.maxErrorRate) {
      result.thresholdViolations.push({
        threshold: 'Taux d\'erreur maximum',
        expected: thresholds.maxErrorRate,
        actual: metrics.errors.rate,
        severity: metrics.errors.rate > thresholds.maxErrorRate * 2 ? 'critical' : 'medium'
      });
    }

    // Vérifier l'utilisation CPU
    if (metrics.resources.cpu.usage > thresholds.maxCpuUsage) {
      result.thresholdViolations.push({
        threshold: 'Utilisation CPU maximum',
        expected: thresholds.maxCpuUsage,
        actual: metrics.resources.cpu.usage,
        severity: metrics.resources.cpu.usage > 90 ? 'critical' : 'medium'
      });
    }

    // Vérifier l'utilisation mémoire
    if (metrics.resources.memory.usage > thresholds.maxMemoryUsage) {
      result.thresholdViolations.push({
        threshold: 'Utilisation mémoire maximum',
        expected: thresholds.maxMemoryUsage,
        actual: metrics.resources.memory.usage,
        severity: metrics.resources.memory.usage > 90 ? 'critical' : 'medium'
      });
    }
  }

  /**
   * Génère des recommandations d'optimisation
   */
  private generateRecommendations(test: PerformanceTest, result: PerformanceTestResult): void {
    const metrics = result.metrics;
    const recommendations: string[] = [];

    // Recommandations basées sur les temps de réponse
    if (metrics.responseTime.p95 > metrics.responseTime.avg * 2) {
      recommendations.push('Optimiser les requêtes les plus lentes (P95 élevé)');
    }

    // Recommandations basées sur l'utilisation des ressources
    if (metrics.resources.cpu.usage > 80) {
      recommendations.push('Optimiser l\'utilisation CPU ou augmenter les ressources');
    }

    if (metrics.resources.memory.usage > 80) {
      recommendations.push('Optimiser l\'utilisation mémoire ou augmenter la RAM');
    }

    // Recommandations basées sur les erreurs
    if (metrics.errors.rate > 1) {
      recommendations.push('Investiguer et corriger les erreurs fréquentes');
    }

    // Recommandations basées sur le débit
    if (metrics.throughput.requestsPerSecond < test.thresholds.minThroughput * 0.8) {
      recommendations.push('Améliorer l\'architecture pour augmenter le débit');
    }

    result.recommendations = recommendations;
  }

  /**
   * Utilitaire pour attendre
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Obtient les résultats d'un test
   */
  getTestResult(testId: string): PerformanceTestResult | undefined {
    return this.testResults.get(testId);
  }

  /**
   * Obtient tous les résultats de tests
   */
  getAllTestResults(): PerformanceTestResult[] {
    return Array.from(this.testResults.values());
  }

  /**
   * Obtient les tests actifs
   */
  getActiveTests(): PerformanceTest[] {
    return Array.from(this.activeTests.values());
  }

  /**
   * Obtient les résultats récents de tests de performance
   */
  async getRecentResults(projectId: string, limit: number = 10): Promise<PerformanceTestResult[]> {
    // Simulation - dans un vrai système, cela viendrait d'une base de données
    const results: PerformanceTestResult[] = [];

    for (let i = 0; i < Math.min(limit, 5); i++) {
      const testId = `perf-test-${Date.now() - i * 3600000}`;
      const startTime = new Date(Date.now() - i * 3600000);
      const endTime = new Date(startTime.getTime() + 300000); // 5 minutes

      results.push({
        testId,
        startTime,
        endTime,
        duration: 300000,
        status: Math.random() > 0.2 ? 'passed' : 'failed',
        metrics: this.generateRandomMetrics(),
        thresholdViolations: this.generateRandomViolations(),
        recommendations: this.generateRandomRecommendations(),
        rawData: {
          timestamps: Array.from({length: 60}, (_, i) => startTime.getTime() + i * 5000),
          responseTimes: Array.from({length: 60}, () => Math.random() * 200 + 50),
          throughput: Array.from({length: 60}, () => Math.random() * 100 + 50),
          errors: [],
          resourceUsage: []
        }
      });
    }

    return results;
  }

  /**
   * Génère des métriques aléatoires pour la simulation
   */
  private generateRandomMetrics(): PerformanceMetrics {
    const responseTime = {
      min: Math.random() * 50 + 10,
      max: Math.random() * 300 + 100,
      avg: Math.random() * 150 + 50,
      p50: Math.random() * 100 + 40,
      p95: Math.random() * 200 + 80,
      p99: Math.random() * 250 + 100
    };

    return {
      responseTime,
      throughput: {
        requestsPerSecond: Math.random() * 100 + 50,
        operationsPerSecond: Math.random() * 200 + 100,
        dataTransferRate: Math.random() * 50 + 10
      },
      resources: {
        cpu: {
          usage: Math.random() * 80 + 10,
          cores: 4,
          loadAverage: [Math.random() * 2, Math.random() * 2, Math.random() * 2]
        },
        memory: {
          used: Math.random() * 8000000000 + 2000000000,
          available: 16000000000,
          usage: Math.random() * 60 + 20,
          heapUsed: Math.random() * 1000000000 + 500000000,
          heapTotal: 2000000000
        },
        network: {
          bytesIn: Math.random() * 1000000 + 100000,
          bytesOut: Math.random() * 1000000 + 100000,
          packetsIn: Math.random() * 10000 + 1000,
          packetsOut: Math.random() * 10000 + 1000,
          latency: Math.random() * 50 + 5
        },
        storage: {
          readOps: Math.random() * 1000 + 100,
          writeOps: Math.random() * 500 + 50,
          readBytes: Math.random() * 10000000 + 1000000,
          writeBytes: Math.random() * 5000000 + 500000,
          iops: Math.random() * 1000 + 200
        }
      },
      errors: {
        total: Math.floor(Math.random() * 10),
        rate: Math.random() * 5,
        types: {
          'timeout': Math.floor(Math.random() * 3),
          'connection': Math.floor(Math.random() * 2),
          'server_error': Math.floor(Math.random() * 2)
        }
      },
      concurrency: {
        activeConnections: Math.floor(Math.random() * 100) + 10,
        maxConnections: 200,
        queueLength: Math.floor(Math.random() * 20)
      }
    };
  }

  /**
   * Génère des violations de seuils aléatoires
   */
  private generateRandomViolations(): PerformanceTestResult['thresholdViolations'] {
    const violations = [];

    if (Math.random() > 0.7) {
      violations.push({
        threshold: 'maxResponseTime',
        expected: 200,
        actual: Math.random() * 100 + 200,
        severity: 'medium' as const
      });
    }

    if (Math.random() > 0.8) {
      violations.push({
        threshold: 'minThroughput',
        expected: 100,
        actual: Math.random() * 50 + 30,
        severity: 'high' as const
      });
    }

    return violations;
  }

  /**
   * Génère des recommandations aléatoires
   */
  private generateRandomRecommendations(): string[] {
    const allRecommendations = [
      'Optimiser les requêtes de base de données',
      'Implémenter la mise en cache',
      'Augmenter les ressources serveur',
      'Optimiser les algorithmes critiques',
      'Réduire la taille des réponses',
      'Implémenter la compression',
      'Optimiser les images et assets',
      'Utiliser un CDN'
    ];

    const count = Math.floor(Math.random() * 3) + 1;
    return allRecommendations.slice(0, count);
  }

  /**
   * Obtient les statistiques de performance globales
   */
  getPerformanceStats(): {
    averageResponseTime: number;
    averageThroughput: number;
    errorRate: number;
    totalTests: number;
  } {
    // Simulation de statistiques
    return {
      averageResponseTime: Math.random() * 100 + 50,
      averageThroughput: Math.random() * 100 + 75,
      errorRate: Math.random() * 5,
      totalTests: Math.floor(Math.random() * 100) + 20
    };
  }

  /**
   * Compare deux résultats de tests de performance
   */
  compareResults(result1: PerformanceTestResult, result2: PerformanceTestResult): {
    responseTimeImprovement: number;
    throughputImprovement: number;
    errorRateChange: number;
    recommendation: string;
  } {
    const responseTimeImprovement = ((result1.metrics.responseTime.avg - result2.metrics.responseTime.avg) / result1.metrics.responseTime.avg) * 100;
    const throughputImprovement = ((result2.metrics.throughput.requestsPerSecond - result1.metrics.throughput.requestsPerSecond) / result1.metrics.throughput.requestsPerSecond) * 100;
    const errorRateChange = result2.metrics.errors.rate - result1.metrics.errors.rate;

    let recommendation = '';
    if (responseTimeImprovement > 10) {
      recommendation = 'Excellente amélioration des temps de réponse';
    } else if (responseTimeImprovement < -10) {
      recommendation = 'Dégradation des performances détectée';
    } else {
      recommendation = 'Performances stables';
    }

    return {
      responseTimeImprovement,
      throughputImprovement,
      errorRateChange,
      recommendation
    };
  }
}
