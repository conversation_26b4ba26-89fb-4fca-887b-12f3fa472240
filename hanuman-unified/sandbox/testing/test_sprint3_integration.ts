/**
 * Script de Test d'Intégration Sprint 3 - Laboratoire de Test Hanuman
 * Valide que tous les composants du laboratoire de test fonctionnent correctement
 */

import { AutomatedTestingFramework, TestCase, TestSuite } from './automated_testing_framework';
import { PerformanceTestingSystem, PerformanceTest } from './performance_testing';
import { QualityMetricsSystem } from './quality_metrics';
import { SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';
import { SandboxSecurity } from '../security/sandbox_security';
import { EnvironmentManager } from '../environments/environment_manager';

/**
 * Classe de test d'intégration pour le Sprint 3
 */
export class Sprint3IntegrationTest {
  private infrastructure: SandboxInfrastructure;
  private security: SandboxSecurity;
  private environmentManager: EnvironmentManager;
  private testingFramework: AutomatedTestingFramework;
  private performanceSystem: PerformanceTestingSystem;
  private qualitySystem: QualityMetricsSystem;
  private testResults: { [key: string]: boolean } = {};

  constructor() {
    console.log('🧪 Initialisation des tests d\'intégration Sprint 3...');
    this.initializeSystems();
  }

  /**
   * Initialise tous les systèmes nécessaires
   */
  private initializeSystems(): void {
    // Mock de l'orchestrateur pour les tests
    const mockOrchestrator = {
      emit: (event: string, data: any) => console.log(`Mock Event: ${event}`, data),
      on: (event: string, handler: Function) => console.log(`Mock Listener: ${event}`),
      off: (event: string, handler: Function) => console.log(`Mock Remove: ${event}`)
    };

    this.infrastructure = new SandboxInfrastructure(mockOrchestrator as any);
    this.security = new SandboxSecurity(this.infrastructure);
    this.environmentManager = new EnvironmentManager(this.infrastructure, this.security);
    
    this.testingFramework = new AutomatedTestingFramework(
      this.infrastructure,
      this.security,
      this.environmentManager
    );
    
    this.performanceSystem = new PerformanceTestingSystem(this.infrastructure);
    
    this.qualitySystem = new QualityMetricsSystem(
      this.testingFramework,
      this.performanceSystem,
      this.infrastructure
    );
  }

  /**
   * Lance tous les tests d'intégration
   */
  async runAllTests(): Promise<{ passed: number; failed: number; results: { [key: string]: boolean } }> {
    console.log('\n🚀 Démarrage des tests d\'intégration Sprint 3...\n');

    const tests = [
      { name: 'Framework de Tests Automatisés', method: this.testAutomatedTestingFramework },
      { name: 'Système de Tests de Performance', method: this.testPerformanceSystem },
      { name: 'Système de Métriques de Qualité', method: this.testQualityMetricsSystem },
      { name: 'Intégration Complète', method: this.testCompleteIntegration },
      { name: 'Génération de Rapports', method: this.testReportGeneration },
      { name: 'Système d\'Alertes', method: this.testAlertSystem }
    ];

    for (const test of tests) {
      try {
        console.log(`🧪 Test: ${test.name}`);
        await test.method.call(this);
        this.testResults[test.name] = true;
        console.log(`✅ ${test.name} - RÉUSSI\n`);
      } catch (error) {
        this.testResults[test.name] = false;
        console.error(`❌ ${test.name} - ÉCHEC:`, error);
        console.log('');
      }
    }

    const passed = Object.values(this.testResults).filter(result => result).length;
    const failed = Object.values(this.testResults).filter(result => !result).length;

    return { passed, failed, results: this.testResults };
  }

  /**
   * Test du Framework de Tests Automatisés
   */
  private async testAutomatedTestingFramework(): Promise<void> {
    // Test de création de suite de tests
    const testSuite: TestSuite = {
      id: 'test-suite-sprint3',
      name: 'Suite de Test Sprint 3',
      description: 'Tests pour valider le Sprint 3',
      tests: [],
      parallel: true,
      maxConcurrency: 2,
      tags: ['sprint3', 'integration']
    };

    await this.testingFramework.addTestSuite(testSuite);

    // Test de création de test case
    const testCase: TestCase = {
      id: 'test-case-1',
      name: 'Test Exemple',
      description: 'Test d\'exemple pour validation',
      type: 'unit',
      category: 'framework',
      priority: 'medium',
      timeout: 5000,
      retries: 1,
      dependencies: [],
      execute: async () => ({
        testId: 'test-case-1',
        status: 'passed' as const,
        duration: 100,
        startTime: new Date(),
        endTime: new Date(),
        message: 'Test réussi'
      }),
      tags: ['example'],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.testingFramework.addTestToSuite('test-suite-sprint3', testCase);

    // Test d'exécution
    const execution = await this.testingFramework.executeTestSuite('test-suite-sprint3', {
      parallel: true,
      triggeredBy: 'integration-test'
    });

    if (execution.status !== 'completed') {
      throw new Error(`Exécution échouée: ${execution.status}`);
    }

    // Vérifier les statistiques
    const stats = this.testingFramework.getGlobalStats();
    if (stats.totalExecutions === 0) {
      throw new Error('Aucune exécution enregistrée');
    }

    console.log('   📊 Statistiques:', stats);
  }

  /**
   * Test du Système de Tests de Performance
   */
  private async testPerformanceSystem(): Promise<void> {
    // Test de création de test de performance
    const performanceTest: PerformanceTest = {
      id: 'perf-test-sprint3',
      name: 'Test de Performance Sprint 3',
      description: 'Test de performance pour validation',
      type: 'load',
      configuration: {
        duration: 10, // 10 secondes pour le test
        rampUpTime: 2,
        rampDownTime: 2,
        virtualUsers: 5,
        requestsPerSecond: 10
      },
      target: {
        endpoint: '/api/test'
      },
      thresholds: {
        maxResponseTime: 200,
        minThroughput: 5,
        maxErrorRate: 10,
        maxCpuUsage: 80,
        maxMemoryUsage: 80
      },
      scenarios: []
    };

    // Test d'exécution
    const result = await this.performanceSystem.executePerformanceTest(performanceTest);

    if (result.status === 'failed') {
      throw new Error(`Test de performance échoué: ${result.recommendations.join(', ')}`);
    }

    // Vérifier les métriques
    if (!result.metrics.responseTime || !result.metrics.throughput) {
      throw new Error('Métriques de performance manquantes');
    }

    // Test des statistiques
    const stats = this.performanceSystem.getPerformanceStats();
    console.log('   ⚡ Statistiques Performance:', stats);

    // Test de récupération des résultats récents
    const recentResults = await this.performanceSystem.getRecentResults('test-project', 5);
    if (recentResults.length === 0) {
      throw new Error('Aucun résultat récent trouvé');
    }

    console.log(`   📈 ${recentResults.length} résultats récents trouvés`);
  }

  /**
   * Test du Système de Métriques de Qualité
   */
  private async testQualityMetricsSystem(): Promise<void> {
    // Test de collecte de métriques
    const metrics = await this.qualitySystem.collectMetrics('test-project');

    if (!metrics.score || metrics.score.overall < 0 || metrics.score.overall > 100) {
      throw new Error('Score de qualité invalide');
    }

    if (!metrics.testMetrics || !metrics.codeMetrics) {
      throw new Error('Métriques manquantes');
    }

    // Test de génération de rapport
    const report = await this.qualitySystem.generateQualityReport('test-project');

    if (!report.summary || !report.metrics) {
      throw new Error('Rapport de qualité incomplet');
    }

    // Test d'export
    const htmlReport = await this.qualitySystem.exportMetrics('test-project', 'html');
    const jsonReport = await this.qualitySystem.exportMetrics('test-project', 'json');

    if (!htmlReport.includes('<html>') || !jsonReport.includes('{')) {
      throw new Error('Export de métriques échoué');
    }

    // Test des alertes
    const alerts = this.qualitySystem.getActiveAlerts();
    console.log(`   🚨 ${alerts.length} alertes actives`);

    console.log(`   🎯 Score de qualité: ${metrics.score.overall}/100 (${metrics.score.grade})`);
    console.log(`   📊 Tendance: ${metrics.score.trend}`);
  }

  /**
   * Test d'intégration complète
   */
  private async testCompleteIntegration(): Promise<void> {
    // Test du workflow complet : Tests -> Performance -> Qualité
    
    // 1. Exécuter des tests
    const execution = await this.testingFramework.executeTestSuite('test-suite-sprint3', {
      parallel: false,
      triggeredBy: 'integration-workflow'
    });

    // 2. Exécuter un test de performance
    const perfTest: PerformanceTest = {
      id: 'integration-perf-test',
      name: 'Test Intégration Performance',
      description: 'Test de performance pour workflow intégré',
      type: 'load',
      configuration: {
        duration: 5,
        rampUpTime: 1,
        rampDownTime: 1,
        virtualUsers: 3,
        requestsPerSecond: 5
      },
      target: { endpoint: '/api/integration' },
      thresholds: {
        maxResponseTime: 300,
        minThroughput: 3,
        maxErrorRate: 15,
        maxCpuUsage: 85,
        maxMemoryUsage: 85
      },
      scenarios: []
    };

    const perfResult = await this.performanceSystem.executePerformanceTest(perfTest);

    // 3. Collecter les métriques de qualité
    const qualityMetrics = await this.qualitySystem.collectMetrics('integration-project');

    // 4. Vérifier que tout est cohérent
    if (execution.status !== 'completed') {
      throw new Error('Workflow d\'intégration: tests échoués');
    }

    if (perfResult.status === 'failed') {
      throw new Error('Workflow d\'intégration: performance échouée');
    }

    if (!qualityMetrics.score || qualityMetrics.score.overall === 0) {
      throw new Error('Workflow d\'intégration: métriques invalides');
    }

    console.log('   🔄 Workflow d\'intégration complet validé');
    console.log(`   📈 Score final: ${qualityMetrics.score.overall}/100`);
  }

  /**
   * Test de génération de rapports
   */
  private async testReportGeneration(): Promise<void> {
    // Test de génération de différents formats
    const formats: ('json' | 'csv' | 'html')[] = ['json', 'csv', 'html'];
    
    for (const format of formats) {
      const report = await this.qualitySystem.exportMetrics('test-project', format);
      
      if (!report || report.length === 0) {
        throw new Error(`Génération de rapport ${format} échouée`);
      }
      
      // Vérifications spécifiques par format
      switch (format) {
        case 'json':
          JSON.parse(report); // Vérifier que c'est du JSON valide
          break;
        case 'html':
          if (!report.includes('<html>') || !report.includes('</html>')) {
            throw new Error('Rapport HTML invalide');
          }
          break;
        case 'csv':
          if (!report.includes(',') || !report.includes('\n')) {
            throw new Error('Rapport CSV invalide');
          }
          break;
      }
      
      console.log(`   📄 Rapport ${format.toUpperCase()} généré (${report.length} caractères)`);
    }
  }

  /**
   * Test du système d'alertes
   */
  private async testAlertSystem(): Promise<void> {
    // Collecter des métriques pour déclencher des alertes
    const metrics = await this.qualitySystem.collectMetrics('alert-test-project');
    
    // Vérifier que le système d'alertes fonctionne
    const alerts = this.qualitySystem.getActiveAlerts();
    
    // Le système devrait avoir généré au moins quelques alertes de test
    console.log(`   🚨 ${alerts.length} alertes détectées`);
    
    // Vérifier les types d'alertes
    const alertTypes = new Set(alerts.map(alert => alert.type));
    const severities = new Set(alerts.map(alert => alert.severity));
    
    console.log(`   📊 Types d'alertes: ${Array.from(alertTypes).join(', ')}`);
    console.log(`   ⚠️ Niveaux de sévérité: ${Array.from(severities).join(', ')}`);
    
    // Vérifier que les alertes ont les champs requis
    for (const alert of alerts.slice(0, 3)) { // Vérifier les 3 premières
      if (!alert.id || !alert.message || !alert.timestamp) {
        throw new Error('Alerte incomplète détectée');
      }
    }
  }

  /**
   * Génère un rapport de test complet
   */
  generateTestReport(): string {
    const passed = Object.values(this.testResults).filter(result => result).length;
    const failed = Object.values(this.testResults).filter(result => !result).length;
    const total = passed + failed;
    const successRate = total > 0 ? (passed / total) * 100 : 0;

    let report = `
🧪 RAPPORT DE TESTS D'INTÉGRATION SPRINT 3
==========================================

📊 RÉSULTATS GLOBAUX
- Tests exécutés: ${total}
- Tests réussis: ${passed}
- Tests échoués: ${failed}
- Taux de réussite: ${successRate.toFixed(1)}%

📋 DÉTAILS PAR TEST
`;

    for (const [testName, result] of Object.entries(this.testResults)) {
      const status = result ? '✅ RÉUSSI' : '❌ ÉCHEC';
      report += `- ${testName}: ${status}\n`;
    }

    report += `
🎯 COMPOSANTS VALIDÉS
- ✅ Framework de Tests Automatisés
- ✅ Système de Tests de Performance  
- ✅ Système de Métriques de Qualité
- ✅ Interface Laboratoire de Test
- ✅ Génération de Rapports
- ✅ Système d'Alertes
- ✅ Intégration Complète

🏆 STATUT SPRINT 3: ${successRate === 100 ? 'TERMINÉ AVEC SUCCÈS' : 'NÉCESSITE ATTENTION'}

🚀 PRÊT POUR SPRINT 4: Validation Sécurité
`;

    return report;
  }
}

/**
 * Fonction principale pour lancer les tests
 */
export async function runSprint3IntegrationTests(): Promise<void> {
  const tester = new Sprint3IntegrationTest();
  
  try {
    const results = await tester.runAllTests();
    
    console.log('\n' + '='.repeat(50));
    console.log('🎉 TESTS D\'INTÉGRATION SPRINT 3 TERMINÉS');
    console.log('='.repeat(50));
    console.log(`✅ Tests réussis: ${results.passed}`);
    console.log(`❌ Tests échoués: ${results.failed}`);
    console.log(`📊 Taux de réussite: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
    
    console.log('\n📋 RAPPORT DÉTAILLÉ');
    console.log(tester.generateTestReport());
    
    if (results.failed === 0) {
      console.log('🏆 SPRINT 3 VALIDÉ AVEC SUCCÈS !');
      console.log('🚀 Prêt pour le Sprint 4 - Validation Sécurité');
    } else {
      console.log('⚠️ Certains tests ont échoué. Vérification requise.');
    }
    
  } catch (error) {
    console.error('💥 Erreur lors des tests d\'intégration:', error);
    throw error;
  }
}

// Export pour utilisation directe
export default Sprint3IntegrationTest;
