// ========================================
// HANUMAN SANDBOX ENHANCED - NATURAL LANGUAGE PROCESSOR
// Processeur intelligent pour commandes en langage naturel
// ========================================

import { 
  DetectedIntent, 
  HanumanContext, 
  NaturalLanguageCommand,
  NaturalLanguageProcessor as INaturalLanguageProcessor
} from '../orchestration/types';

/**
 * Patterns d'intentions pour reconnaissance NLP
 */
const INTENT_PATTERNS = {
  'create_agent': [
    /créer?\s+(un\s+)?(nouvel?\s+)?agent\s+(\w+)/i,
    /générer?\s+(un\s+)?agent\s+(\w+)/i,
    /nouveau\s+agent\s+(\w+)/i,
    /agent\s+(\w+)\s+pour\s+(.+)/i,
    /faire\s+(un\s+)?agent\s+(.+)/i
  ],
  'create_interface': [
    /créer?\s+(une\s+)?interface\s+(\w+)/i,
    /générer?\s+(un\s+)?composant\s+react\s+(\w+)/i,
    /nouvelle\s+interface\s+(\w+)/i,
    /interface\s+(\w+)\s+avec\s+(.+)/i,
    /composant\s+(\w+)\s+pour\s+(.+)/i
  ],
  'create_organ': [
    /créer?\s+(un\s+)?organe\s+(\w+)/i,
    /générer?\s+(un\s+)?organe\s+(\w+)/i,
    /nouvel\s+organe\s+(\w+)/i,
    /organe\s+(\w+)\s+pour\s+(.+)/i
  ],
  'create_service': [
    /créer?\s+(un\s+)?service\s+(\w+)/i,
    /générer?\s+(un\s+)?service\s+(\w+)/i,
    /nouveau\s+service\s+(\w+)/i,
    /service\s+(\w+)\s+pour\s+(.+)/i,
    /api\s+(\w+)/i
  ],
  'setup_project': [
    /initialiser?\s+(un\s+)?projet\s+(\w+)/i,
    /créer?\s+(un\s+)?projet\s+complet\s+(\w+)/i,
    /setup\s+projet\s+(\w+)/i,
    /nouveau\s+projet\s+(\w+)/i,
    /projet\s+(\w+)\s+avec\s+(.+)/i
  ],
  'open_file': [
    /ouvrir?\s+(le\s+)?fichier\s+(.+)/i,
    /ouvre\s+(.+)/i,
    /voir\s+(le\s+)?fichier\s+(.+)/i,
    /éditer\s+(.+)/i
  ],
  'run_tests': [
    /lancer?\s+(les\s+)?tests?/i,
    /exécuter?\s+(les\s+)?tests?/i,
    /tester\s+(.+)/i,
    /run\s+tests?/i
  ],
  'deploy_project': [
    /déployer?\s+(le\s+)?projet/i,
    /déploiement\s+(.+)/i,
    /mettre\s+en\s+production/i,
    /deploy\s+(.+)/i
  ],
  'debug_issue': [
    /débugger?\s+(.+)/i,
    /corriger?\s+(l[ae]\s+)?erreur\s+(.+)/i,
    /résoudre\s+(le\s+)?problème\s+(.+)/i,
    /debug\s+(.+)/i
  ],
  'optimize_code': [
    /optimiser?\s+(le\s+)?code/i,
    /améliorer?\s+(les\s+)?performances/i,
    /refactorer?\s+(.+)/i,
    /optimize\s+(.+)/i
  ],
  'generate_documentation': [
    /générer?\s+(la\s+)?documentation/i,
    /documenter\s+(.+)/i,
    /créer?\s+(la\s+)?doc/i,
    /readme\s+pour\s+(.+)/i
  ],
  'install_dependencies': [
    /installer?\s+(les\s+)?dépendances/i,
    /npm\s+install/i,
    /yarn\s+install/i,
    /ajouter?\s+(la\s+)?dépendance\s+(.+)/i
  ]
};

/**
 * Patterns d'entités pour extraction
 */
const ENTITY_PATTERNS = {
  agentName: [
    /agent\s+(\w+)/i,
    /appelé\s+(\w+)/i,
    /nommé\s+(\w+)/i
  ],
  componentName: [
    /interface\s+(\w+)/i,
    /composant\s+(\w+)/i,
    /component\s+(\w+)/i
  ],
  projectName: [
    /projet\s+(\w+)/i,
    /project\s+(\w+)/i,
    /application\s+(\w+)/i
  ],
  fileName: [
    /fichier\s+([^\s]+)/i,
    /file\s+([^\s]+)/i,
    /([^\s]+\.(ts|js|tsx|jsx|py|md))/i
  ],
  technology: [
    /(react|vue|angular|typescript|javascript|python|node\.?js|express|fastapi)/i,
    /(docker|kubernetes|redis|kafka|postgresql|mongodb)/i,
    /(tailwind|bootstrap|material-ui|styled-components)/i
  ],
  capability: [
    /(frontend|backend|fullstack|ui|api|database|auth|security)/i,
    /(testing|monitoring|logging|caching|messaging)/i,
    /(crud|rest|graphql|websocket|microservice)/i
  ]
};

/**
 * Processeur de langage naturel pour Hanuman Enhanced
 */
export class NaturalLanguageProcessor implements INaturalLanguageProcessor {
  private intentCache: Map<string, DetectedIntent>;
  private entityCache: Map<string, Record<string, any>>;
  private commandHistory: Map<string, NaturalLanguageCommand[]>;

  constructor() {
    this.intentCache = new Map();
    this.entityCache = new Map();
    this.commandHistory = new Map();
  }

  /**
   * Initialisation du processeur NLP
   */
  async initialize(): Promise<void> {
    console.log('🧠 Initialisation Natural Language Processor...');
    
    // Précharger les patterns fréquents
    await this.preloadCommonPatterns();
    
    console.log('✅ NLP Processor initialisé');
  }

  /**
   * Analyse complète d'une commande en langage naturel
   */
  async parse(input: string, context?: HanumanContext): Promise<DetectedIntent> {
    const cacheKey = this.getCacheKey(input, context);
    
    // Vérifier le cache
    if (this.intentCache.has(cacheKey)) {
      return this.intentCache.get(cacheKey)!;
    }

    console.log(`🔍 Analyse NLP: "${input}"`);

    // 1. Nettoyer et normaliser l'entrée
    const normalizedInput = this.normalizeInput(input);

    // 2. Classifier l'intention
    const intentResult = await this.classifyIntent(normalizedInput);

    // 3. Extraire les entités
    const entities = this.extractEntities(normalizedInput);

    // 4. Enrichir avec le contexte
    const enrichedEntities = this.enrichWithContext(entities, context);

    // 5. Calculer la confiance finale
    const finalConfidence = this.calculateFinalConfidence(
      intentResult.confidence,
      entities,
      context
    );

    const result: DetectedIntent = {
      intent: intentResult.intent,
      confidence: finalConfidence,
      entities: enrichedEntities,
      parameters: this.extractParameters(normalizedInput, intentResult.intent),
      context: context ? { ...context } : undefined
    };

    // Mettre en cache
    this.intentCache.set(cacheKey, result);

    console.log(`   ✅ Intent: ${result.intent} (confiance: ${result.confidence.toFixed(2)})`);
    console.log(`   📋 Entités:`, Object.keys(result.entities));

    return result;
  }

  /**
   * Classification de l'intention
   */
  async classifyIntent(input: string): Promise<{ intent: string; confidence: number }> {
    let bestMatch = { intent: 'unknown', confidence: 0 };

    for (const [intent, patterns] of Object.entries(INTENT_PATTERNS)) {
      for (const pattern of patterns) {
        const match = input.match(pattern);
        if (match) {
          // Calculer la confiance basée sur la qualité du match
          const confidence = this.calculatePatternConfidence(match, input, pattern);
          
          if (confidence > bestMatch.confidence) {
            bestMatch = { intent, confidence };
          }
        }
      }
    }

    // Bonus de confiance pour les intentions fréquentes
    if (bestMatch.intent !== 'unknown') {
      bestMatch.confidence = Math.min(1.0, bestMatch.confidence + 0.1);
    }

    return bestMatch;
  }

  /**
   * Extraction des entités
   */
  extractEntities(input: string): Record<string, any> {
    const entities: Record<string, any> = {};

    for (const [entityType, patterns] of Object.entries(ENTITY_PATTERNS)) {
      for (const pattern of patterns) {
        const matches = input.match(pattern);
        if (matches) {
          if (entityType === 'technology' || entityType === 'capability') {
            // Pour les technologies et capacités, collecter toutes les occurrences
            if (!entities[entityType]) entities[entityType] = [];
            entities[entityType].push(matches[1].toLowerCase());
          } else {
            // Pour les autres entités, prendre la première occurrence
            entities[entityType] = matches[1];
          }
        }
      }
    }

    // Extraction d'entités spéciales
    entities.features = this.extractFeatures(input);
    entities.requirements = this.extractRequirements(input);
    entities.modifiers = this.extractModifiers(input);

    return entities;
  }

  /**
   * Validation d'une commande
   */
  async validateCommand(command: NaturalLanguageCommand): Promise<boolean> {
    // Vérifier la confiance minimale
    if (command.confidence < 0.5) {
      return false;
    }

    // Vérifier que l'intention est supportée
    if (!Object.keys(INTENT_PATTERNS).includes(command.intent)) {
      return false;
    }

    // Vérifier les entités requises selon l'intention
    const requiredEntities = this.getRequiredEntities(command.intent);
    for (const required of requiredEntities) {
      if (!command.entities[required]) {
        return false;
      }
    }

    return true;
  }

  /**
   * Récupération de l'historique des commandes
   */
  getCommandHistory(agentId: string): NaturalLanguageCommand[] {
    return this.commandHistory.get(agentId) || [];
  }

  /**
   * Ajout d'une commande à l'historique
   */
  addToHistory(command: NaturalLanguageCommand): void {
    const agentHistory = this.commandHistory.get(command.agentId) || [];
    agentHistory.push(command);
    
    // Garder seulement les 100 dernières commandes
    if (agentHistory.length > 100) {
      agentHistory.shift();
    }
    
    this.commandHistory.set(command.agentId, agentHistory);
  }

  // Méthodes utilitaires privées

  private normalizeInput(input: string): string {
    return input
      .toLowerCase()
      .trim()
      .replace(/[^\w\s\-\.]/g, ' ')
      .replace(/\s+/g, ' ');
  }

  private calculatePatternConfidence(match: RegExpMatchArray, input: string, pattern: RegExp): number {
    const matchLength = match[0].length;
    const inputLength = input.length;
    
    // Confiance basée sur la proportion du match
    let confidence = matchLength / inputLength;
    
    // Bonus pour les matches complets
    if (matchLength === inputLength) {
      confidence += 0.2;
    }
    
    // Bonus pour les patterns spécifiques
    if (pattern.source.includes('\\w+')) {
      confidence += 0.1;
    }
    
    return Math.min(1.0, confidence);
  }

  private enrichWithContext(entities: Record<string, any>, context?: HanumanContext): Record<string, any> {
    if (!context) return entities;

    // Enrichir avec le contexte de l'agent
    if (context.agentId && !entities.agentId) {
      entities.agentId = context.agentId;
    }

    if (context.organId && !entities.organId) {
      entities.organId = context.organId;
    }

    if (context.projectType && !entities.projectType) {
      entities.projectType = context.projectType;
    }

    if (context.technologies && !entities.technology) {
      entities.technology = context.technologies;
    }

    return entities;
  }

  private calculateFinalConfidence(
    intentConfidence: number,
    entities: Record<string, any>,
    context?: HanumanContext
  ): number {
    let confidence = intentConfidence;

    // Bonus pour les entités trouvées
    const entityCount = Object.keys(entities).length;
    confidence += entityCount * 0.05;

    // Bonus pour le contexte
    if (context) {
      confidence += 0.1;
    }

    // Malus pour les commandes ambiguës
    if (intentConfidence < 0.7 && entityCount < 2) {
      confidence -= 0.2;
    }

    return Math.max(0, Math.min(1.0, confidence));
  }

  private extractFeatures(input: string): string[] {
    const features = [];
    
    if (input.includes('moderne') || input.includes('modern')) features.push('modern');
    if (input.includes('responsive')) features.push('responsive');
    if (input.includes('accessible')) features.push('accessible');
    if (input.includes('sécurisé') || input.includes('secure')) features.push('secure');
    if (input.includes('rapide') || input.includes('fast')) features.push('fast');
    if (input.includes('élégant') || input.includes('elegant')) features.push('elegant');
    
    return features;
  }

  private extractRequirements(input: string): string[] {
    const requirements = [];
    
    if (input.includes('test')) requirements.push('testing');
    if (input.includes('sécurité') || input.includes('security')) requirements.push('security');
    if (input.includes('performance')) requirements.push('performance');
    if (input.includes('accessibilité') || input.includes('accessibility')) requirements.push('accessibility');
    if (input.includes('documentation')) requirements.push('documentation');
    
    return requirements;
  }

  private extractModifiers(input: string): string[] {
    const modifiers = [];
    
    if (input.includes('simple')) modifiers.push('simple');
    if (input.includes('complexe') || input.includes('avancé')) modifiers.push('advanced');
    if (input.includes('complet')) modifiers.push('complete');
    if (input.includes('basique') || input.includes('basic')) modifiers.push('basic');
    
    return modifiers;
  }

  private extractParameters(input: string, intent: string): Record<string, any> {
    const parameters: Record<string, any> = {};

    switch (intent) {
      case 'create_agent':
        parameters.generateTests = input.includes('test');
        parameters.generateDocs = input.includes('doc');
        parameters.includeInterface = input.includes('interface') || input.includes('ui');
        break;
        
      case 'create_interface':
        parameters.includeStyles = !input.includes('sans style');
        parameters.includeTests = input.includes('test');
        parameters.responsive = input.includes('responsive');
        break;
        
      case 'setup_project':
        parameters.includeCI = input.includes('ci') || input.includes('pipeline');
        parameters.includeDocker = input.includes('docker');
        parameters.includeTests = input.includes('test');
        break;
    }

    return parameters;
  }

  private getRequiredEntities(intent: string): string[] {
    const requirements: Record<string, string[]> = {
      'create_agent': ['agentName'],
      'create_interface': ['componentName'],
      'create_organ': ['organName'],
      'create_service': ['serviceName'],
      'setup_project': ['projectName'],
      'open_file': ['fileName']
    };

    return requirements[intent] || [];
  }

  private getCacheKey(input: string, context?: HanumanContext): string {
    const contextKey = context ? JSON.stringify(context) : '';
    return `${input}:${contextKey}`;
  }

  private async preloadCommonPatterns(): Promise<void> {
    // Précharger les patterns les plus fréquents pour optimiser les performances
    const commonCommands = [
      'créer un agent frontend',
      'créer une interface react',
      'nouveau projet',
      'ouvrir fichier',
      'lancer tests'
    ];

    for (const command of commonCommands) {
      await this.parse(command);
    }
  }

  // Méthodes publiques pour gestion du cache
  public clearCache(): void {
    this.intentCache.clear();
    this.entityCache.clear();
  }

  public getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.intentCache.size,
      hitRate: 0.85 // TODO: Calculer le vrai taux de hit
    };
  }
}
