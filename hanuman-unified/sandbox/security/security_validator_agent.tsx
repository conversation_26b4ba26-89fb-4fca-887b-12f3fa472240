import React, { useState, useEffect, useCallback } from 'react';
import { SecurityAgent } from '../../../agents/security/src/core/SecurityAgent';
import { VulnerabilityScanner } from './vulnerability_scanner';
import { SandboxContainer } from '../infrastructure/sandbox_infrastructure';
import { SecurityPolicies } from './security_policies';

// Types pour l'agent validateur de sécurité
export interface SecurityValidationRequest {
  id: string;
  type: 'code' | 'container' | 'environment' | 'deployment';
  target: {
    containerId?: string;
    agentId?: string;
    organId?: string;
    codeRepository?: string;
    environmentId?: string;
  };
  priority: 'low' | 'medium' | 'high' | 'critical';
  requestedBy: string;
  timestamp: Date;
  metadata?: any;
}

export interface SecurityValidationResult {
  id: string;
  requestId: string;
  status: 'pending' | 'scanning' | 'completed' | 'failed' | 'blocked';
  score: number; // 0-100
  vulnerabilities: SecurityVulnerability[];
  complianceResults: ComplianceResult[];
  recommendations: SecurityRecommendation[];
  approved: boolean;
  blockers: SecurityBlocker[];
  timestamp: Date;
  scanDuration: number;
}

export interface SecurityVulnerability {
  id: string;
  type: 'code' | 'dependency' | 'configuration' | 'infrastructure';
  severity: 'info' | 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  location: string;
  cve?: string;
  cvss?: number;
  remediation: string;
  status: 'open' | 'acknowledged' | 'fixed' | 'false-positive';
}

export interface ComplianceResult {
  framework: string;
  control: string;
  status: 'compliant' | 'non-compliant' | 'partial' | 'not-applicable';
  score: number;
  details: string;
  remediation?: string;
}

export interface SecurityRecommendation {
  id: string;
  type: 'security' | 'performance' | 'best-practice';
  priority: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  implementation: string;
  impact: string;
}

export interface SecurityBlocker {
  id: string;
  type: 'vulnerability' | 'compliance' | 'policy';
  severity: 'high' | 'critical';
  title: string;
  description: string;
  mustFix: boolean;
  deadline?: Date;
}

interface SecurityValidatorAgentProps {
  securityAgent: SecurityAgent;
  vulnerabilityScanner: VulnerabilityScanner;
  securityPolicies: SecurityPolicies;
  onValidationComplete?: (result: SecurityValidationResult) => void;
  onError?: (error: Error) => void;
}

/**
 * Agent Validateur de Sécurité pour la Sandbox Hanuman
 * Responsable de la validation sécurisée avant déploiement
 */
export const SecurityValidatorAgent: React.FC<SecurityValidatorAgentProps> = ({
  securityAgent,
  vulnerabilityScanner,
  securityPolicies,
  onValidationComplete,
  onError
}) => {
  const [validationRequests, setValidationRequests] = useState<Map<string, SecurityValidationRequest>>(new Map());
  const [validationResults, setValidationResults] = useState<Map<string, SecurityValidationResult>>(new Map());
  const [activeValidations, setActiveValidations] = useState<Set<string>>(new Set());
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialisation de l'agent
  useEffect(() => {
    initializeAgent();
  }, []);

  /**
   * Initialise l'agent validateur de sécurité
   */
  const initializeAgent = useCallback(async () => {
    try {
      console.log('🛡️ Initialisation de l\'Agent Validateur de Sécurité...');

      // Initialiser les composants de sécurité
      await Promise.all([
        securityAgent.initialize(),
        vulnerabilityScanner.initialize(),
        securityPolicies.initialize()
      ]);

      // Configurer les écouteurs d'événements
      setupEventListeners();

      setIsInitialized(true);
      console.log('✅ Agent Validateur de Sécurité initialisé');

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation de l\'agent:', error);
      onError?.(error as Error);
    }
  }, [securityAgent, vulnerabilityScanner, securityPolicies, onError]);

  /**
   * Configure les écouteurs d'événements
   */
  const setupEventListeners = useCallback(() => {
    // Écouter les demandes de validation
    securityAgent.on('validation:requested', handleValidationRequest);
    
    // Écouter les résultats de scan
    vulnerabilityScanner.on('scan:completed', handleScanCompleted);
    
    // Écouter les violations de politique
    securityPolicies.on('policy:violation', handlePolicyViolation);

  }, []);

  /**
   * Traite une demande de validation de sécurité
   */
  const handleValidationRequest = useCallback(async (request: SecurityValidationRequest) => {
    try {
      console.log(`🔍 Nouvelle demande de validation: ${request.id}`);

      // Enregistrer la demande
      setValidationRequests(prev => new Map(prev.set(request.id, request)));
      setActiveValidations(prev => new Set(prev.add(request.id)));

      // Démarrer la validation
      await startSecurityValidation(request);

    } catch (error) {
      console.error('❌ Erreur lors du traitement de la demande:', error);
      onError?.(error as Error);
    }
  }, []);

  /**
   * Démarre le processus de validation de sécurité
   */
  const startSecurityValidation = useCallback(async (request: SecurityValidationRequest) => {
    const startTime = Date.now();

    try {
      // Créer le résultat initial
      const result: SecurityValidationResult = {
        id: `validation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        requestId: request.id,
        status: 'scanning',
        score: 0,
        vulnerabilities: [],
        complianceResults: [],
        recommendations: [],
        approved: false,
        blockers: [],
        timestamp: new Date(),
        scanDuration: 0
      };

      setValidationResults(prev => new Map(prev.set(result.id, result)));

      // Étape 1: Scan de vulnérabilités
      console.log(`🔍 Scan de vulnérabilités pour ${request.id}...`);
      const vulnerabilities = await performVulnerabilityScan(request);
      result.vulnerabilities = vulnerabilities;

      // Étape 2: Vérification de conformité
      console.log(`📋 Vérification de conformité pour ${request.id}...`);
      const complianceResults = await performComplianceCheck(request);
      result.complianceResults = complianceResults;

      // Étape 3: Validation des politiques
      console.log(`🛡️ Validation des politiques pour ${request.id}...`);
      const policyResults = await validatePolicies(request);

      // Étape 4: Génération des recommandations
      console.log(`💡 Génération des recommandations pour ${request.id}...`);
      result.recommendations = generateRecommendations(vulnerabilities, complianceResults);

      // Étape 5: Calcul du score et décision
      result.score = calculateSecurityScore(vulnerabilities, complianceResults);
      result.blockers = identifyBlockers(vulnerabilities, complianceResults, policyResults);
      result.approved = result.blockers.length === 0 && result.score >= securityPolicies.getMinimumScore();
      result.status = 'completed';
      result.scanDuration = Date.now() - startTime;

      // Mettre à jour le résultat
      setValidationResults(prev => new Map(prev.set(result.id, result)));
      setActiveValidations(prev => {
        const newSet = new Set(prev);
        newSet.delete(request.id);
        return newSet;
      });

      console.log(`✅ Validation terminée pour ${request.id} - Score: ${result.score}% - Approuvé: ${result.approved}`);

      // Notifier la completion
      onValidationComplete?.(result);

    } catch (error) {
      console.error(`❌ Erreur lors de la validation ${request.id}:`, error);
      
      // Marquer comme échoué
      setValidationResults(prev => {
        const updated = new Map(prev);
        const result = updated.get(request.id);
        if (result) {
          result.status = 'failed';
          result.scanDuration = Date.now() - startTime;
        }
        return updated;
      });

      setActiveValidations(prev => {
        const newSet = new Set(prev);
        newSet.delete(request.id);
        return newSet;
      });

      onError?.(error as Error);
    }
  }, [securityPolicies, onValidationComplete, onError]);

  /**
   * Effectue un scan de vulnérabilités
   */
  const performVulnerabilityScan = useCallback(async (request: SecurityValidationRequest): Promise<SecurityVulnerability[]> => {
    const vulnerabilities: SecurityVulnerability[] = [];

    try {
      // Scan selon le type de cible
      switch (request.type) {
        case 'code':
          if (request.target.codeRepository) {
            const codeVulns = await vulnerabilityScanner.scanCode(request.target.codeRepository);
            vulnerabilities.push(...codeVulns);
          }
          break;

        case 'container':
          if (request.target.containerId) {
            const containerVulns = await vulnerabilityScanner.scanContainer(request.target.containerId);
            vulnerabilities.push(...containerVulns);
          }
          break;

        case 'environment':
          if (request.target.environmentId) {
            const envVulns = await vulnerabilityScanner.scanEnvironment(request.target.environmentId);
            vulnerabilities.push(...envVulns);
          }
          break;

        case 'deployment':
          // Scan complet pour déploiement
          const deployVulns = await vulnerabilityScanner.scanDeployment(request.target);
          vulnerabilities.push(...deployVulns);
          break;
      }

      return vulnerabilities;

    } catch (error) {
      console.error('❌ Erreur lors du scan de vulnérabilités:', error);
      throw error;
    }
  }, [vulnerabilityScanner]);

  /**
   * Effectue une vérification de conformité
   */
  const performComplianceCheck = useCallback(async (request: SecurityValidationRequest): Promise<ComplianceResult[]> => {
    try {
      // Utiliser l'agent de sécurité pour la vérification de conformité
      const complianceResults = await securityAgent.checkCompliance({
        target: request.target,
        frameworks: ['owasp', 'cis', 'nist'],
        type: request.type
      });

      return complianceResults;

    } catch (error) {
      console.error('❌ Erreur lors de la vérification de conformité:', error);
      throw error;
    }
  }, [securityAgent]);

  /**
   * Valide les politiques de sécurité
   */
  const validatePolicies = useCallback(async (request: SecurityValidationRequest) => {
    try {
      return await securityPolicies.validateRequest(request);
    } catch (error) {
      console.error('❌ Erreur lors de la validation des politiques:', error);
      throw error;
    }
  }, [securityPolicies]);

  /**
   * Génère des recommandations de sécurité
   */
  const generateRecommendations = useCallback((
    vulnerabilities: SecurityVulnerability[],
    complianceResults: ComplianceResult[]
  ): SecurityRecommendation[] => {
    const recommendations: SecurityRecommendation[] = [];

    // Recommandations basées sur les vulnérabilités
    vulnerabilities.forEach(vuln => {
      if (vuln.severity === 'high' || vuln.severity === 'critical') {
        recommendations.push({
          id: `rec_vuln_${vuln.id}`,
          type: 'security',
          priority: vuln.severity === 'critical' ? 'high' : 'medium',
          title: `Corriger la vulnérabilité: ${vuln.title}`,
          description: vuln.description,
          implementation: vuln.remediation,
          impact: `Réduction du risque de sécurité ${vuln.severity}`
        });
      }
    });

    // Recommandations basées sur la conformité
    complianceResults.forEach(result => {
      if (result.status === 'non-compliant' && result.remediation) {
        recommendations.push({
          id: `rec_comp_${result.framework}_${result.control}`,
          type: 'security',
          priority: 'medium',
          title: `Améliorer la conformité ${result.framework}`,
          description: `Contrôle ${result.control} non conforme`,
          implementation: result.remediation,
          impact: 'Amélioration de la conformité réglementaire'
        });
      }
    });

    return recommendations;
  }, []);

  /**
   * Calcule le score de sécurité
   */
  const calculateSecurityScore = useCallback((
    vulnerabilities: SecurityVulnerability[],
    complianceResults: ComplianceResult[]
  ): number => {
    let score = 100;

    // Pénalités pour les vulnérabilités
    vulnerabilities.forEach(vuln => {
      switch (vuln.severity) {
        case 'critical': score -= 25; break;
        case 'high': score -= 15; break;
        case 'medium': score -= 8; break;
        case 'low': score -= 3; break;
        case 'info': score -= 1; break;
      }
    });

    // Pénalités pour la non-conformité
    complianceResults.forEach(result => {
      if (result.status === 'non-compliant') {
        score -= 10;
      } else if (result.status === 'partial') {
        score -= 5;
      }
    });

    return Math.max(0, Math.min(100, score));
  }, []);

  /**
   * Identifie les bloqueurs de sécurité
   */
  const identifyBlockers = useCallback((
    vulnerabilities: SecurityVulnerability[],
    complianceResults: ComplianceResult[],
    policyResults: any
  ): SecurityBlocker[] => {
    const blockers: SecurityBlocker[] = [];

    // Vulnérabilités critiques sont des bloqueurs
    vulnerabilities.forEach(vuln => {
      if (vuln.severity === 'critical') {
        blockers.push({
          id: `blocker_vuln_${vuln.id}`,
          type: 'vulnerability',
          severity: 'critical',
          title: `Vulnérabilité critique: ${vuln.title}`,
          description: vuln.description,
          mustFix: true,
          deadline: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24h
        });
      }
    });

    // Violations de politique critiques
    if (policyResults?.violations) {
      policyResults.violations.forEach((violation: any) => {
        if (violation.severity === 'critical') {
          blockers.push({
            id: `blocker_policy_${violation.id}`,
            type: 'policy',
            severity: 'critical',
            title: `Violation de politique: ${violation.policy}`,
            description: violation.description,
            mustFix: true
          });
        }
      });
    }

    return blockers;
  }, []);

  /**
   * Traite la completion d'un scan
   */
  const handleScanCompleted = useCallback((scanResult: any) => {
    console.log(`📊 Scan terminé: ${scanResult.id}`);
    // Traitement des résultats de scan
  }, []);

  /**
   * Traite une violation de politique
   */
  const handlePolicyViolation = useCallback((violation: any) => {
    console.log(`⚠️ Violation de politique détectée: ${violation.policy}`);
    // Traitement des violations
  }, []);

  // Interface de l'agent (sera étendue dans un composant séparé)
  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initialisation de l'Agent Validateur de Sécurité...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="security-validator-agent">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-800 flex items-center">
            🛡️ Agent Validateur de Sécurité
          </h2>
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="text-sm text-gray-600">Actif</span>
            </div>
            <div className="text-sm text-gray-500">
              Validations actives: {activeValidations.size}
            </div>
          </div>
        </div>

        {/* Statistiques rapides */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{validationRequests.size}</div>
            <div className="text-sm text-blue-800">Demandes totales</div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {Array.from(validationResults.values()).filter(r => r.approved).length}
            </div>
            <div className="text-sm text-green-800">Approuvées</div>
          </div>
          <div className="bg-red-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-red-600">
              {Array.from(validationResults.values()).filter(r => !r.approved && r.status === 'completed').length}
            </div>
            <div className="text-sm text-red-800">Bloquées</div>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600">{activeValidations.size}</div>
            <div className="text-sm text-yellow-800">En cours</div>
          </div>
        </div>

        {/* Message d'état */}
        <div className="text-center py-8 text-gray-500">
          <p className="text-lg">Agent Validateur de Sécurité opérationnel</p>
          <p className="text-sm">Prêt à valider les demandes de sécurité</p>
        </div>
      </div>
    </div>
  );
};

export default SecurityValidatorAgent;
