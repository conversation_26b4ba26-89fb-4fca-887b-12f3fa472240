import { EventEmitter } from 'events';
import { SandboxContainer, SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';

// Types pour la sécurité de la sandbox
export interface SecurityPolicy {
  id: string;
  name: string;
  type: 'network' | 'process' | 'file' | 'resource' | 'access';
  rules: SecurityRule[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  createdAt: Date;
  lastUpdated: Date;
}

export interface SecurityRule {
  id: string;
  condition: string;
  action: 'allow' | 'deny' | 'monitor' | 'alert';
  description: string;
  parameters: Record<string, any>;
}

export interface SecurityIncident {
  id: string;
  type: 'unauthorized_access' | 'resource_abuse' | 'network_violation' | 'process_anomaly' | 'data_breach';
  severity: 'low' | 'medium' | 'high' | 'critical';
  containerId: string;
  agentId?: string;
  organId?: string;
  description: string;
  details: Record<string, any>;
  timestamp: Date;
  status: 'open' | 'investigating' | 'resolved' | 'false_positive';
  actions: SecurityAction[];
}

export interface SecurityAction {
  id: string;
  type: 'isolate' | 'terminate' | 'monitor' | 'alert' | 'log';
  description: string;
  timestamp: Date;
  result: 'success' | 'failed' | 'pending';
}

export interface SecurityMetrics {
  totalIncidents: number;
  criticalIncidents: number;
  resolvedIncidents: number;
  averageResponseTime: number;
  securityScore: number;
  lastScan: Date;
  threatsBlocked: number;
  policiesActive: number;
}

export interface AccessControl {
  containerId: string;
  agentId?: string;
  organId?: string;
  permissions: {
    read: string[];
    write: string[];
    execute: string[];
    network: string[];
  };
  restrictions: {
    blockedPorts: number[];
    blockedDomains: string[];
    maxConnections: number;
    maxFileSize: number;
  };
  auditLog: AuditEntry[];
}

export interface AuditEntry {
  id: string;
  timestamp: Date;
  action: string;
  resource: string;
  result: 'allowed' | 'denied';
  reason?: string;
}

/**
 * Système de Sécurité pour la Sandbox Hanuman
 * Gère l'isolation, le contrôle d'accès et la détection d'anomalies
 */
export class SandboxSecurity extends EventEmitter {
  private infrastructure: SandboxInfrastructure;
  private policies: Map<string, SecurityPolicy> = new Map();
  private incidents: Map<string, SecurityIncident> = new Map();
  private accessControls: Map<string, AccessControl> = new Map();
  private metrics: SecurityMetrics;
  private isMonitoring = false;
  private scanInterval: NodeJS.Timeout | null = null;

  constructor(infrastructure: SandboxInfrastructure) {
    super();
    this.infrastructure = infrastructure;
    this.metrics = this.initializeMetrics();
    this.initializeDefaultPolicies();
    this.startSecurityMonitoring();
  }

  /**
   * Initialise les métriques de sécurité
   */
  private initializeMetrics(): SecurityMetrics {
    return {
      totalIncidents: 0,
      criticalIncidents: 0,
      resolvedIncidents: 0,
      averageResponseTime: 0,
      securityScore: 100,
      lastScan: new Date(),
      threatsBlocked: 0,
      policiesActive: 0
    };
  }

  /**
   * Initialise les politiques de sécurité par défaut
   */
  private initializeDefaultPolicies(): void {
    const defaultPolicies: Omit<SecurityPolicy, 'id' | 'createdAt' | 'lastUpdated'>[] = [
      {
        name: 'Isolation Réseau Complète',
        type: 'network',
        rules: [
          {
            id: 'net-001',
            condition: 'outbound_connection',
            action: 'deny',
            description: 'Bloquer toutes les connexions sortantes non autorisées',
            parameters: { allowedPorts: [80, 443, 22], allowedDomains: ['hanuman-services'] }
          }
        ],
        severity: 'high',
        enabled: true
      },
      {
        name: 'Contrôle des Processus',
        type: 'process',
        rules: [
          {
            id: 'proc-001',
            condition: 'process_spawn',
            action: 'monitor',
            description: 'Surveiller la création de nouveaux processus',
            parameters: { maxProcesses: 50, blockedCommands: ['rm -rf', 'dd', 'mkfs'] }
          }
        ],
        severity: 'medium',
        enabled: true
      },
      {
        name: 'Protection des Fichiers',
        type: 'file',
        rules: [
          {
            id: 'file-001',
            condition: 'file_access',
            action: 'monitor',
            description: 'Surveiller l\'accès aux fichiers sensibles',
            parameters: { protectedPaths: ['/etc', '/sys', '/proc'], maxFileSize: 100000000 }
          }
        ],
        severity: 'high',
        enabled: true
      },
      {
        name: 'Limites de Ressources',
        type: 'resource',
        rules: [
          {
            id: 'res-001',
            condition: 'resource_usage',
            action: 'alert',
            description: 'Alerter en cas d\'utilisation excessive des ressources',
            parameters: { cpuThreshold: 90, memoryThreshold: 95, diskThreshold: 85 }
          }
        ],
        severity: 'medium',
        enabled: true
      },
      {
        name: 'Contrôle d\'Accès Strict',
        type: 'access',
        rules: [
          {
            id: 'acc-001',
            condition: 'unauthorized_access',
            action: 'deny',
            description: 'Bloquer les accès non autorisés',
            parameters: { requireAuthentication: true, maxFailedAttempts: 3 }
          }
        ],
        severity: 'critical',
        enabled: true
      }
    ];

    defaultPolicies.forEach(policyData => {
      const policy: SecurityPolicy = {
        ...policyData,
        id: `policy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date(),
        lastUpdated: new Date()
      };
      this.policies.set(policy.id, policy);
    });

    this.metrics.policiesActive = this.policies.size;
    console.log(`🛡️ ${this.policies.size} politiques de sécurité initialisées`);
  }

  /**
   * Démarre le monitoring de sécurité
   */
  private startSecurityMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;

    // Scan de sécurité toutes les 30 secondes
    this.scanInterval = setInterval(() => {
      this.performSecurityScan();
    }, 30000);

    // Écouter les événements de l'infrastructure
    this.infrastructure.on('container:created', this.onContainerCreated.bind(this));
    this.infrastructure.on('container:started', this.onContainerStarted.bind(this));
    this.infrastructure.on('container:destroyed', this.onContainerDestroyed.bind(this));

    console.log('🔍 Monitoring de sécurité démarré');
  }

  /**
   * Arrête le monitoring de sécurité
   */
  stopSecurityMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;

    if (this.scanInterval) {
      clearInterval(this.scanInterval);
      this.scanInterval = null;
    }

    this.infrastructure.off('container:created', this.onContainerCreated.bind(this));
    this.infrastructure.off('container:started', this.onContainerStarted.bind(this));
    this.infrastructure.off('container:destroyed', this.onContainerDestroyed.bind(this));

    console.log('🛑 Monitoring de sécurité arrêté');
  }

  /**
   * Gestionnaire pour la création de conteneur
   */
  private onContainerCreated(container: SandboxContainer): void {
    this.setupContainerSecurity(container);
  }

  /**
   * Gestionnaire pour le démarrage de conteneur
   */
  private onContainerStarted(container: SandboxContainer): void {
    this.validateContainerSecurity(container);
  }

  /**
   * Gestionnaire pour la destruction de conteneur
   */
  private onContainerDestroyed(container: SandboxContainer): void {
    this.cleanupContainerSecurity(container.id);
  }

  /**
   * Configure la sécurité pour un nouveau conteneur
   */
  private setupContainerSecurity(container: SandboxContainer): void {
    const accessControl: AccessControl = {
      containerId: container.id,
      agentId: container.agentId,
      organId: container.organId,
      permissions: {
        read: this.getDefaultReadPermissions(container.isolation.securityLevel),
        write: this.getDefaultWritePermissions(container.isolation.securityLevel),
        execute: this.getDefaultExecutePermissions(container.isolation.securityLevel),
        network: this.getDefaultNetworkPermissions(container.isolation.securityLevel)
      },
      restrictions: {
        blockedPorts: this.getBlockedPorts(container.isolation.securityLevel),
        blockedDomains: this.getBlockedDomains(container.isolation.securityLevel),
        maxConnections: this.getMaxConnections(container.isolation.securityLevel),
        maxFileSize: this.getMaxFileSize(container.isolation.securityLevel)
      },
      auditLog: []
    };

    this.accessControls.set(container.id, accessControl);
    this.emit('security:container-secured', { container, accessControl });

    console.log(`🔒 Sécurité configurée pour le conteneur ${container.name}`);
  }

  /**
   * Valide la sécurité d'un conteneur
   */
  private async validateContainerSecurity(container: SandboxContainer): Promise<void> {
    const violations: string[] = [];

    // Vérifier l'isolation réseau
    if (!container.isolation.networkIsolated) {
      violations.push('Isolation réseau non activée');
    }

    // Vérifier le chiffrement du stockage pour les niveaux élevés
    if ((container.isolation.securityLevel === 'high' || container.isolation.securityLevel === 'maximum') 
        && !container.isolation.storageEncrypted) {
      violations.push('Stockage non chiffré pour niveau de sécurité élevé');
    }

    // Vérifier l'isolation des processus
    if (!container.isolation.processIsolated) {
      violations.push('Isolation des processus non activée');
    }

    if (violations.length > 0) {
      await this.createSecurityIncident({
        type: 'unauthorized_access',
        severity: 'high',
        containerId: container.id,
        agentId: container.agentId,
        organId: container.organId,
        description: 'Violations de sécurité détectées lors de la validation',
        details: { violations }
      });
    }
  }

  /**
   * Effectue un scan de sécurité complet
   */
  private async performSecurityScan(): Promise<void> {
    this.metrics.lastScan = new Date();
    const containers = this.infrastructure.getContainers();

    for (const container of containers) {
      if (container.status === 'running') {
        await this.scanContainer(container);
      }
    }

    // Calculer le score de sécurité
    this.calculateSecurityScore();
    this.emit('security:scan-completed', this.metrics);
  }

  /**
   * Scanne un conteneur spécifique
   */
  private async scanContainer(container: SandboxContainer): Promise<void> {
    const accessControl = this.accessControls.get(container.id);
    if (!accessControl) return;

    // Simuler la détection d'anomalies
    const anomalies = this.detectAnomalies(container, accessControl);

    for (const anomaly of anomalies) {
      await this.handleAnomaly(container, anomaly);
    }
  }

  /**
   * Détecte les anomalies dans un conteneur
   */
  private detectAnomalies(container: SandboxContainer, accessControl: AccessControl): any[] {
    const anomalies: any[] = [];

    // Simuler la détection d'anomalies basée sur l'activité
    const inactiveTime = Date.now() - container.lastActivity.getTime();
    
    // Conteneur inactif depuis plus de 2 heures
    if (inactiveTime > 7200000) {
      anomalies.push({
        type: 'process_anomaly',
        severity: 'low',
        description: 'Conteneur inactif depuis plus de 2 heures',
        details: { inactiveTime }
      });
    }

    // Utilisation excessive des ressources (simulation)
    if (container.resources.cpu > 3) {
      anomalies.push({
        type: 'resource_abuse',
        severity: 'medium',
        description: 'Utilisation CPU élevée détectée',
        details: { cpuUsage: container.resources.cpu }
      });
    }

    return anomalies;
  }

  /**
   * Gère une anomalie détectée
   */
  private async handleAnomaly(container: SandboxContainer, anomaly: any): Promise<void> {
    await this.createSecurityIncident({
      type: anomaly.type,
      severity: anomaly.severity,
      containerId: container.id,
      agentId: container.agentId,
      organId: container.organId,
      description: anomaly.description,
      details: anomaly.details
    });
  }

  /**
   * Crée un incident de sécurité
   */
  private async createSecurityIncident(incidentData: Omit<SecurityIncident, 'id' | 'timestamp' | 'status' | 'actions'>): Promise<SecurityIncident> {
    const incident: SecurityIncident = {
      ...incidentData,
      id: `incident_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      status: 'open',
      actions: []
    };

    this.incidents.set(incident.id, incident);
    this.metrics.totalIncidents++;

    if (incident.severity === 'critical') {
      this.metrics.criticalIncidents++;
    }

    // Déclencher des actions automatiques selon la gravité
    await this.triggerAutomaticActions(incident);

    this.emit('security:incident-created', incident);
    console.log(`🚨 Incident de sécurité créé: ${incident.description}`);

    return incident;
  }

  /**
   * Déclenche des actions automatiques pour un incident
   */
  private async triggerAutomaticActions(incident: SecurityIncident): Promise<void> {
    const actions: SecurityAction[] = [];

    switch (incident.severity) {
      case 'critical':
        // Isoler immédiatement le conteneur
        actions.push(await this.isolateContainer(incident.containerId));
        actions.push(await this.alertAdministrators(incident));
        break;

      case 'high':
        // Surveiller de près et alerter
        actions.push(await this.enhanceMonitoring(incident.containerId));
        actions.push(await this.alertAdministrators(incident));
        break;

      case 'medium':
        // Enregistrer et surveiller
        actions.push(await this.logIncident(incident));
        actions.push(await this.enhanceMonitoring(incident.containerId));
        break;

      case 'low':
        // Enregistrer seulement
        actions.push(await this.logIncident(incident));
        break;
    }

    incident.actions = actions;
    this.incidents.set(incident.id, incident);
  }

  /**
   * Isole un conteneur
   */
  private async isolateContainer(containerId: string): Promise<SecurityAction> {
    const action: SecurityAction = {
      id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'isolate',
      description: `Isolation du conteneur ${containerId}`,
      timestamp: new Date(),
      result: 'success'
    };

    // Ici, on implémenterait l'isolation réelle
    console.log(`🔒 Conteneur ${containerId} isolé`);
    this.metrics.threatsBlocked++;

    return action;
  }

  /**
   * Alerte les administrateurs
   */
  private async alertAdministrators(incident: SecurityIncident): Promise<SecurityAction> {
    const action: SecurityAction = {
      id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'alert',
      description: 'Alerte envoyée aux administrateurs',
      timestamp: new Date(),
      result: 'success'
    };

    // Ici, on implémenterait l'envoi d'alertes réelles
    console.log(`📧 Alerte envoyée pour l'incident ${incident.id}`);

    return action;
  }

  /**
   * Améliore le monitoring d'un conteneur
   */
  private async enhanceMonitoring(containerId: string): Promise<SecurityAction> {
    const action: SecurityAction = {
      id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'monitor',
      description: `Monitoring renforcé pour le conteneur ${containerId}`,
      timestamp: new Date(),
      result: 'success'
    };

    console.log(`👁️ Monitoring renforcé pour le conteneur ${containerId}`);

    return action;
  }

  /**
   * Enregistre un incident
   */
  private async logIncident(incident: SecurityIncident): Promise<SecurityAction> {
    const action: SecurityAction = {
      id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'log',
      description: 'Incident enregistré dans les logs',
      timestamp: new Date(),
      result: 'success'
    };

    console.log(`📝 Incident ${incident.id} enregistré`);

    return action;
  }

  /**
   * Calcule le score de sécurité global
   */
  private calculateSecurityScore(): void {
    const totalContainers = this.infrastructure.getContainers().length;
    if (totalContainers === 0) {
      this.metrics.securityScore = 100;
      return;
    }

    const openIncidents = Array.from(this.incidents.values())
      .filter(i => i.status === 'open').length;

    const criticalWeight = this.metrics.criticalIncidents * 20;
    const totalWeight = this.metrics.totalIncidents * 5;

    this.metrics.securityScore = Math.max(0, 100 - criticalWeight - totalWeight);
  }

  /**
   * Nettoie la sécurité d'un conteneur détruit
   */
  private cleanupContainerSecurity(containerId: string): void {
    this.accessControls.delete(containerId);
    
    // Résoudre les incidents ouverts pour ce conteneur
    for (const [id, incident] of this.incidents) {
      if (incident.containerId === containerId && incident.status === 'open') {
        incident.status = 'resolved';
        this.metrics.resolvedIncidents++;
      }
    }

    console.log(`🧹 Sécurité nettoyée pour le conteneur ${containerId}`);
  }

  // Méthodes utilitaires pour les permissions par défaut
  private getDefaultReadPermissions(securityLevel: string): string[] {
    const base = ['/tmp', '/var/tmp'];
    if (securityLevel === 'low') return [...base, '/home', '/opt'];
    if (securityLevel === 'medium') return [...base, '/home'];
    return base;
  }

  private getDefaultWritePermissions(securityLevel: string): string[] {
    const base = ['/tmp'];
    if (securityLevel === 'low') return [...base, '/home', '/var/tmp'];
    if (securityLevel === 'medium') return [...base, '/var/tmp'];
    return base;
  }

  private getDefaultExecutePermissions(securityLevel: string): string[] {
    const base = ['/bin', '/usr/bin'];
    if (securityLevel === 'low') return [...base, '/usr/local/bin', '/opt/bin'];
    if (securityLevel === 'medium') return [...base, '/usr/local/bin'];
    return base;
  }

  private getDefaultNetworkPermissions(securityLevel: string): string[] {
    if (securityLevel === 'low') return ['*'];
    if (securityLevel === 'medium') return ['hanuman-services', 'localhost'];
    return ['localhost'];
  }

  private getBlockedPorts(securityLevel: string): number[] {
    const base = [22, 23, 135, 139, 445];
    if (securityLevel === 'maximum') return [...base, 80, 443, 8080];
    if (securityLevel === 'high') return [...base, 8080];
    return base;
  }

  private getBlockedDomains(securityLevel: string): string[] {
    const base = ['malware.com', 'phishing.net'];
    if (securityLevel === 'maximum') return [...base, '*'];
    if (securityLevel === 'high') return [...base, 'social-media.com'];
    return base;
  }

  private getMaxConnections(securityLevel: string): number {
    if (securityLevel === 'maximum') return 5;
    if (securityLevel === 'high') return 10;
    if (securityLevel === 'medium') return 25;
    return 50;
  }

  private getMaxFileSize(securityLevel: string): number {
    if (securityLevel === 'maximum') return 10000000; // 10MB
    if (securityLevel === 'high') return 50000000; // 50MB
    if (securityLevel === 'medium') return 100000000; // 100MB
    return 500000000; // 500MB
  }

  /**
   * Obtient les métriques de sécurité
   */
  getSecurityMetrics(): SecurityMetrics {
    return { ...this.metrics };
  }

  /**
   * Obtient tous les incidents
   */
  getIncidents(): SecurityIncident[] {
    return Array.from(this.incidents.values());
  }

  /**
   * Obtient les incidents ouverts
   */
  getOpenIncidents(): SecurityIncident[] {
    return Array.from(this.incidents.values())
      .filter(i => i.status === 'open');
  }

  /**
   * Obtient les politiques de sécurité
   */
  getPolicies(): SecurityPolicy[] {
    return Array.from(this.policies.values());
  }

  /**
   * Résout un incident
   */
  resolveIncident(incidentId: string): boolean {
    const incident = this.incidents.get(incidentId);
    if (!incident) return false;

    incident.status = 'resolved';
    this.metrics.resolvedIncidents++;
    this.emit('security:incident-resolved', incident);

    return true;
  }
}
