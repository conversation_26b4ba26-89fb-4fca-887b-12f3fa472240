import React, { useState, useEffect, useCallback } from 'react';
import { QAValidatorAgent, QAValidationWorkflow, QATestCase, QAIssue } from '../qa/qa_validator_agent';
import { UITestingFramework, UITestCase, UITestResult } from '../qa/ui_testing_framework';
import { PerformanceValidator, PerformanceTest, PerformanceTestResult } from '../qa/performance_validator';
import { QAReportingSystem, QAReport } from '../qa/qa_reporting_system';

/**
 * Dashboard Principal du Centre de Validation QA
 * Interface unifiée pour tous les aspects de la validation qualité
 */

interface QAValidationDashboardProps {
  qaAgent: QAValidatorAgent;
  uiFramework: UITestingFramework;
  performanceValidator: PerformanceValidator;
  reportingSystem: QAReportingSystem;
  projectId: string;
}

interface QADashboardState {
  activeTab: 'dashboard' | 'functional' | 'ui' | 'performance' | 'reports' | 'alerts';
  validations: QAValidationWorkflow[];
  uiTests: UITestCase[];
  performanceTests: PerformanceTest[];
  reports: QAReport[];
  alerts: QAAlert[];
  isLoading: boolean;
  selectedValidation: QAValidationWorkflow | null;
  selectedUITest: UITestCase | null;
  selectedPerformanceTest: PerformanceTest | null;
  autoRefresh: boolean;
  refreshInterval: number;
  filters: QADashboardFilters;
}

interface QAAlert {
  id: string;
  type: 'error' | 'warning' | 'info' | 'success';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  source: 'functional' | 'ui' | 'performance' | 'system';
  timestamp: Date;
  acknowledged: boolean;
  resolved: boolean;
}

interface QADashboardFilters {
  testType: string[];
  status: string[];
  priority: string[];
  dateRange: { start: Date; end: Date };
}

export const QAValidationDashboard: React.FC<QAValidationDashboardProps> = ({
  qaAgent,
  uiFramework,
  performanceValidator,
  reportingSystem,
  projectId
}) => {
  const [state, setState] = useState<QADashboardState>({
    activeTab: 'dashboard',
    validations: [],
    uiTests: [],
    performanceTests: [],
    reports: [],
    alerts: [],
    isLoading: false,
    selectedValidation: null,
    selectedUITest: null,
    selectedPerformanceTest: null,
    autoRefresh: true,
    refreshInterval: 30000,
    filters: {
      testType: [],
      status: [],
      priority: [],
      dateRange: {
        start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        end: new Date()
      }
    }
  });

  /**
   * Charge les données du dashboard
   */
  const loadDashboardData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      // Charger les validations
      const validations = qaAgent.getWorkflows();
      
      // Charger les tests UI
      const uiTests = uiFramework.getTestCases();
      
      // Charger les tests de performance
      const performanceTests = performanceValidator.getTests();
      
      // Charger les rapports
      const reports = reportingSystem.getReports();
      
      // Générer des alertes simulées
      const alerts = generateMockAlerts();

      setState(prev => ({
        ...prev,
        validations,
        uiTests,
        performanceTests,
        reports,
        alerts,
        isLoading: false
      }));

    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [qaAgent, uiFramework, performanceValidator, reportingSystem]);

  /**
   * Génère des alertes simulées
   */
  const generateMockAlerts = (): QAAlert[] => {
    return [
      {
        id: 'alert_1',
        type: 'warning',
        severity: 'medium',
        title: 'Performance dégradée',
        message: 'Le temps de réponse moyen a augmenté de 15% cette semaine',
        source: 'performance',
        timestamp: new Date(Date.now() - 3600000),
        acknowledged: false,
        resolved: false
      },
      {
        id: 'alert_2',
        type: 'error',
        severity: 'high',
        title: 'Tests UI échoués',
        message: '3 tests d\'accessibilité ont échoué lors de la dernière validation',
        source: 'ui',
        timestamp: new Date(Date.now() - 7200000),
        acknowledged: true,
        resolved: false
      },
      {
        id: 'alert_3',
        type: 'info',
        severity: 'low',
        title: 'Rapport généré',
        message: 'Le rapport QA hebdomadaire a été généré avec succès',
        source: 'system',
        timestamp: new Date(Date.now() - 1800000),
        acknowledged: true,
        resolved: true
      }
    ];
  };

  /**
   * Lance une validation QA complète
   */
  const startFullValidation = async () => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const validation = await qaAgent.startValidation(projectId, {
        testTypes: ['functional', 'ui', 'accessibility', 'performance'],
        environment: 'test'
      });

      setState(prev => ({
        ...prev,
        validations: [validation, ...prev.validations],
        selectedValidation: validation,
        isLoading: false
      }));

      // Basculer vers l'onglet des tests fonctionnels
      setState(prev => ({ ...prev, activeTab: 'functional' }));

    } catch (error) {
      console.error('Erreur lors du lancement de la validation:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Lance un test UI spécifique
   */
  const runUITest = async (testId: string) => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const result = await uiFramework.executeUITest(testId);
      
      // Mettre à jour le test dans la liste
      setState(prev => ({
        ...prev,
        uiTests: prev.uiTests.map(test => 
          test.id === testId ? { ...test, result, status: result.status === 'failed' ? 'failed' : 'passed' } : test
        ),
        isLoading: false
      }));

    } catch (error) {
      console.error('Erreur lors de l\'exécution du test UI:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Lance un test de performance
   */
  const runPerformanceTest = async (testId: string) => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const result = await performanceValidator.runPerformanceTest(testId);
      
      // Mettre à jour le test dans la liste
      setState(prev => ({
        ...prev,
        performanceTests: prev.performanceTests.map(test => 
          test.id === testId ? { ...test, result, status: 'completed' } : test
        ),
        isLoading: false
      }));

    } catch (error) {
      console.error('Erreur lors de l\'exécution du test de performance:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Génère un rapport QA
   */
  const generateQAReport = async (templateId: string = 'comprehensive_report') => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const report = await reportingSystem.generateReport(templateId, projectId, {
        format: 'html',
        includeData: {
          validations: state.validations,
          uiTests: state.uiTests.map(test => test.result).filter(Boolean) as UITestResult[],
          performanceTests: state.performanceTests.map(test => test.result).filter(Boolean) as PerformanceTestResult[]
        }
      });

      setState(prev => ({
        ...prev,
        reports: [report, ...prev.reports],
        isLoading: false
      }));

      // Basculer vers l'onglet des rapports
      setState(prev => ({ ...prev, activeTab: 'reports' }));

    } catch (error) {
      console.error('Erreur lors de la génération du rapport:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  /**
   * Acquitte une alerte
   */
  const acknowledgeAlert = (alertId: string) => {
    setState(prev => ({
      ...prev,
      alerts: prev.alerts.map(alert =>
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      )
    }));
  };

  /**
   * Résout une alerte
   */
  const resolveAlert = (alertId: string) => {
    setState(prev => ({
      ...prev,
      alerts: prev.alerts.map(alert =>
        alert.id === alertId ? { ...alert, resolved: true } : alert
      )
    }));
  };

  // Effet pour le chargement initial et le rafraîchissement automatique
  useEffect(() => {
    loadDashboardData();

    if (state.autoRefresh) {
      const interval = setInterval(loadDashboardData, state.refreshInterval);
      return () => clearInterval(interval);
    }
  }, [loadDashboardData, state.autoRefresh, state.refreshInterval]);

  // Calculer les statistiques du dashboard
  const dashboardStats = {
    totalValidations: state.validations.length,
    completedValidations: state.validations.filter(v => v.status === 'completed').length,
    failedValidations: state.validations.filter(v => v.status === 'failed').length,
    averageScore: state.validations.length > 0 
      ? Math.round(state.validations.reduce((sum, v) => sum + (v.result?.overallScore || 0), 0) / state.validations.length)
      : 0,
    totalUITests: state.uiTests.length,
    passedUITests: state.uiTests.filter(t => t.status === 'passed').length,
    totalPerformanceTests: state.performanceTests.length,
    completedPerformanceTests: state.performanceTests.filter(t => t.status === 'completed').length,
    totalReports: state.reports.length,
    activeAlerts: state.alerts.filter(a => !a.resolved).length,
    criticalAlerts: state.alerts.filter(a => a.severity === 'critical' && !a.resolved).length
  };

  return (
    <div className="qa-validation-dashboard">
      {/* En-tête du dashboard */}
      <div className="dashboard-header">
        <div className="header-content">
          <h1>🎯 Centre de Validation QA</h1>
          <p>Validation qualité complète pour le projet {projectId}</p>
        </div>
        
        <div className="header-actions">
          <button
            className="action-btn primary"
            onClick={startFullValidation}
            disabled={state.isLoading}
          >
            🚀 Validation Complète
          </button>
          <button
            className="action-btn secondary"
            onClick={() => generateQAReport()}
            disabled={state.isLoading}
          >
            📊 Générer Rapport
          </button>
          <button
            className="action-btn secondary"
            onClick={loadDashboardData}
            disabled={state.isLoading}
          >
            🔄 Actualiser
          </button>
        </div>
      </div>

      {/* Navigation par onglets */}
      <div className="dashboard-tabs">
        {[
          { id: 'dashboard', label: '📊 Dashboard', count: null },
          { id: 'functional', label: '🧪 Tests Fonctionnels', count: dashboardStats.totalValidations },
          { id: 'ui', label: '🎨 Tests UI', count: dashboardStats.totalUITests },
          { id: 'performance', label: '⚡ Performance', count: dashboardStats.totalPerformanceTests },
          { id: 'reports', label: '📋 Rapports', count: dashboardStats.totalReports },
          { id: 'alerts', label: '🚨 Alertes', count: dashboardStats.activeAlerts }
        ].map(tab => (
          <button
            key={tab.id}
            className={`tab-btn ${state.activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setState(prev => ({ ...prev, activeTab: tab.id as any }))}
          >
            {tab.label}
            {tab.count !== null && tab.count > 0 && (
              <span className="tab-count">{tab.count}</span>
            )}
          </button>
        ))}
      </div>

      {/* Contenu des onglets */}
      <div className="dashboard-content">
        {state.activeTab === 'dashboard' && (
          <div className="dashboard-overview">
            {/* Statistiques principales */}
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-icon">🎯</div>
                <div className="stat-content">
                  <div className="stat-value">{dashboardStats.averageScore}/100</div>
                  <div className="stat-label">Score Moyen</div>
                </div>
              </div>
              
              <div className="stat-card">
                <div className="stat-icon">✅</div>
                <div className="stat-content">
                  <div className="stat-value">{dashboardStats.completedValidations}/{dashboardStats.totalValidations}</div>
                  <div className="stat-label">Validations Réussies</div>
                </div>
              </div>
              
              <div className="stat-card">
                <div className="stat-icon">🎨</div>
                <div className="stat-content">
                  <div className="stat-value">{dashboardStats.passedUITests}/{dashboardStats.totalUITests}</div>
                  <div className="stat-label">Tests UI Réussis</div>
                </div>
              </div>
              
              <div className="stat-card">
                <div className="stat-icon">⚡</div>
                <div className="stat-content">
                  <div className="stat-value">{dashboardStats.completedPerformanceTests}/{dashboardStats.totalPerformanceTests}</div>
                  <div className="stat-label">Tests Performance</div>
                </div>
              </div>
            </div>

            {/* Alertes récentes */}
            <div className="recent-alerts">
              <h3>🚨 Alertes Récentes</h3>
              <div className="alerts-list">
                {state.alerts.slice(0, 5).map(alert => (
                  <div key={alert.id} className={`alert-item ${alert.type} ${alert.severity}`}>
                    <div className="alert-content">
                      <div className="alert-title">{alert.title}</div>
                      <div className="alert-message">{alert.message}</div>
                      <div className="alert-meta">
                        {alert.source} • {alert.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                    <div className="alert-actions">
                      {!alert.acknowledged && (
                        <button
                          className="alert-btn"
                          onClick={() => acknowledgeAlert(alert.id)}
                        >
                          ✓ Acquitter
                        </button>
                      )}
                      {!alert.resolved && (
                        <button
                          className="alert-btn"
                          onClick={() => resolveAlert(alert.id)}
                        >
                          ✓ Résoudre
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Actions rapides */}
            <div className="quick-actions">
              <h3>⚡ Actions Rapides</h3>
              <div className="action-buttons">
                <button
                  className="action-btn primary"
                  onClick={startFullValidation}
                  disabled={state.isLoading}
                >
                  🧪 Validation Complète
                </button>
                <button
                  className="action-btn secondary"
                  onClick={() => runUITest(state.uiTests[0]?.id)}
                  disabled={state.isLoading || state.uiTests.length === 0}
                >
                  🎨 Test UI Rapide
                </button>
                <button
                  className="action-btn secondary"
                  onClick={() => runPerformanceTest(state.performanceTests[0]?.id)}
                  disabled={state.isLoading || state.performanceTests.length === 0}
                >
                  ⚡ Test Performance
                </button>
                <button
                  className="action-btn secondary"
                  onClick={() => generateQAReport('performance_report')}
                  disabled={state.isLoading}
                >
                  📊 Rapport Performance
                </button>
              </div>
            </div>
          </div>
        )}

        {state.activeTab === 'functional' && (
          <div className="functional-tests">
            <h3>🧪 Tests Fonctionnels</h3>
            <div className="tests-list">
              {state.validations.map(validation => (
                <div key={validation.id} className={`test-item ${validation.status}`}>
                  <div className="test-header">
                    <div className="test-name">{validation.name}</div>
                    <div className="test-status">{validation.status}</div>
                  </div>
                  <div className="test-details">
                    <div>Étapes: {validation.currentStep + 1}/{validation.steps.length}</div>
                    {validation.result && (
                      <div>Score: {validation.result.overallScore}/100</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {state.activeTab === 'ui' && (
          <div className="ui-tests">
            <h3>🎨 Tests Interface Utilisateur</h3>
            <div className="tests-list">
              {state.uiTests.map(test => (
                <div key={test.id} className={`test-item ${test.status}`}>
                  <div className="test-header">
                    <div className="test-name">{test.name}</div>
                    <div className="test-status">{test.status}</div>
                  </div>
                  <div className="test-details">
                    <div>Type: {test.type}</div>
                    <div>Priorité: {test.priority}</div>
                    {test.result && (
                      <div>Score: {test.result.score}/100</div>
                    )}
                  </div>
                  <div className="test-actions">
                    <button
                      className="test-btn"
                      onClick={() => runUITest(test.id)}
                      disabled={state.isLoading}
                    >
                      ▶️ Exécuter
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {state.activeTab === 'performance' && (
          <div className="performance-tests">
            <h3>⚡ Tests de Performance</h3>
            <div className="tests-list">
              {state.performanceTests.map(test => (
                <div key={test.id} className={`test-item ${test.status}`}>
                  <div className="test-header">
                    <div className="test-name">{test.name}</div>
                    <div className="test-status">{test.status}</div>
                  </div>
                  <div className="test-details">
                    <div>Type: {test.type}</div>
                    <div>Concurrence: {test.config.concurrency}</div>
                    {test.result && (
                      <div>Score: {test.result.metrics.scores.overall}/100</div>
                    )}
                  </div>
                  <div className="test-actions">
                    <button
                      className="test-btn"
                      onClick={() => runPerformanceTest(test.id)}
                      disabled={state.isLoading}
                    >
                      ▶️ Exécuter
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {state.activeTab === 'reports' && (
          <div className="reports">
            <h3>📋 Rapports QA</h3>
            <div className="reports-list">
              {state.reports.map(report => (
                <div key={report.id} className={`report-item ${report.status}`}>
                  <div className="report-header">
                    <div className="report-name">{report.name}</div>
                    <div className="report-status">{report.status}</div>
                  </div>
                  <div className="report-details">
                    <div>Type: {report.type}</div>
                    <div>Format: {report.format}</div>
                    <div>Score: {report.summary.overallScore}/100</div>
                  </div>
                  {report.url && (
                    <div className="report-actions">
                      <a href={report.url} target="_blank" rel="noopener noreferrer" className="report-link">
                        📄 Voir le Rapport
                      </a>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {state.activeTab === 'alerts' && (
          <div className="alerts">
            <h3>🚨 Alertes et Notifications</h3>
            <div className="alerts-list">
              {state.alerts.map(alert => (
                <div key={alert.id} className={`alert-item ${alert.type} ${alert.severity}`}>
                  <div className="alert-content">
                    <div className="alert-title">{alert.title}</div>
                    <div className="alert-message">{alert.message}</div>
                    <div className="alert-meta">
                      {alert.source} • {alert.severity} • {alert.timestamp.toLocaleString()}
                    </div>
                  </div>
                  <div className="alert-actions">
                    {!alert.acknowledged && (
                      <button
                        className="alert-btn"
                        onClick={() => acknowledgeAlert(alert.id)}
                      >
                        ✓ Acquitter
                      </button>
                    )}
                    {!alert.resolved && (
                      <button
                        className="alert-btn"
                        onClick={() => resolveAlert(alert.id)}
                      >
                        ✓ Résoudre
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Indicateur de chargement */}
      {state.isLoading && (
        <div className="loading-overlay">
          <div className="loading-spinner">
            <div className="spinner"></div>
            <div>Traitement en cours...</div>
          </div>
        </div>
      )}
    </div>
  );
};
