import React, { useState, useEffect, useCallback } from 'react';
import './deployment_pipeline_interface.css';

// Types pour l'interface
interface DeploymentPipelineInterfaceProps {
  orchestrator?: any;
  pipeline?: any;
  monitoring?: any;
  onDeploymentSubmit?: (deployment: any) => void;
  onRollbackTrigger?: (rollback: any) => void;
}

interface TabData {
  id: string;
  name: string;
  icon: string;
  component: React.ComponentType<any>;
}

/**
 * Interface Pipeline de Déploiement Hanuman
 * Dashboard complet pour la gestion des déploiements
 */
export const DeploymentPipelineInterface: React.FC<DeploymentPipelineInterfaceProps> = ({
  orchestrator,
  pipeline,
  monitoring,
  onDeploymentSubmit,
  onRollbackTrigger
}) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [deployments, setDeployments] = useState<any[]>([]);
  const [pipelines, setPipelines] = useState<any[]>([]);
  const [alerts, setAlerts] = useState<any[]>([]);
  const [metrics, setMetrics] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);

  // Onglets de l'interface
  const tabs: TabData[] = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊', component: DashboardTab },
    { id: 'deployments', name: 'Déploiements', icon: '🚀', component: DeploymentsTab },
    { id: 'pipelines', name: 'Pipelines', icon: '🔄', component: PipelinesTab },
    { id: 'monitoring', name: 'Monitoring', icon: '📈', component: MonitoringTab },
    { id: 'rollbacks', name: 'Rollbacks', icon: '↩️', component: RollbacksTab },
    { id: 'settings', name: 'Paramètres', icon: '⚙️', component: SettingsTab }
  ];

  // Chargement initial des données
  useEffect(() => {
    loadInitialData();
    
    // Mise à jour périodique
    const interval = setInterval(refreshData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadInitialData = async () => {
    setIsLoading(true);
    try {
      await Promise.all([
        loadDeployments(),
        loadPipelines(),
        loadAlerts(),
        loadMetrics()
      ]);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshData = async () => {
    try {
      await Promise.all([
        loadDeployments(),
        loadAlerts(),
        loadMetrics()
      ]);
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
    }
  };

  const loadDeployments = async () => {
    if (orchestrator) {
      const activeDeployments = orchestrator.getActiveDeployments();
      const history = orchestrator.getDeploymentHistory(10);
      setDeployments([...activeDeployments, ...history]);
    }
  };

  const loadPipelines = async () => {
    if (pipeline) {
      const activeExecutions = pipeline.getActiveExecutions();
      const history = pipeline.getExecutionHistory(undefined, 10);
      setPipelines([...activeExecutions, ...history]);
    }
  };

  const loadAlerts = async () => {
    if (monitoring) {
      const activeAlerts = monitoring.getActiveAlerts();
      setAlerts(activeAlerts);
    }
  };

  const loadMetrics = async () => {
    // Charger les métriques globales
    setMetrics({
      totalDeployments: deployments.length,
      successRate: calculateSuccessRate(),
      averageDeploymentTime: calculateAverageTime(),
      activeAlerts: alerts.length
    });
  };

  const calculateSuccessRate = () => {
    if (deployments.length === 0) return 100;
    const successful = deployments.filter(d => d.status === 'completed').length;
    return Math.round((successful / deployments.length) * 100);
  };

  const calculateAverageTime = () => {
    const completedDeployments = deployments.filter(d => d.duration);
    if (completedDeployments.length === 0) return 0;
    const total = completedDeployments.reduce((sum, d) => sum + d.duration, 0);
    return Math.round(total / completedDeployments.length / 1000 / 60); // en minutes
  };

  const handleDeploymentSubmit = (deploymentData: any) => {
    if (onDeploymentSubmit) {
      onDeploymentSubmit(deploymentData);
    }
    loadDeployments();
  };

  const handleRollbackTrigger = (rollbackData: any) => {
    if (onRollbackTrigger) {
      onRollbackTrigger(rollbackData);
    }
  };

  const renderActiveTab = () => {
    const tab = tabs.find(t => t.id === activeTab);
    if (!tab) return null;

    const TabComponent = tab.component;
    return (
      <TabComponent
        deployments={deployments}
        pipelines={pipelines}
        alerts={alerts}
        metrics={metrics}
        orchestrator={orchestrator}
        pipeline={pipeline}
        monitoring={monitoring}
        onDeploymentSubmit={handleDeploymentSubmit}
        onRollbackTrigger={handleRollbackTrigger}
        onRefresh={refreshData}
      />
    );
  };

  if (isLoading) {
    return (
      <div className="deployment-pipeline-interface loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Chargement du pipeline de déploiement...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="deployment-pipeline-interface">
      <header className="interface-header">
        <div className="header-content">
          <h1>🚀 Pipeline de Déploiement Hanuman</h1>
          <div className="header-stats">
            <div className="stat-item">
              <span className="stat-value">{deployments.length}</span>
              <span className="stat-label">Déploiements</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{metrics.successRate}%</span>
              <span className="stat-label">Succès</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{alerts.length}</span>
              <span className="stat-label">Alertes</span>
            </div>
          </div>
        </div>
      </header>

      <nav className="interface-nav">
        <div className="nav-tabs">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span className="tab-icon">{tab.icon}</span>
              <span className="tab-name">{tab.name}</span>
            </button>
          ))}
        </div>
      </nav>

      <main className="interface-content">
        {renderActiveTab()}
      </main>
    </div>
  );
};

// Composants des onglets
const DashboardTab: React.FC<any> = ({ deployments, alerts, metrics, onRefresh }) => (
  <div className="dashboard-tab">
    <div className="dashboard-grid">
      <div className="dashboard-card">
        <h3>📊 Vue d'ensemble</h3>
        <div className="overview-stats">
          <div className="overview-item">
            <span className="overview-label">Déploiements actifs</span>
            <span className="overview-value">{deployments.filter((d: any) => d.status === 'running').length}</span>
          </div>
          <div className="overview-item">
            <span className="overview-label">Taux de succès</span>
            <span className="overview-value">{metrics.successRate}%</span>
          </div>
          <div className="overview-item">
            <span className="overview-label">Temps moyen</span>
            <span className="overview-value">{metrics.averageDeploymentTime}min</span>
          </div>
        </div>
      </div>

      <div className="dashboard-card">
        <h3>🚨 Alertes actives</h3>
        <div className="alerts-list">
          {alerts.slice(0, 5).map((alert: any) => (
            <div key={alert.id} className={`alert-item ${alert.severity}`}>
              <span className="alert-name">{alert.name}</span>
              <span className="alert-time">{new Date(alert.startedAt).toLocaleTimeString()}</span>
            </div>
          ))}
          {alerts.length === 0 && (
            <div className="no-alerts">✅ Aucune alerte active</div>
          )}
        </div>
      </div>

      <div className="dashboard-card">
        <h3>🚀 Déploiements récents</h3>
        <div className="deployments-list">
          {deployments.slice(0, 5).map((deployment: any) => (
            <div key={deployment.id} className={`deployment-item ${deployment.status}`}>
              <span className="deployment-name">{deployment.requestId}</span>
              <span className="deployment-status">{deployment.status}</span>
              <span className="deployment-time">
                {new Date(deployment.startedAt).toLocaleTimeString()}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

const DeploymentsTab: React.FC<any> = ({ deployments, onDeploymentSubmit, onRefresh }) => {
  const [showNewDeployment, setShowNewDeployment] = useState(false);

  return (
    <div className="deployments-tab">
      <div className="tab-header">
        <h2>🚀 Gestion des Déploiements</h2>
        <div className="tab-actions">
          <button 
            className="btn btn-primary"
            onClick={() => setShowNewDeployment(true)}
          >
            ➕ Nouveau Déploiement
          </button>
          <button className="btn btn-secondary" onClick={onRefresh}>
            🔄 Actualiser
          </button>
        </div>
      </div>

      {showNewDeployment && (
        <NewDeploymentForm
          onSubmit={(data) => {
            onDeploymentSubmit(data);
            setShowNewDeployment(false);
          }}
          onCancel={() => setShowNewDeployment(false)}
        />
      )}

      <div className="deployments-table">
        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>Composant</th>
              <th>Version</th>
              <th>Environnement</th>
              <th>Statut</th>
              <th>Progression</th>
              <th>Durée</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {deployments.map((deployment: any) => (
              <tr key={deployment.id}>
                <td>{deployment.id.slice(-8)}</td>
                <td>{deployment.requestId}</td>
                <td>{deployment.version || 'N/A'}</td>
                <td>{deployment.environment || 'N/A'}</td>
                <td>
                  <span className={`status-badge ${deployment.status}`}>
                    {deployment.status}
                  </span>
                </td>
                <td>
                  <div className="progress-bar">
                    <div 
                      className="progress-fill"
                      style={{ width: `${deployment.progress || 0}%` }}
                    ></div>
                  </div>
                </td>
                <td>
                  {deployment.duration ? 
                    `${Math.round(deployment.duration / 1000 / 60)}min` : 
                    'En cours...'
                  }
                </td>
                <td>
                  <button className="btn btn-small">👁️ Voir</button>
                  {deployment.status === 'running' && (
                    <button className="btn btn-small btn-danger">❌ Annuler</button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const PipelinesTab: React.FC<any> = ({ pipelines, onRefresh }) => (
  <div className="pipelines-tab">
    <div className="tab-header">
      <h2>🔄 Pipelines CI/CD</h2>
      <button className="btn btn-secondary" onClick={onRefresh}>
        🔄 Actualiser
      </button>
    </div>
    <div className="pipelines-content">
      <p>Gestion des pipelines CI/CD - En développement</p>
    </div>
  </div>
);

const MonitoringTab: React.FC<any> = ({ metrics, alerts, onRefresh }) => (
  <div className="monitoring-tab">
    <div className="tab-header">
      <h2>📈 Monitoring</h2>
      <button className="btn btn-secondary" onClick={onRefresh}>
        🔄 Actualiser
      </button>
    </div>
    <div className="monitoring-content">
      <p>Monitoring post-déploiement - En développement</p>
    </div>
  </div>
);

const RollbacksTab: React.FC<any> = ({ onRollbackTrigger, onRefresh }) => (
  <div className="rollbacks-tab">
    <div className="tab-header">
      <h2>↩️ Rollbacks</h2>
      <button className="btn btn-secondary" onClick={onRefresh}>
        🔄 Actualiser
      </button>
    </div>
    <div className="rollbacks-content">
      <p>Gestion des rollbacks - En développement</p>
    </div>
  </div>
);

const SettingsTab: React.FC<any> = () => (
  <div className="settings-tab">
    <div className="tab-header">
      <h2>⚙️ Paramètres</h2>
    </div>
    <div className="settings-content">
      <p>Configuration du pipeline - En développement</p>
    </div>
  </div>
);

const NewDeploymentForm: React.FC<{
  onSubmit: (data: any) => void;
  onCancel: () => void;
}> = ({ onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    agentId: '',
    componentName: '',
    version: '',
    environment: 'development',
    priority: 'medium',
    description: '',
    changes: '',
    rollbackPlan: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...formData,
      changes: formData.changes.split('\n').filter(c => c.trim()),
      requestedBy: 'user'
    });
  };

  return (
    <div className="new-deployment-form">
      <div className="form-header">
        <h3>➕ Nouveau Déploiement</h3>
        <button className="btn btn-small" onClick={onCancel}>❌</button>
      </div>
      
      <form onSubmit={handleSubmit}>
        <div className="form-grid">
          <div className="form-group">
            <label>Agent ID</label>
            <input
              type="text"
              value={formData.agentId}
              onChange={(e) => setFormData({...formData, agentId: e.target.value})}
              required
            />
          </div>
          
          <div className="form-group">
            <label>Composant</label>
            <input
              type="text"
              value={formData.componentName}
              onChange={(e) => setFormData({...formData, componentName: e.target.value})}
              required
            />
          </div>
          
          <div className="form-group">
            <label>Version</label>
            <input
              type="text"
              value={formData.version}
              onChange={(e) => setFormData({...formData, version: e.target.value})}
              required
            />
          </div>
          
          <div className="form-group">
            <label>Environnement</label>
            <select
              value={formData.environment}
              onChange={(e) => setFormData({...formData, environment: e.target.value})}
            >
              <option value="development">Développement</option>
              <option value="staging">Staging</option>
              <option value="production">Production</option>
            </select>
          </div>
          
          <div className="form-group">
            <label>Priorité</label>
            <select
              value={formData.priority}
              onChange={(e) => setFormData({...formData, priority: e.target.value})}
            >
              <option value="low">Basse</option>
              <option value="medium">Moyenne</option>
              <option value="high">Haute</option>
              <option value="critical">Critique</option>
            </select>
          </div>
        </div>
        
        <div className="form-group">
          <label>Description</label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData({...formData, description: e.target.value})}
            rows={3}
          />
        </div>
        
        <div className="form-group">
          <label>Changements (un par ligne)</label>
          <textarea
            value={formData.changes}
            onChange={(e) => setFormData({...formData, changes: e.target.value})}
            rows={4}
            placeholder="- Nouvelle fonctionnalité X&#10;- Correction du bug Y&#10;- Amélioration des performances"
          />
        </div>
        
        <div className="form-group">
          <label>Plan de rollback</label>
          <textarea
            value={formData.rollbackPlan}
            onChange={(e) => setFormData({...formData, rollbackPlan: e.target.value})}
            rows={3}
            placeholder="Procédure de rollback en cas de problème..."
          />
        </div>
        
        <div className="form-actions">
          <button type="submit" className="btn btn-primary">
            🚀 Lancer le déploiement
          </button>
          <button type="button" className="btn btn-secondary" onClick={onCancel}>
            Annuler
          </button>
        </div>
      </form>
    </div>
  );
};

export default DeploymentPipelineInterface;
