/* Interface Pipeline de Déploiement <PERSON> */
.deployment-pipeline-interface {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
}

/* Loading State */
.deployment-pipeline-interface.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  text-align: center;
  color: white;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header */
.interface-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px 30px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.interface-header h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.8rem;
  font-weight: 700;
  color: #667eea;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-top: 4px;
}

/* Navigation */
.interface-nav {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0 30px;
}

.nav-tabs {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  gap: 2px;
}

.nav-tab {
  background: none;
  border: none;
  padding: 15px 25px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.nav-tab:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.nav-tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.tab-icon {
  font-size: 1.1rem;
}

/* Content */
.interface-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 30px;
  min-height: calc(100vh - 200px);
}

/* Dashboard Tab */
.dashboard-tab {
  animation: fadeIn 0.5s ease;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
}

.dashboard-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dashboard-card h3 {
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
}

.overview-stats {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.overview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.overview-item:last-child {
  border-bottom: none;
}

.overview-label {
  font-weight: 500;
  color: #666;
}

.overview-value {
  font-weight: 700;
  font-size: 1.2rem;
  color: #667eea;
}

/* Alerts */
.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-radius: 8px;
  font-size: 0.9rem;
}

.alert-item.critical {
  background: rgba(220, 53, 69, 0.1);
  border-left: 4px solid #dc3545;
}

.alert-item.warning {
  background: rgba(255, 193, 7, 0.1);
  border-left: 4px solid #ffc107;
}

.alert-item.info {
  background: rgba(13, 202, 240, 0.1);
  border-left: 4px solid #0dcaf0;
}

.no-alerts {
  text-align: center;
  color: #28a745;
  font-weight: 500;
  padding: 20px;
}

/* Deployments */
.deployments-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.deployment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.05);
  font-size: 0.9rem;
}

.deployment-item.completed {
  border-left: 4px solid #28a745;
}

.deployment-item.running {
  border-left: 4px solid #007bff;
}

.deployment-item.failed {
  border-left: 4px solid #dc3545;
}

/* Tab Headers */
.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.tab-header h2 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: white;
}

.tab-actions {
  display: flex;
  gap: 15px;
}

/* Buttons */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  background: white;
  transform: translateY(-1px);
}

.btn-small {
  padding: 6px 12px;
  font-size: 0.8rem;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

/* Tables */
.deployments-table {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.deployments-table table {
  width: 100%;
  border-collapse: collapse;
}

.deployments-table th {
  background: rgba(102, 126, 234, 0.1);
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.deployments-table td {
  padding: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.deployments-table tr:hover {
  background: rgba(102, 126, 234, 0.05);
}

/* Status Badges */
.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.completed {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.status-badge.running {
  background: rgba(0, 123, 255, 0.2);
  color: #007bff;
}

.status-badge.failed {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.status-badge.pending {
  background: rgba(108, 117, 125, 0.2);
  color: #6c757d;
}

/* Progress Bar */
.progress-bar {
  width: 100px;
  height: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

/* Forms */
.new-deployment-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.form-header h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: #333;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .interface-content {
    padding: 20px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .nav-tabs {
    flex-wrap: wrap;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
}
