{"name": "hanuman-sandbox", "version": "1.0.0", "description": "🏗️ Environnement de développement et test sécurisé pour les agents d'Hanuman", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node start_sandbox.ts", "start": "node dist/start_sandbox.js", "test": "ts-node tests/infrastructure_tests.ts", "test:infrastructure": "ts-node -e \"import('./tests/infrastructure_tests').then(m => m.runSandboxTests())\"", "test:security": "ts-node tests/security_tests.ts", "test:qa": "ts-node tests/qa_sprint5_tests.ts", "test:deployment": "ts-node tests/deployment_sprint6_tests.ts", "test:performance": "npm run test", "test:all": "npm run test:infrastructure && npm run test:security && npm run test:qa && npm run test:deployment", "demo": "ts-node start_sandbox.ts --demo", "demo:test-only": "ts-node start_sandbox.ts --test-only", "demo:sprint6": "ts-node scripts/demo_sprint6_deployment.ts", "demo:deployment": "ts-node scripts/demo_sprint6_deployment.ts", "sprint1": "ts-node start_sprint1.ts", "sprint1:demo": "npm run sprint1", "sprint1:test": "ts-node test_sprint1.ts", "api": "ts-node api/sandbox_api_server_enhanced.ts", "orchestrator": "ts-node orchestration/ide_agent_orchestrator.ts", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "clean": "rm -rf dist", "watch": "tsc --watch", "docs": "typedoc --out docs src", "prepare": "npm run build"}, "keywords": ["hanuman", "sandbox", "development", "testing", "security", "containers", "isolation", "microservices", "ai-agents"], "author": "Hanuman Development Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "events": "^3.3.0", "express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "puppeteer": "^21.6.1", "ws": "^8.14.2", "redis": "^4.6.11", "ioredis": "^5.3.2", "natural": "^6.12.0", "compromise": "^14.10.0", "joi": "^17.11.0", "uuid": "^9.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "chalk": "^4.1.2", "dotenv": "^16.3.1", "winston": "^3.11.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "ts-node": "^10.9.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "typedoc": "^0.25.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/ws": "^8.5.10", "@types/puppeteer": "^7.0.4", "@types/natural": "^5.1.5", "@types/joi": "^17.2.3", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/jest": "^29.5.8", "jest": "^29.7.0", "ts-jest": "^29.1.1", "nodemon": "^3.0.2", "prettier": "^3.1.0", "concurrently": "^8.2.2"}, "peerDependencies": {"hanuman-orchestrator": "*"}, "files": ["dist/**/*", "README.md", "package.json"], "repository": {"type": "git", "url": "git+https://github.com/hanuman-project/sandbox.git"}, "bugs": {"url": "https://github.com/hanuman-project/sandbox/issues"}, "homepage": "https://github.com/hanuman-project/sandbox#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "config": {"sandbox": {"defaultSecurityLevel": "medium", "maxContainers": 50, "enableMonitoring": true, "enableSecurity": true}}}