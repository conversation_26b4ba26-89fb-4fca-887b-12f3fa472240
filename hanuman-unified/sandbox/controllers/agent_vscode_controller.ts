// ========================================
// HANUMAN SANDBOX ENHANCED - AGENT VSCODE CONTROLLER
// Contrôleur spécialisé pour automation VS Code par agent
// ========================================

import { Browser, Page } from 'puppeteer';
import {
  IDEAction,
  IDEActionResult,
  HanumanContext,
  VSCodeCommand,
  OrchestrationError
} from '../orchestration/types';

/**
 * Configuration pour le contrôleur VS Code
 */
interface VSCodeControllerConfig {
  headless: boolean;
  timeout: number;
  retries: number;
  viewport: { width: number; height: number };
  enableLogging: boolean;
}

/**
 * Session Puppeteer pour un agent
 */
interface PuppeteerSession {
  browser: Browser;
  page: Page;
  agentId: string;
  vscodeUrl: string;
  isReady: boolean;
  lastActivity: Date;
}

/**
 * Contrôleur spécialisé pour interaction directe avec VS Code et Roo Code
 * Utilise Puppeteer pour automation UI et API pour actions backend
 */
export class AgentVSCodeController {
  private agentId: string;
  private config: VSCodeControllerConfig;
  private session?: PuppeteerSession;
  private vscodeAPI: any; // VSCodeAPI
  private rooCodeAPI: any; // RooCodeAPI
  private isInitialized = false;

  constructor(agentId: string, config?: Partial<VSCodeControllerConfig>) {
    this.agentId = agentId;

    this.config = {
      headless: process.env.NODE_ENV === 'production',
      timeout: 30000,
      retries: 3,
      viewport: { width: 1920, height: 1080 },
      enableLogging: true,
      ...config
    };

    // TODO: Initialiser les API
    // this.vscodeAPI = new VSCodeAPI(agentId);
    // this.rooCodeAPI = new RooCodeAPI(agentId);
  }

  /**
   * Initialisation du contrôleur avec session Puppeteer
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log(`🎮 Initialisation contrôleur agent: ${this.agentId}`);

    try {
      // Créer la session Puppeteer
      await this.createPuppeteerSession();

      // Initialiser les API
      await this.initializeAPIs();

      // Configurer VS Code
      await this.configureVSCode();

      this.isInitialized = true;
      console.log(`✅ Contrôleur ${this.agentId} initialisé`);

    } catch (error) {
      console.error(`❌ Erreur initialisation contrôleur ${this.agentId}:`, error);
      throw new OrchestrationError(
        `Échec initialisation contrôleur ${this.agentId}`,
        'CONTROLLER_INIT_FAILED',
        error
      );
    }
  }

  /**
   * Exécuter une action IDE spécifique
   */
  async executeAction(action: IDEAction): Promise<IDEActionResult> {
    await this.ensureInitialized();

    const startTime = Date.now();
    const actionId = `${this.agentId}_${action.type}_${Date.now()}`;

    this.log(`⚡ Exécution action: ${action.type}`, action.params);

    try {
      let result: any;

      switch (action.type) {
        case 'open_file':
          result = await this.openFile(action.params.path, action.params.focus);
          break;

        case 'generate_code':
          result = await this.generateCode(action.params, action.context);
          break;

        case 'run_command':
          result = await this.runTerminalCommand(action.params.command, action.params.workingDir);
          break;

        case 'install_extension':
          result = await this.installExtension(action.params.extensionId);
          break;

        case 'create_project':
          result = await this.createProject(action.params, action.context);
          break;

        case 'navigate':
          result = await this.navigateToLocation(action.params);
          break;

        case 'edit':
          result = await this.editFile(action.params);
          break;

        default:
          throw new OrchestrationError(
            `Type d'action non supporté: ${action.type}`,
            'UNSUPPORTED_ACTION',
            { actionType: action.type }
          );
      }

      const duration = Date.now() - startTime;
      this.log(`✅ Action ${action.type} terminée en ${duration}ms`);

      return {
        actionId,
        success: true,
        result,
        duration,
        timestamp: new Date()
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      this.log(`❌ Erreur action ${action.type}:`, error);

      return {
        actionId,
        success: false,
        error: error.message,
        duration,
        timestamp: new Date()
      };
    }
  }

  /**
   * Ouverture de fichier avec navigation intelligente
   */
  private async openFile(filePath: string, focus: boolean = false): Promise<any> {
    try {
      this.log(`📁 Ouverture fichier: ${filePath}`);

      // Méthode 1: Via API VS Code (plus rapide)
      if (this.vscodeAPI) {
        await this.vscodeAPI.openFile(filePath);
      }

      if (focus && this.session?.page) {
        // Méthode 2: Via Puppeteer pour focus UI
        await this.ensureVSCodeReady();

        // Attendre que le fichier soit chargé
        await this.session.page.waitForSelector('.monaco-editor', { timeout: 10000 });

        // Trouver et cliquer sur l'onglet du fichier
        const fileName = filePath.split('/').pop();
        const tabSelector = `[aria-label*="${fileName}"]`;

        try {
          await this.session.page.waitForSelector(tabSelector, { timeout: 5000 });
          await this.session.page.click(tabSelector);

          // Focus sur l'éditeur
          await this.session.page.click('.monaco-editor .view-lines');

        } catch (tabError) {
          this.log(`⚠️ Impossible de focuser sur l'onglet ${fileName}, focus général`);
          await this.session.page.click('.monaco-editor');
        }
      }

      return { filePath, opened: true, focused: focus };

    } catch (error) {
      throw new OrchestrationError(
        `Erreur ouverture fichier ${filePath}`,
        'FILE_OPEN_FAILED',
        error
      );
    }
  }

  /**
   * Génération de code contextuelle avec Roo Code
   */
  private async generateCode(params: any, context?: HanumanContext): Promise<any> {
    try {
      this.log(`🤖 Génération de code:`, params);

      let generatedCode: string;

      if (params.template) {
        // Génération basée sur template Hanuman
        const hanumanContext = this.buildEnhancedHanumanContext(params.variables || {}, context);

        // TODO: Utiliser RooCodeAPI
        generatedCode = await this.generateFromTemplate(params.template, {
          ...params.variables,
          ...hanumanContext
        });

      } else if (params.prompt) {
        // Génération basée sur prompt libre
        const enhancedPrompt = this.enhancePromptWithContext(params.prompt, context);
        generatedCode = await this.generateFromPrompt(enhancedPrompt, context);

      } else {
        throw new OrchestrationError(
          'Template ou prompt requis pour génération de code',
          'MISSING_GENERATION_PARAMS'
        );
      }

      // Traitement du code généré
      generatedCode = this.postProcessGeneratedCode(generatedCode, params, context);

      // Insertion dans l'éditeur
      if (params.outputPath) {
        await this.writeCodeToFile(params.outputPath, generatedCode);
        await this.openFile(params.outputPath, true);
      } else {
        await this.insertCodeInEditor(generatedCode);
      }

      return {
        code: generatedCode,
        length: generatedCode.length,
        outputPath: params.outputPath
      };

    } catch (error) {
      throw new OrchestrationError(
        'Erreur génération code',
        'CODE_GENERATION_FAILED',
        error
      );
    }
  }

  /**
   * Exécution de commandes terminal avec monitoring
   */
  private async runTerminalCommand(command: string, workingDir?: string): Promise<any> {
    try {
      this.log(`⚡ Exécution commande: ${command}`);

      await this.ensureVSCodeReady();

      if (!this.session?.page) {
        throw new Error('Session Puppeteer non disponible');
      }

      // Ouvrir/Activer le terminal intégré
      await this.session.page.keyboard.press('Control+Shift+`');
      await this.session.page.waitForSelector('.terminal-wrapper', { timeout: 8000 });

      // Attendre que le terminal soit prêt
      await this.delay(1000);

      // Changer de répertoire si nécessaire
      if (workingDir) {
        const cdCommand = `cd "${workingDir}"`;
        await this.session.page.type('.terminal-wrapper .xterm-screen', cdCommand);
        await this.session.page.keyboard.press('Enter');
        await this.delay(1000);
      }

      // Exécuter la commande principale
      await this.session.page.type('.terminal-wrapper .xterm-screen', command);
      await this.session.page.keyboard.press('Enter');

      // Attendre l'exécution (timeout adaptatif selon la commande)
      const timeout = this.getCommandTimeout(command);
      await this.delay(timeout);

      return { command, workingDir, executed: true };

    } catch (error) {
      throw new OrchestrationError(
        `Erreur exécution commande "${command}"`,
        'COMMAND_EXECUTION_FAILED',
        error
      );
    }
  }

  /**
   * Installation d'extensions VS Code
   */
  private async installExtension(extensionId: string): Promise<any> {
    try {
      this.log(`🔌 Installation extension: ${extensionId}`);

      // Via API VS Code si disponible
      if (this.vscodeAPI) {
        await this.vscodeAPI.installExtension(extensionId);
        return { extensionId, installed: true, method: 'api' };
      }

      // Sinon via interface utilisateur
      await this.ensureVSCodeReady();

      if (!this.session?.page) {
        throw new Error('Session Puppeteer non disponible');
      }

      // Ouvrir la palette de commandes
      await this.session.page.keyboard.press('Control+Shift+P');
      await this.session.page.waitForSelector('.quick-input-widget', { timeout: 5000 });

      // Taper la commande d'installation
      await this.session.page.type('.quick-input-widget input', 'Extensions: Install Extensions');
      await this.session.page.keyboard.press('Enter');

      // Attendre la vue des extensions
      await this.session.page.waitForSelector('.extensions-viewlet', { timeout: 8000 });

      // Chercher l'extension
      await this.session.page.type('.extensions-viewlet input[placeholder*="Search"]', extensionId);
      await this.delay(2000);

      // Cliquer sur installer (premier résultat)
      const installButton = '.extensions-viewlet .extension .extension-action-bar .action-item .action-label[title*="Install"]';
      await this.session.page.waitForSelector(installButton, { timeout: 5000 });
      await this.session.page.click(installButton);

      return { extensionId, installed: true, method: 'ui' };

    } catch (error) {
      throw new OrchestrationError(
        `Erreur installation extension ${extensionId}`,
        'EXTENSION_INSTALL_FAILED',
        error
      );
    }
  }

  /**
   * Création de projet complet
   */
  private async createProject(params: any, context?: HanumanContext): Promise<any> {
    const projectName = params.projectName || params.name;
    const projectType = params.projectType || context?.projectType || 'agent';

    this.log(`🚀 Création projet: ${projectName} (${projectType})`);

    try {
      // 1. Créer la structure de dossiers
      await this.runTerminalCommand(
        `mkdir -p ${projectName}/{src,tests,docs,config}`,
        '/workspace'
      );

      // 2. Initialiser le projet Node.js
      await this.runTerminalCommand('npm init -y', `/workspace/${projectName}`);

      // 3. Installer les dépendances Hanuman
      const dependencies = this.getProjectDependencies(projectType);
      await this.runTerminalCommand(
        `npm install ${dependencies.join(' ')}`,
        `/workspace/${projectName}`
      );

      // 4. Générer les fichiers de base
      await this.generateProjectFiles(projectName, projectType, context);

      // 5. Ouvrir le projet dans VS Code
      await this.openFile(`${projectName}/src/index.ts`, true);

      return {
        projectName,
        projectType,
        created: true,
        dependencies: dependencies.length
      };

    } catch (error) {
      throw new OrchestrationError(
        `Erreur création projet ${projectName}`,
        'PROJECT_CREATION_FAILED',
        error
      );
    }
  }

  // Méthodes utilitaires privées

  private async createPuppeteerSession(): Promise<void> {
    this.log('🌐 Création session Puppeteer...');

    const puppeteer = await import('puppeteer');
    const browser = await puppeteer.launch({
      headless: this.config.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--allow-running-insecure-content'
      ]
    });

    const page = await browser.newPage();
    await page.setViewport(this.config.viewport);

    // Naviguer vers l'instance VS Code de l'agent
    const vscodeUrl = await this.getAgentVSCodeURL();
    this.log(`🔗 Navigation vers VS Code: ${vscodeUrl}`);

    await page.goto(vscodeUrl, { waitUntil: 'networkidle2' });

    this.session = {
      browser,
      page,
      agentId: this.agentId,
      vscodeUrl,
      isReady: false,
      lastActivity: new Date()
    };
  }

  private async initializeAPIs(): Promise<void> {
    // TODO: Initialiser les vraies API
    this.log('🔌 Initialisation des API...');
  }

  private async configureVSCode(): Promise<void> {
    // TODO: Configurer VS Code pour l'agent
    this.log('⚙️ Configuration VS Code...');
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  private async ensureVSCodeReady(): Promise<void> {
    if (!this.session?.page) {
      throw new Error('Session Puppeteer non disponible');
    }

    if (!this.session.isReady) {
      await this.session.page.waitForSelector('.monaco-workbench', { timeout: 15000 });
      this.session.isReady = true;
    }

    this.session.lastActivity = new Date();
  }

  private async getAgentVSCodeURL(): Promise<string> {
    // TODO: Récupérer l'URL depuis le VSCodeServerManager
    return `http://localhost:8081?agent=${this.agentId}`;
  }

  private buildEnhancedHanumanContext(variables: Record<string, any>, context?: HanumanContext): Record<string, any> {
    return {
      // Métadonnées Hanuman
      hanumanVersion: '2.0.0-enhanced',
      architecture: 'biomimetic-organs-agents-enhanced',

      // Contexte de l'agent
      agentId: this.agentId,
      organId: context?.organId,
      projectType: context?.projectType || 'agent',

      // Variables spécifiques
      ...variables
    };
  }

  private enhancePromptWithContext(prompt: string, context?: HanumanContext): string {
    return `
Contexte Hanuman Enhanced:
- Agent: ${this.agentId}
- Type: ${context?.projectType || 'agent'}
- Architecture: Biomimétique avec organes spécialisés

Standards de code à respecter:
- TypeScript strict avec interfaces complètes
- Gestion d'erreurs avec try/catch et logs
- Communication via EventEmitter pour agents
- Tests unitaires avec Jest
- Documentation TSDoc

Demande: ${prompt}

Génère le code en respectant ces standards.`;
  }

  private postProcessGeneratedCode(code: string, params: any, context?: HanumanContext): string {
    let processedCode = code;

    // Ajouter l'en-tête de fichier Hanuman
    const header = `// Generated by Hanuman Enhanced IDE
// Agent: ${this.agentId}
// Template: ${params.template || 'custom'}
// Generated at: ${new Date().toISOString()}

`;

    return header + processedCode;
  }

  private async generateFromTemplate(template: string, variables: any): Promise<string> {
    // TODO: Implémenter génération depuis template
    return `// Code généré depuis template: ${template}\n// Variables: ${JSON.stringify(variables)}`;
  }

  private async generateFromPrompt(prompt: string, context?: HanumanContext): Promise<string> {
    // TODO: Implémenter génération depuis prompt
    return `// Code généré depuis prompt: ${prompt}`;
  }

  private async writeCodeToFile(filePath: string, code: string): Promise<void> {
    // TODO: Utiliser l'API Sandbox pour écrire le fichier
    this.log(`💾 Écriture fichier: ${filePath}`);
  }

  private async insertCodeInEditor(code: string): Promise<void> {
    if (!this.session?.page) return;

    // Sélectionner tout et remplacer
    await this.session.page.click('.monaco-editor .view-lines');
    await this.session.page.keyboard.press('Control+A');
    await this.session.page.keyboard.type(code);

    // Formater le code
    await this.session.page.keyboard.press('Shift+Alt+F');
  }

  private async navigateToLocation(params: any): Promise<any> {
    // TODO: Implémenter navigation
    return { navigated: true };
  }

  private async editFile(params: any): Promise<any> {
    // TODO: Implémenter édition
    return { edited: true };
  }

  private async generateProjectFiles(projectName: string, projectType: string, context?: HanumanContext): Promise<void> {
    this.log(`📁 Génération fichiers projet: ${projectName}`);

    // Générer package.json
    const packageJson = {
      name: projectName.toLowerCase(),
      version: '1.0.0',
      description: `Projet Hanuman ${projectType}`,
      main: 'dist/index.js',
      scripts: {
        build: 'tsc',
        start: 'node dist/index.js',
        dev: 'nodemon src/index.ts',
        test: 'jest'
      },
      keywords: ['hanuman', projectType, 'biomimetic', 'ai'],
      author: 'Hanuman Enhanced System',
      license: 'MIT'
    };

    await this.writeCodeToFile(`${projectName}/package.json`, JSON.stringify(packageJson, null, 2));

    // Générer tsconfig.json
    const tsConfig = {
      compilerOptions: {
        target: 'ES2020',
        module: 'commonjs',
        lib: ['ES2020'],
        outDir: './dist',
        rootDir: './src',
        strict: true,
        esModuleInterop: true,
        skipLibCheck: true,
        forceConsistentCasingInFileNames: true
      },
      include: ['src/**/*'],
      exclude: ['node_modules', 'dist', 'tests']
    };

    await this.writeCodeToFile(`${projectName}/tsconfig.json`, JSON.stringify(tsConfig, null, 2));

    // Générer le fichier principal selon le type
    let mainFileContent = '';
    switch (projectType) {
      case 'agent':
        mainFileContent = this.generateAgentMainFile(projectName);
        break;
      case 'interface':
        mainFileContent = this.generateInterfaceMainFile(projectName);
        break;
      case 'service':
        mainFileContent = this.generateServiceMainFile(projectName);
        break;
      default:
        mainFileContent = `console.log('Hello from ${projectName}!');`;
    }

    await this.writeCodeToFile(`${projectName}/src/index.ts`, mainFileContent);
  }

  private generateAgentMainFile(projectName: string): string {
    return `import { EventEmitter } from 'events';

/**
 * Agent ${projectName} - Généré par Hanuman Enhanced
 */
export class ${projectName}Agent extends EventEmitter {
  private isActive = false;

  constructor() {
    super();
    this.initialize();
  }

  private async initialize(): Promise<void> {
    console.log('🤖 Initialisation agent ${projectName}...');
    this.emit('agent:initialized', { agent: '${projectName}' });
  }

  async activate(): Promise<void> {
    this.isActive = true;
    this.emit('agent:activated', { agent: '${projectName}' });
    console.log('✅ Agent ${projectName} activé');
  }

  async deactivate(): Promise<void> {
    this.isActive = false;
    this.emit('agent:deactivated', { agent: '${projectName}' });
    console.log('😴 Agent ${projectName} désactivé');
  }

  getStatus(): { active: boolean } {
    return { active: this.isActive };
  }
}

// Démarrage de l'agent
const agent = new ${projectName}Agent();
agent.activate();

export default ${projectName}Agent;`;
  }

  private generateInterfaceMainFile(projectName: string): string {
    return `import React from 'react';

interface ${projectName}Props {
  title?: string;
}

/**
 * Interface ${projectName} - Générée par Hanuman Enhanced
 */
const ${projectName}: React.FC<${projectName}Props> = ({ title = '${projectName}' }) => {
  return (
    <div className="hanuman-interface">
      <h1 className="text-2xl font-bold text-center mb-4">
        {title}
      </h1>
      <p className="text-center text-gray-600">
        Interface générée par Hanuman Enhanced System
      </p>
    </div>
  );
};

export default ${projectName};`;
  }

  private generateServiceMainFile(projectName: string): string {
    return `import express from 'express';

/**
 * Service ${projectName} - Généré par Hanuman Enhanced
 */
class ${projectName}Service {
  private app = express();
  private port = process.env.PORT || 3000;

  constructor() {
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    this.app.use(express.json());
  }

  private setupRoutes(): void {
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        service: '${projectName}',
        timestamp: new Date().toISOString()
      });
    });

    this.app.get('/', (req, res) => {
      res.json({
        message: 'Service ${projectName} - Hanuman Enhanced',
        version: '1.0.0'
      });
    });
  }

  async start(): Promise<void> {
    this.app.listen(this.port, () => {
      console.log(\`🚀 Service ${projectName} démarré sur le port \${this.port}\`);
    });
  }
}

// Démarrage du service
const service = new ${projectName}Service();
service.start();

export default ${projectName}Service;`;
  }

  private getProjectDependencies(projectType: string): string[] {
    const baseDeps = ['typescript', '@types/node', 'ts-node', 'nodemon'];

    switch (projectType) {
      case 'agent':
        return [...baseDeps, 'events', 'kafkajs', 'redis'];
      case 'interface':
        return [...baseDeps, 'react', 'react-dom', '@types/react', '@types/react-dom'];
      case 'service':
        return [...baseDeps, 'express', 'cors', 'helmet'];
      default:
        return baseDeps;
    }
  }

  private getCommandTimeout(command: string): number {
    // Timeouts adaptatifs selon le type de commande
    if (command.includes('npm install') || command.includes('yarn install')) return 30000;
    if (command.includes('npm run build') || command.includes('yarn build')) return 20000;
    if (command.includes('git clone')) return 15000;
    if (command.includes('docker')) return 25000;
    return 5000; // Timeout par défaut
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private log(message: string, data?: any): void {
    if (this.config.enableLogging) {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] [${this.agentId}] ${message}`, data || '');
    }
  }

  // Nettoyage des ressources
  async cleanup(): Promise<void> {
    if (this.session?.browser) {
      await this.session.browser.close();
    }
    this.isInitialized = false;
    this.log(`🧹 Contrôleur nettoyé`);
  }

  // Méthodes publiques pour monitoring
  public getStatus(): { agentId: string; isInitialized: boolean; isReady: boolean; lastActivity?: Date } {
    return {
      agentId: this.agentId,
      isInitialized: this.isInitialized,
      isReady: this.session?.isReady || false,
      lastActivity: this.session?.lastActivity
    };
  }
}
