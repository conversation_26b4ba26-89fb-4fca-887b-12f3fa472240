#!/usr/bin/env node

/**
 * Test de Base de Hanuman - Organisme IA Vivant
 * Vérifie les fonctionnalités essentielles de Hanuman
 */

const fs = require('fs');
const path = require('path');

console.log('🕉️ HANUMAN - Test de Fonctionnalités de Base');
console.log('=' .repeat(50));

// Test 1: Vérification de la structure
console.log('\n📁 Test 1: Vérification de la Structure');
const requiredDirs = [
    'brain/cortex-central',
    'agents',
    'specialized-agents',
    'vital-organs',
    'sensory-organs',
    'immune-system',
    'monitoring',
    'sandbox'
];

let structureOK = true;
requiredDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
        console.log(`✅ ${dir} - Présent`);
    } else {
        console.log(`❌ ${dir} - Manquant`);
        structureOK = false;
    }
});

// Test 2: Vérification des agents
console.log('\n🤖 Test 2: Vérification des Agents');
const agentsDir = 'specialized-agents';
if (fs.existsSync(agentsDir)) {
    const agents = fs.readdirSync(agentsDir);
    console.log(`✅ ${agents.length} agents détectés:`);
    agents.forEach(agent => {
        console.log(`   - ${agent}`);
    });
} else {
    console.log('❌ Répertoire des agents manquant');
}

// Test 3: Vérification des organes vitaux
console.log('\n💓 Test 3: Vérification des Organes Vitaux');
const organsDir = 'vital-organs';
if (fs.existsSync(organsDir)) {
    const organs = fs.readdirSync(organsDir);
    console.log(`✅ ${organs.length} organes vitaux détectés:`);
    organs.forEach(organ => {
        console.log(`   - ${organ}`);
    });
} else {
    console.log('❌ Répertoire des organes vitaux manquant');
}

// Test 4: Vérification du système immunitaire
console.log('\n🛡️ Test 4: Vérification du Système Immunitaire');
const immuneDir = 'immune-system';
if (fs.existsSync(immuneDir)) {
    const immuneComponents = fs.readdirSync(immuneDir);
    console.log(`✅ ${immuneComponents.length} composants immunitaires détectés:`);
    immuneComponents.forEach(component => {
        console.log(`   - ${component}`);
    });
} else {
    console.log('❌ Système immunitaire manquant');
}

// Test 5: Vérification du sandbox
console.log('\n🧪 Test 5: Vérification du Sandbox');
const sandboxDir = 'sandbox';
if (fs.existsSync(sandboxDir)) {
    const sandboxComponents = fs.readdirSync(sandboxDir);
    console.log(`✅ ${sandboxComponents.length} composants sandbox détectés:`);
    sandboxComponents.forEach(component => {
        console.log(`   - ${component}`);
    });
} else {
    console.log('❌ Sandbox manquant');
}

// Test 6: Vérification des scripts
console.log('\n📜 Test 6: Vérification des Scripts');
const scriptsDir = 'scripts';
if (fs.existsSync(scriptsDir)) {
    const scripts = fs.readdirSync(scriptsDir);
    console.log(`✅ ${scripts.length} scripts détectés:`);
    scripts.forEach(script => {
        console.log(`   - ${script}`);
    });
} else {
    console.log('❌ Répertoire des scripts manquant');
}

// Test 7: Vérification de la configuration
console.log('\n⚙️ Test 7: Vérification de la Configuration');
const configFiles = [
    'package.json',
    'tsconfig.json',
    'docker-compose.v3.8.yml'
];

configFiles.forEach(file => {
    if (fs.existsSync(file)) {
        console.log(`✅ ${file} - Présent`);
    } else {
        console.log(`❌ ${file} - Manquant`);
    }
});

// Résumé final
console.log('\n' + '=' .repeat(50));
console.log('🕉️ RÉSUMÉ DU TEST HANUMAN');
console.log('=' .repeat(50));

if (structureOK) {
    console.log('✅ Structure de base: VALIDE');
} else {
    console.log('❌ Structure de base: PROBLÈMES DÉTECTÉS');
}

console.log('\n🎯 PROCHAINES ÉTAPES RECOMMANDÉES:');
console.log('1. Corriger les problèmes de dépendances Next.js');
console.log('2. Tester le démarrage du cortex central');
console.log('3. Vérifier la connectivité entre les agents');
console.log('4. Activer le monitoring 24/7');
console.log('5. Intégrer avec le projet Retreat And Be');

console.log('\n🕉️ Hanuman est prêt à protéger et évoluer ! 🤖✨');
