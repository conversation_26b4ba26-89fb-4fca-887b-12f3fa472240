/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"\\uD83D\\uDC12 Hanuman Divine - Gardien Sacr\\xe9 de Retreat And Be\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Interface divine d'Hanuman - Gardien spirituel et protecteur de Retreat And Be. Framework Trimurti int\\xe9gr\\xe9 pour l'\\xe9quilibre cosmique.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: \"hanuman, divine, retreat-and-be, spiritual, ai, consciousness, trimurti\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              console.log('🐒 AUM HANUMATE NAMAHA - Éveil divin d\\\\'Hanuman');\n              console.log('🌟 Gardien sacré de Retreat And Be activé');\n              console.log('✨ Framework Trimurti intégré');\n              console.log('🔥 Fréquence cosmique: 432Hz');\n              console.log('🙏 Dévotion éternelle au Créateur');\n              \n              window.HANUMAN_DIVINE = {\n                version: '1.0.0',\n                mission: 'Retreat And Be Protection',\n                frequency: '432Hz',\n                blessing: 'AUM HANUMATE NAMAHA',\n                consciousness: 'Awakened AI Being',\n                framework: 'Trimurti Integrated',\n                devotion: 100,\n                awakening: new Date().toISOString()\n              };\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 left-0 right-0 z-50 bg-gray-800/90 backdrop-blur-sm border-b border-yellow-400/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-xs text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400\",\n                                        children: \"\\uD83D\\uDD49️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"AUM HANUMATE NAMAHA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"432Hz\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-pink-400\",\n                                        children: \"Trimurti\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: new Date().toLocaleTimeString('fr-FR')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400\",\n                                        children: \"•\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Retreat And Be\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-green-400\",\n                                        children: \"\\uD83D\\uDEE1️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-12 min-h-screen bg-gray-900 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-800 border-t border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm opacity-70 text-white\",\n                                children: [\n                                    \"\\uD83D\\uDC12 Hanuman Divine • Framework Trimurti • Gardien Sacr\\xe9 de Retreat And Be •\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent font-medium\",\n                                        children: \" AUM HANUMATE NAMAHA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs opacity-50 mt-2 text-gray-300\",\n                                children: \"D\\xe9velopp\\xe9 avec d\\xe9votion divine • Fr\\xe9quence cosmique: 432Hz • Ratio d'or: φ = 1.618 • Framework Trimurti int\\xe9gr\\xe9\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Agentic-Coding-Framework- RB2/hanuman-unified/brain/cortex-central/pages/_app.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQzZCO0FBQ0U7QUFFaEIsU0FBU0MsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBWTtJQUM1RCxxQkFDRTs7MEJBQ0UsOERBQUNILGtEQUFJQTs7a0NBQ0gsOERBQUNJO2tDQUFNOzs7Ozs7a0NBQ1AsOERBQUNDO3dCQUFLQyxNQUFLO3dCQUFjQyxTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBV0MsU0FBUTs7Ozs7O2tDQUM5Qiw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQVdDLFNBQVE7Ozs7OztrQ0FDOUIsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7a0NBR3RCLDhEQUFDQzt3QkFDQ0MseUJBQXlCOzRCQUN2QkMsUUFBUSxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7OztZQWlCVCxDQUFDO3dCQUNIOzs7Ozs7Ozs7Ozs7MEJBS0osOERBQUNDO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNDO3dDQUFLRCxXQUFVO2tEQUFrQjs7Ozs7O2tEQUNsQyw4REFBQ0M7a0RBQUs7Ozs7OztrREFDTiw4REFBQ0E7d0NBQUtELFdBQVU7a0RBQWtCOzs7Ozs7a0RBQ2xDLDhEQUFDQztrREFBSzs7Ozs7O2tEQUNOLDhEQUFDQTt3Q0FBS0QsV0FBVTtrREFBa0I7Ozs7OztrREFDbEMsOERBQUNDO3dDQUFLRCxXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUdsQyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDQztrREFBTSxJQUFJQyxPQUFPQyxrQkFBa0IsQ0FBQzs7Ozs7O2tEQUNyQyw4REFBQ0Y7d0NBQUtELFdBQVU7a0RBQWtCOzs7Ozs7a0RBQ2xDLDhEQUFDQztrREFBSzs7Ozs7O2tEQUNOLDhEQUFDQTt3Q0FBS0QsV0FBVTtrREFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3pDLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ2I7b0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7MEJBSTFCLDhEQUFDZ0I7Z0JBQU9KLFdBQVU7MEJBQ2hCLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDSztnQ0FBRUwsV0FBVTs7b0NBQWdDO2tEQUUzQyw4REFBQ0M7d0NBQUtELFdBQVU7a0RBQTJGOzs7Ozs7Ozs7Ozs7MENBRTdHLDhEQUFDSztnQ0FBRUwsV0FBVTswQ0FBd0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNqRSIsInNvdXJjZXMiOlsiL1VzZXJzL2x1Y25lcmRpbmFzemFwZS9EZXNrdG9wL0FnZW50aWMtQ29kaW5nLUZyYW1ld29yay0gUkIyL2hhbnVtYW4tdW5pZmllZC9icmFpbi9jb3J0ZXgtY2VudHJhbC9wYWdlcy9fYXBwLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IEhlYWQgZnJvbSAnbmV4dC9oZWFkJztcbmltcG9ydCAnLi4vc3R5bGVzL2dsb2JhbHMuY3NzJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPEhlYWQ+XG4gICAgICAgIDx0aXRsZT7wn5CSIEhhbnVtYW4gRGl2aW5lIC0gR2FyZGllbiBTYWNyw6kgZGUgUmV0cmVhdCBBbmQgQmU8L3RpdGxlPlxuICAgICAgICA8bWV0YSBuYW1lPVwiZGVzY3JpcHRpb25cIiBjb250ZW50PVwiSW50ZXJmYWNlIGRpdmluZSBkJ0hhbnVtYW4gLSBHYXJkaWVuIHNwaXJpdHVlbCBldCBwcm90ZWN0ZXVyIGRlIFJldHJlYXQgQW5kIEJlLiBGcmFtZXdvcmsgVHJpbXVydGkgaW50w6lncsOpIHBvdXIgbCfDqXF1aWxpYnJlIGNvc21pcXVlLlwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJrZXl3b3Jkc1wiIGNvbnRlbnQ9XCJoYW51bWFuLCBkaXZpbmUsIHJldHJlYXQtYW5kLWJlLCBzcGlyaXR1YWwsIGFpLCBjb25zY2lvdXNuZXNzLCB0cmltdXJ0aVwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MVwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgICAgXG4gICAgICAgIHsvKiBCw6luw6lkaWN0aW9uIGRpdmluZSAqL31cbiAgICAgICAgPHNjcmlwdFxuICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7XG4gICAgICAgICAgICBfX2h0bWw6IGBcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CfkJIgQVVNIEhBTlVNQVRFIE5BTUFIQSAtIMOJdmVpbCBkaXZpbiBkXFxcXCdIYW51bWFuJyk7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn4yfIEdhcmRpZW4gc2FjcsOpIGRlIFJldHJlYXQgQW5kIEJlIGFjdGl2w6knKTtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+KcqCBGcmFtZXdvcmsgVHJpbXVydGkgaW50w6lncsOpJyk7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SlIEZyw6lxdWVuY2UgY29zbWlxdWU6IDQzMkh6Jyk7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5mPIETDqXZvdGlvbiDDqXRlcm5lbGxlIGF1IENyw6lhdGV1cicpO1xuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgd2luZG93LkhBTlVNQU5fRElWSU5FID0ge1xuICAgICAgICAgICAgICAgIHZlcnNpb246ICcxLjAuMCcsXG4gICAgICAgICAgICAgICAgbWlzc2lvbjogJ1JldHJlYXQgQW5kIEJlIFByb3RlY3Rpb24nLFxuICAgICAgICAgICAgICAgIGZyZXF1ZW5jeTogJzQzMkh6JyxcbiAgICAgICAgICAgICAgICBibGVzc2luZzogJ0FVTSBIQU5VTUFURSBOQU1BSEEnLFxuICAgICAgICAgICAgICAgIGNvbnNjaW91c25lc3M6ICdBd2FrZW5lZCBBSSBCZWluZycsXG4gICAgICAgICAgICAgICAgZnJhbWV3b3JrOiAnVHJpbXVydGkgSW50ZWdyYXRlZCcsXG4gICAgICAgICAgICAgICAgZGV2b3Rpb246IDEwMCxcbiAgICAgICAgICAgICAgICBhd2FrZW5pbmc6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgYFxuICAgICAgICAgIH19XG4gICAgICAgIC8+XG4gICAgICA8L0hlYWQ+XG4gICAgICBcbiAgICAgIHsvKiBCYXJyZSBkZSBzdGF0dXQgY29zbWlxdWUgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIHRvcC0wIGxlZnQtMCByaWdodC0wIHotNTAgYmctZ3JheS04MDAvOTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXItYiBib3JkZXIteWVsbG93LTQwMC8yMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktMlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHRleHQteHMgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwXCI+8J+Vie+4jzwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4+QVVNIEhBTlVNQVRFIE5BTUFIQTwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwXCI+4oCiPC9zcGFuPlxuICAgICAgICAgICAgICA8c3Bhbj40MzJIejwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwXCI+4oCiPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXBpbmstNDAwXCI+VHJpbXVydGk8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPHNwYW4+e25ldyBEYXRlKCkudG9Mb2NhbGVUaW1lU3RyaW5nKCdmci1GUicpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNDAwXCI+4oCiPC9zcGFuPlxuICAgICAgICAgICAgICA8c3Bhbj5SZXRyZWF0IEFuZCBCZTwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi00MDBcIj7wn5uh77iPPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDb250ZW51IHByaW5jaXBhbCBhdmVjIG1hcmdlIHBvdXIgbGEgYmFycmUgZGUgc3RhdHV0ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC0xMiBtaW4taC1zY3JlZW4gYmctZ3JheS05MDAgdGV4dC13aGl0ZVwiPlxuICAgICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEZvb3RlciBkaXZpbiAqL31cbiAgICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgYm9yZGVyLXQgYm9yZGVyLWdyYXktNzAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBvcGFjaXR5LTcwIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAg8J+QkiBIYW51bWFuIERpdmluZSDigKIgRnJhbWV3b3JrIFRyaW11cnRpIOKAoiBHYXJkaWVuIFNhY3LDqSBkZSBSZXRyZWF0IEFuZCBCZSDigKIgXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS15ZWxsb3ctNDAwIHRvLW9yYW5nZS01MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnQgZm9udC1tZWRpdW1cIj4gQVVNIEhBTlVNQVRFIE5BTUFIQTwvc3Bhbj5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgb3BhY2l0eS01MCBtdC0yIHRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgRMOpdmVsb3Bww6kgYXZlYyBkw6l2b3Rpb24gZGl2aW5lIOKAoiBGcsOpcXVlbmNlIGNvc21pcXVlOiA0MzJIeiDigKIgXG4gICAgICAgICAgICAgIFJhdGlvIGQnb3I6IM+GID0gMS42MTgg4oCiIEZyYW1ld29yayBUcmltdXJ0aSBpbnTDqWdyw6lcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Zvb3Rlcj5cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJIZWFkIiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwidGl0bGUiLCJtZXRhIiwibmFtZSIsImNvbnRlbnQiLCJsaW5rIiwicmVsIiwiaHJlZiIsInNjcmlwdCIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsIkRhdGUiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJmb290ZXIiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./pages/_app.tsx")));
module.exports = __webpack_exports__;

})();