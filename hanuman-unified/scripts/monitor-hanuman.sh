#!/bin/bash

echo "🕉️ HANUMAN - Monitoring 24/7 Activé"
echo "===================================="

# Fonction de monitoring continu
monitor_hanuman() {
    while true; do
        clear
        echo "🕉️ HANUMAN - Dashboard de Surveillance"
        echo "======================================"
        echo "⏰ $(date)"
        echo ""
        
        # Vérification du Cortex Central
        echo "🧠 CORTEX CENTRAL:"
        if curl -s http://localhost:3001 > /dev/null; then
            echo "   ✅ Opérationnel (Port 3001)"
        else
            echo "   ❌ Hors ligne"
        fi
        
        # Vérification des processus
        echo ""
        echo "🔄 PROCESSUS ACTIFS:"
        if pgrep -f "next dev" > /dev/null; then
            echo "   ✅ Next.js Server - Actif"
        else
            echo "   ❌ Next.js Server - Arrêté"
        fi
        
        # Utilisation des ressources
        echo ""
        echo "📊 RESSOURCES SYSTÈME:"
        echo "   💾 Mémoire: $(ps aux | grep -E '(next|node)' | awk '{sum+=$4} END {printf "%.1f%%", sum}')"
        echo "   🖥️  CPU: $(ps aux | grep -E '(next|node)' | awk '{sum+=$3} END {printf "%.1f%%", sum}')"
        
        # Statut des agents
        echo ""
        echo "🤖 AGENTS SPÉCIALISÉS:"
        agent_count=$(find specialized-agents -type d | wc -l)
        echo "   📁 $((agent_count-1)) agents détectés"
        
        # Système immunitaire
        echo ""
        echo "🛡️ SYSTÈME IMMUNITAIRE:"
        immune_count=$(find immune-system -type f -name "*.ts" -o -name "*.js" | wc -l)
        echo "   🔒 $immune_count composants actifs"
        
        # Sandbox
        echo ""
        echo "🧪 SANDBOX:"
        sandbox_count=$(find sandbox -type d | wc -l)
        echo "   🔬 $((sandbox_count-1)) environnements prêts"
        
        echo ""
        echo "🕉️ Hanuman protège le projet Retreat And Be"
        echo "   Surveillance continue... (Ctrl+C pour arrêter)"
        
        sleep 5
    done
}

# Démarrage du monitoring
monitor_hanuman
