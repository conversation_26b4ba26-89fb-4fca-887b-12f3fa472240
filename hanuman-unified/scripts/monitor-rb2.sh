#!/bin/bash

echo "🕉️ HANUMAN - Surveillance Retreat And Be"
echo "========================================"

# Surveillance continue du projet RB2
while true; do
    clear
    echo "🕉️ HANUMAN protège Retreat And Be"
    echo "================================="
    echo "⏰ $(date)"
    echo ""
    
    # Vérification des ports RB2
    echo "🌐 SERVICES RB2:"
    
    # Frontend (port 3000)
    if curl -s http://localhost:3000 > /dev/null; then
        echo "   ✅ Frontend RB2 (Port 3000) - Opérationnel"
    else
        echo "   ⚠️  Frontend RB2 (Port 3000) - Hors ligne"
    fi
    
    # Backend (port 3001 ou 8000)
    if curl -s http://localhost:8000 > /dev/null; then
        echo "   ✅ Backend RB2 (Port 8000) - Opérationnel"
    elif curl -s http://localhost:3002 > /dev/null; then
        echo "   ✅ Backend RB2 (Port 3002) - Opérationnel"
    else
        echo "   ⚠️  Backend RB2 - Hors ligne"
    fi
    
    # Hanuman Cortex
    if curl -s http://localhost:3001 > /dev/null; then
        echo "   ✅ Hanuman Cortex (Port 3001) - Protège RB2"
    else
        echo "   ❌ Hanuman Cortex - Redémarrage nécessaire"
    fi
    
    echo ""
    echo "🛡️ PROTECTION ACTIVE:"
    echo "   🔍 Surveillance continue"
    echo "   🚨 Détection d'anomalies"
    echo "   🔧 Auto-réparation"
    echo "   📊 Optimisation performance"
    
    echo ""
    echo "🕉️ Hanuman veille sur Retreat And Be..."
    echo "   (Ctrl+C pour arrêter)"
    
    sleep 10
done
