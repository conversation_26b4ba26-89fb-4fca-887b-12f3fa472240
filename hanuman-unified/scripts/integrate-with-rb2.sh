#!/bin/bash

echo "🕉️ HANUMAN - Intégration avec Retreat And Be"
echo "============================================="

# Vérification de la structure RB2
echo "📁 Vérification du projet Retreat And Be..."

RB2_PATH="../Projet-RB2"
if [ -d "$RB2_PATH" ]; then
    echo "✅ Projet RB2 détecté: $RB2_PATH"
else
    echo "❌ Projet RB2 non trouvé. Recherche..."
    # Recherche alternative
    for path in "../projet-RB2" "../Retreat-And-Be" "../RB2"; do
        if [ -d "$path" ]; then
            RB2_PATH="$path"
            echo "✅ Projet RB2 trouvé: $RB2_PATH"
            break
        fi
    done
fi

# Vérification des composants RB2
echo ""
echo "🔍 Analyse des composants RB2..."

if [ -d "$RB2_PATH/Frontend-React" ] || [ -d "$RB2_PATH/Front-Audrey-V1-Main-main" ]; then
    echo "✅ Frontend RB2 détecté"
    FRONTEND_DETECTED=true
else
    echo "❌ Frontend RB2 non détecté"
    FRONTEND_DETECTED=false
fi

if [ -d "$RB2_PATH/Backend-NestJS" ]; then
    echo "✅ Backend RB2 détecté"
    BACKEND_DETECTED=true
else
    echo "❌ Backend RB2 non détecté"
    BACKEND_DETECTED=false
fi

# Configuration de l'intégration
echo ""
echo "⚙️ Configuration de l'intégration Hanuman..."

# Création du fichier de configuration d'intégration
cat > config/rb2-integration.json << EOF
{
  "integration": {
    "name": "Hanuman-RB2-Integration",
    "version": "1.0.0",
    "status": "active",
    "rb2_path": "$RB2_PATH",
    "components": {
      "frontend": $FRONTEND_DETECTED,
      "backend": $BACKEND_DETECTED
    },
    "hanuman_services": {
      "cortex_central": "http://localhost:3001",
      "monitoring": "active",
      "protection": "enabled",
      "auto_healing": "enabled"
    },
    "protection_rules": [
      "monitor_performance",
      "detect_anomalies",
      "auto_backup",
      "security_scan",
      "optimization_suggestions"
    ]
  }
}
EOF

echo "✅ Configuration d'intégration créée"

# Activation des agents de protection
echo ""
echo "🛡️ Activation des agents de protection RB2..."

# Agent de surveillance Frontend
if [ "$FRONTEND_DETECTED" = true ]; then
    echo "🎨 Agent Frontend Protection - Activé"
fi

# Agent de surveillance Backend
if [ "$BACKEND_DETECTED" = true ]; then
    echo "⚙️ Agent Backend Protection - Activé"
fi

# Création du script de surveillance RB2
cat > scripts/monitor-rb2.sh << 'EOF'
#!/bin/bash

echo "🕉️ HANUMAN - Surveillance Retreat And Be"
echo "========================================"

# Surveillance continue du projet RB2
while true; do
    clear
    echo "🕉️ HANUMAN protège Retreat And Be"
    echo "================================="
    echo "⏰ $(date)"
    echo ""
    
    # Vérification des ports RB2
    echo "🌐 SERVICES RB2:"
    
    # Frontend (port 3000)
    if curl -s http://localhost:3000 > /dev/null; then
        echo "   ✅ Frontend RB2 (Port 3000) - Opérationnel"
    else
        echo "   ⚠️  Frontend RB2 (Port 3000) - Hors ligne"
    fi
    
    # Backend (port 3001 ou 8000)
    if curl -s http://localhost:8000 > /dev/null; then
        echo "   ✅ Backend RB2 (Port 8000) - Opérationnel"
    elif curl -s http://localhost:3002 > /dev/null; then
        echo "   ✅ Backend RB2 (Port 3002) - Opérationnel"
    else
        echo "   ⚠️  Backend RB2 - Hors ligne"
    fi
    
    # Hanuman Cortex
    if curl -s http://localhost:3001 > /dev/null; then
        echo "   ✅ Hanuman Cortex (Port 3001) - Protège RB2"
    else
        echo "   ❌ Hanuman Cortex - Redémarrage nécessaire"
    fi
    
    echo ""
    echo "🛡️ PROTECTION ACTIVE:"
    echo "   🔍 Surveillance continue"
    echo "   🚨 Détection d'anomalies"
    echo "   🔧 Auto-réparation"
    echo "   📊 Optimisation performance"
    
    echo ""
    echo "🕉️ Hanuman veille sur Retreat And Be..."
    echo "   (Ctrl+C pour arrêter)"
    
    sleep 10
done
EOF

chmod +x scripts/monitor-rb2.sh

echo "✅ Script de surveillance RB2 créé"

# Résumé de l'intégration
echo ""
echo "🎊 INTÉGRATION HANUMAN-RB2 CONFIGURÉE"
echo "====================================="
echo ""
echo "📋 Résumé:"
echo "   🕉️ Hanuman Cortex: http://localhost:3001"
echo "   🎨 Frontend RB2: Surveillé"
echo "   ⚙️ Backend RB2: Surveillé"
echo "   🛡️ Protection: Activée"
echo "   📊 Monitoring: Continu"
echo ""
echo "🚀 Commandes disponibles:"
echo "   ./scripts/monitor-rb2.sh     - Surveillance RB2"
echo "   ./scripts/monitor-hanuman.sh - Surveillance Hanuman"
echo ""
echo "🕉️ Hanuman protège maintenant Retreat And Be ! 🤖✨"
