#!/bin/bash

echo "🕉️ HANUMAN - Réparation du Cortex Central"
echo "=========================================="

# Aller dans le répertoire du cortex central
cd brain/cortex-central

echo "📁 Nettoyage des dépendances corrompues..."
rm -rf node_modules package-lock.json

echo "📦 Réinstallation des dépendances..."
npm install

echo "🔧 Vérification de l'installation Next.js..."
if [ -f "node_modules/.bin/next" ]; then
    echo "✅ Next.js installé correctement"
else
    echo "❌ Problème avec Next.js, réinstallation..."
    npm install next@latest
fi

echo "🧪 Test de démarrage..."
timeout 10s npm run dev &
sleep 5

if pgrep -f "next dev" > /dev/null; then
    echo "✅ Cortex Central démarré avec succès!"
    pkill -f "next dev"
else
    echo "❌ Problème de démarrage détecté"
fi

echo "🕉️ Réparation terminée!"
