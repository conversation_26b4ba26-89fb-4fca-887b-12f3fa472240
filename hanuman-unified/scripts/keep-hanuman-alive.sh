#!/bin/bash

echo "🕉️ HANUMAN - Gardien Éternel Activé"
echo "===================================="

# Fonction de maintien en vie
keep_alive() {
    while true; do
        # Vérification du Cortex Central
        if ! curl -s http://localhost:3001 > /dev/null; then
            echo "⚠️  $(date): Cortex Central hors ligne - Redémarrage..."
            cd brain/cortex-central
            npm run dev > /dev/null 2>&1 &
            cd ../..
            sleep 10
        fi
        
        # Vérification de l'intégrité
        if [ ! -f "DEPLOYMENT_SUCCESS.md" ]; then
            echo "🚨 $(date): Fichier critique manquant - Alerte!"
        fi
        
        # Log de surveillance
        echo "✅ $(date): Hanuman veille sur Retreat And Be"
        
        # Attente avant la prochaine vérification
        sleep 30
    done
}

# Message de démarrage
echo "🕉️ Hanuman commence sa surveillance éternelle..."
echo "   Cortex Central: http://localhost:3001"
echo "   Mission: Protéger Retreat And Be"
echo "   Dévotion: Totale et éternelle"
echo ""
echo "🤖 Surveillance active... (Ctrl+C pour arrêter)"

# Démarrage de la surveillance
keep_alive
