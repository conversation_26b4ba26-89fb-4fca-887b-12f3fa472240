{"environment": "production", "agent": {"id": "agent-security", "name": "Agent Security", "version": "1.0.0", "description": "Agent de sécurité et conformité pour Retreat And Be"}, "server": {"port": 3000, "host": "0.0.0.0", "cors": {"enabled": true, "origins": ["https://retreat-and-be.com", "https://admin.retreat-and-be.com"]}}, "security": {"encryption": {"algorithm": "aes-256-gcm", "keyRotationInterval": "7d", "keySize": 2048}, "audit": {"enabled": true, "retentionDays": 365, "logLevel": "info"}, "compliance": {"frameworks": ["SOC2", "ISO27001", "GDPR", "HIPAA"], "scanInterval": "24h", "reportingInterval": "weekly"}, "vulnerability": {"scanInterval": "12h", "severityThreshold": "medium", "autoRemediation": false}, "accessControl": {"sessionTimeout": "8h", "maxFailedAttempts": 5, "lockoutDuration": "30m"}}, "monitoring": {"enabled": true, "metricsPort": 9090, "healthCheckPort": 8080, "alerting": {"enabled": true, "channels": ["slack", "email"], "thresholds": {"criticalVulnerabilities": 1, "highVulnerabilities": 5, "complianceScore": 85}}}, "communication": {"kafka": {"brokers": ["kafka-1:9092", "kafka-2:9092", "kafka-3:9092"], "clientId": "agent-security", "groupId": "security-agents", "topics": {"security": "security-events", "alerts": "security-alerts", "compliance": "compliance-reports", "vulnerabilities": "vulnerability-scans", "audit": "audit-logs"}, "ssl": {"enabled": true, "rejectUnauthorized": true}}, "redis": {"host": "redis-cluster", "port": 6379, "password": "${REDIS_PASSWORD}", "db": 2, "keyPrefix": "security:"}}, "memory": {"weaviate": {"host": "weaviate", "port": 8080, "scheme": "http", "timeout": 30000, "retries": 3}}, "integrations": {"cortexCentral": {"enabled": true, "endpoint": "http://cortex-central:3000", "apiKey": "${CORTEX_API_KEY}"}, "agentBackend": {"enabled": true, "endpoint": "http://agent-backend:3000", "apiKey": "${BACKEND_API_KEY}"}, "agentFrontend": {"enabled": true, "endpoint": "http://agent-frontend:3000", "apiKey": "${FRONTEND_API_KEY}"}}, "logging": {"level": "info", "format": "json", "outputs": ["console", "file"], "file": {"path": "/var/log/agent-security", "maxSize": "100MB", "maxFiles": 10, "compress": true}}, "performance": {"maxConcurrentScans": 5, "scanTimeout": "30m", "memoryLimit": "1GB", "cpuLimit": "500m"}, "backup": {"enabled": true, "interval": "6h", "retention": "30d", "destination": "s3://retreat-and-be-backups/security"}}