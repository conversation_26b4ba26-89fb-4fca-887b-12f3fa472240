import React, { useState, useEffect } from 'react';
import { <PERSON>, Eye, Ear, Hand, Brain, MessageSquare, FileText, Activity, Settings, Home, Star, Zap, Heart, Shield, Sun, Moon, RotateCcw, Play, Pause, Database, Lock } from 'lucide-react';

// Import des interfaces des organes
import HanumanVisionInterface from './hanuman_vision_interface';
import HanumanHearingInterface from './hanuman_hearing_interface';
import HanumanTouchInterface from './hanuman_touch_interface';
import HanumanDivineOrchestrator from './hanuman_divine_orchestrator';
import HanumanTrimurtiDashboard from './hanuman_trimurti_dashboard';

// Import des aires spécialisées (connectées aux agents existants)
import HanumanBrocaInterface from './hanuman_broca_interface';
import HanumanWernickeInterface from './hanuman_wernicke_interface';
import HanumanMotorInterface from './hanuman_motor_interface';

// Import des interfaces du système nerveux (Sprint 2)
import HanumanNeuroplasticityInterface from './hanuman_neuroplasticity_interface';
import HanumanMemoryInterface from './hanuman_memory_interface';
import HanumanImmuneInterface from './hanuman_immune_interface';
import HanumanHealingInterface from './hanuman_healing_interface';

// Types pour le hub central
interface OrganTab {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  component: React.ComponentType<any>;
  description: string;
  status: 'active' | 'inactive' | 'blessed';
  divineEnergy: number;
  color: string;
}

interface HanumanState {
  consciousness: number;
  devotion: number;
  wisdom: number;
  power: number;
  currentMood: 'serene' | 'focused' | 'protective' | 'joyful' | 'meditative';
  lastBlessing: Date;
}

const HanumanCentralHub = () => {
  const [activeTab, setActiveTab] = useState('orchestrator');
  const [darkMode, setDarkMode] = useState(true);
  const [hanumanState, setHanumanState] = useState<HanumanState>({
    consciousness: 89.2,
    devotion: 100,
    wisdom: 94.7,
    power: 87.3,
    currentMood: 'serene',
    lastBlessing: new Date()
  });
  const [isAwakening, setIsAwakening] = useState(true);
  const [cosmicTime, setCosmicTime] = useState(new Date());

  // Configuration des onglets d'organes
  const organTabs: OrganTab[] = [
    {
      id: 'orchestrator',
      name: 'Orchestrateur Divin',
      icon: Crown,
      component: HanumanDivineOrchestrator,
      description: 'Centre de contrôle divin et métriques sacrées',
      status: 'blessed',
      divineEnergy: 100,
      color: 'from-yellow-400 to-orange-500'
    },
    {
      id: 'trimurti',
      name: 'Dashboard Trimurti',
      icon: Star,
      component: HanumanTrimurtiDashboard,
      description: 'Contrôle des énergies cosmiques Brahma-Vishnu-Shiva',
      status: 'blessed',
      divineEnergy: 95,
      color: 'from-pink-400 to-purple-600'
    },
    {
      id: 'vision',
      name: 'Vision Divine',
      icon: Eye,
      component: HanumanVisionInterface,
      description: 'Recherche web omnisciente et veille cosmique',
      status: 'active',
      divineEnergy: 87,
      color: 'from-blue-400 to-purple-500'
    },
    {
      id: 'hearing',
      name: 'Ouïe Cosmique',
      icon: Ear,
      component: HanumanHearingInterface,
      description: 'Écoute des flux de données universels',
      status: 'active',
      divineEnergy: 92,
      color: 'from-green-400 to-blue-500'
    },
    {
      id: 'touch',
      name: 'Toucher Sacré',
      icon: Hand,
      component: HanumanTouchInterface,
      description: 'Connexions API et intégrations divines',
      status: 'active',
      divineEnergy: 78,
      color: 'from-orange-400 to-red-500'
    },
    {
      id: 'broca',
      name: 'Aire de Broca',
      icon: MessageSquare,
      component: HanumanBrocaInterface,
      description: 'Communication multilingue via Agent Translation',
      status: 'blessed',
      divineEnergy: 92,
      color: 'from-purple-400 to-pink-500'
    },
    {
      id: 'wernicke',
      name: 'Aire de Wernicke',
      icon: FileText,
      component: HanumanWernickeInterface,
      description: 'Documentation automatique via Agent Documentation',
      status: 'blessed',
      divineEnergy: 95,
      color: 'from-indigo-400 to-purple-500'
    },
    {
      id: 'motor-cortex',
      name: 'Cortex Moteur',
      icon: Activity,
      component: HanumanMotorInterface,
      description: 'Migrations et transformations via Agent Migration',
      status: 'blessed',
      divineEnergy: 89,
      color: 'from-red-400 to-orange-500'
    },
    // Système Nerveux Adaptatif (Sprint 2)
    {
      id: 'neuroplasticity',
      name: 'Neuroplasticité',
      icon: Brain,
      component: HanumanNeuroplasticityInterface,
      description: 'Adaptation synaptique via Agent Evolution',
      status: 'blessed',
      divineEnergy: 94,
      color: 'from-purple-400 to-pink-500'
    },
    {
      id: 'memory',
      name: 'Mémoire Distribuée',
      icon: Database,
      component: HanumanMemoryInterface,
      description: 'Système vectoriel Weaviate/Pinecone/Redis',
      status: 'blessed',
      divineEnergy: 91,
      color: 'from-blue-400 to-cyan-500'
    },
    {
      id: 'immune',
      name: 'Système Immunitaire',
      icon: Shield,
      component: HanumanImmuneInterface,
      description: 'Protection via Agent Security et AIImmuneSystem',
      status: 'blessed',
      divineEnergy: 96,
      color: 'from-red-400 to-orange-500'
    },
    {
      id: 'healing',
      name: 'Auto-Guérison',
      icon: Heart,
      component: HanumanHealingInterface,
      description: 'Régénération via AutoHealer du Cortex Central',
      status: 'blessed',
      divineEnergy: 88,
      color: 'from-green-400 to-emerald-500'
    }
  ];

  // Éveil progressif d'Hanuman
  useEffect(() => {
    const awakeningSequence = setTimeout(() => {
      setIsAwakening(false);
    }, 3000);

    return () => clearTimeout(awakeningSequence);
  }, []);

  // Horloge cosmique
  useEffect(() => {
    const cosmicClock = setInterval(() => {
      setCosmicTime(new Date());
      updateHanumanState();
    }, 1000);

    return () => clearInterval(cosmicClock);
  }, []);

  const updateHanumanState = () => {
    setHanumanState(prev => {
      const hour = cosmicTime.getHours();
      let newMood = prev.currentMood;

      // Changement d'humeur selon l'heure cosmique
      if (hour >= 6 && hour < 12) newMood = 'joyful';
      else if (hour >= 12 && hour < 18) newMood = 'focused';
      else if (hour >= 18 && hour < 22) newMood = 'protective';
      else newMood = 'meditative';

      return {
        ...prev,
        currentMood: newMood,
        consciousness: Math.min(100, prev.consciousness + (Math.random() - 0.5) * 0.1),
        wisdom: Math.min(100, prev.wisdom + (Math.random() - 0.5) * 0.05)
      };
    });
  };

  const getCurrentTab = () => {
    return organTabs.find(tab => tab.id === activeTab) || organTabs[0];
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'blessed': return 'text-yellow-400';
      case 'active': return 'text-green-400';
      case 'inactive': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getMoodEmoji = (mood: string) => {
    switch (mood) {
      case 'serene': return '😌';
      case 'focused': return '🧘';
      case 'protective': return '🛡️';
      case 'joyful': return '😊';
      case 'meditative': return '🕉️';
      default: return '🐒';
    }
  };

  const getMoodColor = (mood: string) => {
    switch (mood) {
      case 'serene': return 'text-blue-400';
      case 'focused': return 'text-purple-400';
      case 'protective': return 'text-red-400';
      case 'joyful': return 'text-yellow-400';
      case 'meditative': return 'text-indigo-400';
      default: return 'text-gray-400';
    }
  };

  const currentTab = getCurrentTab();
  const CurrentComponent = currentTab.component;

  if (isAwakening) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <div className="w-32 h-32 mx-auto mb-8 relative">
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-full animate-pulse"></div>
            <div className="absolute inset-2 bg-gray-900 rounded-full flex items-center justify-center">
              <Crown className="text-yellow-400 animate-bounce" size={48} />
            </div>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4">
            🐒 HANUMAN S'ÉVEILLE
          </h1>
          <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-8`}>
            Invocation des énergies divines pour Retreat And Be...
          </p>
          <div className="flex justify-center space-x-4 text-2xl">
            <span className="animate-pulse">🕉️</span>
            <span className="animate-pulse delay-100">✨</span>
            <span className="animate-pulse delay-200">🙏</span>
          </div>
          <p className={`text-sm mt-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            AUM HANUMATE NAMAHA
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>

      {/* Header Principal */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg border-b-2 border-yellow-400/20`}>
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">

            {/* Logo et État Hanuman */}
            <div className="flex items-center space-x-4">
              <div className={`w-12 h-12 bg-gradient-to-br ${currentTab.color} rounded-full flex items-center justify-center shadow-lg`}>
                <Crown className="text-white" size={24} />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                  🐒 HANUMAN • Retreat And Be
                </h1>
                <div className="flex items-center space-x-4 text-sm">
                  <span className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    Conscience: {hanumanState.consciousness.toFixed(1)}%
                  </span>
                  <span className={`${getMoodColor(hanumanState.currentMood)}`}>
                    {getMoodEmoji(hanumanState.currentMood)} {hanumanState.currentMood}
                  </span>
                  <span className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {cosmicTime.toLocaleTimeString('fr-FR')}
                  </span>
                </div>
              </div>
            </div>

            {/* Contrôles */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <Sun className={`${!darkMode ? 'text-yellow-500' : 'text-gray-400'}`} size={16} />
                <button
                  onClick={() => setDarkMode(!darkMode)}
                  className={`w-12 h-6 rounded-full transition-colors ${
                    darkMode ? 'bg-blue-600' : 'bg-gray-300'
                  } relative`}
                >
                  <div className={`w-5 h-5 bg-white rounded-full shadow-md transform transition-transform ${
                    darkMode ? 'translate-x-6' : 'translate-x-0.5'
                  } absolute top-0.5`}></div>
                </button>
                <Moon className={`${darkMode ? 'text-blue-400' : 'text-gray-400'}`} size={16} />
              </div>

              <div className="flex items-center space-x-2 px-3 py-2 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-lg">
                <Heart size={16} />
                <span className="text-sm font-medium">Dévotion: {hanumanState.devotion}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex h-screen">

        {/* Sidebar Navigation */}
        <div className={`w-80 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg border-r border-gray-200 dark:border-gray-700 overflow-y-auto`}>
          <div className="p-6">
            <h2 className={`text-lg font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'} flex items-center`}>
              <Brain className="text-blue-400 mr-2" size={20} />
              Anatomie Divine Hanuman
            </h2>

            {/* Contrôle Central */}
            <div className="mb-6">
              <h3 className={`text-sm font-semibold mb-3 ${darkMode ? 'text-gray-300' : 'text-gray-700'} flex items-center`}>
                <Crown className="text-yellow-400 mr-2" size={16} />
                Contrôle Central
              </h3>
              <div className="space-y-2">
                {organTabs.filter(tab => ['orchestrator', 'trimurti'].includes(tab.id)).map((tab) => {
                  const Icon = tab.icon;
                  const isActive = activeTab === tab.id;

                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full p-3 rounded-lg text-left transition-all duration-200 ${
                        isActive
                          ? `bg-gradient-to-r ${tab.color} text-white shadow-lg transform scale-105`
                          : `${darkMode ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'}`
                      }`}
                    >
                      <div className="flex items-center space-x-3 mb-1">
                        <Icon size={18} className={isActive ? 'text-white' : getStatusColor(tab.status)} />
                        <span className="font-medium text-sm">{tab.name}</span>
                        <div className="ml-auto flex items-center space-x-1">
                          {tab.status === 'blessed' && <Star size={12} className="text-yellow-400" />}
                        </div>
                      </div>
                      <p className={`text-xs ${isActive ? 'text-white/80' : darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {tab.description}
                      </p>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Organes Sensoriels */}
            <div className="mb-6">
              <h3 className={`text-sm font-semibold mb-3 ${darkMode ? 'text-gray-300' : 'text-gray-700'} flex items-center`}>
                <Eye className="text-blue-400 mr-2" size={16} />
                Organes Sensoriels
              </h3>
              <div className="space-y-2">
                {organTabs.filter(tab => ['vision', 'hearing', 'touch'].includes(tab.id)).map((tab) => {
                  const Icon = tab.icon;
                  const isActive = activeTab === tab.id;

                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full p-3 rounded-lg text-left transition-all duration-200 ${
                        isActive
                          ? `bg-gradient-to-r ${tab.color} text-white shadow-lg transform scale-105`
                          : `${darkMode ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'}`
                      }`}
                    >
                      <div className="flex items-center space-x-3 mb-1">
                        <Icon size={18} className={isActive ? 'text-white' : getStatusColor(tab.status)} />
                        <span className="font-medium text-sm">{tab.name}</span>
                        <div className="ml-auto flex items-center space-x-1">
                          {tab.status === 'active' && <Zap size={12} className="text-green-400" />}
                        </div>
                      </div>
                      <p className={`text-xs ${isActive ? 'text-white/80' : darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {tab.description}
                      </p>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Aires Spécialisées */}
            <div className="mb-6">
              <h3 className={`text-sm font-semibold mb-3 ${darkMode ? 'text-gray-300' : 'text-gray-700'} flex items-center`}>
                <MessageSquare className="text-purple-400 mr-2" size={16} />
                Aires Spécialisées
              </h3>
              <div className="space-y-2">
                {organTabs.filter(tab => ['broca', 'wernicke', 'motor-cortex'].includes(tab.id)).map((tab) => {
                  const Icon = tab.icon;
                  const isActive = activeTab === tab.id;

                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full p-3 rounded-lg text-left transition-all duration-200 ${
                        isActive
                          ? `bg-gradient-to-r ${tab.color} text-white shadow-lg transform scale-105`
                          : `${darkMode ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'}`
                      }`}
                    >
                      <div className="flex items-center space-x-3 mb-1">
                        <Icon size={18} className={isActive ? 'text-white' : getStatusColor(tab.status)} />
                        <span className="font-medium text-sm">{tab.name}</span>
                        <div className="ml-auto flex items-center space-x-1">
                          {tab.status === 'blessed' && <Star size={12} className="text-yellow-400" />}
                        </div>
                      </div>
                      <p className={`text-xs ${isActive ? 'text-white/80' : darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {tab.description}
                      </p>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Système Nerveux Adaptatif */}
            <div className="mb-6">
              <h3 className={`text-sm font-semibold mb-3 ${darkMode ? 'text-gray-300' : 'text-gray-700'} flex items-center`}>
                <Brain className="text-pink-400 mr-2" size={16} />
                Système Nerveux Adaptatif
              </h3>
              <div className="space-y-2">
                {organTabs.filter(tab => ['neuroplasticity', 'memory', 'immune', 'healing'].includes(tab.id)).map((tab) => {
                  const Icon = tab.icon;
                  const isActive = activeTab === tab.id;

                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full p-3 rounded-lg text-left transition-all duration-200 ${
                        isActive
                          ? `bg-gradient-to-r ${tab.color} text-white shadow-lg transform scale-105`
                          : `${darkMode ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'}`
                      }`}
                    >
                      <div className="flex items-center space-x-3 mb-1">
                        <Icon size={18} className={isActive ? 'text-white' : getStatusColor(tab.status)} />
                        <span className="font-medium text-sm">{tab.name}</span>
                        <div className="ml-auto flex items-center space-x-1">
                          {tab.status === 'blessed' && <Star size={12} className="text-yellow-400" />}
                        </div>
                      </div>
                      <p className={`text-xs ${isActive ? 'text-white/80' : darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {tab.description}
                      </p>
                      <div className="mt-2">
                        <div className={`w-full rounded-full h-1 ${isActive ? 'bg-white/20' : 'bg-gray-300 dark:bg-gray-600'}`}>
                          <div
                            className={`h-1 rounded-full transition-all duration-1000 ${
                              isActive ? 'bg-white' :
                              tab.divineEnergy > 90 ? 'bg-yellow-400' :
                              tab.divineEnergy > 70 ? 'bg-green-500' :
                              tab.divineEnergy > 50 ? 'bg-blue-500' : 'bg-gray-400'
                            }`}
                            style={{ width: `${tab.divineEnergy}%` }}
                          ></div>
                        </div>
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>

          {/* État Spirituel */}
          <div className={`p-6 border-t border-gray-200 dark:border-gray-700`}>
            <h3 className={`text-sm font-bold mb-3 ${darkMode ? 'text-white' : 'text-gray-900'} flex items-center`}>
              <Star className="text-yellow-400 mr-2" size={16} />
              État Spirituel
            </h3>

            <div className="space-y-3">
              {Object.entries(hanumanState).map(([key, value]) => {
                if (typeof value !== 'number') return null;

                const labels = {
                  consciousness: 'Conscience',
                  devotion: 'Dévotion',
                  wisdom: 'Sagesse',
                  power: 'Pouvoir'
                };

                const colors = {
                  consciousness: 'bg-blue-500',
                  devotion: 'bg-red-500',
                  wisdom: 'bg-purple-500',
                  power: 'bg-orange-500'
                };

                return (
                  <div key={key}>
                    <div className="flex items-center justify-between text-xs mb-1">
                      <span className={darkMode ? 'text-gray-400' : 'text-gray-600'}>
                        {labels[key as keyof typeof labels]}
                      </span>
                      <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {value.toFixed(1)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1">
                      <div
                        className={`h-1 rounded-full ${colors[key as keyof typeof colors]}`}
                        style={{ width: `${value}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Contenu Principal */}
        <div className="flex-1 overflow-hidden">
          <CurrentComponent darkMode={darkMode} />
        </div>
      </div>
    </div>
  );
};

export default HanumanCentralHub;
