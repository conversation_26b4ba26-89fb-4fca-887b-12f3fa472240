import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>ext, BookOpen, Edit3, Eye, Download, Upload, Search, Filter, CheckCircle, AlertCircle, Clock, BarChart3, Wifi, WifiOff, Zap } from 'lucide-react';

// Interfaces pour l'Agent Documentation (Aire de Wernicke)
interface DocumentationItem {
  id: string;
  title: string;
  type: 'api' | 'guide' | 'tutorial' | 'reference' | 'changelog' | 'readme';
  content: string;
  language: string;
  status: 'draft' | 'review' | 'published' | 'outdated';
  lastUpdated: Date;
  author: string;
  tags: string[];
  readability: number;
  completeness: number;
  accuracy: number;
}

interface DocumentationMetrics {
  totalDocuments: number;
  publishedDocuments: number;
  averageReadability: number;
  averageCompleteness: number;
  documentationCoverage: number;
  lastGenerated: Date;
}

interface CodeAnalysis {
  id: string;
  filePath: string;
  functions: number;
  classes: number;
  documented: number;
  coverage: number;
  complexity: number;
  suggestions: string[];
}

interface GenerationRequest {
  id: string;
  type: 'api-docs' | 'user-guide' | 'technical-spec' | 'readme' | 'changelog';
  source: string;
  target: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  timestamp: Date;
  result?: DocumentationItem;
}

const HanumanWernickeInterface = ({ darkMode = true }) => {
  const [documents, setDocuments] = useState<DocumentationItem[]>([]);
  const [metrics, setMetrics] = useState<DocumentationMetrics>({
    totalDocuments: 247,
    publishedDocuments: 189,
    averageReadability: 87.3,
    averageCompleteness: 92.1,
    documentationCoverage: 78.5,
    lastGenerated: new Date()
  });
  const [codeAnalysis, setCodeAnalysis] = useState<CodeAnalysis[]>([]);
  const [generationRequests, setGenerationRequests] = useState<GenerationRequest[]>([]);
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const wsRef = useRef<WebSocket | null>(null);

  // Connexion à l'Agent Documentation
  useEffect(() => {
    const connectToDocumentationAgent = () => {
      try {
        wsRef.current = new WebSocket('ws://localhost:3005/documentation');
        
        wsRef.current.onopen = () => {
          console.log('🔗 Connexion établie avec Agent Documentation');
          setIsConnected(true);
          
          wsRef.current?.send(JSON.stringify({
            type: 'GET_DOCUMENTATION_STATUS',
            timestamp: Date.now()
          }));
        };

        wsRef.current.onmessage = (event) => {
          const data = JSON.parse(event.data);
          handleDocumentationMessage(data);
        };

        wsRef.current.onclose = () => {
          console.log('❌ Connexion fermée avec Agent Documentation');
          setIsConnected(false);
          setTimeout(connectToDocumentationAgent, 5000);
        };

        wsRef.current.onerror = (error) => {
          console.error('❌ Erreur WebSocket documentation:', error);
        };

      } catch (error) {
        console.error('❌ Erreur de connexion documentation:', error);
        setTimeout(connectToDocumentationAgent, 5000);
      }
    };

    connectToDocumentationAgent();
    return () => wsRef.current?.close();
  }, []);

  // Simulation de données en temps réel
  useEffect(() => {
    const interval = setInterval(() => {
      simulateDocumentationActivity();
      updateMetrics();
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleDocumentationMessage = (data: any) => {
    switch (data.type) {
      case 'DOCUMENTATION_GENERATED':
        addGeneratedDocument(data.document);
        break;
      case 'CODE_ANALYSIS_COMPLETED':
        updateCodeAnalysis(data.analysis);
        break;
      case 'METRICS_UPDATE':
        setMetrics(data.metrics);
        break;
      case 'GENERATION_STATUS_UPDATE':
        updateGenerationStatus(data.requestId, data.status, data.result);
        break;
    }
  };

  const simulateDocumentationActivity = () => {
    // Simulation de génération de documentation
    const docTypes = ['api', 'guide', 'tutorial', 'reference', 'changelog', 'readme'] as const;
    const statuses = ['draft', 'review', 'published'] as const;
    
    const mockDoc: DocumentationItem = {
      id: `doc_${Date.now()}`,
      title: generateDocTitle(),
      type: docTypes[Math.floor(Math.random() * docTypes.length)],
      content: generateDocContent(),
      language: 'fr',
      status: statuses[Math.floor(Math.random() * statuses.length)],
      lastUpdated: new Date(),
      author: 'Agent Documentation',
      tags: generateTags(),
      readability: 80 + Math.random() * 20,
      completeness: 85 + Math.random() * 15,
      accuracy: 90 + Math.random() * 10
    };

    setDocuments(prev => [mockDoc, ...prev.slice(0, 49)]);
  };

  const generateDocTitle = (): string => {
    const titles = [
      'Guide d\'installation API REST',
      'Documentation technique microservices',
      'Manuel utilisateur interface admin',
      'Référence API authentification',
      'Tutoriel intégration webhook',
      'Guide déploiement production',
      'Documentation architecture système',
      'Manuel configuration base de données'
    ];
    return titles[Math.floor(Math.random() * titles.length)];
  };

  const generateDocContent = (): string => {
    return `# Documentation générée automatiquement

## Vue d'ensemble
Cette documentation a été générée automatiquement par l'Agent Documentation d'Hanuman.

## Fonctionnalités
- Génération automatique basée sur l'analyse du code
- Mise à jour en temps réel
- Support multilingue
- Validation de la qualité

## Utilisation
Consultez les sections suivantes pour plus de détails...

*Dernière mise à jour: ${new Date().toLocaleString()}*`;
  };

  const generateTags = (): string[] => {
    const allTags = ['api', 'backend', 'frontend', 'security', 'deployment', 'configuration', 'tutorial', 'guide'];
    const numTags = Math.floor(Math.random() * 4) + 1;
    return allTags.sort(() => 0.5 - Math.random()).slice(0, numTags);
  };

  const addGeneratedDocument = (document: DocumentationItem) => {
    setDocuments(prev => [document, ...prev]);
  };

  const updateCodeAnalysis = (analysis: CodeAnalysis) => {
    setCodeAnalysis(prev => {
      const existing = prev.find(a => a.id === analysis.id);
      if (existing) {
        return prev.map(a => a.id === analysis.id ? analysis : a);
      } else {
        return [analysis, ...prev.slice(0, 19)];
      }
    });
  };

  const updateGenerationStatus = (requestId: string, status: string, result?: DocumentationItem) => {
    setGenerationRequests(prev => prev.map(req => 
      req.id === requestId ? { ...req, status: status as any, result } : req
    ));
  };

  const updateMetrics = () => {
    setMetrics(prev => ({
      ...prev,
      totalDocuments: prev.totalDocuments + Math.floor(Math.random() * 2),
      averageReadability: Math.max(80, Math.min(95, prev.averageReadability + (Math.random() - 0.5) * 2)),
      averageCompleteness: Math.max(85, Math.min(98, prev.averageCompleteness + (Math.random() - 0.5) * 1)),
      documentationCoverage: Math.max(70, Math.min(95, prev.documentationCoverage + (Math.random() - 0.5) * 1)),
      lastGenerated: new Date()
    }));
  };

  const requestDocumentGeneration = (type: string) => {
    const request: GenerationRequest = {
      id: `gen_${Date.now()}`,
      type: type as any,
      source: 'Manual Request',
      target: 'Documentation Portal',
      priority: 'medium',
      status: 'processing',
      timestamp: new Date()
    };

    setGenerationRequests(prev => [request, ...prev]);
    setIsGenerating(true);

    // Envoyer via WebSocket si connecté
    if (wsRef.current && isConnected) {
      wsRef.current.send(JSON.stringify({
        type: 'GENERATE_DOCUMENTATION',
        request
      }));
    }

    // Simulation de génération
    setTimeout(() => {
      const generatedDoc: DocumentationItem = {
        id: `doc_${Date.now()}`,
        title: `${type.charAt(0).toUpperCase() + type.slice(1)} généré automatiquement`,
        type: type as any,
        content: generateDocContent(),
        language: 'fr',
        status: 'draft',
        lastUpdated: new Date(),
        author: 'Agent Documentation',
        tags: [type, 'auto-generated'],
        readability: 85 + Math.random() * 10,
        completeness: 90 + Math.random() * 10,
        accuracy: 95 + Math.random() * 5
      };

      updateGenerationStatus(request.id, 'completed', generatedDoc);
      addGeneratedDocument(generatedDoc);
      setIsGenerating(false);
    }, 3000);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published': return <CheckCircle size={14} className="text-green-400" />;
      case 'review': return <Eye size={14} className="text-yellow-400" />;
      case 'draft': return <Edit3 size={14} className="text-blue-400" />;
      case 'outdated': return <AlertCircle size={14} className="text-red-400" />;
      default: return <FileText size={14} className="text-gray-400" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'api': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'guide': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'tutorial': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'reference': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'changelog': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'readme': return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const getQualityColor = (score: number) => {
    if (score >= 90) return 'text-green-500';
    if (score >= 80) return 'text-yellow-500';
    if (score >= 70) return 'text-orange-500';
    return 'text-red-500';
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesType = selectedType === 'all' || doc.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || doc.status === selectedStatus;
    const matchesSearch = searchQuery === '' || 
      doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesType && matchesStatus && matchesSearch;
  });

  return (
    <div className={`h-full ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'} overflow-hidden`}>
      {/* Header */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 border-b border-gray-200 dark:border-gray-700`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-full flex items-center justify-center">
              <FileText className="text-white" size={24} />
            </div>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-400 to-purple-500 bg-clip-text text-transparent">
                📚 Aire de Wernicke • Documentation Divine
              </h1>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Documentation automatique via Agent Documentation
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 
              'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                Agent Documentation {isConnected ? 'Connecté' : 'Déconnecté'}
              </span>
            </div>
            
            {isGenerating && (
              <div className="flex items-center space-x-2 px-3 py-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-lg">
                <Zap size={16} className="animate-pulse" />
                <span className="text-sm font-medium">Génération en cours...</span>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="flex h-full">
        {/* Sidebar Métriques et Contrôles */}
        <div className={`w-80 ${darkMode ? 'bg-gray-800' : 'bg-white'} border-r border-gray-200 dark:border-gray-700 p-6 overflow-y-auto`}>
          {/* Métriques Globales */}
          <div className="mb-6">
            <h3 className="text-lg font-bold mb-4 flex items-center">
              <BarChart3 className="text-indigo-400 mr-2" size={20} />
              Métriques Documentation
            </h3>
            
            <div className="grid grid-cols-2 gap-3">
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-xs text-gray-500 mb-1">Total Docs</div>
                <div className="text-lg font-bold text-indigo-400">{metrics.totalDocuments}</div>
              </div>
              
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-xs text-gray-500 mb-1">Publiés</div>
                <div className="text-lg font-bold text-green-400">{metrics.publishedDocuments}</div>
              </div>
              
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-xs text-gray-500 mb-1">Lisibilité</div>
                <div className="text-lg font-bold text-blue-400">{metrics.averageReadability.toFixed(1)}%</div>
              </div>
              
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-xs text-gray-500 mb-1">Couverture</div>
                <div className="text-lg font-bold text-purple-400">{metrics.documentationCoverage.toFixed(1)}%</div>
              </div>
            </div>
          </div>

          {/* Génération Rapide */}
          <div className="mb-6">
            <h3 className="text-sm font-bold mb-3 flex items-center">
              <Zap className="text-indigo-400 mr-2" size={16} />
              Génération Rapide
            </h3>
            
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={() => requestDocumentGeneration('api')}
                disabled={isGenerating}
                className="p-2 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded hover:bg-blue-200 dark:hover:bg-blue-800 disabled:opacity-50"
              >
                API Docs
              </button>
              <button
                onClick={() => requestDocumentGeneration('guide')}
                disabled={isGenerating}
                className="p-2 text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded hover:bg-green-200 dark:hover:bg-green-800 disabled:opacity-50"
              >
                Guide
              </button>
              <button
                onClick={() => requestDocumentGeneration('tutorial')}
                disabled={isGenerating}
                className="p-2 text-xs bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded hover:bg-purple-200 dark:hover:bg-purple-800 disabled:opacity-50"
              >
                Tutorial
              </button>
              <button
                onClick={() => requestDocumentGeneration('readme')}
                disabled={isGenerating}
                className="p-2 text-xs bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200 rounded hover:bg-pink-200 dark:hover:bg-pink-800 disabled:opacity-50"
              >
                README
              </button>
            </div>
          </div>

          {/* Filtres */}
          <div className="mb-6">
            <h3 className="text-sm font-bold mb-3 flex items-center">
              <Filter className="text-indigo-400 mr-2" size={16} />
              Filtres
            </h3>
            
            <div className="space-y-3">
              <div>
                <label className="text-xs text-gray-500 mb-1 block">Recherche</label>
                <div className="relative">
                  <Search size={16} className="absolute left-3 top-2.5 text-gray-400" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Rechercher..."
                    className={`w-full pl-10 pr-3 py-2 rounded border text-sm ${
                      darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'
                    }`}
                  />
                </div>
              </div>
              
              <div>
                <label className="text-xs text-gray-500 mb-1 block">Type</label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className={`w-full p-2 rounded border text-sm ${
                    darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'
                  }`}
                >
                  <option value="all">Tous les types</option>
                  <option value="api">API</option>
                  <option value="guide">Guide</option>
                  <option value="tutorial">Tutorial</option>
                  <option value="reference">Référence</option>
                  <option value="changelog">Changelog</option>
                  <option value="readme">README</option>
                </select>
              </div>
              
              <div>
                <label className="text-xs text-gray-500 mb-1 block">Statut</label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className={`w-full p-2 rounded border text-sm ${
                    darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'
                  }`}
                >
                  <option value="all">Tous les statuts</option>
                  <option value="draft">Brouillon</option>
                  <option value="review">En révision</option>
                  <option value="published">Publié</option>
                  <option value="outdated">Obsolète</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Zone Documentation */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-bold">Documentation Générée</h2>
            <div className="text-sm text-gray-500">
              {filteredDocuments.length} documents
            </div>
          </div>

          {filteredDocuments.length === 0 ? (
            <div className="text-center text-gray-500 mt-20">
              <BookOpen size={48} className="mx-auto mb-4 opacity-50" />
              <p>Aucune documentation trouvée</p>
            </div>
          ) : (
            filteredDocuments.map((doc) => (
              <div
                key={doc.id}
                className={`p-4 rounded-lg border ${
                  darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
                } hover:shadow-lg transition-shadow`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(doc.status)}
                    <div>
                      <h3 className="font-medium text-lg">{doc.title}</h3>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className={`text-xs px-2 py-1 rounded ${getTypeColor(doc.type)}`}>
                          {doc.type}
                        </span>
                        <span className="text-xs text-gray-500">
                          par {doc.author}
                        </span>
                        <span className="text-xs text-gray-500">
                          {doc.lastUpdated.toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                      <Eye size={16} />
                    </button>
                    <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                      <Download size={16} />
                    </button>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 mb-3">
                  <div className="text-center">
                    <div className="text-xs text-gray-500">Lisibilité</div>
                    <div className={`text-sm font-bold ${getQualityColor(doc.readability)}`}>
                      {doc.readability.toFixed(1)}%
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-gray-500">Complétude</div>
                    <div className={`text-sm font-bold ${getQualityColor(doc.completeness)}`}>
                      {doc.completeness.toFixed(1)}%
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-gray-500">Précision</div>
                    <div className={`text-sm font-bold ${getQualityColor(doc.accuracy)}`}>
                      {doc.accuracy.toFixed(1)}%
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-1">
                    {doc.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                  
                  <span className={`text-xs px-2 py-1 rounded ${
                    doc.status === 'published' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                    doc.status === 'review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                    doc.status === 'draft' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                    'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}>
                    {doc.status}
                  </span>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default HanumanWernickeInterface;
