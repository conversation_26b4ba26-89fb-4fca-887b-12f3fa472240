import React, { useState, useEffect, useRef } from 'react';
import { MessageSquare, Send, Users, Activity, Zap, AlertCircle, CheckCircle, Clock, BarChart3, Wifi, WifiOff, Volume2, Mic, MicOff } from 'lucide-react';

// Interfaces pour la communication inter-agents
interface AgentMessage {
  id: string;
  fromAgent: string;
  toAgent: string;
  content: string;
  type: 'command' | 'response' | 'notification' | 'error' | 'heartbeat';
  priority: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  status: 'sent' | 'delivered' | 'processed' | 'failed';
  metadata?: Record<string, any>;
}

interface CommunicationChannel {
  id: string;
  name: string;
  type: 'kafka' | 'redis' | 'websocket' | 'direct';
  status: 'active' | 'inactive' | 'error';
  messageCount: number;
  latency: number;
  throughput: number;
  lastActivity: Date;
}

interface AgentStatus {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'busy' | 'error';
  lastSeen: Date;
  messagesSent: number;
  messagesReceived: number;
  responseTime: number;
  load: number;
}

interface CommunicationMetrics {
  totalMessages: number;
  activeChannels: number;
  averageLatency: number;
  throughput: number;
  errorRate: number;
  onlineAgents: number;
}

const HanumanBrocaInterface = ({ darkMode = true }) => {
  const [messages, setMessages] = useState<AgentMessage[]>([]);
  const [channels, setChannels] = useState<CommunicationChannel[]>([]);
  const [agents, setAgents] = useState<AgentStatus[]>([]);
  const [metrics, setMetrics] = useState<CommunicationMetrics>({
    totalMessages: 15847,
    activeChannels: 8,
    averageLatency: 45,
    throughput: 234,
    errorRate: 0.3,
    onlineAgents: 12
  });
  const [selectedAgent, setSelectedAgent] = useState<string>('all');
  const [messageFilter, setMessageFilter] = useState<string>('all');
  const [isConnected, setIsConnected] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const [isListening, setIsListening] = useState(true);
  const wsRef = useRef<WebSocket | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Connexion aux services de communication
  useEffect(() => {
    const connectToCommunicationServices = () => {
      try {
        wsRef.current = new WebSocket('ws://localhost:3003/broca');

        wsRef.current.onopen = () => {
          console.log('🔗 Connexion établie avec les services de communication');
          setIsConnected(true);

          wsRef.current?.send(JSON.stringify({
            type: 'GET_COMMUNICATION_STATUS',
            timestamp: Date.now()
          }));
        };

        wsRef.current.onmessage = (event) => {
          const data = JSON.parse(event.data);
          handleCommunicationMessage(data);
        };

        wsRef.current.onclose = () => {
          console.log('❌ Connexion fermée avec les services de communication');
          setIsConnected(false);
          setTimeout(connectToCommunicationServices, 5000);
        };

        wsRef.current.onerror = (error) => {
          console.error('❌ Erreur WebSocket communication:', error);
        };

      } catch (error) {
        console.error('❌ Erreur de connexion communication:', error);
        setTimeout(connectToCommunicationServices, 5000);
      }
    };

    connectToCommunicationServices();
    return () => wsRef.current?.close();
  }, []);

  // Simulation de données en temps réel
  useEffect(() => {
    const interval = setInterval(() => {
      if (isListening) {
        simulateAgentCommunication();
        updateMetrics();
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [isListening]);

  // Auto-scroll vers le bas
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleCommunicationMessage = (data: any) => {
    switch (data.type) {
      case 'AGENT_MESSAGE':
        setMessages(prev => [data.message, ...prev.slice(0, 99)]);
        break;
      case 'AGENT_STATUS_UPDATE':
        updateAgentStatus(data.agent);
        break;
      case 'CHANNEL_UPDATE':
        updateChannelStatus(data.channel);
        break;
      case 'METRICS_UPDATE':
        setMetrics(data.metrics);
        break;
    }
  };

  const simulateAgentCommunication = () => {
    const agentNames = ['Frontend', 'Backend', 'DevOps', 'QA', 'Security', 'Evolution', 'Documentation'];
    const messageTypes = ['command', 'response', 'notification', 'heartbeat'] as const;
    const priorities = ['low', 'medium', 'high'] as const;

    const fromAgent = agentNames[Math.floor(Math.random() * agentNames.length)];
    const toAgent = agentNames[Math.floor(Math.random() * agentNames.length)];

    if (fromAgent !== toAgent) {
      const mockMessage: AgentMessage = {
        id: `msg_${Date.now()}`,
        fromAgent,
        toAgent,
        content: generateMessageContent(messageTypes[Math.floor(Math.random() * messageTypes.length)]),
        type: messageTypes[Math.floor(Math.random() * messageTypes.length)],
        priority: priorities[Math.floor(Math.random() * priorities.length)],
        timestamp: new Date(),
        status: 'delivered'
      };

      setMessages(prev => [mockMessage, ...prev.slice(0, 99)]);
    }
  };

  const generateMessageContent = (type: string): string => {
    const contents = {
      command: [
        'Démarrer analyse de performance',
        'Exécuter tests d\'intégration',
        'Déployer version 1.2.3',
        'Générer rapport de sécurité',
        'Optimiser base de données'
      ],
      response: [
        'Analyse terminée avec succès',
        'Tests passés: 98/100',
        'Déploiement en cours...',
        'Rapport généré et envoyé',
        'Optimisation appliquée'
      ],
      notification: [
        'Nouveau commit détecté',
        'Alerte de sécurité résolue',
        'Performance améliorée de 15%',
        'Documentation mise à jour',
        'Backup automatique effectué'
      ],
      heartbeat: [
        'Agent opérationnel',
        'Statut: OK',
        'Ressources disponibles',
        'Prêt pour nouvelles tâches',
        'Système stable'
      ]
    };

    const typeContents = contents[type as keyof typeof contents] || contents.notification;
    return typeContents[Math.floor(Math.random() * typeContents.length)];
  };

  const updateAgentStatus = (agentData: any) => {
    setAgents(prev => {
      const existing = prev.find(a => a.id === agentData.id);
      if (existing) {
        return prev.map(a => a.id === agentData.id ? { ...a, ...agentData } : a);
      } else {
        return [...prev, agentData];
      }
    });
  };

  const updateChannelStatus = (channelData: any) => {
    setChannels(prev => {
      const existing = prev.find(c => c.id === channelData.id);
      if (existing) {
        return prev.map(c => c.id === channelData.id ? { ...c, ...channelData } : c);
      } else {
        return [...prev, channelData];
      }
    });
  };

  const updateMetrics = () => {
    setMetrics(prev => ({
      ...prev,
      totalMessages: prev.totalMessages + Math.floor(Math.random() * 5),
      averageLatency: Math.max(20, prev.averageLatency + (Math.random() - 0.5) * 10),
      throughput: Math.max(100, prev.throughput + (Math.random() - 0.5) * 50),
      errorRate: Math.max(0, Math.min(5, prev.errorRate + (Math.random() - 0.5) * 0.2))
    }));
  };

  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const message: AgentMessage = {
      id: `manual_${Date.now()}`,
      fromAgent: 'Hanuman',
      toAgent: selectedAgent === 'all' ? 'Broadcast' : selectedAgent,
      content: newMessage,
      type: 'command',
      priority: 'medium',
      timestamp: new Date(),
      status: 'sent'
    };

    setMessages(prev => [message, ...prev]);
    setNewMessage('');

    // Envoyer via WebSocket si connecté
    if (wsRef.current && isConnected) {
      wsRef.current.send(JSON.stringify({
        type: 'SEND_MESSAGE',
        message
      }));
    }
  };

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'command': return <Send size={14} className="text-blue-400" />;
      case 'response': return <CheckCircle size={14} className="text-green-400" />;
      case 'notification': return <AlertCircle size={14} className="text-yellow-400" />;
      case 'error': return <AlertCircle size={14} className="text-red-400" />;
      case 'heartbeat': return <Activity size={14} className="text-gray-400" />;
      default: return <MessageSquare size={14} className="text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'border-l-red-500 bg-red-50 dark:bg-red-900/20';
      case 'high': return 'border-l-orange-500 bg-orange-50 dark:bg-orange-900/20';
      case 'medium': return 'border-l-yellow-500 bg-yellow-50 dark:bg-yellow-900/20';
      case 'low': return 'border-l-green-500 bg-green-50 dark:bg-green-900/20';
      default: return 'border-l-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const filteredMessages = messages.filter(msg => {
    if (messageFilter === 'all') return true;
    if (selectedAgent === 'all') return msg.type === messageFilter;
    return (msg.fromAgent === selectedAgent || msg.toAgent === selectedAgent) &&
           (messageFilter === 'all' || msg.type === messageFilter);
  });

  return (
    <div className={`h-full ${darkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'} overflow-hidden`}>
      {/* Header */}
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 border-b border-gray-200 dark:border-gray-700`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full flex items-center justify-center">
              <MessageSquare className="text-white" size={24} />
            </div>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent">
                🗣️ Aire de Broca • Communication Divine
              </h1>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Communication multilingue via Agent Translation
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
              'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                {isConnected ? 'Connecté' : 'Déconnecté'}
              </span>
            </div>

            <button
              onClick={() => setIsListening(!isListening)}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                isListening ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' :
                'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
              }`}
            >
              {isListening ? <Mic size={16} /> : <MicOff size={16} />}
              <span className="text-sm font-medium">
                {isListening ? 'Écoute Active' : 'Écoute Suspendue'}
              </span>
            </button>
          </div>
        </div>
      </div>

      <div className="flex h-full">
        {/* Sidebar Métriques */}
        <div className={`w-80 ${darkMode ? 'bg-gray-800' : 'bg-white'} border-r border-gray-200 dark:border-gray-700 p-6 overflow-y-auto`}>
          {/* Métriques Globales */}
          <div className="mb-6">
            <h3 className="text-lg font-bold mb-4 flex items-center">
              <BarChart3 className="text-purple-400 mr-2" size={20} />
              Métriques Communication
            </h3>

            <div className="grid grid-cols-2 gap-3">
              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-xs text-gray-500 mb-1">Messages Total</div>
                <div className="text-lg font-bold text-purple-400">{metrics.totalMessages.toLocaleString()}</div>
              </div>

              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-xs text-gray-500 mb-1">Agents En Ligne</div>
                <div className="text-lg font-bold text-green-400">{metrics.onlineAgents}</div>
              </div>

              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-xs text-gray-500 mb-1">Latence Moy.</div>
                <div className="text-lg font-bold text-blue-400">{metrics.averageLatency.toFixed(0)}ms</div>
              </div>

              <div className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                <div className="text-xs text-gray-500 mb-1">Taux d'Erreur</div>
                <div className="text-lg font-bold text-red-400">{metrics.errorRate.toFixed(1)}%</div>
              </div>
            </div>
          </div>

          {/* Filtres */}
          <div className="mb-6">
            <h3 className="text-sm font-bold mb-3">Filtres</h3>

            <div className="space-y-3">
              <div>
                <label className="text-xs text-gray-500 mb-1 block">Agent</label>
                <select
                  value={selectedAgent}
                  onChange={(e) => setSelectedAgent(e.target.value)}
                  className={`w-full p-2 rounded border text-sm ${
                    darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'
                  }`}
                >
                  <option value="all">Tous les agents</option>
                  <option value="Frontend">Frontend</option>
                  <option value="Backend">Backend</option>
                  <option value="DevOps">DevOps</option>
                  <option value="QA">QA</option>
                  <option value="Security">Security</option>
                  <option value="Evolution">Evolution</option>
                  <option value="Documentation">Documentation</option>
                </select>
              </div>

              <div>
                <label className="text-xs text-gray-500 mb-1 block">Type de Message</label>
                <select
                  value={messageFilter}
                  onChange={(e) => setMessageFilter(e.target.value)}
                  className={`w-full p-2 rounded border text-sm ${
                    darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'
                  }`}
                >
                  <option value="all">Tous les types</option>
                  <option value="command">Commandes</option>
                  <option value="response">Réponses</option>
                  <option value="notification">Notifications</option>
                  <option value="error">Erreurs</option>
                  <option value="heartbeat">Heartbeats</option>
                </select>
              </div>
            </div>
          </div>

          {/* Envoi de Message */}
          <div className="mb-6">
            <h3 className="text-sm font-bold mb-3">Envoyer Message</h3>

            <div className="space-y-3">
              <textarea
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Tapez votre message divin..."
                className={`w-full p-3 rounded border text-sm resize-none ${
                  darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'
                }`}
                rows={3}
              />

              <button
                onClick={sendMessage}
                disabled={!newMessage.trim()}
                className="w-full bg-gradient-to-r from-purple-400 to-pink-500 text-white py-2 px-4 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg transition-all"
              >
                <Send size={16} className="inline mr-2" />
                Envoyer
              </button>
            </div>
          </div>
        </div>

        {/* Zone Messages */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-6 space-y-3">
            {filteredMessages.length === 0 ? (
              <div className="text-center text-gray-500 mt-20">
                <MessageSquare size={48} className="mx-auto mb-4 opacity-50" />
                <p>Aucun message trouvé pour les filtres sélectionnés</p>
              </div>
            ) : (
              filteredMessages.map((message) => (
                <div
                  key={message.id}
                  className={`p-4 rounded-lg border-l-4 ${getPriorityColor(message.priority)} ${
                    darkMode ? 'border-gray-700' : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getMessageIcon(message.type)}
                      <span className="font-medium text-sm">
                        {message.fromAgent} → {message.toAgent}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded ${
                        message.priority === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                        message.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                        message.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                        'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      }`}>
                        {message.priority}
                      </span>
                    </div>

                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <Clock size={12} />
                      <span>{message.timestamp.toLocaleTimeString()}</span>
                    </div>
                  </div>

                  <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                    {message.content}
                  </p>

                  <div className="flex items-center justify-between text-xs">
                    <span className={`px-2 py-1 rounded ${
                      message.type === 'command' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      message.type === 'response' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      message.type === 'notification' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      message.type === 'error' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}>
                      {message.type}
                    </span>

                    <span className={`${
                      message.status === 'delivered' ? 'text-green-500' :
                      message.status === 'sent' ? 'text-blue-500' :
                      message.status === 'processed' ? 'text-purple-500' :
                      'text-red-500'
                    }`}>
                      {message.status}
                    </span>
                  </div>
                </div>
              ))
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanBrocaInterface;
