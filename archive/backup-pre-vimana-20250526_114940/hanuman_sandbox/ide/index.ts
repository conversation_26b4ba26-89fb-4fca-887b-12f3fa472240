/**
 * 💻 IDE HANUMAN - POINT D'ENTRÉE PRINCIPAL
 * =========================================
 * 
 * Environnement de développement intégré pour les agents Hanuman
 * Sprint 2: VS Code + Roo Coder + Templates + Git + Simulation
 * 
 * @version 2.0.0
 * <AUTHOR> Development Team
 */

// VS Code Server et Roo Coder
export { VSCodeServerManager, VSCodeInstance, VSCodeServerConfig, RooCoder } from './vscode/vscode_server_manager';
export { RooCoderIntegration, RooCoderTemplate, RooCoderPrompt, RooCoderConfig } from './vscode/roo_coder_integration';

// Templates
export { TemplateManager, ProjectTemplate, TemplateGenerationOptions } from './templates/template_manager';
export { TemplateInterface } from './templates/template_interface';

// Git
export { GitManager, GitRepository, GitStatus, GitCommit, GitBranch, GitConfig } from './git/git_manager';

// Simulateur d'environnement
export { EnvironmentSimulator, SimulationEnvironment, SimulatedService, HotReloadConfig } from './simulator/environment_simulator';

// Interface principale
export { IDEInterface } from './ide_interface';

import { EventEmitter } from 'events';
import { SandboxInfrastructure } from '../infrastructure/sandbox_infrastructure';
import { VSCodeServerManager } from './vscode/vscode_server_manager';
import { RooCoderIntegration } from './vscode/roo_coder_integration';
import { TemplateManager } from './templates/template_manager';
import { GitManager } from './git/git_manager';
import { EnvironmentSimulator } from './simulator/environment_simulator';

// Types pour l'IDE Hanuman
export interface HanumanIDEConfig {
  vscode: {
    enabled: boolean;
    defaultPort: number;
    autoStart: boolean;
    extensions: string[];
  };
  rooCoder: {
    enabled: boolean;
    model: 'gpt-4' | 'claude-3' | 'codellama';
    apiKey?: string;
    customPrompts: boolean;
  };
  templates: {
    enabled: boolean;
    autoLoad: boolean;
    customTemplates: boolean;
  };
  git: {
    enabled: boolean;
    autoCommit: boolean;
    autoCommitInterval: number;
  };
  simulator: {
    enabled: boolean;
    hotReload: boolean;
    monitoring: boolean;
  };
}

export interface IDEStats {
  vscodeInstances: number;
  activeProjects: number;
  templatesAvailable: number;
  gitRepositories: number;
  runningSimulations: number;
  totalAgents: number;
}

/**
 * Classe principale de l'IDE Hanuman
 * Orchestre tous les composants de l'environnement de développement
 */
export class HanumanIDE extends EventEmitter {
  private infrastructure: SandboxInfrastructure;
  private config: HanumanIDEConfig;
  private vscodeManager: VSCodeServerManager;
  private rooCoderIntegration: RooCoderIntegration;
  private templateManager: TemplateManager;
  private gitManager: GitManager;
  private environmentSimulator: EnvironmentSimulator;
  private isInitialized = false;

  constructor(infrastructure: SandboxInfrastructure, config?: Partial<HanumanIDEConfig>) {
    super();
    
    this.infrastructure = infrastructure;
    this.config = {
      vscode: {
        enabled: true,
        defaultPort: 8080,
        autoStart: true,
        extensions: [
          'ms-vscode.vscode-typescript-next',
          'esbenp.prettier-vscode',
          'ms-vscode.vscode-eslint',
          'roo-coder.roo-coder'
        ]
      },
      rooCoder: {
        enabled: true,
        model: 'gpt-4',
        customPrompts: true
      },
      templates: {
        enabled: true,
        autoLoad: true,
        customTemplates: true
      },
      git: {
        enabled: true,
        autoCommit: true,
        autoCommitInterval: 15
      },
      simulator: {
        enabled: true,
        hotReload: true,
        monitoring: true
      },
      ...config
    };

    this.initializeComponents();
  }

  /**
   * Initialise tous les composants de l'IDE
   */
  private async initializeComponents(): Promise<void> {
    try {
      console.log('💻 Initialisation de l\'IDE Hanuman...');

      // Initialiser VS Code Server Manager
      if (this.config.vscode.enabled) {
        this.vscodeManager = new VSCodeServerManager(this.infrastructure);
        console.log('✅ VS Code Server Manager initialisé');
      }

      // Initialiser Roo Coder Integration
      if (this.config.rooCoder.enabled) {
        this.rooCoderIntegration = new RooCoderIntegration();
        console.log('✅ Roo Coder Integration initialisé');
      }

      // Initialiser Template Manager
      if (this.config.templates.enabled) {
        this.templateManager = new TemplateManager();
        console.log('✅ Template Manager initialisé');
      }

      // Initialiser Git Manager
      if (this.config.git.enabled) {
        this.gitManager = new GitManager();
        console.log('✅ Git Manager initialisé');
      }

      // Initialiser Environment Simulator
      if (this.config.simulator.enabled) {
        this.environmentSimulator = new EnvironmentSimulator();
        console.log('✅ Environment Simulator initialisé');
      }

      // Configurer les événements
      this.setupEventHandlers();

      this.isInitialized = true;
      this.emit('ide:initialized');
      console.log('🎉 IDE Hanuman initialisé avec succès');

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation de l\'IDE:', error);
      this.emit('ide:error', error);
      throw error;
    }
  }

  /**
   * Configure les gestionnaires d'événements
   */
  private setupEventHandlers(): void {
    // Événements de l'infrastructure
    this.infrastructure.on('container:created', this.onContainerCreated.bind(this));
    this.infrastructure.on('container:destroyed', this.onContainerDestroyed.bind(this));

    // Événements VS Code
    if (this.vscodeManager) {
      this.vscodeManager.on('vscode:deployed', (instance) => {
        this.emit('ide:vscode-deployed', instance);
      });
      this.vscodeManager.on('vscode:error', (data) => {
        this.emit('ide:error', data.error);
      });
    }

    // Événements Git
    if (this.gitManager) {
      this.gitManager.on('repository:initialized', (repo) => {
        this.emit('ide:repository-created', repo);
      });
      this.gitManager.on('repository:committed', (data) => {
        this.emit('ide:commit-made', data);
      });
    }

    // Événements Template
    if (this.templateManager) {
      this.templateManager.on('generation:completed', (data) => {
        this.emit('ide:project-generated', data);
      });
    }

    // Événements Simulator
    if (this.environmentSimulator) {
      this.environmentSimulator.on('environment:created', (env) => {
        this.emit('ide:simulation-created', env);
      });
      this.environmentSimulator.on('environment:hot-reload', (data) => {
        this.emit('ide:hot-reload', data);
      });
    }
  }

  /**
   * Gestionnaire pour la création de conteneur
   */
  private async onContainerCreated(container: any): Promise<void> {
    if (container.type === 'development' && this.config.vscode.autoStart) {
      try {
        // Déployer automatiquement VS Code
        const vscodeInstance = await this.vscodeManager.deployVSCodeServer(container);
        
        // Configurer Roo Coder
        if (this.config.rooCoder.enabled) {
          await this.rooCoderIntegration.configureForInstance(vscodeInstance);
        }
        
        // Initialiser Git
        if (this.config.git.enabled) {
          await this.gitManager.initializeRepository(vscodeInstance);
        }
        
        // Créer l'environnement de simulation
        if (this.config.simulator.enabled) {
          await this.environmentSimulator.createEnvironment(container, vscodeInstance);
        }

        this.emit('ide:development-environment-ready', {
          container,
          vscodeInstance
        });

      } catch (error) {
        console.error('❌ Erreur lors de la configuration automatique:', error);
        this.emit('ide:error', error);
      }
    }
  }

  /**
   * Gestionnaire pour la destruction de conteneur
   */
  private async onContainerDestroyed(container: any): Promise<void> {
    // Nettoyer les ressources associées
    if (this.vscodeManager) {
      const instances = this.vscodeManager.getInstances();
      const instance = instances.find(i => i.containerId === container.id);
      if (instance) {
        await this.vscodeManager.stopVSCodeServer(instance.id);
      }
    }

    if (this.environmentSimulator) {
      const environments = this.environmentSimulator.getEnvironments();
      const environment = environments.find(e => e.containerId === container.id);
      if (environment) {
        await this.environmentSimulator.stopEnvironment(environment.id);
      }
    }
  }

  /**
   * Crée un nouveau projet de développement complet
   */
  async createDevelopmentProject(options: {
    name: string;
    agentId?: string;
    organId?: string;
    templateId?: string;
    gitRemote?: string;
    autoStart?: boolean;
  }): Promise<{
    container: any;
    vscodeInstance: any;
    repository?: any;
    simulation?: any;
  }> {
    if (!this.isInitialized) {
      throw new Error('IDE non initialisé');
    }

    try {
      console.log(`🚀 Création du projet de développement: ${options.name}`);

      // 1. Créer le conteneur
      const container = await this.infrastructure.createContainer({
        name: options.name,
        type: 'development',
        namespace: 'development',
        agentId: options.agentId,
        organId: options.organId,
        securityLevel: 'medium'
      });

      // 2. Déployer VS Code
      const vscodeInstance = await this.vscodeManager.deployVSCodeServer(container);

      // 3. Configurer Roo Coder
      if (this.config.rooCoder.enabled) {
        await this.rooCoderIntegration.configureForInstance(vscodeInstance);
      }

      // 4. Générer le projet à partir d'un template si spécifié
      if (options.templateId && this.config.templates.enabled) {
        await this.templateManager.generateProject({
          templateId: options.templateId,
          projectName: options.name,
          outputPath: vscodeInstance.config.workspaceFolder,
          variables: {
            agentName: options.agentId || 'NewAgent',
            organName: options.organId || 'NewOrgan',
            projectName: options.name
          },
          overwrite: true,
          dryRun: false
        });
      }

      // 5. Initialiser Git
      let repository;
      if (this.config.git.enabled) {
        repository = await this.gitManager.initializeRepository(vscodeInstance, {
          remoteUrl: options.gitRemote,
          autoCommit: this.config.git.autoCommit
        });
      }

      // 6. Créer l'environnement de simulation
      let simulation;
      if (this.config.simulator.enabled) {
        simulation = await this.environmentSimulator.createEnvironment(container, vscodeInstance);
      }

      const result = {
        container,
        vscodeInstance,
        repository,
        simulation
      };

      this.emit('ide:project-created', result);
      console.log(`✅ Projet de développement créé: ${options.name}`);

      return result;

    } catch (error) {
      console.error(`❌ Erreur lors de la création du projet:`, error);
      this.emit('ide:error', error);
      throw error;
    }
  }

  /**
   * Obtient les statistiques de l'IDE
   */
  getStats(): IDEStats {
    const vscodeInstances = this.vscodeManager?.getInstances() || [];
    const repositories = this.gitManager?.getRepositories() || [];
    const simulations = this.environmentSimulator?.getEnvironments() || [];
    const templates = this.templateManager?.getTemplates() || [];

    return {
      vscodeInstances: vscodeInstances.length,
      activeProjects: vscodeInstances.filter(i => i.status === 'running').length,
      templatesAvailable: templates.length,
      gitRepositories: repositories.length,
      runningSimulations: simulations.filter(s => s.status === 'running').length,
      totalAgents: new Set([
        ...vscodeInstances.map(i => i.agentId),
        ...repositories.map(r => r.agentId)
      ].filter(Boolean)).size
    };
  }

  /**
   * Obtient tous les composants de l'IDE
   */
  getComponents() {
    return {
      vscodeManager: this.vscodeManager,
      rooCoderIntegration: this.rooCoderIntegration,
      templateManager: this.templateManager,
      gitManager: this.gitManager,
      environmentSimulator: this.environmentSimulator
    };
  }

  /**
   * Met à jour la configuration de l'IDE
   */
  updateConfig(newConfig: Partial<HanumanIDEConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Appliquer les changements de configuration
    if (newConfig.git?.autoCommit !== undefined && this.gitManager) {
      this.gitManager.updateConfig({
        autoCommit: newConfig.git.autoCommit,
        autoCommitInterval: newConfig.git.autoCommitInterval
      });
    }

    this.emit('ide:config-updated', this.config);
  }

  /**
   * Obtient la configuration actuelle
   */
  getConfig(): HanumanIDEConfig {
    return { ...this.config };
  }

  /**
   * Vérifie si l'IDE est initialisé
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Nettoie les ressources de l'IDE
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Nettoyage de l\'IDE Hanuman...');

    if (this.gitManager) {
      this.gitManager.cleanup();
    }

    if (this.environmentSimulator) {
      this.environmentSimulator.cleanup();
    }

    this.removeAllListeners();
    this.isInitialized = false;

    console.log('✅ IDE Hanuman nettoyé');
  }
}

/**
 * Fonction utilitaire pour créer une instance de l'IDE
 */
export function createHanumanIDE(
  infrastructure: SandboxInfrastructure,
  config?: Partial<HanumanIDEConfig>
): HanumanIDE {
  return new HanumanIDE(infrastructure, config);
}

// Export par défaut
export default HanumanIDE;
