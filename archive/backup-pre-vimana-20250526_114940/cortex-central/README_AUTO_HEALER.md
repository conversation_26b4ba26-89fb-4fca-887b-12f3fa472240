# 🛡️ Auto-Healer - Système de Réparation Automatique

## Vue d'ensemble

L'Auto-Healer est un système intelligent de réparation automatique intégré au Cortex Central de l'architecture Retreat And Be. Il surveille en continu la santé du système, détecte automatiquement les anomalies et applique des stratégies de guérison intelligentes pour maintenir la disponibilité et les performances.

## 🎯 Fonctionnalités Principales

### 🔍 Détection Intelligente
- **Surveillance Continue** - Monitoring 24/7 des métriques système
- **Détection d'Anomalies** - Algorithmes avancés de détection d'anomalies
- **Analyse Prédictive** - Identification proactive des problèmes potentiels
- **Corrélation Multi-Métriques** - Analyse croisée des indicateurs de santé

### 🔧 Réparation Automatique
- **Stratégies Adaptatives** - Sélection intelligente des actions de guérison
- **Actions Graduelles** - Escalade progressive des interventions
- **Rollback Automatique** - Annulation automatique en cas d'échec
- **Gestion des Risques** - Évaluation et limitation des risques

### 🧠 Apprentissage Continu
- **Adaptation Intelligente** - Apprentissage à partir des succès et échecs
- **Optimisation des Stratégies** - Amélioration continue des performances
- **Règles Dynamiques** - Création automatique de nouvelles règles
- **Mémoire Persistante** - Conservation des connaissances acquises

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    AIImmuneSystem                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ AnomalyDetector │  │   AutoHealer    │  │AdaptationEng.│ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │ServiceController│  │HealingStrategies│                   │
│  └─────────────────┘  └─────────────────┘                   │
└─────────────────────────────────────────────────────────────┘
```

### Composants

- **AIImmuneSystem** - Orchestrateur principal
- **AutoHealer** - Moteur de réparation
- **AnomalyDetector** - Détecteur d'anomalies
- **ServiceController** - Contrôleur de services
- **HealingStrategies** - Gestionnaire de stratégies
- **AdaptationEngine** - Moteur d'apprentissage

## 🚀 Installation et Configuration

### Prérequis

```bash
# Node.js 18+
node --version

# TypeScript
npm install -g typescript

# Outils d'infrastructure (optionnels)
kubectl version  # Kubernetes
docker version   # Docker
systemctl --version  # systemd
```

### Installation

```bash
# Installation des dépendances
npm install

# Compilation TypeScript
npm run build

# Configuration
cp config/auto-healer.example.ts config/auto-healer.config.ts
```

### Configuration de Base

```typescript
import { AIImmuneSystem } from './src/immune/AIImmuneSystem';
import { CentralMemory } from './src/memory/CentralMemory';
import { SynapticCommunication } from './src/communication/SynapticCommunication';

const immuneSystem = new AIImmuneSystem({
  memory: new CentralMemory(),
  communication: new SynapticCommunication(),
  monitoringInterval: 30000,
  healingEnabled: true,
  adaptationEnabled: true
});

await immuneSystem.initialize();
```

## 📊 Types d'Anomalies Supportées

| Type | Description | Actions Typiques |
|------|-------------|------------------|
| `performance_degradation` | Dégradation des performances | Redémarrage, Optimisation |
| `high_error_rate` | Taux d'erreur élevé | Rollback, Redirection |
| `resource_exhaustion` | Épuisement des ressources | Mise à l'échelle, Nettoyage |
| `communication_failure` | Échec de communication | Redémarrage, Isolation |
| `agent_failure` | Défaillance d'agent | Redémarrage, Remplacement |
| `security_breach` | Violation de sécurité | Isolation, Notification |
| `service_unavailable` | Service indisponible | Redirection, Redémarrage |
| `data_corruption` | Corruption de données | Rollback, Restauration |

## 🎯 Stratégies de Guérison

### Stratégies Prédéfinies

1. **Redémarrage Simple** - Action de base pour la plupart des problèmes
2. **Mise à l'Échelle** - Augmentation des ressources
3. **Redirection de Trafic** - Basculement vers des services de secours
4. **Optimisation** - Ajustement des paramètres et nettoyage
5. **Isolation** - Quarantaine des composants problématiques
6. **Rollback** - Retour à une version stable

### Création de Stratégies Personnalisées

```typescript
const customStrategy: HealingStrategy = {
  id: 'custom-database-fix',
  name: 'Réparation Base de Données',
  description: 'Optimise et redémarre la base de données',
  applicableAnomalies: ['performance_degradation', 'communication_failure'],
  actions: [
    {
      type: 'optimize',
      target: 'database-service',
      parameters: { clearCache: true, reindexTables: true },
      timeout: 120000
    },
    {
      type: 'restart',
      target: 'database-service',
      parameters: { gracefulShutdown: true },
      timeout: 180000
    }
  ],
  estimatedRecoveryTime: 150000,
  riskLevel: 0.4,
  rollbackActions: [
    {
      type: 'restart',
      target: 'database-service',
      parameters: { forceRestart: true },
      timeout: 60000
    }
  ]
};

await healingStrategies.addStrategy(customStrategy);
```

## 📈 Surveillance et Métriques

### Métriques de Santé

```typescript
interface HealthMetrics {
  systemLoad: number;        // Charge CPU (0-100%)
  memoryUsage: number;       // Utilisation mémoire (0-100%)
  responseTime: number;      // Temps de réponse (ms)
  errorRate: number;         // Taux d'erreur (%)
  agentHealth: Map<string, number>;  // Santé des agents
  synapticHealth: number;    // Santé synaptique
  throughput: number;        // Débit (req/s)
}
```

### Événements

```typescript
// Écoute des événements
immuneSystem.on('anomaly-detected', (event) => {
  console.log('🚨 Anomalie détectée:', event.anomaly.description);
});

immuneSystem.on('healing-successful', (event) => {
  console.log('✅ Guérison réussie:', event.strategy.name);
});

immuneSystem.on('healing-failed', (event) => {
  console.log('❌ Échec de guérison:', event.result.error);
});

immuneSystem.on('adaptation-applied', (event) => {
  console.log('🧠 Adaptation appliquée:', event.adaptation.name);
});
```

## 🧪 Tests

### Exécution des Tests

```bash
# Tests unitaires
npm test

# Tests d'intégration
npm run test:integration

# Tests de performance
npm run test:performance

# Couverture de code
npm run test:coverage
```

### Tests Manuels

```bash
# Simulation d'anomalie
npm run simulate:anomaly -- --type=performance_degradation --severity=high

# Test de stratégie
npm run test:strategy -- --strategy=restart-simple --target=test-service

# Validation de rollback
npm run test:rollback -- --service=api-service
```

## 🔧 Dépannage

### Problèmes Courants

#### Guérisons en Boucle
```bash
# Vérification des seuils
npm run check:thresholds

# Ajustement de la sensibilité
npm run config:sensitivity -- --level=0.5
```

#### Échecs de Guérison
```bash
# Vérification des permissions
npm run check:permissions

# Validation de la configuration
npm run validate:config
```

#### Performance Dégradée
```bash
# Optimisation des intervalles
npm run optimize:intervals

# Réduction de la concurrence
npm run config:concurrency -- --max=2
```

### Logs et Diagnostics

```bash
# Logs détaillés
export LOG_LEVEL=debug
npm start

# Diagnostic complet
npm run diagnose

# Export des métriques
npm run export:metrics -- --format=json --output=metrics.json
```

## 🔒 Sécurité

### Niveaux de Risque

- **0.0-0.3** - Risque faible (actions sûres)
- **0.4-0.6** - Risque moyen (validation recommandée)
- **0.7-1.0** - Risque élevé (intervention manuelle)

### Contraintes de Sécurité

```typescript
const securityConstraints = {
  maxDowntime: 300000,           // 5 minutes max
  maxResourceUsage: 90,          // 90% max
  protectedComponents: [         // Composants protégés
    'critical-database',
    'payment-service',
    'user-authentication'
  ],
  allowedActions: [              // Actions autorisées
    'restart', 'scale', 'optimize'
  ],
  restrictedActions: [           // Actions restreintes
    'isolate', 'rollback'
  ]
};
```

## 📚 Documentation

- [Guide Complet](./docs/AUTO_HEALER_GUIDE.md)
- [Configuration Avancée](./config/auto-healer.example.ts)
- [API Reference](./docs/API_REFERENCE.md)
- [Exemples d'Utilisation](./examples/)

## 🤝 Contribution

### Développement

```bash
# Fork du projet
git clone https://github.com/your-username/cortex-central.git

# Installation des dépendances de développement
npm install --include=dev

# Lancement en mode développement
npm run dev
```

### Guidelines

1. **Tests** - Tous les nouveaux composants doivent avoir des tests
2. **Documentation** - Documenter les nouvelles fonctionnalités
3. **Types** - Utiliser TypeScript strict
4. **Logs** - Ajouter des logs appropriés
5. **Sécurité** - Évaluer les risques des nouvelles actions

## 📄 Licence

MIT License - voir [LICENSE](./LICENSE) pour plus de détails.

## 🆘 Support

- **Issues** - [GitHub Issues](https://github.com/retreat-and-be/cortex-central/issues)
- **Discussions** - [GitHub Discussions](https://github.com/retreat-and-be/cortex-central/discussions)
- **Email** - <EMAIL>
- **Slack** - #auto-healer-support

---

**Développé avec ❤️ par l'équipe Retreat And Be**
