/**
 * Exemple d'utilisation de l'EvolutionEngine
 * Démontre comment configurer et utiliser le système d'évolution continue
 */

import {
  createEvolutionEngine,
  EvolutionUtils,
  DEFAULT_EVOLUTION_CONFIG,
  EVOLUTION_EVENTS
} from '../src/evolution';

async function demonstrateEvolutionEngine() {
  console.log('🧬 Démonstration de l\'EvolutionEngine\n');

  // 1. Configuration personnalisée
  console.log('📋 Configuration de l\'EvolutionEngine...');
  
  const customConfig = {
    ...DEFAULT_EVOLUTION_CONFIG,
    scanInterval: 1, // 1 heure pour la démo
    maxConcurrentEvolutions: 1,
    autoApproveThreshold: 70,
    enableAutomaticEvolution: false, // Manuel pour la démo
    technologySources: [
      {
        name: 'GitHub Trending Demo',
        type: 'github' as const,
        url: 'https://api.github.com',
        lastScanned: new Date(0),
        isActive: true,
        scanInterval: 1,
        priority: 8,
        filters: ['javascript', 'typescript', 'react']
      },
      {
        name: 'NPM Demo',
        type: 'npm' as const,
        url: 'https://registry.npmjs.org',
        lastScanned: new Date(0),
        isActive: true,
        scanInterval: 1,
        priority: 9,
        filters: ['framework', 'library']
      }
    ],
    priorityKeywords: [
      'react',
      'typescript',
      'performance',
      'security',
      'ai'
    ]
  };

  // Validation de la configuration
  try {
    EvolutionUtils.validateConfig(customConfig);
    console.log('✅ Configuration validée');
  } catch (error) {
    console.error('❌ Configuration invalide:', error.message);
    return;
  }

  // 2. Création de l'EvolutionEngine
  const evolutionEngine = createEvolutionEngine(customConfig);

  // 3. Configuration des événements
  console.log('\n🔔 Configuration des événements...');
  
  evolutionEngine.on('evolutionEvent', (event) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${event.type}: ${event.message}`);
    
    // Gestion spécifique des événements
    switch (event.type) {
      case EVOLUTION_EVENTS.TECHNOLOGY_DISCOVERED:
        console.log(`   📡 ${event.data.count} nouvelles technologies découvertes`);
        break;
      case EVOLUTION_EVENTS.PLAN_CREATED:
        console.log(`   📋 Plan créé: ${event.data.plan.name}`);
        console.log(`   ⏱️  Durée estimée: ${event.data.plan.totalDuration}h`);
        break;
      case EVOLUTION_EVENTS.PHASE_COMPLETED:
        console.log(`   ✅ Phase complétée: ${event.data.phaseName}`);
        break;
      case EVOLUTION_EVENTS.ROLLBACK_TRIGGERED:
        console.log(`   ⚠️  Rollback déclenché: ${event.data.reason}`);
        break;
    }
  });

  // 4. Démarrage de l'EvolutionEngine
  console.log('\n🚀 Démarrage de l\'EvolutionEngine...');
  
  try {
    await evolutionEngine.start();
    console.log('✅ EvolutionEngine démarré avec succès');
  } catch (error) {
    console.error('❌ Échec du démarrage:', error.message);
    return;
  }

  // 5. Affichage de l'état initial
  console.log('\n📊 État initial du système:');
  const initialMetrics = evolutionEngine.getMetrics();
  console.log(`   Évolutions totales: ${initialMetrics.totalEvolutions}`);
  console.log(`   Agents évoluées: ${initialMetrics.agentsEvolved}`);
  console.log(`   Prochaine évolution: ${initialMetrics.nextScheduledEvolution.toISOString()}`);

  // 6. Exécution d'un cycle d'évolution manuel
  console.log('\n🔄 Exécution d\'un cycle d\'évolution...');
  
  try {
    const report = await evolutionEngine.performEvolutionCycle();
    
    console.log('\n📈 Rapport d\'évolution:');
    console.log(`   ID du rapport: ${report.id}`);
    console.log(`   Technologies évaluées: ${report.technologiesEvaluated}`);
    console.log(`   Agents mis à jour: ${report.agentsUpdated}`);
    console.log(`   Gains de performance: ${report.performanceGains.toFixed(1)}%`);
    console.log(`   Rollbacks: ${report.rollbacks}`);
    console.log(`   Durée: ${report.duration.toFixed(1)}h`);
    console.log(`   Taux de succès: ${report.successRate.toFixed(1)}%`);
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommandations:');
      report.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }
    
    if (report.lessons.length > 0) {
      console.log('\n📚 Leçons apprises:');
      report.lessons.forEach((lesson, index) => {
        console.log(`   ${index + 1}. ${lesson}`);
      });
    }

  } catch (error) {
    console.error('❌ Échec du cycle d\'évolution:', error.message);
  }

  // 7. Affichage des métriques finales
  console.log('\n📊 Métriques finales:');
  const finalMetrics = evolutionEngine.getMetrics();
  console.log(`   Évolutions totales: ${finalMetrics.totalEvolutions}`);
  console.log(`   Évolutions réussies: ${finalMetrics.successfulEvolutions}`);
  console.log(`   Évolutions échouées: ${finalMetrics.failedEvolutions}`);
  console.log(`   Durée moyenne: ${finalMetrics.averageDuration.toFixed(1)}h`);
  console.log(`   Gain de performance moyen: ${finalMetrics.averagePerformanceGain.toFixed(1)}%`);
  console.log(`   Rollbacks totaux: ${finalMetrics.totalRollbacks}`);
  console.log(`   Technologies adoptées: ${finalMetrics.technologiesAdopted}`);
  console.log(`   Agents évoluées: ${finalMetrics.agentsEvolved}`);

  // 8. Affichage des évolutions en cours
  const currentEvolutions = evolutionEngine.getCurrentEvolutions();
  if (currentEvolutions.length > 0) {
    console.log('\n🔄 Évolutions en cours:');
    currentEvolutions.forEach((evolution, index) => {
      console.log(`   ${index + 1}. ${evolution.name} (${evolution.status})`);
    });
  } else {
    console.log('\n✅ Aucune évolution en cours');
  }

  // 9. Arrêt de l'EvolutionEngine
  console.log('\n🛑 Arrêt de l\'EvolutionEngine...');
  
  try {
    await evolutionEngine.stop();
    console.log('✅ EvolutionEngine arrêté avec succès');
  } catch (error) {
    console.error('❌ Échec de l\'arrêt:', error.message);
  }

  console.log('\n🎉 Démonstration terminée!');
}

/**
 * Exemple de configuration pour différents environnements
 */
async function demonstrateEnvironmentConfigs() {
  console.log('\n🌍 Configurations d\'environnement:\n');

  // Configuration de développement
  console.log('🔧 Configuration de développement:');
  const devConfig = EvolutionUtils.createDevConfig();
  console.log(`   Intervalle de scan: ${devConfig.scanInterval}h`);
  console.log(`   Évolution automatique: ${devConfig.enableAutomaticEvolution}`);
  console.log(`   Seuil d'auto-approbation: ${devConfig.autoApproveThreshold}%`);

  // Configuration de production
  console.log('\n🏭 Configuration de production:');
  const prodConfig = EvolutionUtils.createProdConfig();
  console.log(`   Intervalle de scan: ${prodConfig.scanInterval}h`);
  console.log(`   Évolutions simultanées max: ${prodConfig.maxConcurrentEvolutions}`);
  console.log(`   Timeout rollback: ${prodConfig.rollbackTimeout}min`);

  // Configuration de test
  console.log('\n🧪 Configuration de test:');
  const testConfig = EvolutionUtils.createTestConfig();
  console.log(`   Intervalle de scan: ${testConfig.scanInterval}h`);
  console.log(`   Sources externes: ${testConfig.technologySources.length}`);
  console.log(`   Seuil d'auto-approbation: ${testConfig.autoApproveThreshold}%`);
}

/**
 * Exemple de monitoring avancé
 */
async function demonstrateAdvancedMonitoring() {
  console.log('\n📊 Monitoring avancé:\n');

  const evolutionEngine = createEvolutionEngine();
  await evolutionEngine.start();

  // Monitoring en temps réel
  const monitoringInterval = setInterval(() => {
    const metrics = evolutionEngine.getMetrics();
    const isRunning = evolutionEngine.isEvolutionRunning();
    const currentEvolutions = evolutionEngine.getCurrentEvolutions();

    console.log(`[${new Date().toISOString()}] Status:`);
    console.log(`   Système actif: ${isRunning ? '🟢' : '🔴'}`);
    console.log(`   Évolutions en cours: ${currentEvolutions.length}`);
    console.log(`   Taux de succès global: ${(metrics.successfulEvolutions / Math.max(metrics.totalEvolutions, 1) * 100).toFixed(1)}%`);
    
    if (currentEvolutions.length > 0) {
      console.log(`   Évolutions actives:`);
      currentEvolutions.forEach(evolution => {
        console.log(`     - ${evolution.name} (${evolution.status})`);
      });
    }
    console.log('');
  }, 10000); // Toutes les 10 secondes

  // Arrêt du monitoring après 1 minute
  setTimeout(async () => {
    clearInterval(monitoringInterval);
    await evolutionEngine.stop();
    console.log('🛑 Monitoring arrêté');
  }, 60000);
}

/**
 * Exemple de gestion d'erreurs
 */
async function demonstrateErrorHandling() {
  console.log('\n⚠️ Gestion d\'erreurs:\n');

  const evolutionEngine = createEvolutionEngine({
    ...DEFAULT_EVOLUTION_CONFIG,
    technologySources: [] // Sources vides pour provoquer une erreur
  });

  // Gestion des erreurs d'événements
  evolutionEngine.on('evolutionEvent', (event) => {
    if (event.severity === 'error' || event.severity === 'critical') {
      console.error(`🚨 Erreur détectée: ${event.message}`);
      
      // Actions correctives possibles
      switch (event.type) {
        case EVOLUTION_EVENTS.VALIDATION_FAILED:
          console.log('   🔧 Action: Vérification des critères de validation');
          break;
        case EVOLUTION_EVENTS.ROLLBACK_TRIGGERED:
          console.log('   🔄 Action: Surveillance du processus de rollback');
          break;
        default:
          console.log('   📞 Action: Notification de l\'équipe de support');
      }
    }
  });

  try {
    await evolutionEngine.start();
    await evolutionEngine.performEvolutionCycle();
  } catch (error) {
    console.error('❌ Erreur capturée:', error.message);
    console.log('🔧 Actions de récupération:');
    console.log('   1. Vérification de la configuration');
    console.log('   2. Test de connectivité des sources');
    console.log('   3. Redémarrage avec configuration par défaut');
  } finally {
    await evolutionEngine.stop();
  }
}

// Exécution des exemples
async function runExamples() {
  try {
    await demonstrateEvolutionEngine();
    await demonstrateEnvironmentConfigs();
    await demonstrateAdvancedMonitoring();
    await demonstrateErrorHandling();
  } catch (error) {
    console.error('Erreur dans les exemples:', error);
  }
}

// Point d'entrée
if (require.main === module) {
  runExamples().catch(console.error);
}

export {
  demonstrateEvolutionEngine,
  demonstrateEnvironmentConfigs,
  demonstrateAdvancedMonitoring,
  demonstrateErrorHandling
};
