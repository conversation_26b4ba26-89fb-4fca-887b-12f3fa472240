import { 
  ImmuneSystemConfig, 
  AutoHealerConfig, 
  HealingStrategy,
  AdaptationEngineConfig 
} from '../src/immune/types';

/**
 * Configuration d'exemple pour le système Auto-Healer
 * 
 * Copiez ce fichier vers auto-healer.config.ts et adaptez selon vos besoins.
 */

// Configuration du Système Immunitaire
export const immuneSystemConfig: Partial<ImmuneSystemConfig> = {
  // Intervalle de surveillance en millisecondes (30 secondes par défaut)
  monitoringInterval: 30000,
  
  // Activation de la guérison automatique
  healingEnabled: true,
  
  // Activation de l'apprentissage et adaptation
  adaptationEnabled: true
};

// Configuration de l'Auto-Healer
export const autoHealerConfig: Partial<AutoHealerConfig> = {
  // Nombre maximum de guérisons simultanées
  maxConcurrentHealings: 3,
  
  // Timeout par défaut pour les actions (5 minutes)
  defaultTimeout: 300000,
  
  // Seuil de risque au-delà duquel les actions sont rejetées (0.0-1.0)
  riskThreshold: 0.7,
  
  // Activation de l'apprentissage
  enableLearning: true,
  
  // Activation du rollback automatique
  enableRollback: true
};

// Configuration du Moteur d'Adaptation
export const adaptationEngineConfig: Partial<AdaptationEngineConfig> = {
  // Taux d'apprentissage (0.0-1.0)
  learningRate: 0.1,
  
  // Seuil de confiance minimum pour appliquer une règle
  confidenceThreshold: 0.7,
  
  // Nombre maximum de règles d'adaptation
  maxRules: 1000,
  
  // Création automatique de nouvelles règles
  enableAutoCreation: true
};

// Stratégies personnalisées d'exemple
export const customHealingStrategies: HealingStrategy[] = [
  {
    id: 'database-connection-fix',
    name: 'Réparation Connexion Base de Données',
    description: 'Redémarre les pools de connexion et optimise les requêtes',
    applicableAnomalies: ['communication_failure', 'performance_degradation'],
    actions: [
      {
        type: 'optimize',
        target: 'database-service',
        parameters: {
          clearConnectionPool: true,
          optimizeQueries: true,
          adjustTimeout: 30000
        },
        timeout: 120000
      },
      {
        type: 'restart',
        target: 'database-service',
        parameters: { gracefulShutdown: true },
        timeout: 180000
      }
    ],
    estimatedRecoveryTime: 150000, // 2.5 minutes
    riskLevel: 0.4,
    prerequisites: ['database_backup_available'],
    rollbackActions: [
      {
        type: 'restart',
        target: 'database-service',
        parameters: { forceRestart: true },
        timeout: 60000
      }
    ],
    successCriteria: [
      'connection_pool_healthy',
      'query_response_time < 100ms',
      'error_rate < 0.1%'
    ],
    maxRetries: 2
  },
  
  {
    id: 'memory-leak-mitigation',
    name: 'Atténuation Fuite Mémoire',
    description: 'Détecte et corrige les fuites mémoire',
    applicableAnomalies: ['resource_exhaustion', 'performance_degradation'],
    actions: [
      {
        type: 'optimize',
        target: '${anomaly.affectedComponents[0]}',
        parameters: {
          garbageCollect: true,
          clearCache: true,
          adjustMemoryLimits: true
        },
        timeout: 60000
      },
      {
        type: 'scale',
        target: '${anomaly.affectedComponents[0]}',
        parameters: { 
          replicas: '${current_replicas + 1}',
          memoryLimit: '${current_memory * 1.5}'
        },
        timeout: 120000
      }
    ],
    estimatedRecoveryTime: 90000, // 1.5 minutes
    riskLevel: 0.3,
    rollbackActions: [
      {
        type: 'scale',
        target: '${anomaly.affectedComponents[0]}',
        parameters: { replicas: '${original_replicas}' },
        timeout: 120000
      }
    ]
  },
  
  {
    id: 'security-breach-response',
    name: 'Réponse Violation Sécurité',
    description: 'Isole et sécurise en cas de violation de sécurité',
    applicableAnomalies: ['security_breach'],
    actions: [
      {
        type: 'isolate',
        target: '${anomaly.affectedComponents[0]}',
        parameters: { 
          blockAllTraffic: true,
          preserveLogs: true 
        },
        timeout: 30000
      },
      {
        type: 'notify',
        target: 'security-team',
        parameters: {
          message: 'ALERTE SÉCURITÉ: Composant ${anomaly.affectedComponents[0]} isolé',
          severity: 'critical',
          channels: ['email', 'slack', 'sms']
        },
        timeout: 10000
      },
      {
        type: 'rollback',
        target: '${anomaly.affectedComponents[0]}',
        parameters: { 
          version: 'last_known_good',
          preserveData: false 
        },
        timeout: 300000
      }
    ],
    estimatedRecoveryTime: 180000, // 3 minutes
    riskLevel: 0.9, // Risque élevé mais nécessaire pour la sécurité
    prerequisites: ['security_team_notified'],
    successCriteria: [
      'component_isolated',
      'security_team_alerted',
      'clean_version_deployed'
    ]
  },
  
  {
    id: 'api-rate-limit-adjustment',
    name: 'Ajustement Limite de Taux API',
    description: 'Ajuste dynamiquement les limites de taux API',
    applicableAnomalies: ['high_error_rate', 'performance_degradation'],
    actions: [
      {
        type: 'optimize',
        target: 'api-gateway',
        parameters: {
          adjustRateLimits: true,
          increaseThrottling: true,
          enableCaching: true
        },
        timeout: 30000
      }
    ],
    estimatedRecoveryTime: 30000, // 30 secondes
    riskLevel: 0.1, // Très faible risque
    rollbackActions: [
      {
        type: 'optimize',
        target: 'api-gateway',
        parameters: {
          resetRateLimits: true,
          disableThrottling: false
        },
        timeout: 30000
      }
    ]
  }
];

// Configuration des seuils d'anomalie
export const anomalyThresholds = {
  systemLoad: { 
    warning: 70,   // 70% d'utilisation CPU
    critical: 90   // 90% d'utilisation CPU
  },
  memoryUsage: { 
    warning: 80,   // 80% d'utilisation mémoire
    critical: 95   // 95% d'utilisation mémoire
  },
  responseTime: { 
    warning: 1000, // 1 seconde
    critical: 5000 // 5 secondes
  },
  errorRate: { 
    warning: 1,    // 1% d'erreurs
    critical: 5    // 5% d'erreurs
  },
  agentHealth: { 
    warning: 80,   // 80% de santé
    critical: 50   // 50% de santé
  },
  synapticHealth: { 
    warning: 70,   // 70% de santé synaptique
    critical: 40   // 40% de santé synaptique
  }
};

// Configuration des contraintes système
export const systemConstraints = {
  // Temps d'arrêt maximum autorisé (5 minutes)
  maxDowntime: 300000,
  
  // Utilisation maximale des ressources (90%)
  maxResourceUsage: 90,
  
  // Actions autorisées
  allowedActions: [
    'restart',
    'scale',
    'optimize',
    'redirect',
    'notify'
  ],
  
  // Actions interdites (nécessitent intervention manuelle)
  restrictedActions: [
    'isolate', // Seulement pour les violations de sécurité
    'rollback' // Seulement en cas d'urgence
  ],
  
  // Composants protégés (ne peuvent pas être redémarrés automatiquement)
  protectedComponents: [
    'critical-database',
    'payment-service',
    'user-authentication'
  ],
  
  // Services de secours disponibles
  backupServices: {
    'api-service': 'api-service-backup',
    'web-frontend': 'static-fallback',
    'recommendation-engine': 'simple-recommender'
  }
};

// Configuration de notification
export const notificationConfig = {
  // Canaux de notification
  channels: {
    email: {
      enabled: true,
      recipients: ['<EMAIL>', '<EMAIL>']
    },
    slack: {
      enabled: true,
      webhook: process.env.SLACK_WEBHOOK_URL,
      channel: '#alerts'
    },
    sms: {
      enabled: false, // Activé seulement pour les alertes critiques
      numbers: ['+**********']
    }
  },
  
  // Niveaux de notification
  levels: {
    low: ['email'],
    medium: ['email', 'slack'],
    high: ['email', 'slack'],
    critical: ['email', 'slack', 'sms']
  }
};

// Configuration de monitoring
export const monitoringConfig = {
  // Métriques à surveiller
  metrics: [
    'system_load',
    'memory_usage',
    'response_time',
    'error_rate',
    'agent_health',
    'synaptic_health',
    'throughput',
    'disk_usage',
    'network_latency'
  ],
  
  // Intervalles de collecte
  intervals: {
    health_check: 30000,    // 30 secondes
    metrics_collection: 60000, // 1 minute
    anomaly_detection: 30000,  // 30 secondes
    adaptation_learning: 300000 // 5 minutes
  },
  
  // Rétention des données
  retention: {
    metrics_history: 86400000,    // 24 heures
    healing_history: 604800000,   // 7 jours
    adaptation_rules: **********, // 30 jours
    logs: 259200000              // 3 jours
  }
};

// Export de la configuration complète
export const autoHealerFullConfig = {
  immuneSystem: immuneSystemConfig,
  autoHealer: autoHealerConfig,
  adaptationEngine: adaptationEngineConfig,
  customStrategies: customHealingStrategies,
  anomalyThresholds,
  systemConstraints,
  notification: notificationConfig,
  monitoring: monitoringConfig
};
