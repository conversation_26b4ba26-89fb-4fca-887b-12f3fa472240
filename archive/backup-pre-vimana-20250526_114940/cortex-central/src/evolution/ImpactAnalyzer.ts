/**
 * ImpactAnalyzer - Analyseur d'impact des nouvelles technologies
 * Évalue l'impact potentiel des technologies sur le système et les agents
 */

import { EventEmitter } from 'events';
import { Logger } from '../utils/logger';
import { Technology, ImpactAnalysis, AgentCapability } from './types';

export class ImpactAnalyzer extends EventEmitter {
  private logger: Logger;
  private agentCapabilities: Map<string, AgentCapability[]> = new Map();
  private systemArchitecture: any = {};
  private businessMetrics: any = {};

  constructor() {
    super();
    this.logger = new Logger('ImpactAnalyzer');
  }

  /**
   * Initialise l'analyseur d'impact
   */
  async initialize(): Promise<void> {
    this.logger.info('Initializing ImpactAnalyzer...');
    
    try {
      // Chargement des capacités des agents
      await this.loadAgentCapabilities();
      
      // Chargement de l'architecture système
      await this.loadSystemArchitecture();
      
      // Chargement des métriques business
      await this.loadBusinessMetrics();
      
      this.logger.info('ImpactAnalyzer initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize ImpactAnalyzer:', error);
      throw error;
    }
  }

  /**
   * Analyse l'impact d'une technologie
   */
  async analyze(technology: Technology): Promise<ImpactAnalysis> {
    this.logger.debug(`Analyzing impact for technology: ${technology.name}`);
    
    try {
      // Analyse des agents affectés
      const affectedAgents = await this.identifyAffectedAgents(technology);
      
      // Évaluation des bénéfices
      const benefits = await this.evaluateBenefits(technology, affectedAgents);
      
      // Évaluation des risques
      const risks = await this.evaluateRisks(technology, affectedAgents);
      
      // Évaluation de la complexité de migration
      const migrationComplexity = await this.assessMigrationComplexity(technology, affectedAgents);
      
      // Estimation de l'effort
      const estimatedEffort = await this.estimateEffort(technology, affectedAgents, migrationComplexity);
      
      // Évaluation de la valeur business
      const businessValue = await this.evaluateBusinessValue(technology, benefits);
      
      // Évaluation de la dette technique
      const technicalDebt = await this.evaluateTechnicalDebt(technology);
      
      // Implications sécuritaires
      const securityImplications = await this.analyzeSecurityImplications(technology);
      
      // Impact sur les performances
      const performanceImpact = await this.analyzePerformanceImpact(technology);
      
      // Problèmes de compatibilité
      const compatibilityIssues = await this.identifyCompatibilityIssues(technology, affectedAgents);
      
      // Dépendances
      const dependencies = await this.analyzeDependencies(technology);
      
      // Changements cassants
      const breakingChanges = await this.identifyBreakingChanges(technology);
      
      // Calcul du score d'impact global
      const impactScore = this.calculateImpactScore({
        businessValue,
        technicalDebt,
        migrationComplexity,
        performanceImpact,
        securityImplications,
        risks
      });

      const analysis: ImpactAnalysis = {
        technology,
        impactScore,
        affectedAgents,
        benefits,
        risks,
        migrationComplexity,
        estimatedEffort,
        businessValue,
        technicalDebt,
        securityImplications,
        performanceImpact,
        compatibilityIssues,
        dependencies,
        breakingChanges,
        learningCurve: this.assessLearningCurve(technology),
        communitySupport: this.assessCommunitySupport(technology),
        ecosystemMaturity: this.assessEcosystemMaturity(technology)
      };

      this.logger.debug(`Impact analysis completed for ${technology.name}: score ${impactScore}`);
      return analysis;

    } catch (error) {
      this.logger.error(`Impact analysis failed for ${technology.name}:`, error);
      throw error;
    }
  }

  /**
   * Identifie les agents affectés par une technologie
   */
  private async identifyAffectedAgents(technology: Technology): Promise<string[]> {
    const affectedAgents: string[] = [];
    
    // Mapping des catégories de technologies aux agents
    const categoryAgentMap: Record<string, string[]> = {
      'framework': ['agent-frontend', 'agent-backend'],
      'library': ['agent-frontend', 'agent-backend', 'agent-qa'],
      'tool': ['agent-devops', 'agent-qa', 'agent-security'],
      'language': ['agent-frontend', 'agent-backend', 'agent-qa'],
      'platform': ['agent-devops', 'agent-backend'],
      'protocol': ['agent-backend', 'agent-security', 'agent-api-monitor']
    };

    const potentialAgents = categoryAgentMap[technology.category] || [];
    
    // Vérification plus fine basée sur les capacités
    for (const agentId of potentialAgents) {
      const capabilities = this.agentCapabilities.get(agentId) || [];
      
      if (this.technologyMatchesCapabilities(technology, capabilities)) {
        affectedAgents.push(agentId);
      }
    }

    // Analyse basée sur les tags
    for (const tag of technology.tags) {
      const tagAgentMap: Record<string, string[]> = {
        'react': ['agent-frontend'],
        'vue': ['agent-frontend'],
        'angular': ['agent-frontend'],
        'nodejs': ['agent-backend'],
        'python': ['agent-backend', 'agent-qa'],
        'docker': ['agent-devops'],
        'kubernetes': ['agent-devops'],
        'security': ['agent-security'],
        'testing': ['agent-qa'],
        'monitoring': ['agent-api-monitor', 'agent-performance']
      };

      const tagAgents = tagAgentMap[tag.toLowerCase()] || [];
      affectedAgents.push(...tagAgents);
    }

    return [...new Set(affectedAgents)]; // Déduplication
  }

  /**
   * Évalue les bénéfices d'une technologie
   */
  private async evaluateBenefits(technology: Technology, affectedAgents: string[]): Promise<string[]> {
    const benefits: string[] = [];

    // Bénéfices basés sur la maturité
    if (technology.maturity === 'stable' || technology.maturity === 'mature') {
      benefits.push('Technologie stable et éprouvée');
    }

    // Bénéfices basés sur la popularité
    if (technology.githubStars && technology.githubStars > 10000) {
      benefits.push('Large communauté et support');
    }

    if (technology.npmDownloads && technology.npmDownloads > 0.8) {
      benefits.push('Adoption élevée dans l\'écosystème');
    }

    // Bénéfices basés sur la catégorie
    const categoryBenefits: Record<string, string[]> = {
      'framework': ['Structure et organisation améliorées', 'Développement plus rapide'],
      'library': ['Fonctionnalités prêtes à l\'emploi', 'Réduction du code custom'],
      'tool': ['Automatisation des tâches', 'Amélioration de la productivité'],
      'language': ['Nouvelles capacités de développement', 'Performance améliorée'],
      'platform': ['Scalabilité améliorée', 'Gestion simplifiée'],
      'protocol': ['Communication améliorée', 'Interopérabilité renforcée']
    };

    benefits.push(...(categoryBenefits[technology.category] || []));

    // Bénéfices spécifiques aux agents
    for (const agentId of affectedAgents) {
      const agentBenefits = this.getAgentSpecificBenefits(agentId, technology);
      benefits.push(...agentBenefits);
    }

    return benefits;
  }

  /**
   * Évalue les risques d'une technologie
   */
  private async evaluateRisks(technology: Technology, affectedAgents: string[]): Promise<string[]> {
    const risks: string[] = [];

    // Risques basés sur la maturité
    if (technology.maturity === 'experimental' || technology.maturity === 'alpha') {
      risks.push('Technologie immature, risque d\'instabilité');
    }

    if (technology.maturity === 'deprecated') {
      risks.push('Technologie obsolète, support limité');
    }

    // Risques basés sur l'adoption
    if (technology.adoptionRate < 0.1) {
      risks.push('Faible adoption, risque d\'abandon');
    }

    // Risques de sécurité
    if (technology.securityScore && technology.securityScore < 50) {
      risks.push('Problèmes de sécurité identifiés');
    }

    // Risques de performance
    if (technology.performanceScore && technology.performanceScore < 50) {
      risks.push('Impact négatif potentiel sur les performances');
    }

    // Risques de maintenance
    if (technology.lastUpdate && (Date.now() - technology.lastUpdate.getTime()) > 365 * 24 * 60 * 60 * 1000) {
      risks.push('Technologie non maintenue récemment');
    }

    // Risques spécifiques aux agents
    for (const agentId of affectedAgents) {
      const agentRisks = this.getAgentSpecificRisks(agentId, technology);
      risks.push(...agentRisks);
    }

    return risks;
  }

  /**
   * Évalue la complexité de migration
   */
  private async assessMigrationComplexity(technology: Technology, affectedAgents: string[]): Promise<ImpactAnalysis['migrationComplexity']> {
    let complexityScore = 0;

    // Complexité basée sur le nombre d'agents affectés
    complexityScore += affectedAgents.length * 10;

    // Complexité basée sur la catégorie
    const categoryComplexity: Record<string, number> = {
      'framework': 40,
      'library': 20,
      'tool': 15,
      'language': 50,
      'platform': 45,
      'protocol': 35
    };

    complexityScore += categoryComplexity[technology.category] || 20;

    // Complexité basée sur les changements cassants
    if (technology.version.startsWith('0.')) {
      complexityScore += 20; // Version pré-1.0, plus de risques de changements
    }

    // Complexité basée sur les dépendances existantes
    const existingDependencies = await this.getExistingDependencies(technology);
    complexityScore += existingDependencies.length * 5;

    if (complexityScore < 30) return 'low';
    if (complexityScore < 60) return 'medium';
    if (complexityScore < 90) return 'high';
    return 'very_high';
  }

  /**
   * Estime l'effort nécessaire
   */
  private async estimateEffort(
    technology: Technology, 
    affectedAgents: string[], 
    complexity: ImpactAnalysis['migrationComplexity']
  ): Promise<number> {
    let baseEffort = 0;

    // Effort de base par agent
    const agentEffortMap: Record<string, number> = {
      'agent-frontend': 16,
      'agent-backend': 20,
      'agent-devops': 12,
      'agent-qa': 8,
      'agent-security': 10,
      'agent-marketing': 4,
      'agent-seo': 6
    };

    for (const agentId of affectedAgents) {
      baseEffort += agentEffortMap[agentId] || 8;
    }

    // Multiplicateur basé sur la complexité
    const complexityMultiplier: Record<ImpactAnalysis['migrationComplexity'], number> = {
      'low': 1,
      'medium': 1.5,
      'high': 2.5,
      'very_high': 4
    };

    baseEffort *= complexityMultiplier[complexity];

    // Ajustement basé sur la maturité
    const maturityMultiplier: Record<Technology['maturity'], number> = {
      'experimental': 2,
      'alpha': 1.5,
      'beta': 1.2,
      'stable': 1,
      'mature': 0.8,
      'deprecated': 3
    };

    baseEffort *= maturityMultiplier[technology.maturity];

    return Math.round(baseEffort);
  }

  /**
   * Évalue la valeur business
   */
  private async evaluateBusinessValue(technology: Technology, benefits: string[]): Promise<number> {
    let value = 0;

    // Valeur basée sur les bénéfices
    value += benefits.length * 10;

    // Valeur basée sur la popularité
    if (technology.githubStars) {
      value += Math.min(technology.githubStars / 1000, 30);
    }

    // Valeur basée sur l'adoption
    value += technology.adoptionRate * 40;

    // Valeur basée sur la catégorie
    const categoryValue: Record<string, number> = {
      'framework': 25,
      'library': 15,
      'tool': 20,
      'language': 30,
      'platform': 25,
      'protocol': 20
    };

    value += categoryValue[technology.category] || 15;

    return Math.min(Math.round(value), 100);
  }

  /**
   * Évalue la dette technique
   */
  private async evaluateTechnicalDebt(technology: Technology): Promise<number> {
    let debt = 0;

    // Dette basée sur la maturité
    const maturityDebt: Record<Technology['maturity'], number> = {
      'experimental': 80,
      'alpha': 60,
      'beta': 40,
      'stable': 20,
      'mature': 10,
      'deprecated': 90
    };

    debt += maturityDebt[technology.maturity];

    // Dette basée sur la maintenance
    if (technology.lastUpdate) {
      const daysSinceUpdate = (Date.now() - technology.lastUpdate.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceUpdate > 365) {
        debt += 30;
      } else if (daysSinceUpdate > 180) {
        debt += 15;
      }
    }

    // Dette basée sur la documentation
    if (!technology.documentation) {
      debt += 20;
    }

    return Math.min(debt, 100);
  }

  /**
   * Analyse les implications sécuritaires
   */
  private async analyzeSecurityImplications(technology: Technology): Promise<string[]> {
    const implications: string[] = [];

    // Vérification des vulnérabilités connues
    if (technology.securityScore && technology.securityScore < 70) {
      implications.push('Vulnérabilités de sécurité identifiées');
    }

    // Vérification de la licence
    if (technology.license && ['GPL', 'AGPL'].some(l => technology.license!.includes(l))) {
      implications.push('Licence copyleft, implications légales à considérer');
    }

    // Vérification des dépendances
    if (technology.category === 'library' || technology.category === 'framework') {
      implications.push('Vérification des dépendances transitives requise');
    }

    return implications;
  }

  /**
   * Analyse l'impact sur les performances
   */
  private async analyzePerformanceImpact(technology: Technology): Promise<number> {
    let impact = 0;

    // Impact basé sur le score de performance
    if (technology.performanceScore) {
      impact = technology.performanceScore - 50; // Centré sur 0
    }

    // Impact basé sur la catégorie
    const categoryImpact: Record<string, number> = {
      'framework': -5, // Les frameworks peuvent ajouter de l'overhead
      'library': -2,
      'tool': 5, // Les outils améliorent généralement les performances
      'language': 10,
      'platform': 15,
      'protocol': 5
    };

    impact += categoryImpact[technology.category] || 0;

    return Math.max(-100, Math.min(100, impact));
  }

  /**
   * Identifie les problèmes de compatibilité
   */
  private async identifyCompatibilityIssues(technology: Technology, affectedAgents: string[]): Promise<string[]> {
    const issues: string[] = [];

    // Vérification des versions Node.js
    if (technology.name.toLowerCase().includes('node') && technology.version) {
      const majorVersion = parseInt(technology.version.split('.')[0]);
      if (majorVersion > 18) {
        issues.push('Nécessite une version récente de Node.js');
      }
    }

    // Vérification des conflits potentiels
    for (const agentId of affectedAgents) {
      const agentIssues = await this.getAgentCompatibilityIssues(agentId, technology);
      issues.push(...agentIssues);
    }

    return issues;
  }

  /**
   * Analyse les dépendances
   */
  private async analyzeDependencies(technology: Technology): Promise<string[]> {
    const dependencies: string[] = [];

    // Dépendances basées sur la catégorie
    const categoryDependencies: Record<string, string[]> = {
      'framework': ['Runtime environment', 'Build tools'],
      'library': ['Package manager'],
      'tool': ['System dependencies'],
      'language': ['Compiler/Interpreter', 'Standard library'],
      'platform': ['Infrastructure', 'Orchestration'],
      'protocol': ['Network stack', 'Security certificates']
    };

    dependencies.push(...(categoryDependencies[technology.category] || []));

    return dependencies;
  }

  /**
   * Identifie les changements cassants
   */
  private async identifyBreakingChanges(technology: Technology): Promise<string[]> {
    const changes: string[] = [];

    // Changements basés sur la version
    if (technology.version.startsWith('0.')) {
      changes.push('Version pré-1.0, API potentiellement instable');
    }

    const majorVersion = parseInt(technology.version.split('.')[0]);
    if (majorVersion > 1) {
      changes.push('Version majeure, changements cassants possibles');
    }

    return changes;
  }

  /**
   * Calcule le score d'impact global
   */
  private calculateImpactScore(factors: {
    businessValue: number;
    technicalDebt: number;
    migrationComplexity: ImpactAnalysis['migrationComplexity'];
    performanceImpact: number;
    securityImplications: string[];
    risks: string[];
  }): number {
    let score = 0;

    // Score basé sur la valeur business (40% du score)
    score += factors.businessValue * 0.4;

    // Pénalité pour la dette technique (20% du score)
    score -= factors.technicalDebt * 0.2;

    // Pénalité pour la complexité de migration (20% du score)
    const complexityPenalty: Record<ImpactAnalysis['migrationComplexity'], number> = {
      'low': 0,
      'medium': 10,
      'high': 25,
      'very_high': 40
    };
    score -= complexityPenalty[factors.migrationComplexity];

    // Bonus/Pénalité pour l'impact performance (10% du score)
    score += factors.performanceImpact * 0.1;

    // Pénalité pour les risques de sécurité (10% du score)
    score -= factors.securityImplications.length * 5;
    score -= factors.risks.length * 3;

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Méthodes utilitaires
   */
  private technologyMatchesCapabilities(technology: Technology, capabilities: AgentCapability[]): boolean {
    return capabilities.some(cap => 
      cap.name.toLowerCase().includes(technology.name.toLowerCase()) ||
      technology.tags.some(tag => cap.name.toLowerCase().includes(tag.toLowerCase()))
    );
  }

  private getAgentSpecificBenefits(agentId: string, technology: Technology): string[] {
    const benefits: Record<string, string[]> = {
      'agent-frontend': ['Interface utilisateur améliorée', 'Expérience développeur optimisée'],
      'agent-backend': ['Performance API améliorée', 'Scalabilité renforcée'],
      'agent-devops': ['Déploiement simplifié', 'Monitoring amélioré'],
      'agent-qa': ['Tests automatisés', 'Couverture de code améliorée'],
      'agent-security': ['Sécurité renforcée', 'Conformité améliorée']
    };

    return benefits[agentId] || [];
  }

  private getAgentSpecificRisks(agentId: string, technology: Technology): string[] {
    const risks: Record<string, string[]> = {
      'agent-frontend': ['Compatibilité navigateur', 'Taille du bundle'],
      'agent-backend': ['Charge serveur', 'Compatibilité base de données'],
      'agent-devops': ['Complexité déploiement', 'Dépendances système'],
      'agent-qa': ['Courbe d\'apprentissage', 'Intégration CI/CD'],
      'agent-security': ['Surface d\'attaque', 'Vulnérabilités tierces']
    };

    return risks[agentId] || [];
  }

  private async getExistingDependencies(technology: Technology): Promise<string[]> {
    // Simulation - dans un vrai système, cela interrogerait la base de données
    return [];
  }

  private async getAgentCompatibilityIssues(agentId: string, technology: Technology): Promise<string[]> {
    // Simulation - dans un vrai système, cela vérifierait les incompatibilités connues
    return [];
  }

  private assessLearningCurve(technology: Technology): number {
    const categoryComplexity: Record<string, number> = {
      'framework': 7,
      'library': 4,
      'tool': 3,
      'language': 9,
      'platform': 8,
      'protocol': 6
    };

    let curve = categoryComplexity[technology.category] || 5;

    if (technology.maturity === 'experimental' || technology.maturity === 'alpha') {
      curve += 2;
    }

    if (!technology.documentation) {
      curve += 2;
    }

    return Math.min(10, curve);
  }

  private assessCommunitySupport(technology: Technology): number {
    let support = 5;

    if (technology.githubStars) {
      support += Math.min(technology.githubStars / 2000, 3);
    }

    if (technology.stackOverflowQuestions) {
      support += Math.min(technology.stackOverflowQuestions / 1000, 2);
    }

    return Math.min(10, Math.round(support));
  }

  private assessEcosystemMaturity(technology: Technology): number {
    let maturity = 5;

    const maturityScore: Record<Technology['maturity'], number> = {
      'experimental': 2,
      'alpha': 3,
      'beta': 5,
      'stable': 8,
      'mature': 10,
      'deprecated': 1
    };

    maturity = maturityScore[technology.maturity];

    if (technology.lastUpdate) {
      const daysSinceUpdate = (Date.now() - technology.lastUpdate.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceUpdate < 30) {
        maturity += 1;
      } else if (daysSinceUpdate > 365) {
        maturity -= 2;
      }
    }

    return Math.max(1, Math.min(10, maturity));
  }

  /**
   * Méthodes de chargement des données
   */
  private async loadAgentCapabilities(): Promise<void> {
    // Simulation - dans un vrai système, cela chargerait depuis une base de données
    this.agentCapabilities.set('agent-frontend', [
      { name: 'React', version: '18.0', description: 'UI Framework', dependencies: [], performance: 85, stability: 90, lastUpdated: new Date() },
      { name: 'TypeScript', version: '5.0', description: 'Type System', dependencies: [], performance: 80, stability: 95, lastUpdated: new Date() }
    ]);

    this.agentCapabilities.set('agent-backend', [
      { name: 'Node.js', version: '18.0', description: 'Runtime', dependencies: [], performance: 85, stability: 90, lastUpdated: new Date() },
      { name: 'Express', version: '4.18', description: 'Web Framework', dependencies: [], performance: 80, stability: 95, lastUpdated: new Date() }
    ]);
  }

  private async loadSystemArchitecture(): Promise<void> {
    // Simulation - chargement de l'architecture système
    this.systemArchitecture = {
      microservices: true,
      containerized: true,
      cloudNative: true,
      eventDriven: true
    };
  }

  private async loadBusinessMetrics(): Promise<void> {
    // Simulation - chargement des métriques business
    this.businessMetrics = {
      developmentVelocity: 75,
      systemReliability: 95,
      userSatisfaction: 85,
      maintenanceCost: 60
    };
  }
}
