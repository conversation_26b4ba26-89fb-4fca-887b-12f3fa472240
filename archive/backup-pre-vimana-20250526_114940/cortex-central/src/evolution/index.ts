/**
 * EvolutionEngine - Système d'évolution continue pour l'adaptation automatique
 * 
 * Ce module implémente un système complet d'évolution continue qui permet
 * au système d'agents IA de s'adapter automatiquement aux nouvelles technologies
 * et d'évoluer de manière autonome.
 * 
 * Composants principaux :
 * - EvolutionEngine : Orchestrateur principal
 * - TechnologyScanner : Détection des nouvelles technologies
 * - ImpactAnalyzer : Analyse d'impact des changements
 * - EvolutionPlanner : Planification des évolutions
 * - AgentTrainer : Formation des agents
 * - DeploymentManager : Déploiement progressif
 * - ValidationSystem : Validation et rollback
 */

// Exports des classes principales
export { EvolutionEngine } from './EvolutionEngine';
export { TechnologyScanner } from './TechnologyScanner';
export { ImpactAnalyzer } from './ImpactAnalyzer';
export { EvolutionPlanner } from './EvolutionPlanner';
export { AgentTrainer } from './AgentTrainer';
export { DeploymentManager } from './DeploymentManager';
export { ValidationSystem } from './ValidationSystem';

// Exports des types
export * from './types';

// Configuration par défaut pour l'EvolutionEngine
export const DEFAULT_EVOLUTION_CONFIG = {
  scanInterval: 24, // 24 heures
  maxConcurrentEvolutions: 2,
  autoApproveThreshold: 75,
  rollbackTimeout: 30, // 30 minutes
  validationTimeout: 15, // 15 minutes
  enableAutomaticEvolution: true,
  enablePredictiveEvolution: true,
  technologySources: [
    {
      name: 'GitHub Trending',
      type: 'github' as const,
      url: 'https://api.github.com',
      lastScanned: new Date(0),
      isActive: true,
      scanInterval: 12,
      priority: 8,
      filters: ['javascript', 'typescript', 'python', 'go', 'rust']
    },
    {
      name: 'NPM Registry',
      type: 'npm' as const,
      url: 'https://registry.npmjs.org',
      lastScanned: new Date(0),
      isActive: true,
      scanInterval: 6,
      priority: 9,
      filters: ['framework', 'library', 'tool']
    },
    {
      name: 'Stack Overflow',
      type: 'stackoverflow' as const,
      url: 'https://api.stackexchange.com',
      lastScanned: new Date(0),
      isActive: true,
      scanInterval: 24,
      priority: 6
    },
    {
      name: 'Reddit Programming',
      type: 'reddit' as const,
      url: 'https://www.reddit.com/r/programming',
      lastScanned: new Date(0),
      isActive: true,
      scanInterval: 12,
      priority: 5
    },
    {
      name: 'Hacker News',
      type: 'hackernews' as const,
      url: 'https://hacker-news.firebaseio.com',
      lastScanned: new Date(0),
      isActive: true,
      scanInterval: 8,
      priority: 7
    }
  ],
  excludedTechnologies: [
    'flash',
    'silverlight',
    'internet-explorer',
    'jquery' // Exemple de technologie à exclure
  ],
  priorityKeywords: [
    'ai',
    'machine-learning',
    'microservices',
    'kubernetes',
    'docker',
    'serverless',
    'graphql',
    'typescript',
    'react',
    'vue',
    'angular',
    'node.js',
    'python',
    'go',
    'rust',
    'webassembly'
  ],
  notificationChannels: [
    'slack',
    'email',
    'webhook'
  ],
  backupRetention: 30, // 30 jours
  maxRollbackAttempts: 3
};

/**
 * Factory function pour créer une instance d'EvolutionEngine avec la configuration par défaut
 */
export function createEvolutionEngine(customConfig?: Partial<typeof DEFAULT_EVOLUTION_CONFIG>) {
  const config = {
    ...DEFAULT_EVOLUTION_CONFIG,
    ...customConfig
  };

  return new EvolutionEngine(config);
}

/**
 * Utilitaires pour la configuration
 */
export const EvolutionUtils = {
  /**
   * Valide une configuration d'évolution
   */
  validateConfig(config: any): boolean {
    const required = [
      'scanInterval',
      'maxConcurrentEvolutions',
      'autoApproveThreshold',
      'rollbackTimeout',
      'validationTimeout'
    ];

    for (const field of required) {
      if (!(field in config)) {
        throw new Error(`Missing required configuration field: ${field}`);
      }
    }

    if (config.scanInterval < 1) {
      throw new Error('scanInterval must be at least 1 hour');
    }

    if (config.maxConcurrentEvolutions < 1) {
      throw new Error('maxConcurrentEvolutions must be at least 1');
    }

    if (config.autoApproveThreshold < 0 || config.autoApproveThreshold > 100) {
      throw new Error('autoApproveThreshold must be between 0 and 100');
    }

    return true;
  },

  /**
   * Crée une configuration pour un environnement de développement
   */
  createDevConfig() {
    return {
      ...DEFAULT_EVOLUTION_CONFIG,
      scanInterval: 1, // 1 heure pour le dev
      enableAutomaticEvolution: false, // Désactivé en dev
      autoApproveThreshold: 90, // Plus strict en dev
      technologySources: DEFAULT_EVOLUTION_CONFIG.technologySources.map(source => ({
        ...source,
        scanInterval: source.scanInterval / 2 // Plus fréquent en dev
      }))
    };
  },

  /**
   * Crée une configuration pour un environnement de production
   */
  createProdConfig() {
    return {
      ...DEFAULT_EVOLUTION_CONFIG,
      scanInterval: 48, // 48 heures en prod
      enableAutomaticEvolution: true,
      autoApproveThreshold: 85, // Plus permissif en prod
      maxConcurrentEvolutions: 1, // Plus conservateur en prod
      rollbackTimeout: 60, // Plus de temps pour le rollback
      validationTimeout: 30 // Plus de temps pour la validation
    };
  },

  /**
   * Crée une configuration pour les tests
   */
  createTestConfig() {
    return {
      ...DEFAULT_EVOLUTION_CONFIG,
      scanInterval: 0.1, // 6 minutes pour les tests
      enableAutomaticEvolution: false,
      autoApproveThreshold: 50,
      rollbackTimeout: 1,
      validationTimeout: 1,
      technologySources: [], // Pas de sources externes en test
      excludedTechnologies: [],
      priorityKeywords: ['test-tech']
    };
  }
};

/**
 * Constantes pour les événements d'évolution
 */
export const EVOLUTION_EVENTS = {
  SCAN_STARTED: 'scan_started',
  TECHNOLOGY_DISCOVERED: 'technology_discovered',
  PLAN_CREATED: 'plan_created',
  EVOLUTION_STARTED: 'evolution_started',
  PHASE_COMPLETED: 'phase_completed',
  EVOLUTION_COMPLETED: 'evolution_completed',
  ROLLBACK_TRIGGERED: 'rollback_triggered',
  VALIDATION_FAILED: 'validation_failed',
  ERROR: 'error'
} as const;

/**
 * Métriques par défaut pour l'évolution
 */
export const DEFAULT_METRICS = {
  PERFORMANCE_THRESHOLDS: {
    responseTime: 200, // ms
    errorRate: 5, // %
    cpuUsage: 80, // %
    memoryUsage: 85, // %
    diskUsage: 90, // %
    networkLatency: 100 // ms
  },
  SECURITY_THRESHOLDS: {
    criticalVulnerabilities: 0,
    highVulnerabilities: 2,
    mediumVulnerabilities: 10,
    lowVulnerabilities: 50
  },
  QUALITY_THRESHOLDS: {
    testCoverage: 80, // %
    codeQuality: 85, // score
    documentation: 70, // %
    maintainability: 75 // score
  }
};

/**
 * Templates de plans d'évolution
 */
export const EVOLUTION_TEMPLATES = {
  FRONTEND_UPDATE: {
    name: 'Frontend Technology Update',
    description: 'Standard template for updating frontend technologies',
    phases: [
      {
        name: 'Dependencies Update',
        description: 'Update package dependencies',
        estimatedDuration: 2
      },
      {
        name: 'Code Migration',
        description: 'Migrate code to new APIs',
        estimatedDuration: 8
      },
      {
        name: 'Testing & Validation',
        description: 'Run tests and validate changes',
        estimatedDuration: 4
      }
    ]
  },
  BACKEND_MIGRATION: {
    name: 'Backend Technology Migration',
    description: 'Template for migrating backend technologies',
    phases: [
      {
        name: 'Infrastructure Preparation',
        description: 'Prepare infrastructure for migration',
        estimatedDuration: 4
      },
      {
        name: 'Service Migration',
        description: 'Migrate services to new technology',
        estimatedDuration: 12
      },
      {
        name: 'Data Migration',
        description: 'Migrate data if necessary',
        estimatedDuration: 6
      },
      {
        name: 'Validation & Rollout',
        description: 'Validate and complete rollout',
        estimatedDuration: 4
      }
    ]
  },
  SECURITY_UPDATE: {
    name: 'Security Technology Update',
    description: 'Template for security-related updates',
    phases: [
      {
        name: 'Security Assessment',
        description: 'Assess current security posture',
        estimatedDuration: 2
      },
      {
        name: 'Security Implementation',
        description: 'Implement security updates',
        estimatedDuration: 6
      },
      {
        name: 'Security Validation',
        description: 'Validate security improvements',
        estimatedDuration: 4
      }
    ]
  }
};

/**
 * Version de l'EvolutionEngine
 */
export const EVOLUTION_ENGINE_VERSION = '1.0.0';

/**
 * Informations sur l'EvolutionEngine
 */
export const EVOLUTION_ENGINE_INFO = {
  name: 'EvolutionEngine',
  version: EVOLUTION_ENGINE_VERSION,
  description: 'Système d\'évolution continue pour l\'adaptation automatique des agents IA',
  author: 'Cortex Central Team',
  license: 'MIT',
  repository: 'https://github.com/cortex-central/evolution-engine',
  documentation: 'https://docs.cortex-central.com/evolution-engine',
  support: 'https://support.cortex-central.com'
};
