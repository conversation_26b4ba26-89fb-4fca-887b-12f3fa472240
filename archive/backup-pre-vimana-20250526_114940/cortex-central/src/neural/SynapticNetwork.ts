import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { CentralMemory } from '../memory/CentralMemory';

export interface NeuralSignal {
  id: string;
  type: NeuralSignalType;
  source: string;
  target?: string;
  payload: any;
  priority: SignalPriority;
  timestamp: Date;
  ttl?: number; // Time to live in milliseconds
  route?: SignalRoute;
}

export enum NeuralSignalType {
  // Signaux cognitifs
  THOUGHT = 'thought',
  DECISION = 'decision',
  MEMORY_FORMATION = 'memory_formation',
  LEARNING = 'learning',
  
  // Signaux sensoriels
  SENSORY_INPUT = 'sensory_input',
  PERCEPTION = 'perception',
  ATTENTION = 'attention',
  
  // Signaux moteurs
  ACTION_INITIATION = 'action_initiation',
  MOTOR_COMMAND = 'motor_command',
  COORDINATION = 'coordination',
  
  // Signaux émotionnels
  EMOTION = 'emotion',
  MOTIVATION = 'motivation',
  REWARD = 'reward',
  
  // Signaux système
  HEALTH_CHECK = 'health_check',
  SYNCHRONIZATION = 'synchronization',
  ADAPTATION = 'adaptation',
  EVOLUTION = 'evolution'
}

export enum SignalPriority {
  CRITICAL = 0,    // Signaux vitaux (sécurité, erreurs critiques)
  HIGH = 1,        // Signaux importants (décisions, actions)
  NORMAL = 2,      // Signaux standard (communication normale)
  LOW = 3,         // Signaux de fond (monitoring, logs)
  BACKGROUND = 4   // Signaux non urgents (apprentissage, optimisation)
}

export interface SignalRoute {
  protocol: 'kafka' | 'nats' | 'redis' | 'direct';
  path: string[];
  latency: number;
  reliability: number;
}

export interface SynapticConnection {
  id: string;
  source: string;
  target: string;
  strength: number; // 0.0 to 1.0
  type: ConnectionType;
  lastUsed: Date;
  usageCount: number;
  averageLatency: number;
  successRate: number;
  isActive: boolean;
}

export enum ConnectionType {
  EXCITATORY = 'excitatory',     // Connexion activatrice
  INHIBITORY = 'inhibitory',     // Connexion inhibitrice
  MODULATORY = 'modulatory',     // Connexion modulatrice
  FEEDBACK = 'feedback',         // Connexion de rétroaction
  FEEDFORWARD = 'feedforward'    // Connexion anticipatrice
}

export interface SynapticNetworkConfig {
  memory: CentralMemory;
  kafkaConfig?: {
    brokers: string[];
    clientId: string;
  };
  natsConfig?: {
    servers: string[];
    reconnect: boolean;
  };
  redisConfig?: {
    host: string;
    port: number;
    cluster?: boolean;
  };
  adaptationEnabled?: boolean;
  pruningEnabled?: boolean;
  learningRate?: number;
}

export interface NetworkTopology {
  nodes: string[];
  connections: SynapticConnection[];
  clusters: NetworkCluster[];
  centralityScores: Map<string, number>;
  pathLengths: Map<string, Map<string, number>>;
}

export interface NetworkCluster {
  id: string;
  nodes: string[];
  density: number;
  function: string;
}

/**
 * Réseau Synaptique - Communication Neuronale Avancée
 * 
 * Implémente un système de communication inspiré du cerveau humain
 * avec adaptation dynamique des connexions et routage intelligent.
 */
export class SynapticNetwork extends EventEmitter {
  private memory: CentralMemory;
  private adaptationEnabled: boolean;
  private pruningEnabled: boolean;
  private learningRate: number;
  
  private isInitialized: boolean = false;
  private connections: Map<string, SynapticConnection> = new Map();
  private signalQueue: Map<SignalPriority, NeuralSignal[]> = new Map();
  private routingTable: Map<string, SignalRoute> = new Map();
  private networkTopology: NetworkTopology;
  
  // Protocoles de communication
  private kafka: any;
  private nats: any;
  private redis: any;
  
  // Métriques réseau
  private networkMetrics = {
    totalSignals: 0,
    successfulDeliveries: 0,
    failedDeliveries: 0,
    averageLatency: 0,
    networkEfficiency: 0,
    adaptationEvents: 0
  };

  constructor(config: SynapticNetworkConfig) {
    super();
    
    this.memory = config.memory;
    this.adaptationEnabled = config.adaptationEnabled !== false;
    this.pruningEnabled = config.pruningEnabled !== false;
    this.learningRate = config.learningRate || 0.1;
    
    // Initialisation des files de priorité
    Object.values(SignalPriority).forEach(priority => {
      if (typeof priority === 'number') {
        this.signalQueue.set(priority, []);
      }
    });
    
    this.networkTopology = {
      nodes: [],
      connections: [],
      clusters: [],
      centralityScores: new Map(),
      pathLengths: new Map()
    };
  }

  /**
   * Initialise le réseau synaptique
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('🧠 Initialisation du Réseau Synaptique...');

      // Chargement de la topologie depuis la mémoire
      await this.loadNetworkTopology();

      // Initialisation des protocoles de communication
      await this.initializeCommunicationProtocols();

      // Démarrage du processeur de signaux
      this.startSignalProcessor();

      // Démarrage de l'adaptation réseau
      if (this.adaptationEnabled) {
        this.startNetworkAdaptation();
      }

      // Démarrage de l'élagage des connexions
      if (this.pruningEnabled) {
        this.startConnectionPruning();
      }

      this.isInitialized = true;
      logger.info('✅ Réseau Synaptique initialisé');

    } catch (error) {
      logger.error('❌ Erreur lors de l\'initialisation du Réseau Synaptique:', error);
      throw error;
    }
  }

  /**
   * Envoie un signal neural
   */
  public async sendNeuralSignal(signal: Partial<NeuralSignal>): Promise<void> {
    const completeSignal: NeuralSignal = {
      id: signal.id || this.generateSignalId(),
      type: signal.type!,
      source: signal.source!,
      target: signal.target,
      payload: signal.payload,
      priority: signal.priority || SignalPriority.NORMAL,
      timestamp: new Date(),
      ttl: signal.ttl || 30000, // 30 secondes par défaut
      route: signal.route
    };

    // Détermination de la route optimale
    if (!completeSignal.route && completeSignal.target) {
      completeSignal.route = await this.determineOptimalRoute(
        completeSignal.source,
        completeSignal.target,
        completeSignal.priority
      );
    }

    // Ajout à la file de priorité
    const queue = this.signalQueue.get(completeSignal.priority)!;
    queue.push(completeSignal);
    
    // Tri par priorité et timestamp
    queue.sort((a, b) => {
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      return a.timestamp.getTime() - b.timestamp.getTime();
    });

    this.networkMetrics.totalSignals++;

    this.emit('signal-queued', {
      signal: completeSignal,
      queueSize: queue.length,
      timestamp: new Date()
    });
  }

  /**
   * Établit une connexion synaptique
   */
  public async establishConnection(
    source: string,
    target: string,
    type: ConnectionType = ConnectionType.EXCITATORY,
    initialStrength: number = 0.5
  ): Promise<void> {
    const connectionId = `${source}->${target}`;
    
    const connection: SynapticConnection = {
      id: connectionId,
      source,
      target,
      strength: Math.max(0, Math.min(1, initialStrength)),
      type,
      lastUsed: new Date(),
      usageCount: 0,
      averageLatency: 0,
      successRate: 100,
      isActive: true
    };

    this.connections.set(connectionId, connection);
    
    // Mise à jour de la topologie
    await this.updateNetworkTopology();
    
    // Sauvegarde en mémoire
    await this.saveNetworkTopology();

    logger.debug(`🔗 Connexion synaptique établie: ${connectionId} (force: ${initialStrength})`);

    this.emit('connection-established', {
      connection,
      timestamp: new Date()
    });
  }

  /**
   * Détermine la route optimale pour un signal
   */
  private async determineOptimalRoute(
    source: string,
    target: string,
    priority: SignalPriority
  ): Promise<SignalRoute> {
    // Vérification du cache de routage
    const cacheKey = `${source}->${target}-${priority}`;
    const cachedRoute = this.routingTable.get(cacheKey);
    
    if (cachedRoute && this.isRouteValid(cachedRoute)) {
      return cachedRoute;
    }

    // Calcul de la route optimale
    const route = await this.calculateOptimalRoute(source, target, priority);
    
    // Mise en cache
    this.routingTable.set(cacheKey, route);
    
    return route;
  }

  /**
   * Calcule la route optimale
   */
  private async calculateOptimalRoute(
    source: string,
    target: string,
    priority: SignalPriority
  ): Promise<SignalRoute> {
    // Algorithme de routage basé sur la priorité et les métriques réseau
    let protocol: SignalRoute['protocol'];
    let latency: number;
    let reliability: number;

    switch (priority) {
      case SignalPriority.CRITICAL:
        protocol = 'direct'; // Communication directe pour les signaux critiques
        latency = 1;
        reliability = 0.99;
        break;
        
      case SignalPriority.HIGH:
        protocol = 'nats'; // NATS pour les signaux temps réel
        latency = 5;
        reliability = 0.95;
        break;
        
      case SignalPriority.NORMAL:
        protocol = 'redis'; // Redis pour les signaux standard
        latency = 10;
        reliability = 0.90;
        break;
        
      case SignalPriority.LOW:
      case SignalPriority.BACKGROUND:
        protocol = 'kafka'; // Kafka pour les signaux non urgents
        latency = 50;
        reliability = 0.85;
        break;
        
      default:
        protocol = 'redis';
        latency = 10;
        reliability = 0.90;
    }

    // Calcul du chemin optimal dans la topologie
    const path = this.findShortestPath(source, target);

    return {
      protocol,
      path,
      latency,
      reliability
    };
  }

  /**
   * Trouve le chemin le plus court entre deux nœuds
   */
  private findShortestPath(source: string, target: string): string[] {
    // Implémentation simplifiée de Dijkstra
    const distances = new Map<string, number>();
    const previous = new Map<string, string>();
    const unvisited = new Set(this.networkTopology.nodes);

    // Initialisation
    distances.set(source, 0);
    this.networkTopology.nodes.forEach(node => {
      if (node !== source) {
        distances.set(node, Infinity);
      }
    });

    while (unvisited.size > 0) {
      // Nœud avec la distance minimale
      let current = Array.from(unvisited).reduce((min, node) => 
        distances.get(node)! < distances.get(min)! ? node : min
      );

      if (current === target) break;
      
      unvisited.delete(current);

      // Mise à jour des distances des voisins
      const neighbors = this.getNeighbors(current);
      neighbors.forEach(neighbor => {
        if (unvisited.has(neighbor)) {
          const connection = this.connections.get(`${current}->${neighbor}`);
          const weight = connection ? (1 - connection.strength) : 1;
          const alt = distances.get(current)! + weight;
          
          if (alt < distances.get(neighbor)!) {
            distances.set(neighbor, alt);
            previous.set(neighbor, current);
          }
        }
      });
    }

    // Reconstruction du chemin
    const path: string[] = [];
    let current = target;
    
    while (current !== undefined) {
      path.unshift(current);
      current = previous.get(current)!;
    }

    return path.length > 1 ? path : [source, target];
  }

  /**
   * Obtient les voisins d'un nœud
   */
  private getNeighbors(node: string): string[] {
    return Array.from(this.connections.values())
      .filter(conn => conn.source === node && conn.isActive)
      .map(conn => conn.target);
  }

  /**
   * Vérifie si une route est valide
   */
  private isRouteValid(route: SignalRoute): boolean {
    // Vérification de la validité des nœuds dans le chemin
    return route.path.every(node => this.networkTopology.nodes.includes(node));
  }

  /**
   * Démarre le processeur de signaux
   */
  private startSignalProcessor(): void {
    setInterval(async () => {
      await this.processSignalQueue();
    }, 10); // Traitement toutes les 10ms

    logger.info('🔄 Processeur de signaux démarré');
  }

  /**
   * Traite la file de signaux
   */
  private async processSignalQueue(): Promise<void> {
    // Traitement par ordre de priorité
    for (const priority of [
      SignalPriority.CRITICAL,
      SignalPriority.HIGH,
      SignalPriority.NORMAL,
      SignalPriority.LOW,
      SignalPriority.BACKGROUND
    ]) {
      const queue = this.signalQueue.get(priority)!;
      
      if (queue.length > 0) {
        const signal = queue.shift()!;
        
        // Vérification du TTL
        if (Date.now() - signal.timestamp.getTime() > signal.ttl!) {
          this.networkMetrics.failedDeliveries++;
          continue;
        }

        try {
          await this.deliverSignal(signal);
          this.networkMetrics.successfulDeliveries++;
        } catch (error) {
          logger.error('❌ Erreur lors de la livraison du signal:', error);
          this.networkMetrics.failedDeliveries++;
        }
        
        // Traitement d'un seul signal par cycle pour les priorités élevées
        if (priority <= SignalPriority.HIGH) {
          break;
        }
      }
    }
  }

  /**
   * Livre un signal à sa destination
   */
  private async deliverSignal(signal: NeuralSignal): Promise<void> {
    const startTime = Date.now();
    
    try {
      if (signal.route) {
        switch (signal.route.protocol) {
          case 'direct':
            await this.deliverDirectSignal(signal);
            break;
          case 'kafka':
            await this.deliverKafkaSignal(signal);
            break;
          case 'nats':
            await this.deliverNATSSignal(signal);
            break;
          case 'redis':
            await this.deliverRedisSignal(signal);
            break;
        }
      }

      // Mise à jour des métriques de connexion
      if (signal.target) {
        await this.updateConnectionMetrics(signal.source, signal.target, true, Date.now() - startTime);
      }

      this.emit('signal-delivered', {
        signal,
        latency: Date.now() - startTime,
        timestamp: new Date()
      });

    } catch (error) {
      // Mise à jour des métriques d'échec
      if (signal.target) {
        await this.updateConnectionMetrics(signal.source, signal.target, false, Date.now() - startTime);
      }
      throw error;
    }
  }

  /**
   * Livre un signal en direct
   */
  private async deliverDirectSignal(signal: NeuralSignal): Promise<void> {
    this.emit('neural-signal', signal);
  }

  /**
   * Livre un signal via Kafka
   */
  private async deliverKafkaSignal(signal: NeuralSignal): Promise<void> {
    if (this.kafka) {
      const producer = this.kafka.producer();
      await producer.send({
        topic: `neural-signals-${signal.target}`,
        messages: [{
          key: signal.id,
          value: JSON.stringify(signal)
        }]
      });
    } else {
      await this.deliverDirectSignal(signal);
    }
  }

  /**
   * Livre un signal via NATS
   */
  private async deliverNATSSignal(signal: NeuralSignal): Promise<void> {
    if (this.nats) {
      this.nats.publish(`neural.${signal.target}`, JSON.stringify(signal));
    } else {
      await this.deliverDirectSignal(signal);
    }
  }

  /**
   * Livre un signal via Redis
   */
  private async deliverRedisSignal(signal: NeuralSignal): Promise<void> {
    if (this.redis) {
      await this.redis.lpush(`neural:${signal.target}`, JSON.stringify(signal));
    } else {
      await this.deliverDirectSignal(signal);
    }
  }

  /**
   * Met à jour les métriques de connexion
   */
  private async updateConnectionMetrics(
    source: string,
    target: string,
    success: boolean,
    latency: number
  ): Promise<void> {
    const connectionId = `${source}->${target}`;
    const connection = this.connections.get(connectionId);
    
    if (connection) {
      connection.usageCount++;
      connection.lastUsed = new Date();
      connection.averageLatency = (connection.averageLatency + latency) / 2;
      
      if (success) {
        connection.successRate = Math.min(100, connection.successRate + 0.1);
      } else {
        connection.successRate = Math.max(0, connection.successRate - 1);
      }

      this.connections.set(connectionId, connection);
    }
  }

  /**
   * Démarre l'adaptation du réseau
   */
  private startNetworkAdaptation(): void {
    setInterval(async () => {
      await this.adaptSynapticStrength();
    }, 60000); // Adaptation toutes les minutes

    logger.info('🧠 Adaptation du réseau démarrée');
  }

  /**
   * Adapte la force synaptique
   */
  private async adaptSynapticStrength(): Promise<void> {
    for (const connection of this.connections.values()) {
      const usagePattern = this.analyzeUsagePattern(connection);
      
      if (usagePattern.frequency > 0.8) {
        // Renforcement des connexions fréquemment utilisées
        connection.strength = Math.min(1.0, connection.strength + this.learningRate);
        this.networkMetrics.adaptationEvents++;
      } else if (usagePattern.frequency < 0.2) {
        // Affaiblissement des connexions peu utilisées
        connection.strength = Math.max(0.0, connection.strength - this.learningRate);
      }
    }

    await this.saveNetworkTopology();
  }

  /**
   * Analyse le pattern d'usage d'une connexion
   */
  private analyzeUsagePattern(connection: SynapticConnection): { frequency: number } {
    const timeSinceLastUse = Date.now() - connection.lastUsed.getTime();
    const hoursSinceLastUse = timeSinceLastUse / (1000 * 60 * 60);
    
    // Calcul de fréquence basé sur l'usage récent
    const frequency = Math.max(0, 1 - (hoursSinceLastUse / 24)); // Décroissance sur 24h
    
    return { frequency };
  }

  /**
   * Démarre l'élagage des connexions
   */
  private startConnectionPruning(): void {
    setInterval(async () => {
      await this.pruneWeakConnections();
    }, 300000); // Élagage toutes les 5 minutes

    logger.info('✂️ Élagage des connexions démarré');
  }

  /**
   * Élague les connexions faibles
   */
  private async pruneWeakConnections(): Promise<void> {
    const connectionsToRemove: string[] = [];
    
    for (const [id, connection] of this.connections) {
      if (connection.strength < 0.1 && connection.successRate < 50) {
        connectionsToRemove.push(id);
      }
    }

    for (const id of connectionsToRemove) {
      this.connections.delete(id);
      logger.debug(`✂️ Connexion élaguée: ${id}`);
    }

    if (connectionsToRemove.length > 0) {
      await this.updateNetworkTopology();
      await this.saveNetworkTopology();
    }
  }

  /**
   * Initialise les protocoles de communication
   */
  private async initializeCommunicationProtocols(): Promise<void> {
    // Initialisation simplifiée - dans une vraie implémentation,
    // on initialiserait Kafka, NATS et Redis
    logger.info('📡 Protocoles de communication initialisés');
  }

  /**
   * Met à jour la topologie du réseau
   */
  private async updateNetworkTopology(): Promise<void> {
    // Extraction des nœuds uniques
    const nodes = new Set<string>();
    const connections = Array.from(this.connections.values());
    
    connections.forEach(conn => {
      nodes.add(conn.source);
      nodes.add(conn.target);
    });

    this.networkTopology = {
      nodes: Array.from(nodes),
      connections,
      clusters: this.detectClusters(Array.from(nodes), connections),
      centralityScores: this.calculateCentralityScores(Array.from(nodes), connections),
      pathLengths: new Map()
    };
  }

  /**
   * Détecte les clusters dans le réseau
   */
  private detectClusters(nodes: string[], connections: SynapticConnection[]): NetworkCluster[] {
    // Implémentation simplifiée de détection de clusters
    return [];
  }

  /**
   * Calcule les scores de centralité
   */
  private calculateCentralityScores(nodes: string[], connections: SynapticConnection[]): Map<string, number> {
    const scores = new Map<string, number>();
    
    nodes.forEach(node => {
      const inDegree = connections.filter(c => c.target === node).length;
      const outDegree = connections.filter(c => c.source === node).length;
      scores.set(node, inDegree + outDegree);
    });
    
    return scores;
  }

  /**
   * Charge la topologie depuis la mémoire
   */
  private async loadNetworkTopology(): Promise<void> {
    try {
      const topology = await this.memory.retrieve('network_topology');
      if (topology) {
        this.networkTopology = topology;
        
        // Reconstruction des connexions
        topology.connections.forEach((conn: SynapticConnection) => {
          this.connections.set(conn.id, conn);
        });
        
        logger.info(`🧠 Topologie chargée: ${topology.nodes.length} nœuds, ${topology.connections.length} connexions`);
      }
    } catch (error) {
      logger.warn('⚠️ Impossible de charger la topologie réseau:', error);
    }
  }

  /**
   * Sauvegarde la topologie en mémoire
   */
  private async saveNetworkTopology(): Promise<void> {
    try {
      await this.memory.store('network_topology', this.networkTopology);
    } catch (error) {
      logger.error('❌ Erreur lors de la sauvegarde de la topologie:', error);
    }
  }

  /**
   * Génère un ID unique pour un signal
   */
  private generateSignalId(): string {
    return `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Obtient les métriques du réseau
   */
  public getNetworkMetrics() {
    const efficiency = this.networkMetrics.totalSignals > 0 ? 
      this.networkMetrics.successfulDeliveries / this.networkMetrics.totalSignals : 0;

    return {
      ...this.networkMetrics,
      networkEfficiency: efficiency,
      activeConnections: this.connections.size,
      averageConnectionStrength: this.calculateAverageConnectionStrength(),
      topologyStats: {
        nodes: this.networkTopology.nodes.length,
        connections: this.networkTopology.connections.length,
        clusters: this.networkTopology.clusters.length
      }
    };
  }

  /**
   * Calcule la force moyenne des connexions
   */
  private calculateAverageConnectionStrength(): number {
    if (this.connections.size === 0) return 0;
    
    const totalStrength = Array.from(this.connections.values())
      .reduce((sum, conn) => sum + conn.strength, 0);
    
    return totalStrength / this.connections.size;
  }

  /**
   * Arrêt gracieux du réseau
   */
  public async shutdown(): Promise<void> {
    logger.info('🛑 Arrêt du Réseau Synaptique...');
    
    // Sauvegarde finale
    await this.saveNetworkTopology();
    
    // Fermeture des connexions
    if (this.kafka) await this.kafka.disconnect();
    if (this.nats) await this.nats.close();
    if (this.redis) await this.redis.disconnect();

    this.isInitialized = false;
    logger.info('✅ Réseau Synaptique arrêté');
  }
}
