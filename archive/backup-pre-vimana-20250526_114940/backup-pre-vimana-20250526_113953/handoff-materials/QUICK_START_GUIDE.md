# Quick Start Guide - Retreat And Be Distributed Nervous System

## System Overview
The Retreat And Be platform is a distributed nervous system with 14 specialized AI agents coordinated by Cortex Central.

## Quick Start Commands

### Start the System
```bash
# Start all services
./scripts/start-full-system.sh

# Monitor system health
./scripts/monitor-performance.sh
```

### Test the System
```bash
# Run integration tests
./scripts/test-system-integration.sh

# Test end-to-end workflows
node ./scripts/test-end-to-end-workflow.js
```

### Deploy to Production
```bash
# Deploy to production
./scripts/deploy-production.sh
```

## Key Endpoints
- **Cortex Central:** http://localhost:3000
- **Health Check:** http://localhost:3000/api/health
- **Documentation:** ./final-documentation/README.md

## Emergency Contacts
- **Technical Lead:** [Contact Information]
- **DevOps Team:** [Contact Information]
- **Security Team:** [Contact Information]

## Support Resources
- **Operations Manual:** ./final-documentation/operations/operations-manual.md
- **API Reference:** ./final-documentation/api/api-reference.md
- **Troubleshooting:** ./final-documentation/operations/troubleshooting-guide.md
