# Système Multi-Agent pour Retraites de Bien-être

Un système complet pour la planification, l'organisation et la gestion de retraites de bien-être, ainsi que pour la mise en relation de professionnels (partenaires) et l'assistance aux clients.

## Fonctionnalités

- **Planification de retraites**: Création et organisation de retraites de bien-être
- **Mise en relation de partenaires**: Recherche et mise en relation de professionnels adaptés
- **Assistance client**: Recommandations de retraites et réponses aux questions des clients
- **Gestion des réservations**: Création et suivi des réservations
- **Notifications**: Envoi de notifications par email et SMS

## Architecture

Le système est basé sur une architecture multi-agent où différents agents spécialisés collaborent pour accomplir des tâches complexes. Il utilise LangGraph pour orchestrer les workflows entre les agents.

### Composants

- **Agents**: Composants spécialisés qui effectuent des tâches spécifiques
- **Workflows**: Graphes dirigés qui définissent le flux entre les agents
- **API REST**: Interface pour interagir avec le système
- **Base de données**: Stockage des données des retraites, partenaires, clients et réservations
- **Système de notification**: Envoi de notifications aux utilisateurs

## Installation

1. Cloner le dépôt:
   ```bash
   git clone https://github.com/yourusername/retreat-system.git
   cd retreat-system
   ```

2. Installer les dépendances:
   ```bash
   pip install -r requirements.txt
   ```

3. Initialiser la base de données:
   ```bash
   python -c "from src.database.connection import init_db; init_db()"
   ```

## Utilisation

### Exécuter l'API

```bash
python run_api.py
```

L'API sera disponible à l'adresse http://localhost:8000. La documentation Swagger est accessible à http://localhost:8000/docs.

### Exécuter les tests

```bash
./run_tests.sh
```

### Exemples d'utilisation

#### Planification de retraites

```python
from src.graph.types import State
from src.graph.retreat_workflow import run_retreat_workflow

# Créer un état initial avec les détails de la retraite
initial_state = State(
    task={
        "type": "retreat_planning",
        "parameters": {
            "retreat_type": "yoga",
            "location": {"country": "France", "region": "Provence"},
            "duration": 7
        }
    }
)

# Exécuter le workflow
final_state = await run_retreat_workflow(initial_state)

# Accéder aux résultats
retreat = final_state.results.get("retreat")
```

#### Recherche de partenaires

```python
from src.graph.types import State
from src.graph.partner_workflow import run_partner_workflow

# Créer un état initial avec les critères de recherche
initial_state = State(
    task={
        "type": "partner_matching",
        "parameters": {
            "partner_types": ["wellness_expert", "accommodation"],
            "specialties": ["yoga", "meditation"],
            "location": {"country": "France", "region": "Provence"}
        }
    }
)

# Exécuter le workflow
final_state = await run_partner_workflow(initial_state)

# Accéder aux résultats
partners = final_state.results.get("partners")
```

## API REST

L'API REST expose les fonctionnalités suivantes:

- `/api/partners`: Gestion des partenaires
- `/api/retreats`: Gestion des retraites
- `/api/clients`: Gestion des clients
- `/api/bookings`: Gestion des réservations
- `/api/workflows`: Exécution des workflows

## Structure du projet

```
.
├── data/                  # Données de la base de données
├── src/                   # Code source
│   ├── agents/            # Agents du système
│   │   └── nodes/         # Nœuds des agents
│   ├── api/               # API REST
│   │   └── routes/        # Routes de l'API
│   ├── config/            # Configuration
│   ├── database/          # Accès à la base de données
│   │   └── repositories/  # Repositories pour l'accès aux données
│   ├── graph/             # Workflows
│   ├── models/            # Modèles de données
│   ├── notifications/     # Système de notification
│   └── utils/             # Utilitaires
├── tests/                 # Tests
│   ├── api/               # Tests de l'API
│   ├── database/          # Tests de la base de données
│   ├── models/            # Tests des modèles
│   └── notifications/     # Tests du système de notification
├── run_api.py             # Script pour exécuter l'API
├── run_tests.sh           # Script pour exécuter les tests
└── requirements.txt       # Dépendances
```

## Prochaines étapes

- Intégration avec des services de paiement
- Interface utilisateur web
- Système d'authentification et d'autorisation
- Déploiement sur un serveur de production
- Intégration avec des services tiers (calendriers, cartes, etc.)

## Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.
