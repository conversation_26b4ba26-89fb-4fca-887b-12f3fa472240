{"name": "rb2-backend", "version": "1.0.0", "description": "Backend pour le projet RB2", "type": "module", "scripts": {"start": "node dist/main.js", "build": "tsc -p tsconfig.build.json", "dev": "nodemon src/main.ts", "test": "jest --config jest.config.js", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "validate-types": "ts-node src/scripts/validate-types.ts", "complexity": "ts-node src/scripts/cyclomatic-complexity.ts"}}