const CacheAdapter = require('../services/cache/CacheAdapter');
const { CacheTier } = require('../services/cache/CacheConfig');
const logger = require('../utils/logger');

/**
 * Middleware de cache pour les routes API
 */

/**
 * Middleware de cache pour les routes de livestream
 */
const livestreamCacheMiddleware = CacheAdapter.createApiCacheMiddleware({
  keyPrefix: 'api:livestream',
  tier: CacheTier.FREQUENT,
  tags: ['api', 'livestreams'],
  methods: ['GET'],
});

/**
 * Middleware de cache pour les routes de blog
 */
const blogCacheMiddleware = CacheAdapter.createApiCacheMiddleware({
  keyPrefix: 'api:blog',
  tier: CacheTier.STANDARD,
  tags: ['api', 'blog'],
  methods: ['GET'],
});

/**
 * Middleware pour invalider le cache après une modification
 * @param {Array<string>} tags Tags à invalider
 * @returns {Function} Middleware Express
 */
const invalidateCacheMiddleware = (tags = []) => {
  return (req, res, next) => {
    // Stocker la méthode end originale
    const originalEnd = res.end;
    
    // Remplacer la méthode end pour intercepter la réponse
    res.end = function(chunk, encoding) {
      // Restaurer la méthode originale
      res.end = originalEnd;
      
      // Si la réponse est un succès (2xx), invalider le cache
      if (res.statusCode >= 200 && res.statusCode < 300) {
        try {
          const invalidatedCount = CacheAdapter.invalidateTags(tags);
          logger.info(`Invalidated ${invalidatedCount} cache entries for tags: ${tags.join(', ')}`);
        } catch (error) {
          logger.error(`Error invalidating cache: ${error.message}`, error);
        }
      }
      
      // Appeler la méthode originale
      return originalEnd.call(this, chunk, encoding);
    };
    
    next();
  };
};

/**
 * Middleware pour invalider le cache des livestreams
 */
const invalidateLivestreamCacheMiddleware = (req, res, next) => {
  // Extraire l'ID du livestream des paramètres de la requête
  const { id } = req.params;
  
  // Définir les tags à invalider
  const tags = ['livestreams'];
  
  if (id) {
    tags.push(`livestream:${id}`);
  }
  
  // Appliquer le middleware d'invalidation
  return invalidateCacheMiddleware(tags)(req, res, next);
};

/**
 * Middleware pour invalider le cache des articles de blog
 */
const invalidateBlogCacheMiddleware = (req, res, next) => {
  // Extraire l'ID de l'article de blog des paramètres de la requête
  const { id } = req.params;
  
  // Définir les tags à invalider
  const tags = ['blog:posts'];
  
  if (id) {
    tags.push(`blog:post:${id}`);
  }
  
  // Appliquer le middleware d'invalidation
  return invalidateCacheMiddleware(tags)(req, res, next);
};

module.exports = {
  livestreamCacheMiddleware,
  blogCacheMiddleware,
  invalidateCacheMiddleware,
  invalidateLivestreamCacheMiddleware,
  invalidateBlogCacheMiddleware,
};
