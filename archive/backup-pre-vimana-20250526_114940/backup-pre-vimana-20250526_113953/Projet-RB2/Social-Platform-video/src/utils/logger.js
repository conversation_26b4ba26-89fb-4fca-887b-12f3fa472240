/**
 * Utilitaire de journalisation
 */
const logger = {
  /**
   * Journalise un message d'information
   * @param {string} message Message à journaliser
   */
  info(message) {
    console.log(`[INFO] ${new Date().toISOString()} - ${message}`);
  },
  
  /**
   * Journalise un message d'avertissement
   * @param {string} message Message à journaliser
   */
  warn(message) {
    console.warn(`[WARN] ${new Date().toISOString()} - ${message}`);
  },
  
  /**
   * Journalise un message d'erreur
   * @param {string} message Message à journaliser
   * @param {Error} error Erreur à journaliser
   */
  error(message, error) {
    console.error(`[ERROR] ${new Date().toISOString()} - ${message}`);
    
    if (error && error.stack) {
      console.error(error.stack);
    }
  },
  
  /**
   * Journalise un message de débogage
   * @param {string} message Message à journaliser
   * @param {any} data Données à journaliser
   */
  debug(message, data) {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[DEBUG] ${new Date().toISOString()} - ${message}`);
      
      if (data !== undefined) {
        console.log(data);
      }
    }
  },
};

module.exports = logger;
