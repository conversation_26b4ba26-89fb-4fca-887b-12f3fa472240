const LivestreamService = require('./LivestreamService');
const { cacheService } = require('./cache');
const { CacheTier } = require('./cache/CacheConfig');
const CacheUtils = require('./cache/CacheUtils');
const logger = require('../utils/logger');

/**
 * Service pour la gestion des livestreams avec mise en cache
 */
class CachedLivestreamService {
  /**
   * Récupère tous les livestreams avec filtres optionnels
   * @param {Object} filters Filtres à appliquer
   * @returns {Promise<Array>} Liste des livestreams
   */
  async getLivestreams(filters = {}) {
    try {
      const cacheKey = CacheUtils.generateLivestreamsListKey(filters);
      
      // Déterminer le niveau de cache en fonction des filtres
      let tier = CacheTier.STANDARD;
      
      // Les livestreams actifs changent plus fréquemment
      if (filters.status === 'live') {
        tier = CacheTier.FREQUENT;
      }
      
      return await cacheService.getOrSet(
        cacheKey,
        () => LivestreamService.getLivestreams(filters),
        {
          tier,
          tags: ['livestreams', `status:${filters.status || 'all'}`]
        }
      );
    } catch (error) {
      logger.error(`Error in cached getLivestreams: ${error.message}`, error);
      // En cas d'erreur, appeler directement le service
      return LivestreamService.getLivestreams(filters);
    }
  }

  /**
   * Récupère un livestream par son ID
   * @param {string} id ID du livestream
   * @returns {Promise<Object|null>} Livestream trouvé ou null
   */
  async getLivestreamById(id) {
    try {
      const cacheKey = CacheUtils.generateLivestreamKey(id);
      
      return await cacheService.getOrSet(
        cacheKey,
        () => LivestreamService.getLivestreamById(id),
        {
          tier: CacheTier.STANDARD,
          tags: ['livestreams', `livestream:${id}`]
        }
      );
    } catch (error) {
      logger.error(`Error in cached getLivestreamById: ${error.message}`, error);
      return LivestreamService.getLivestreamById(id);
    }
  }

  /**
   * Crée un nouveau livestream
   * @param {Object} livestreamData Données du livestream
   * @returns {Promise<Object>} Livestream créé
   */
  async createLivestream(livestreamData) {
    try {
      const result = await LivestreamService.createLivestream(livestreamData);
      
      // Invalider le cache des listes de livestreams
      CacheUtils.invalidateLivestreamCache(result.id);
      
      return result;
    } catch (error) {
      logger.error(`Error in cached createLivestream: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Met à jour un livestream existant
   * @param {string} id ID du livestream
   * @param {Object} updateData Données à mettre à jour
   * @returns {Promise<Object|null>} Livestream mis à jour ou null
   */
  async updateLivestream(id, updateData) {
    try {
      const result = await LivestreamService.updateLivestream(id, updateData);
      
      if (result) {
        // Invalider le cache du livestream
        CacheUtils.invalidateLivestreamCache(id);
      }
      
      return result;
    } catch (error) {
      logger.error(`Error in cached updateLivestream: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Démarre un livestream
   * @param {string} id ID du livestream
   * @returns {Promise<Object|null>} Livestream mis à jour ou null
   */
  async startLivestream(id) {
    try {
      const result = await LivestreamService.startLivestream(id);
      
      if (result) {
        // Invalider le cache du livestream et des listes
        CacheUtils.invalidateLivestreamCache(id);
      }
      
      return result;
    } catch (error) {
      logger.error(`Error in cached startLivestream: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Termine un livestream
   * @param {string} id ID du livestream
   * @returns {Promise<Object|null>} Livestream mis à jour ou null
   */
  async endLivestream(id) {
    try {
      const result = await LivestreamService.endLivestream(id);
      
      if (result) {
        // Invalider le cache du livestream et des listes
        CacheUtils.invalidateLivestreamCache(id);
      }
      
      return result;
    } catch (error) {
      logger.error(`Error in cached endLivestream: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Récupère les messages d'un livestream
   * @param {string} id ID du livestream
   * @returns {Promise<Array>} Liste des messages
   */
  async getLivestreamMessages(id) {
    try {
      const cacheKey = CacheUtils.generateLivestreamMessagesKey(id);
      
      // Les messages de livestream changent très fréquemment
      return await cacheService.getOrSet(
        cacheKey,
        () => LivestreamService.getLivestreamMessages(id),
        {
          tier: CacheTier.MICRO, // Très court TTL
          tags: ['livestreams', `livestream:${id}`, 'messages']
        }
      );
    } catch (error) {
      logger.error(`Error in cached getLivestreamMessages: ${error.message}`, error);
      return LivestreamService.getLivestreamMessages(id);
    }
  }

  /**
   * Envoie un message dans un livestream
   * @param {string} id ID du livestream
   * @param {Object} messageData Données du message
   * @returns {Promise<Object>} Message créé
   */
  async sendLivestreamMessage(id, messageData) {
    try {
      const result = await LivestreamService.sendLivestreamMessage(id, messageData);
      
      // Invalider le cache des messages du livestream
      cacheService.invalidateByTag(`livestream:${id}:messages`);
      
      return result;
    } catch (error) {
      logger.error(`Error in cached sendLivestreamMessage: ${error.message}`, error);
      throw error;
    }
  }
}

module.exports = new CachedLivestreamService();
