import securityService from './securityService';

/**
 * Interface pour les clés de chiffrement
 */
interface KeyPair {
  publicKey: string;
  privateKey: string;
}

/**
 * Interface pour un message chiffré
 */
interface EncryptedMessage {
  ciphertext: string;
  iv: string;
  ephemeralPublicKey: string;
  signature: string;
}

/**
 * Service de chiffrement de bout en bout pour les messages
 * Utilise le protocole Double Ratchet via le microservice Security
 */
export class E2EEncryptionService {
  private static readonly STORAGE_KEY = 'e2e_encryption_keys';
  private keyPair: KeyPair | null = null;
  private sessionKeys: Map<string, any> = new Map();
  
  constructor() {
    this.loadKeys();
  }
  
  /**
   * Charge les clés depuis le stockage local
   */
  private loadKeys(): void {
    try {
      const storedKeys = localStorage.getItem(E2EEncryptionService.STORAGE_KEY);
      if (storedKeys) {
        this.keyPair = JSON.parse(storedKeys);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des clés de chiffrement:', error);
    }
  }
  
  /**
   * Sauvegarde les clés dans le stockage local
   */
  private saveKeys(): void {
    try {
      if (this.keyPair) {
        localStorage.setItem(E2EEncryptionService.STORAGE_KEY, JSON.stringify(this.keyPair));
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des clés de chiffrement:', error);
    }
  }
  
  /**
   * Vérifie si les clés de chiffrement sont déjà générées
   */
  hasKeys(): boolean {
    return this.keyPair !== null;
  }
  
  /**
   * Génère une nouvelle paire de clés
   */
  async generateKeys(): Promise<void> {
    try {
      // Utiliser le service de sécurité pour générer les clés
      const response = await fetch('/api/security/encryption/generate-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('Erreur lors de la génération des clés');
      }
      
      const keys = await response.json();
      this.keyPair = {
        publicKey: keys.publicKey,
        privateKey: keys.privateKey
      };
      
      this.saveKeys();
    } catch (error) {
      console.error('Erreur lors de la génération des clés:', error);
      throw new Error('Impossible de générer les clés de chiffrement');
    }
  }
  
  /**
   * Obtient la clé publique de l'utilisateur
   */
  getPublicKey(): string | null {
    return this.keyPair?.publicKey || null;
  }
  
  /**
   * Initialise une session de chiffrement avec un autre utilisateur
   * @param userId ID de l'utilisateur
   * @param publicKey Clé publique de l'utilisateur
   */
  async initSession(userId: string, publicKey: string): Promise<void> {
    if (!this.keyPair) {
      throw new Error('Les clés de chiffrement ne sont pas générées');
    }
    
    try {
      // Utiliser le service de sécurité pour initialiser la session
      const response = await fetch('/api/security/encryption/init-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          publicKey,
          myPublicKey: this.keyPair.publicKey
        })
      });
      
      if (!response.ok) {
        throw new Error('Erreur lors de l\'initialisation de la session');
      }
      
      const sessionData = await response.json();
      this.sessionKeys.set(userId, sessionData);
    } catch (error) {
      console.error('Erreur lors de l\'initialisation de la session:', error);
      throw new Error('Impossible d\'initialiser la session de chiffrement');
    }
  }
  
  /**
   * Chiffre un message pour un destinataire spécifique
   * @param message Message à chiffrer
   * @param recipientId ID du destinataire
   * @param recipientPublicKey Clé publique du destinataire
   */
  async encryptMessage(message: string, recipientId: string, recipientPublicKey: string): Promise<string> {
    if (!this.keyPair) {
      throw new Error('Les clés de chiffrement ne sont pas générées');
    }
    
    try {
      // Vérifier si une session existe déjà avec ce destinataire
      if (!this.sessionKeys.has(recipientId)) {
        await this.initSession(recipientId, recipientPublicKey);
      }
      
      // Utiliser le service de sécurité pour le chiffrement
      const encryptedMessage = await securityService.encryptE2E(message, recipientPublicKey);
      return encryptedMessage;
    } catch (error) {
      console.error('Erreur lors du chiffrement du message:', error);
      throw new Error('Impossible de chiffrer le message');
    }
  }
  
  /**
   * Déchiffre un message
   * @param encryptedMessage Message chiffré
   * @param senderId ID de l'expéditeur
   */
  async decryptMessage(encryptedMessage: string, senderId: string): Promise<string> {
    if (!this.keyPair) {
      throw new Error('Les clés de chiffrement ne sont pas générées');
    }
    
    try {
      // Utiliser le service de sécurité pour le déchiffrement
      const decryptedMessage = await securityService.decryptE2E(encryptedMessage);
      return decryptedMessage;
    } catch (error) {
      console.error('Erreur lors du déchiffrement du message:', error);
      throw new Error('Impossible de déchiffrer le message');
    }
  }
  
  /**
   * Vérifie l'empreinte de sécurité d'un utilisateur
   * @param userId ID de l'utilisateur
   * @param publicKey Clé publique de l'utilisateur
   */
  async verifyFingerprint(userId: string, publicKey: string): Promise<boolean> {
    try {
      // Utiliser le service de sécurité pour vérifier l'empreinte
      const response = await fetch('/api/security/encryption/verify-fingerprint', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId,
          publicKey
        })
      });
      
      if (!response.ok) {
        throw new Error('Erreur lors de la vérification de l\'empreinte');
      }
      
      const result = await response.json();
      return result.verified;
    } catch (error) {
      console.error('Erreur lors de la vérification de l\'empreinte:', error);
      return false;
    }
  }
  
  /**
   * Génère une empreinte de sécurité pour une clé publique
   * @param publicKey Clé publique
   */
  generateFingerprint(publicKey: string): string {
    // Cette méthode devrait être remplacée par l'implémentation réelle
    // de la génération d'empreinte
    
    // Exemple simple (ne pas utiliser en production)
    const hash = Array.from(publicKey)
      .reduce((acc, char) => acc + char.charCodeAt(0), 0)
      .toString(16);
    
    // Formater l'empreinte en groupes de 4 caractères
    return hash.match(/.{1,4}/g)?.join('-') || hash;
  }
  
  /**
   * Réinitialise les clés de chiffrement
   * Attention: cette action supprime définitivement les clés actuelles
   */
  async resetKeys(): Promise<void> {
    this.keyPair = null;
    this.sessionKeys.clear();
    localStorage.removeItem(E2EEncryptionService.STORAGE_KEY);
    
    // Générer de nouvelles clés
    await this.generateKeys();
  }
}

// Exporte une instance par défaut du service
export default new E2EEncryptionService();
