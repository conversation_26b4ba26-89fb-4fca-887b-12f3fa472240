/**
 * Lazy Loading Service
 * 
 * This service provides utilities for lazy loading images and videos,
 * including progressive loading, low-quality image placeholders,
 * and viewport detection.
 */

// Configuration for lazy loading
export interface LazyLoadingConfig {
  rootMargin: string; // Margin around the root
  threshold: number[]; // Visibility thresholds
  placeholderColor: string; // Background color for placeholders
  placeholderQuality: number; // Quality of placeholder images (0-100)
  placeholderBlur: number; // Blur radius for placeholders
  loadingDelay: number; // Delay before loading in ms
  loadingPriority: 'low' | 'medium' | 'high'; // Priority for loading
  preloadDistance: number; // Distance in pixels to preload
  retryCount: number; // Number of retries for failed loads
  retryDelay: number; // Delay between retries in ms
  useProgressiveJpeg: boolean; // Whether to use progressive JPEG
  useWebP: boolean; // Whether to use WebP when supported
  useAvif: boolean; // Whether to use AVIF when supported
}

// Default configuration
export const DEFAULT_LAZY_LOADING_CONFIG: LazyLoadingConfig = {
  rootMargin: '200px 0px', // 200px above and below viewport
  threshold: [0, 0.25, 0.5, 0.75, 1], // Visibility thresholds
  placeholderColor: '#f0f0f0', // Light gray
  placeholderQuality: 10, // Very low quality
  placeholderBlur: 10, // 10px blur
  loadingDelay: 0, // No delay
  loadingPriority: 'medium', // Medium priority
  preloadDistance: 1000, // Preload 1000px away
  retryCount: 3, // Retry 3 times
  retryDelay: 1000, // 1 second between retries
  useProgressiveJpeg: true, // Use progressive JPEG
  useWebP: true, // Use WebP when supported
  useAvif: true, // Use AVIF when supported
};

// Lazy loading item state
export enum LazyLoadState {
  INITIAL = 'initial', // Not yet observed
  PLACEHOLDER = 'placeholder', // Placeholder is visible
  LOADING = 'loading', // Full image/video is loading
  LOADED = 'loaded', // Full image/video is loaded
  ERROR = 'error', // Error loading image/video
}

// Lazy loading item
export interface LazyLoadItem {
  id: string;
  url: string;
  placeholderUrl?: string;
  width: number;
  height: number;
  state: LazyLoadState;
  priority: 'low' | 'medium' | 'high';
  type: 'image' | 'video';
  intersectionRatio: number;
  retryCount: number;
  loadStartTime?: number;
  loadEndTime?: number;
  error?: string;
}

// Map of IntersectionObserver instances
const observers: Map<string, IntersectionObserver> = new Map();

// Map of lazy load items
const items: Map<string, LazyLoadItem> = new Map();

// Map of callbacks
const callbacks: Map<string, (item: LazyLoadItem) => void> = new Map();

/**
 * Initialize lazy loading
 * @param config Configuration options
 */
export function initLazyLoading(
  config: Partial<LazyLoadingConfig> = {}
): void {
  // Merge with default config
  const mergedConfig = { ...DEFAULT_LAZY_LOADING_CONFIG, ...config };
  
  // Create the IntersectionObserver
  const observer = new IntersectionObserver(
    handleIntersection,
    {
      rootMargin: mergedConfig.rootMargin,
      threshold: mergedConfig.threshold,
    }
  );
  
  // Store the observer
  observers.set('default', observer);
  
  // Check for WebP support
  checkWebPSupport();
  
  // Check for AVIF support
  checkAvifSupport();
}

// WebP support flag
let webpSupported: boolean | null = null;

// AVIF support flag
let avifSupported: boolean | null = null;

/**
 * Check if WebP is supported
 * @returns Promise resolving to whether WebP is supported
 */
export async function checkWebPSupport(): Promise<boolean> {
  if (webpSupported !== null) {
    return webpSupported;
  }
  
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = function() {
      webpSupported = (img.width > 0) && (img.height > 0);
      resolve(webpSupported);
    };
    img.onerror = function() {
      webpSupported = false;
      resolve(webpSupported);
    };
    img.src = 'data:image/webp;base64,UklGRhoAAABXRUJQVlA4TA0AAAAvAAAAEAcQERGIiP4HAA==';
  });
}

/**
 * Check if AVIF is supported
 * @returns Promise resolving to whether AVIF is supported
 */
export async function checkAvifSupport(): Promise<boolean> {
  if (avifSupported !== null) {
    return avifSupported;
  }
  
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = function() {
      avifSupported = (img.width > 0) && (img.height > 0);
      resolve(avifSupported);
    };
    img.onerror = function() {
      avifSupported = false;
      resolve(avifSupported);
    };
    img.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A=';
  });
}

/**
 * Get the optimal image format based on browser support
 * @param url Original image URL
 * @param config Lazy loading configuration
 * @returns Optimal image URL
 */
export function getOptimalImageUrl(
  url: string,
  config: LazyLoadingConfig = DEFAULT_LAZY_LOADING_CONFIG
): string {
  // If the URL already has a format query parameter, return as is
  if (url.includes('format=')) {
    return url;
  }
  
  // Check if we should use AVIF
  if (config.useAvif && avifSupported) {
    return appendFormatToUrl(url, 'avif');
  }
  
  // Check if we should use WebP
  if (config.useWebP && webpSupported) {
    return appendFormatToUrl(url, 'webp');
  }
  
  // Use progressive JPEG if enabled
  if (config.useProgressiveJpeg && url.endsWith('.jpg') || url.endsWith('.jpeg')) {
    return appendFormatToUrl(url, 'pjpg');
  }
  
  // Return the original URL
  return url;
}

/**
 * Append format parameter to URL
 * @param url Original URL
 * @param format Format to append
 * @returns URL with format parameter
 */
function appendFormatToUrl(url: string, format: string): string {
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}format=${format}`;
}

/**
 * Generate a placeholder URL
 * @param url Original image URL
 * @param width Placeholder width
 * @param height Placeholder height
 * @param quality Placeholder quality (0-100)
 * @returns Placeholder URL
 */
export function generatePlaceholderUrl(
  url: string,
  width: number = 20,
  height: number = 20,
  quality: number = DEFAULT_LAZY_LOADING_CONFIG.placeholderQuality
): string {
  // In a real implementation, this would call a server-side image processing service
  // For now, we'll just append query parameters
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}width=${width}&height=${height}&quality=${quality}&placeholder=true`;
}

/**
 * Handle intersection changes
 * @param entries Intersection observer entries
 * @param observer Intersection observer
 */
function handleIntersection(
  entries: IntersectionObserverEntry[],
  observer: IntersectionObserver
): void {
  entries.forEach((entry) => {
    const id = entry.target.getAttribute('data-lazy-id');
    if (!id) return;
    
    const item = items.get(id);
    if (!item) return;
    
    // Update intersection ratio
    item.intersectionRatio = entry.intersectionRatio;
    
    // If the item is visible, load it
    if (entry.isIntersecting) {
      loadItem(item);
    }
    
    // Call the callback
    const callback = callbacks.get(id);
    if (callback) {
      callback(item);
    }
  });
}

/**
 * Load an item
 * @param item Item to load
 */
function loadItem(item: LazyLoadItem): void {
  // If the item is already loading or loaded, do nothing
  if (item.state === LazyLoadState.LOADING || item.state === LazyLoadState.LOADED) {
    return;
  }
  
  // Update state
  item.state = LazyLoadState.LOADING;
  item.loadStartTime = Date.now();
  
  // Call the callback
  const callback = callbacks.get(item.id);
  if (callback) {
    callback(item);
  }
  
  // Load the item
  if (item.type === 'image') {
    loadImage(item);
  } else if (item.type === 'video') {
    loadVideo(item);
  }
}

/**
 * Load an image
 * @param item Image item to load
 */
function loadImage(item: LazyLoadItem): void {
  const img = new Image();
  
  img.onload = () => {
    item.state = LazyLoadState.LOADED;
    item.loadEndTime = Date.now();
    
    // Call the callback
    const callback = callbacks.get(item.id);
    if (callback) {
      callback(item);
    }
  };
  
  img.onerror = (error) => {
    handleLoadError(item, error);
  };
  
  // Set the source to start loading
  img.src = getOptimalImageUrl(item.url);
}

/**
 * Load a video
 * @param item Video item to load
 */
function loadVideo(item: LazyLoadItem): void {
  const video = document.createElement('video');
  
  video.onloadeddata = () => {
    item.state = LazyLoadState.LOADED;
    item.loadEndTime = Date.now();
    
    // Call the callback
    const callback = callbacks.get(item.id);
    if (callback) {
      callback(item);
    }
  };
  
  video.onerror = (error) => {
    handleLoadError(item, error);
  };
  
  // Set the source to start loading
  video.src = item.url;
  video.load();
}

/**
 * Handle load error
 * @param item Item that failed to load
 * @param error Error that occurred
 */
function handleLoadError(item: LazyLoadItem, error: any): void {
  // Increment retry count
  item.retryCount++;
  
  // If we've reached the retry limit, mark as error
  if (item.retryCount >= DEFAULT_LAZY_LOADING_CONFIG.retryCount) {
    item.state = LazyLoadState.ERROR;
    item.error = error?.message || 'Failed to load';
    
    // Call the callback
    const callback = callbacks.get(item.id);
    if (callback) {
      callback(item);
    }
    
    return;
  }
  
  // Otherwise, retry after a delay
  setTimeout(() => {
    item.state = LazyLoadState.PLACEHOLDER;
    loadItem(item);
  }, DEFAULT_LAZY_LOADING_CONFIG.retryDelay);
}

/**
 * Register an element for lazy loading
 * @param element Element to observe
 * @param item Lazy load item
 * @param callback Callback to call when the item's state changes
 * @returns Lazy load item
 */
export function registerLazyLoad(
  element: HTMLElement,
  item: Omit<LazyLoadItem, 'state' | 'intersectionRatio' | 'retryCount'>,
  callback?: (item: LazyLoadItem) => void
): LazyLoadItem {
  // Create the full item
  const fullItem: LazyLoadItem = {
    ...item,
    state: LazyLoadState.INITIAL,
    intersectionRatio: 0,
    retryCount: 0,
  };
  
  // Store the item
  items.set(item.id, fullItem);
  
  // Store the callback
  if (callback) {
    callbacks.set(item.id, callback);
  }
  
  // Set the data attribute
  element.setAttribute('data-lazy-id', item.id);
  
  // Start observing the element
  const observer = observers.get('default');
  if (observer) {
    observer.observe(element);
  }
  
  return fullItem;
}

/**
 * Unregister an element from lazy loading
 * @param element Element to unobserve
 */
export function unregisterLazyLoad(element: HTMLElement): void {
  const id = element.getAttribute('data-lazy-id');
  if (!id) return;
  
  // Remove the item
  items.delete(id);
  
  // Remove the callback
  callbacks.delete(id);
  
  // Stop observing the element
  const observer = observers.get('default');
  if (observer) {
    observer.unobserve(element);
  }
}

/**
 * Clean up lazy loading
 */
export function cleanupLazyLoading(): void {
  // Disconnect all observers
  observers.forEach((observer) => {
    observer.disconnect();
  });
  
  // Clear all maps
  observers.clear();
  items.clear();
  callbacks.clear();
}
