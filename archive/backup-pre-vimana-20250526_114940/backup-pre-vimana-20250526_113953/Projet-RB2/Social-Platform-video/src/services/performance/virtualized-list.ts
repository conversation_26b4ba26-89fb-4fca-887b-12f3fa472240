/**
 * Virtualized List Service
 * 
 * This service provides utilities for optimizing list rendering performance
 * through virtualization, prefetching, and memory management.
 */

// Configuration for virtualized list
export interface VirtualizedListConfig {
  itemHeight: number | ((index: number) => number); // Fixed height or function to calculate height
  overscan: number; // Number of items to render beyond visible area
  threshold: number; // Scroll threshold for loading more items (0-1)
  batchSize: number; // Number of items to load in each batch
  initialBatchSize: number; // Number of items to load initially
  maxItems: number; // Maximum number of items to keep in memory
  prefetchDistance: number; // Distance in pixels to prefetch items
  debounceTime: number; // Debounce time for scroll events in ms
}

// Default configuration
export const DEFAULT_VIRTUALIZED_LIST_CONFIG: VirtualizedListConfig = {
  itemHeight: 200, // Default item height in pixels
  overscan: 3, // Render 3 items beyond visible area
  threshold: 0.8, // Load more when scrolled 80% of the way
  batchSize: 10, // Load 10 items at a time
  initialBatchSize: 20, // Load 20 items initially
  maxItems: 200, // Keep at most 200 items in memory
  prefetchDistance: 1000, // Prefetch items 1000px away
  debounceTime: 100, // Debounce scroll events by 100ms
};

// Item range
export interface ItemRange {
  startIndex: number;
  endIndex: number;
}

// Virtualized list state
export interface VirtualizedListState<T> {
  items: T[]; // All items
  visibleItems: T[]; // Currently visible items
  visibleRange: ItemRange; // Range of visible items
  totalItems: number; // Total number of items
  isLoading: boolean; // Whether more items are being loaded
  hasMore: boolean; // Whether there are more items to load
  scrollTop: number; // Current scroll position
  scrollHeight: number; // Total scroll height
  containerHeight: number; // Height of the container
}

/**
 * Calculate the range of items that should be rendered based on scroll position
 * @param scrollTop Current scroll position
 * @param containerHeight Height of the container
 * @param itemHeight Height of each item or function to calculate height
 * @param totalItems Total number of items
 * @param overscan Number of items to render beyond visible area
 * @returns Range of items to render
 */
export function calculateVisibleRange(
  scrollTop: number,
  containerHeight: number,
  itemHeight: number | ((index: number) => number),
  totalItems: number,
  overscan: number = DEFAULT_VIRTUALIZED_LIST_CONFIG.overscan
): ItemRange {
  // If itemHeight is a function, we need to calculate the start and end indices differently
  if (typeof itemHeight === 'function') {
    // This is a simplified approach for variable height items
    // In a real implementation, you would need to maintain a map of item positions
    let currentHeight = 0;
    let startIndex = 0;
    
    // Find the first visible item
    for (let i = 0; i < totalItems; i++) {
      const height = itemHeight(i);
      if (currentHeight + height > scrollTop) {
        startIndex = i;
        break;
      }
      currentHeight += height;
    }
    
    // Find the last visible item
    let endIndex = startIndex;
    let visibleHeight = 0;
    
    for (let i = startIndex; i < totalItems; i++) {
      const height = itemHeight(i);
      visibleHeight += height;
      if (visibleHeight > containerHeight) {
        endIndex = i;
        break;
      }
    }
    
    // Apply overscan
    startIndex = Math.max(0, startIndex - overscan);
    endIndex = Math.min(totalItems - 1, endIndex + overscan);
    
    return { startIndex, endIndex };
  } else {
    // For fixed height items, the calculation is simpler
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      totalItems - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    
    return { startIndex, endIndex };
  }
}

/**
 * Calculate the total height of all items
 * @param totalItems Total number of items
 * @param itemHeight Height of each item or function to calculate height
 * @returns Total height
 */
export function calculateTotalHeight(
  totalItems: number,
  itemHeight: number | ((index: number) => number)
): number {
  if (typeof itemHeight === 'function') {
    let totalHeight = 0;
    for (let i = 0; i < totalItems; i++) {
      totalHeight += itemHeight(i);
    }
    return totalHeight;
  } else {
    return totalItems * itemHeight;
  }
}

/**
 * Calculate the offset for an item at a specific index
 * @param index Item index
 * @param itemHeight Height of each item or function to calculate height
 * @returns Offset from the top
 */
export function calculateItemOffset(
  index: number,
  itemHeight: number | ((index: number) => number)
): number {
  if (typeof itemHeight === 'function') {
    let offset = 0;
    for (let i = 0; i < index; i++) {
      offset += itemHeight(i);
    }
    return offset;
  } else {
    return index * itemHeight;
  }
}

/**
 * Check if more items should be loaded
 * @param scrollTop Current scroll position
 * @param scrollHeight Total scroll height
 * @param containerHeight Height of the container
 * @param threshold Threshold for loading more items (0-1)
 * @returns Whether more items should be loaded
 */
export function shouldLoadMore(
  scrollTop: number,
  scrollHeight: number,
  containerHeight: number,
  threshold: number = DEFAULT_VIRTUALIZED_LIST_CONFIG.threshold
): boolean {
  // Calculate the scroll position as a percentage of the scrollable area
  const scrollPercentage = (scrollTop + containerHeight) / scrollHeight;
  
  // Load more if we've scrolled past the threshold
  return scrollPercentage >= threshold;
}

/**
 * Manage memory by removing items that are far from the visible range
 * @param items All items
 * @param visibleRange Range of visible items
 * @param maxItems Maximum number of items to keep in memory
 * @returns Filtered items
 */
export function manageMemory<T>(
  items: T[],
  visibleRange: ItemRange,
  maxItems: number = DEFAULT_VIRTUALIZED_LIST_CONFIG.maxItems
): T[] {
  if (items.length <= maxItems) {
    return items;
  }
  
  // Calculate the range of items to keep
  const { startIndex, endIndex } = visibleRange;
  const rangeSize = endIndex - startIndex + 1;
  
  // If the visible range is already larger than maxItems, just return the visible range
  if (rangeSize >= maxItems) {
    return items.slice(startIndex, endIndex + 1);
  }
  
  // Calculate how many items to keep on each side of the visible range
  const itemsToKeep = maxItems - rangeSize;
  const itemsBeforeVisible = startIndex;
  const itemsAfterVisible = items.length - endIndex - 1;
  
  // Distribute the remaining items proportionally
  const keepBefore = Math.floor((itemsToKeep * itemsBeforeVisible) / (itemsBeforeVisible + itemsAfterVisible));
  const keepAfter = itemsToKeep - keepBefore;
  
  // Calculate the new range
  const newStartIndex = Math.max(0, startIndex - keepBefore);
  const newEndIndex = Math.min(items.length - 1, endIndex + keepAfter);
  
  // Return the filtered items
  return items.slice(newStartIndex, newEndIndex + 1);
}

/**
 * Create a debounced function
 * @param func Function to debounce
 * @param wait Wait time in ms
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number = DEFAULT_VIRTUALIZED_LIST_CONFIG.debounceTime
): (...args: Parameters<T>) => void {
  let timeout: number | null = null;
  
  return function(...args: Parameters<T>): void {
    const later = () => {
      timeout = null;
      func(...args);
    };
    
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    
    timeout = window.setTimeout(later, wait);
  };
}

/**
 * Create a throttled function
 * @param func Function to throttle
 * @param limit Limit in ms
 * @returns Throttled function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number = 100
): (...args: Parameters<T>) => void {
  let inThrottle = false;
  
  return function(...args: Parameters<T>): void {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
}
