const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

// Simulation d'une base de données pour les livestreams
const livestreams = [];
const livestreamMessages = {};

/**
 * Service pour la gestion des livestreams
 */
class LivestreamService {
  /**
   * Récupère tous les livestreams avec filtres optionnels
   * @param {Object} filters Filtres à appliquer
   * @returns {Promise<Array>} Liste des livestreams
   */
  async getLivestreams(filters = {}) {
    try {
      let filteredLivestreams = [...livestreams];
      
      // Appliquer les filtres
      if (filters.status) {
        filteredLivestreams = filteredLivestreams.filter(stream => stream.status === filters.status);
      }
      
      if (filters.hostId) {
        filteredLivestreams = filteredLivestreams.filter(stream => stream.hostId === filters.hostId);
      }
      
      // Tri par date de début (du plus récent au plus ancien)
      filteredLivestreams.sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
      
      return filteredLivestreams;
    } catch (error) {
      logger.error(`Error in getLivestreams: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Récupère un livestream par son ID
   * @param {string} id ID du livestream
   * @returns {Promise<Object|null>} Livestream trouvé ou null
   */
  async getLivestreamById(id) {
    try {
      return livestreams.find(stream => stream.id === id) || null;
    } catch (error) {
      logger.error(`Error in getLivestreamById: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Crée un nouveau livestream
   * @param {Object} livestreamData Données du livestream
   * @returns {Promise<Object>} Livestream créé
   */
  async createLivestream(livestreamData) {
    try {
      const newLivestream = {
        id: uuidv4(),
        ...livestreamData,
        status: 'scheduled',
        startTime: livestreamData.startTime || new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        viewerCount: 0,
      };
      
      livestreams.push(newLivestream);
      livestreamMessages[newLivestream.id] = [];
      
      return newLivestream;
    } catch (error) {
      logger.error(`Error in createLivestream: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Met à jour un livestream existant
   * @param {string} id ID du livestream
   * @param {Object} updateData Données à mettre à jour
   * @returns {Promise<Object|null>} Livestream mis à jour ou null
   */
  async updateLivestream(id, updateData) {
    try {
      const index = livestreams.findIndex(stream => stream.id === id);
      
      if (index === -1) {
        return null;
      }
      
      // Empêcher la modification de certains champs
      const { id: _, createdAt, status, ...allowedUpdates } = updateData;
      
      // Mettre à jour le livestream
      livestreams[index] = {
        ...livestreams[index],
        ...allowedUpdates,
        updatedAt: new Date().toISOString(),
      };
      
      return livestreams[index];
    } catch (error) {
      logger.error(`Error in updateLivestream: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Démarre un livestream
   * @param {string} id ID du livestream
   * @returns {Promise<Object|null>} Livestream mis à jour ou null
   */
  async startLivestream(id) {
    try {
      const index = livestreams.findIndex(stream => stream.id === id);
      
      if (index === -1) {
        return null;
      }
      
      // Vérifier que le livestream n'est pas déjà terminé
      if (livestreams[index].status === 'ended') {
        throw new Error('Cannot start a livestream that has already ended');
      }
      
      // Mettre à jour le statut
      livestreams[index] = {
        ...livestreams[index],
        status: 'live',
        startTime: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      return livestreams[index];
    } catch (error) {
      logger.error(`Error in startLivestream: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Termine un livestream
   * @param {string} id ID du livestream
   * @returns {Promise<Object|null>} Livestream mis à jour ou null
   */
  async endLivestream(id) {
    try {
      const index = livestreams.findIndex(stream => stream.id === id);
      
      if (index === -1) {
        return null;
      }
      
      // Vérifier que le livestream est en cours
      if (livestreams[index].status !== 'live') {
        throw new Error('Cannot end a livestream that is not live');
      }
      
      // Mettre à jour le statut
      livestreams[index] = {
        ...livestreams[index],
        status: 'ended',
        endTime: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      return livestreams[index];
    } catch (error) {
      logger.error(`Error in endLivestream: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Récupère les messages d'un livestream
   * @param {string} id ID du livestream
   * @returns {Promise<Array>} Liste des messages
   */
  async getLivestreamMessages(id) {
    try {
      return livestreamMessages[id] || [];
    } catch (error) {
      logger.error(`Error in getLivestreamMessages: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Envoie un message dans un livestream
   * @param {string} id ID du livestream
   * @param {Object} messageData Données du message
   * @returns {Promise<Object>} Message créé
   */
  async sendLivestreamMessage(id, messageData) {
    try {
      // Vérifier que le livestream existe
      const livestream = await this.getLivestreamById(id);
      
      if (!livestream) {
        throw new Error('Livestream not found');
      }
      
      // Vérifier que le livestream est en cours
      if (livestream.status !== 'live') {
        throw new Error('Cannot send message to a livestream that is not live');
      }
      
      // Créer le message
      const newMessage = {
        id: uuidv4(),
        ...messageData,
        livestreamId: id,
        timestamp: new Date().toISOString(),
      };
      
      // Ajouter le message
      if (!livestreamMessages[id]) {
        livestreamMessages[id] = [];
      }
      
      livestreamMessages[id].push(newMessage);
      
      return newMessage;
    } catch (error) {
      logger.error(`Error in sendLivestreamMessage: ${error.message}`, error);
      throw error;
    }
  }
}

module.exports = new LivestreamService();
