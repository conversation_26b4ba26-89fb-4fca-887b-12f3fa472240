const BlogService = require('./BlogService');
const { cacheService } = require('./cache');
const { CacheTier } = require('./cache/CacheConfig');
const CacheUtils = require('./cache/CacheUtils');
const logger = require('../utils/logger');

/**
 * Service pour la gestion des articles de blog avec mise en cache
 */
class CachedBlogService {
  /**
   * Récupère tous les articles de blog avec filtres optionnels
   * @param {Object} filters Filtres à appliquer
   * @returns {Promise<Array>} Liste des articles de blog
   */
  async getBlogPosts(filters = {}) {
    try {
      const cacheKey = CacheUtils.generateBlogPostsListKey(filters);
      
      return await cacheService.getOrSet(
        cacheKey,
        () => BlogService.getBlogPosts(filters),
        {
          tier: CacheTier.STANDARD,
          tags: ['blog:posts', `filter:${JSON.stringify(filters)}`]
        }
      );
    } catch (error) {
      logger.error(`Error in cached getBlogPosts: ${error.message}`, error);
      // En cas d'erreur, appeler directement le service
      return BlogService.getBlogPosts(filters);
    }
  }

  /**
   * Récupère un article de blog par son ID
   * @param {string} id ID de l'article de blog
   * @returns {Promise<Object|null>} Article de blog trouvé ou null
   */
  async getBlogPostById(id) {
    try {
      const cacheKey = CacheUtils.generateBlogPostKey(id);
      
      return await cacheService.getOrSet(
        cacheKey,
        () => BlogService.getBlogPostById(id),
        {
          tier: CacheTier.STANDARD,
          tags: ['blog:posts', `blog:post:${id}`]
        }
      );
    } catch (error) {
      logger.error(`Error in cached getBlogPostById: ${error.message}`, error);
      return BlogService.getBlogPostById(id);
    }
  }

  /**
   * Crée un nouvel article de blog
   * @param {Object} blogPostData Données de l'article de blog
   * @returns {Promise<Object>} Article de blog créé
   */
  async createBlogPost(blogPostData) {
    try {
      const result = await BlogService.createBlogPost(blogPostData);
      
      // Invalider le cache des listes d'articles
      cacheService.invalidateByTag('blog:posts');
      
      return result;
    } catch (error) {
      logger.error(`Error in cached createBlogPost: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Met à jour un article de blog existant
   * @param {string} id ID de l'article de blog
   * @param {Object} updateData Données à mettre à jour
   * @returns {Promise<Object|null>} Article de blog mis à jour ou null
   */
  async updateBlogPost(id, updateData) {
    try {
      const result = await BlogService.updateBlogPost(id, updateData);
      
      if (result) {
        // Invalider le cache de l'article et des listes
        CacheUtils.invalidateBlogPostCache(id);
      }
      
      return result;
    } catch (error) {
      logger.error(`Error in cached updateBlogPost: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Supprime un article de blog
   * @param {string} id ID de l'article de blog
   * @returns {Promise<boolean>} Succès de la suppression
   */
  async deleteBlogPost(id) {
    try {
      const result = await BlogService.deleteBlogPost(id);
      
      if (result) {
        // Invalider le cache de l'article et des listes
        CacheUtils.invalidateBlogPostCache(id);
      }
      
      return result;
    } catch (error) {
      logger.error(`Error in cached deleteBlogPost: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Récupère les commentaires d'un article de blog
   * @param {string} id ID de l'article de blog
   * @returns {Promise<Array>} Liste des commentaires
   */
  async getBlogPostComments(id) {
    try {
      const cacheKey = CacheUtils.generateBlogPostCommentsKey(id);
      
      return await cacheService.getOrSet(
        cacheKey,
        () => BlogService.getBlogPostComments(id),
        {
          tier: CacheTier.FREQUENT,
          tags: ['blog:posts', `blog:post:${id}`, 'comments']
        }
      );
    } catch (error) {
      logger.error(`Error in cached getBlogPostComments: ${error.message}`, error);
      return BlogService.getBlogPostComments(id);
    }
  }

  /**
   * Ajoute un commentaire à un article de blog
   * @param {string} id ID de l'article de blog
   * @param {Object} commentData Données du commentaire
   * @returns {Promise<Object>} Commentaire créé
   */
  async addBlogPostComment(id, commentData) {
    try {
      const result = await BlogService.addBlogPostComment(id, commentData);
      
      // Invalider le cache des commentaires et de l'article
      cacheService.invalidateByTags([
        `blog:post:${id}:comments`,
        `blog:post:${id}`
      ]);
      
      return result;
    } catch (error) {
      logger.error(`Error in cached addBlogPostComment: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * Ajoute ou retire un like d'un article de blog
   * @param {string} id ID de l'article de blog
   * @param {string} userId ID de l'utilisateur
   * @param {string} action Action à effectuer ('like' ou 'unlike')
   * @returns {Promise<Object>} Résultat de l'opération
   */
  async toggleBlogPostLike(id, userId, action = 'like') {
    try {
      const result = await BlogService.toggleBlogPostLike(id, userId, action);
      
      // Invalider le cache de l'article
      cacheService.invalidateByTag(`blog:post:${id}`);
      
      return result;
    } catch (error) {
      logger.error(`Error in cached toggleBlogPostLike: ${error.message}`, error);
      throw error;
    }
  }
}

module.exports = new CachedBlogService();
