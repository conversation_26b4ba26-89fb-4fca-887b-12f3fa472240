/**
 * Bandwidth Detection Service
 * 
 * This service provides utilities for detecting and monitoring network bandwidth
 * to support adaptive streaming decisions.
 */

// Bandwidth measurement history
interface BandwidthMeasurement {
  timestamp: number;
  bandwidth: number; // in kbps
  duration: number; // measurement duration in ms
  bytes: number; // bytes downloaded
}

// Configuration for bandwidth detection
interface BandwidthDetectionConfig {
  sampleSize: number; // Number of samples to keep in history
  minSampleInterval: number; // Minimum time between samples in ms
  testFileUrl: string; // URL to a test file for active bandwidth testing
  testFileSize: number; // Size of the test file in bytes
  weightRecentSamples: boolean; // Whether to give more weight to recent samples
}

// Default configuration
const DEFAULT_CONFIG: BandwidthDetectionConfig = {
  sampleSize: 10,
  minSampleInterval: 5000, // 5 seconds
  testFileUrl: '/assets/bandwidth-test.bin', // 1MB test file
  testFileSize: 1024 * 1024, // 1MB
  weightRecentSamples: true,
};

// Bandwidth history
let bandwidthHistory: BandwidthMeasurement[] = [];
let lastMeasurementTime = 0;
let config: BandwidthDetectionConfig = DEFAULT_CONFIG;

/**
 * Initialize the bandwidth detection service
 * @param customConfig Custom configuration options
 */
export function initBandwidthDetection(customConfig: Partial<BandwidthDetectionConfig> = {}): void {
  config = { ...DEFAULT_CONFIG, ...customConfig };
  bandwidthHistory = [];
  lastMeasurementTime = 0;
  
  // Set up performance observer to monitor resource timing
  if (typeof PerformanceObserver !== 'undefined') {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        if (entry.entryType === 'resource' && entry.initiatorType === 'video') {
          const resourceEntry = entry as PerformanceResourceTiming;
          
          // Calculate bandwidth based on resource timing
          const transferSize = resourceEntry.transferSize || 0;
          const duration = resourceEntry.duration || 1; // Avoid division by zero
          
          if (transferSize > 0 && duration > 0) {
            // Calculate bandwidth in kbps
            const bandwidth = (transferSize * 8) / (duration / 1000) / 1000;
            addBandwidthMeasurement(bandwidth, duration, transferSize);
          }
        }
      });
    });
    
    observer.observe({ entryTypes: ['resource'] });
  }
}

/**
 * Add a bandwidth measurement to the history
 * @param bandwidth Bandwidth in kbps
 * @param duration Measurement duration in ms
 * @param bytes Bytes downloaded
 */
export function addBandwidthMeasurement(
  bandwidth: number,
  duration: number,
  bytes: number
): void {
  const now = Date.now();
  
  // Ensure minimum interval between measurements
  if (now - lastMeasurementTime < config.minSampleInterval) {
    return;
  }
  
  lastMeasurementTime = now;
  
  // Add measurement to history
  bandwidthHistory.push({
    timestamp: now,
    bandwidth,
    duration,
    bytes,
  });
  
  // Keep only the most recent samples
  if (bandwidthHistory.length > config.sampleSize) {
    bandwidthHistory = bandwidthHistory.slice(-config.sampleSize);
  }
}

/**
 * Get the current bandwidth estimate based on historical measurements
 * @returns Estimated bandwidth in kbps, or 0 if no measurements available
 */
export function getBandwidthEstimate(): number {
  if (bandwidthHistory.length === 0) {
    return 5000; // Default to 5 Mbps if no measurements available
  }
  
  if (config.weightRecentSamples) {
    // Calculate weighted average, giving more weight to recent samples
    let totalWeight = 0;
    let weightedSum = 0;
    
    bandwidthHistory.forEach((measurement, index) => {
      const weight = index + 1; // More recent samples get higher weight
      weightedSum += measurement.bandwidth * weight;
      totalWeight += weight;
    });
    
    return weightedSum / totalWeight;
  } else {
    // Calculate simple average
    const sum = bandwidthHistory.reduce(
      (total, measurement) => total + measurement.bandwidth,
      0
    );
    return sum / bandwidthHistory.length;
  }
}

/**
 * Get bandwidth statistics
 * @returns Bandwidth statistics
 */
export function getBandwidthStats(): {
  current: number;
  average: number;
  min: number;
  max: number;
  measurements: number;
} {
  if (bandwidthHistory.length === 0) {
    return {
      current: 0,
      average: 0,
      min: 0,
      max: 0,
      measurements: 0,
    };
  }
  
  const current = bandwidthHistory[bandwidthHistory.length - 1].bandwidth;
  const average = getBandwidthEstimate();
  const min = Math.min(...bandwidthHistory.map((m) => m.bandwidth));
  const max = Math.max(...bandwidthHistory.map((m) => m.bandwidth));
  
  return {
    current,
    average,
    min,
    max,
    measurements: bandwidthHistory.length,
  };
}

/**
 * Actively test bandwidth by downloading a test file
 * @returns Promise resolving to the measured bandwidth in kbps
 */
export async function testBandwidth(): Promise<number> {
  const startTime = Date.now();
  
  try {
    const response = await fetch(config.testFileUrl, {
      method: 'GET',
      cache: 'no-store', // Prevent caching
    });
    
    if (!response.ok) {
      throw new Error('Failed to download test file');
    }
    
    const blob = await response.blob();
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Calculate bandwidth in kbps
    const bandwidth = (blob.size * 8) / (duration / 1000) / 1000;
    
    // Add to measurement history
    addBandwidthMeasurement(bandwidth, duration, blob.size);
    
    return bandwidth;
  } catch (error) {
    console.error('Bandwidth test failed:', error);
    return getBandwidthEstimate(); // Fall back to historical estimate
  }
}

/**
 * Get the network connection type if available
 * @returns Network connection type or null if not available
 */
export function getConnectionType(): string | null {
  if (
    typeof navigator !== 'undefined' &&
    navigator.connection &&
    navigator.connection.effectiveType
  ) {
    return navigator.connection.effectiveType; // 'slow-2g', '2g', '3g', or '4g'
  }
  
  return null;
}

/**
 * Estimate initial bandwidth based on connection type
 * @returns Estimated bandwidth in kbps
 */
export function estimateInitialBandwidth(): number {
  const connectionType = getConnectionType();
  
  if (!connectionType) {
    return 5000; // Default to 5 Mbps
  }
  
  // Rough estimates based on connection type
  switch (connectionType) {
    case 'slow-2g':
      return 100; // 100 kbps
    case '2g':
      return 300; // 300 kbps
    case '3g':
      return 1500; // 1.5 Mbps
    case '4g':
      return 5000; // 5 Mbps
    default:
      return 5000; // Default to 5 Mbps
  }
}

/**
 * Reset bandwidth history
 */
export function resetBandwidthHistory(): void {
  bandwidthHistory = [];
  lastMeasurementTime = 0;
}

/**
 * Check if the current bandwidth is sufficient for a given bitrate
 * @param requiredBitrate Required bitrate in kbps
 * @param safetyFactor Safety factor (e.g., 1.5 means 50% headroom)
 * @returns Whether the bandwidth is sufficient
 */
export function isBandwidthSufficient(
  requiredBitrate: number,
  safetyFactor: number = 1.5
): boolean {
  const currentBandwidth = getBandwidthEstimate();
  return currentBandwidth >= requiredBitrate * safetyFactor;
}
