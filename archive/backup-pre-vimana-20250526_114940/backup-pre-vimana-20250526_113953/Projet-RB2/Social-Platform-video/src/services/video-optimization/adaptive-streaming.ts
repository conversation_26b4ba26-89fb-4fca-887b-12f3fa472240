/**
 * Adaptive Streaming Service
 * 
 * This service handles adaptive video streaming functionality, including:
 * - Multi-bitrate encoding
 * - Automatic quality switching
 * - Bandwidth detection
 * - HLS/DASH manifest generation
 */

import { getBandwidthEstimate } from './bandwidth-detection';

// Video quality presets
export enum VideoQuality {
  AUTO = 'auto',
  LOW = '360p',
  MEDIUM = '720p',
  HIGH = '1080p',
  ULTRA = '2160p', // 4K
}

// Video codec options
export enum VideoCodec {
  H264 = 'h264',
  H265 = 'h265', // HEVC
  VP9 = 'vp9',
  AV1 = 'av1',
}

// Video format options
export enum VideoFormat {
  HLS = 'hls',
  DASH = 'dash',
  MP4 = 'mp4',
}

// Video encoding preset
export interface EncodingPreset {
  name: string;
  width: number;
  height: number;
  bitrate: number; // in kbps
  codec: VideoCodec;
  audioQuality: number; // in kbps
}

// Video stream representation
export interface VideoStream {
  url: string;
  quality: VideoQuality;
  bitrate: number;
  resolution: {
    width: number;
    height: number;
  };
  codec: VideoCodec;
  size: number; // in bytes
}

// Video manifest
export interface VideoManifest {
  id: string;
  format: VideoFormat;
  duration: number;
  streams: VideoStream[];
  defaultQuality: VideoQuality;
  thumbnails: {
    url: string;
    width: number;
    height: number;
    timestamp: number;
  }[];
}

// Default encoding presets
export const DEFAULT_ENCODING_PRESETS: EncodingPreset[] = [
  {
    name: '360p',
    width: 640,
    height: 360,
    bitrate: 800,
    codec: VideoCodec.H264,
    audioQuality: 96,
  },
  {
    name: '480p',
    width: 854,
    height: 480,
    bitrate: 1400,
    codec: VideoCodec.H264,
    audioQuality: 128,
  },
  {
    name: '720p',
    width: 1280,
    height: 720,
    bitrate: 2800,
    codec: VideoCodec.H264,
    audioQuality: 128,
  },
  {
    name: '1080p',
    width: 1920,
    height: 1080,
    bitrate: 5000,
    codec: VideoCodec.H264,
    audioQuality: 192,
  },
  {
    name: '1440p',
    width: 2560,
    height: 1440,
    bitrate: 8000,
    codec: VideoCodec.H265,
    audioQuality: 192,
  },
  {
    name: '2160p',
    width: 3840,
    height: 2160,
    bitrate: 16000,
    codec: VideoCodec.H265,
    audioQuality: 192,
  },
];

// Advanced encoding presets for newer codecs
export const ADVANCED_ENCODING_PRESETS: EncodingPreset[] = [
  {
    name: '720p-av1',
    width: 1280,
    height: 720,
    bitrate: 1800, // AV1 is more efficient
    codec: VideoCodec.AV1,
    audioQuality: 128,
  },
  {
    name: '1080p-av1',
    width: 1920,
    height: 1080,
    bitrate: 3500, // AV1 is more efficient
    codec: VideoCodec.AV1,
    audioQuality: 192,
  },
];

/**
 * Determines the optimal video quality based on bandwidth and device capabilities
 * @param availableStreams Available video streams
 * @param currentBandwidth Current bandwidth in kbps
 * @param maxHeight Maximum supported height by the device
 * @returns The optimal video quality
 */
export function determineOptimalQuality(
  availableStreams: VideoStream[],
  currentBandwidth: number,
  maxHeight: number = 2160
): VideoQuality {
  // Filter streams by device capability
  const supportedStreams = availableStreams.filter(
    (stream) => stream.resolution.height <= maxHeight
  );
  
  if (supportedStreams.length === 0) {
    return VideoQuality.LOW; // Fallback to lowest quality
  }
  
  // Sort by bitrate (ascending)
  const sortedStreams = [...supportedStreams].sort(
    (a, b) => a.bitrate - b.bitrate
  );
  
  // Find the highest quality stream that's below 70% of available bandwidth
  // (leaving 30% headroom to prevent buffering)
  const targetBitrate = currentBandwidth * 0.7;
  
  for (let i = sortedStreams.length - 1; i >= 0; i--) {
    if (sortedStreams[i].bitrate <= targetBitrate) {
      return sortedStreams[i].quality;
    }
  }
  
  // If all streams require more bandwidth, return the lowest quality
  return sortedStreams[0].quality;
}

/**
 * Adaptive streaming player configuration
 */
export interface AdaptivePlayerConfig {
  initialQuality: VideoQuality;
  autoQualitySwitching: boolean;
  bufferSize: number; // in seconds
  startPosition: number; // in seconds
  preferredCodec?: VideoCodec;
  maxHeight?: number;
  enableABR: boolean; // Adaptive Bitrate
}

/**
 * Default adaptive player configuration
 */
export const DEFAULT_PLAYER_CONFIG: AdaptivePlayerConfig = {
  initialQuality: VideoQuality.AUTO,
  autoQualitySwitching: true,
  bufferSize: 30,
  startPosition: 0,
  preferredCodec: VideoCodec.H264,
  enableABR: true,
};

/**
 * Creates a video player configuration with adaptive streaming support
 * @param manifest Video manifest
 * @param userConfig User configuration overrides
 * @returns Player configuration
 */
export function createAdaptivePlayerConfig(
  manifest: VideoManifest,
  userConfig: Partial<AdaptivePlayerConfig> = {}
): AdaptivePlayerConfig {
  const config = { ...DEFAULT_PLAYER_CONFIG, ...userConfig };
  
  // If auto quality is selected, determine the initial quality based on bandwidth
  if (config.initialQuality === VideoQuality.AUTO) {
    const bandwidth = getBandwidthEstimate();
    config.initialQuality = determineOptimalQuality(
      manifest.streams,
      bandwidth,
      config.maxHeight
    );
  }
  
  return config;
}

/**
 * Generates a HLS manifest for adaptive streaming
 * @param videoId Video ID
 * @param streams Available video streams
 * @param duration Video duration in seconds
 * @returns HLS manifest content
 */
export function generateHLSManifest(
  videoId: string,
  streams: VideoStream[],
  duration: number
): string {
  // Sort streams by bitrate (ascending)
  const sortedStreams = [...streams].sort((a, b) => a.bitrate - b.bitrate);
  
  let manifest = '#EXTM3U\n';
  manifest += '#EXT-X-VERSION:3\n';
  
  // Add stream variants
  sortedStreams.forEach((stream) => {
    manifest += `#EXT-X-STREAM-INF:BANDWIDTH=${stream.bitrate * 1000},RESOLUTION=${stream.resolution.width}x${stream.resolution.height}\n`;
    manifest += `${stream.url}\n`;
  });
  
  return manifest;
}

/**
 * Generates a DASH manifest for adaptive streaming
 * @param videoId Video ID
 * @param streams Available video streams
 * @param duration Video duration in seconds
 * @returns DASH manifest content (MPD)
 */
export function generateDASHManifest(
  videoId: string,
  streams: VideoStream[],
  duration: number
): string {
  // This is a simplified version. A real implementation would be more complex.
  const sortedStreams = [...streams].sort((a, b) => a.bitrate - b.bitrate);
  
  let manifest = '<?xml version="1.0" encoding="UTF-8"?>\n';
  manifest += '<MPD xmlns="urn:mpeg:dash:schema:mpd:2011" type="static" mediaPresentationDuration="PT' + duration + 'S" minBufferTime="PT10S" profiles="urn:mpeg:dash:profile:isoff-live:2011">\n';
  manifest += '  <Period>\n';
  manifest += '    <AdaptationSet mimeType="video/mp4" segmentAlignment="true" startWithSAP="1">\n';
  
  // Add stream variants
  sortedStreams.forEach((stream) => {
    manifest += `      <Representation id="${stream.quality}" width="${stream.resolution.width}" height="${stream.resolution.height}" bandwidth="${stream.bitrate * 1000}" codecs="${stream.codec}">\n`;
    manifest += `        <BaseURL>${stream.url}</BaseURL>\n`;
    manifest += '      </Representation>\n';
  });
  
  manifest += '    </AdaptationSet>\n';
  manifest += '  </Period>\n';
  manifest += '</MPD>';
  
  return manifest;
}

/**
 * Monitors playback and adjusts quality based on network conditions
 * @param player Video player instance
 * @param manifest Video manifest
 * @param config Player configuration
 */
export function setupAdaptiveQualitySwitching(
  player: HTMLVideoElement,
  manifest: VideoManifest,
  config: AdaptivePlayerConfig
): () => void {
  if (!config.autoQualitySwitching || !config.enableABR) {
    return () => {}; // No-op if adaptive switching is disabled
  }
  
  let currentQuality = config.initialQuality;
  
  // Check bandwidth and adjust quality every 5 seconds
  const intervalId = setInterval(() => {
    const bandwidth = getBandwidthEstimate();
    const optimalQuality = determineOptimalQuality(
      manifest.streams,
      bandwidth,
      config.maxHeight
    );
    
    if (optimalQuality !== currentQuality) {
      // In a real implementation, this would switch the video source
      // For now, we just log the quality change
      console.log(`Switching quality from ${currentQuality} to ${optimalQuality} (bandwidth: ${bandwidth} kbps)`);
      currentQuality = optimalQuality;
    }
  }, 5000);
  
  // Return cleanup function
  return () => {
    clearInterval(intervalId);
  };
}
