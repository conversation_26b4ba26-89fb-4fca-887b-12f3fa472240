/**
 * Video Compression Service
 * 
 * This service handles video compression functionality, including:
 * - Efficient codec selection (H.265/HEVC, AV1)
 * - Size-quality balance optimization
 * - Background processing
 */

import { VideoCodec } from './adaptive-streaming';

// Compression quality presets
export enum CompressionQuality {
  LOWEST = 'lowest',
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  HIGHEST = 'highest',
}

// Compression speed presets
export enum CompressionSpeed {
  FASTEST = 'fastest',
  FAST = 'fast',
  MEDIUM = 'medium',
  SLOW = 'slow',
  SLOWEST = 'slowest',
}

// Compression options
export interface CompressionOptions {
  codec: VideoCodec;
  quality: CompressionQuality;
  speed: CompressionSpeed;
  targetBitrate?: number; // in kbps, if not specified, will be calculated based on resolution and quality
  maxBitrate?: number; // in kbps
  keyframeInterval?: number; // in seconds
  twoPass: boolean; // whether to use two-pass encoding for better quality
  audioCodec: 'aac' | 'opus';
  audioBitrate: number; // in kbps
  preserveAlpha?: boolean; // whether to preserve alpha channel
  cropToAspectRatio?: boolean; // whether to crop to target aspect ratio
  denoising?: boolean; // whether to apply denoising
  enhanceDetails?: boolean; // whether to enhance details
  hdr?: boolean; // whether to preserve HDR
}

// Default compression options
export const DEFAULT_COMPRESSION_OPTIONS: CompressionOptions = {
  codec: VideoCodec.H264,
  quality: CompressionQuality.MEDIUM,
  speed: CompressionSpeed.MEDIUM,
  keyframeInterval: 2,
  twoPass: false,
  audioCodec: 'aac',
  audioBitrate: 128,
  denoising: true,
  enhanceDetails: false,
  hdr: false,
};

// Advanced compression options
export const ADVANCED_COMPRESSION_OPTIONS: CompressionOptions = {
  codec: VideoCodec.H265,
  quality: CompressionQuality.HIGH,
  speed: CompressionSpeed.SLOW,
  keyframeInterval: 2,
  twoPass: true,
  audioCodec: 'opus',
  audioBitrate: 128,
  denoising: true,
  enhanceDetails: true,
  hdr: true,
};

// Compression job status
export enum CompressionJobStatus {
  QUEUED = 'queued',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELED = 'canceled',
}

// Compression job
export interface CompressionJob {
  id: string;
  videoId: string;
  sourceUrl: string;
  outputUrl?: string;
  options: CompressionOptions;
  status: CompressionJobStatus;
  progress: number; // 0-100
  startTime?: number;
  endTime?: number;
  error?: string;
  size?: {
    original: number; // in bytes
    compressed: number; // in bytes
  };
}

// Compression job queue
const compressionQueue: CompressionJob[] = [];
let isProcessing = false;

/**
 * Calculate target bitrate based on resolution and quality
 * @param width Video width
 * @param height Video height
 * @param quality Compression quality
 * @param codec Video codec
 * @returns Target bitrate in kbps
 */
export function calculateTargetBitrate(
  width: number,
  height: number,
  quality: CompressionQuality,
  codec: VideoCodec
): number {
  // Base bitrate calculation based on resolution
  const pixels = width * height;
  let baseBitrate = 0;
  
  if (pixels <= 409920) { // 854x480
    baseBitrate = 1000; // 1 Mbps
  } else if (pixels <= 921600) { // 1280x720
    baseBitrate = 2500; // 2.5 Mbps
  } else if (pixels <= 2073600) { // 1920x1080
    baseBitrate = 5000; // 5 Mbps
  } else if (pixels <= 8294400) { // 3840x2160 (4K)
    baseBitrate = 15000; // 15 Mbps
  } else {
    baseBitrate = 30000; // 30 Mbps for 8K
  }
  
  // Apply quality multiplier
  let qualityMultiplier = 1.0;
  switch (quality) {
    case CompressionQuality.LOWEST:
      qualityMultiplier = 0.5;
      break;
    case CompressionQuality.LOW:
      qualityMultiplier = 0.75;
      break;
    case CompressionQuality.MEDIUM:
      qualityMultiplier = 1.0;
      break;
    case CompressionQuality.HIGH:
      qualityMultiplier = 1.25;
      break;
    case CompressionQuality.HIGHEST:
      qualityMultiplier = 1.5;
      break;
  }
  
  // Apply codec efficiency factor
  let codecFactor = 1.0;
  switch (codec) {
    case VideoCodec.H264:
      codecFactor = 1.0;
      break;
    case VideoCodec.H265: // HEVC is ~40% more efficient than H.264
      codecFactor = 0.6;
      break;
    case VideoCodec.VP9: // VP9 is ~30% more efficient than H.264
      codecFactor = 0.7;
      break;
    case VideoCodec.AV1: // AV1 is ~50% more efficient than H.264
      codecFactor = 0.5;
      break;
  }
  
  return Math.round(baseBitrate * qualityMultiplier * codecFactor);
}

/**
 * Create a compression job
 * @param videoId Video ID
 * @param sourceUrl Source video URL
 * @param options Compression options
 * @returns Compression job
 */
export function createCompressionJob(
  videoId: string,
  sourceUrl: string,
  options: Partial<CompressionOptions> = {}
): CompressionJob {
  const jobId = `job_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  
  const job: CompressionJob = {
    id: jobId,
    videoId,
    sourceUrl,
    options: { ...DEFAULT_COMPRESSION_OPTIONS, ...options },
    status: CompressionJobStatus.QUEUED,
    progress: 0,
  };
  
  compressionQueue.push(job);
  
  // Start processing if not already running
  if (!isProcessing) {
    processNextJob();
  }
  
  return job;
}

/**
 * Get a compression job by ID
 * @param jobId Job ID
 * @returns Compression job or undefined if not found
 */
export function getCompressionJob(jobId: string): CompressionJob | undefined {
  return compressionQueue.find((job) => job.id === jobId);
}

/**
 * Cancel a compression job
 * @param jobId Job ID
 * @returns Whether the job was successfully canceled
 */
export function cancelCompressionJob(jobId: string): boolean {
  const job = getCompressionJob(jobId);
  
  if (!job) {
    return false;
  }
  
  if (job.status === CompressionJobStatus.QUEUED) {
    job.status = CompressionJobStatus.CANCELED;
    return true;
  }
  
  // Cannot cancel jobs that are already processing, completed, or failed
  return false;
}

/**
 * Process the next job in the queue
 */
async function processNextJob(): Promise<void> {
  // Find the next queued job
  const nextJob = compressionQueue.find(
    (job) => job.status === CompressionJobStatus.QUEUED
  );
  
  if (!nextJob) {
    isProcessing = false;
    return;
  }
  
  isProcessing = true;
  nextJob.status = CompressionJobStatus.PROCESSING;
  nextJob.startTime = Date.now();
  
  try {
    // In a real implementation, this would call a backend service or Web Worker
    // For now, we'll simulate the compression process
    await simulateCompression(nextJob);
    
    nextJob.status = CompressionJobStatus.COMPLETED;
    nextJob.progress = 100;
    nextJob.endTime = Date.now();
    
    // Process the next job
    processNextJob();
  } catch (error) {
    nextJob.status = CompressionJobStatus.FAILED;
    nextJob.error = error instanceof Error ? error.message : 'Unknown error';
    nextJob.endTime = Date.now();
    
    // Process the next job
    processNextJob();
  }
}

/**
 * Simulate video compression (for demo purposes)
 * @param job Compression job
 */
async function simulateCompression(job: CompressionJob): Promise<void> {
  const totalSteps = 100;
  const stepDuration = 50; // ms per step
  
  // Simulate original file size (5-50MB)
  const originalSize = Math.floor(Math.random() * 45000000) + 5000000;
  
  // Calculate compression ratio based on codec and quality
  let compressionRatio = 0.5; // Default 50% reduction
  
  switch (job.options.codec) {
    case VideoCodec.H264:
      compressionRatio = 0.7;
      break;
    case VideoCodec.H265:
      compressionRatio = 0.5;
      break;
    case VideoCodec.VP9:
      compressionRatio = 0.55;
      break;
    case VideoCodec.AV1:
      compressionRatio = 0.4;
      break;
  }
  
  // Adjust based on quality
  switch (job.options.quality) {
    case CompressionQuality.LOWEST:
      compressionRatio *= 0.6;
      break;
    case CompressionQuality.LOW:
      compressionRatio *= 0.8;
      break;
    case CompressionQuality.MEDIUM:
      // No adjustment
      break;
    case CompressionQuality.HIGH:
      compressionRatio *= 1.2;
      break;
    case CompressionQuality.HIGHEST:
      compressionRatio *= 1.4;
      break;
  }
  
  // Calculate compressed size
  const compressedSize = Math.floor(originalSize * compressionRatio);
  
  // Update job with size information
  job.size = {
    original: originalSize,
    compressed: compressedSize,
  };
  
  // Simulate progress updates
  for (let step = 1; step <= totalSteps; step++) {
    await new Promise((resolve) => setTimeout(resolve, stepDuration));
    job.progress = Math.floor((step / totalSteps) * 100);
    
    // Simulate failure (5% chance)
    if (Math.random() < 0.05 && step < totalSteps - 1) {
      throw new Error('Simulated compression failure');
    }
  }
  
  // Set output URL
  job.outputUrl = job.sourceUrl.replace('.mp4', `_${job.options.codec}_${job.options.quality}.mp4`);
}

/**
 * Get compression statistics
 * @returns Compression statistics
 */
export function getCompressionStats(): {
  totalJobs: number;
  queuedJobs: number;
  processingJobs: number;
  completedJobs: number;
  failedJobs: number;
  canceledJobs: number;
  averageCompressionRatio?: number;
  totalSizeSaved?: number;
} {
  const totalJobs = compressionQueue.length;
  const queuedJobs = compressionQueue.filter(
    (job) => job.status === CompressionJobStatus.QUEUED
  ).length;
  const processingJobs = compressionQueue.filter(
    (job) => job.status === CompressionJobStatus.PROCESSING
  ).length;
  const completedJobs = compressionQueue.filter(
    (job) => job.status === CompressionJobStatus.COMPLETED
  ).length;
  const failedJobs = compressionQueue.filter(
    (job) => job.status === CompressionJobStatus.FAILED
  ).length;
  const canceledJobs = compressionQueue.filter(
    (job) => job.status === CompressionJobStatus.CANCELED
  ).length;
  
  // Calculate compression statistics
  const completedJobsWithSize = compressionQueue.filter(
    (job) => job.status === CompressionJobStatus.COMPLETED && job.size
  );
  
  let averageCompressionRatio;
  let totalSizeSaved;
  
  if (completedJobsWithSize.length > 0) {
    const totalOriginalSize = completedJobsWithSize.reduce(
      (sum, job) => sum + (job.size?.original || 0),
      0
    );
    const totalCompressedSize = completedJobsWithSize.reduce(
      (sum, job) => sum + (job.size?.compressed || 0),
      0
    );
    
    averageCompressionRatio = totalCompressedSize / totalOriginalSize;
    totalSizeSaved = totalOriginalSize - totalCompressedSize;
  }
  
  return {
    totalJobs,
    queuedJobs,
    processingJobs,
    completedJobs,
    failedJobs,
    canceledJobs,
    averageCompressionRatio,
    totalSizeSaved,
  };
}
