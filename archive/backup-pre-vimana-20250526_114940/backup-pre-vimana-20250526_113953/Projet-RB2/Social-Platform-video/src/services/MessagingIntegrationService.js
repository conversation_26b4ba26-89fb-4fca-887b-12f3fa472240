const axios = require('axios');
const logger = require('../utils/logger');
const config = require('../config');

/**
 * Service d'intégration avec le microservice de messagerie
 * Permet d'envoyer des messages et des notifications depuis le microservice Social-Platform-Video
 */
class MessagingIntegrationService {
  constructor() {
    this.messagingServiceUrl = config.messagingService.url || 'http://messaging-service:5178';
    this.apiKey = config.messagingService.apiKey || 'default-api-key';
  }

  /**
   * Envoie une notification concernant un livestream via le microservice de messagerie
   * @param {string} livestreamId ID du livestream
   * @param {string|null} recipientId ID du destinataire (null pour tous les abonnés)
   * @param {'created'|'started'|'ended'} eventType Type d'événement
   * @param {string} message Message de notification
   * @returns {Promise<void>}
   */
  async sendLivestreamNotification(livestreamId, recipientId, eventType, message) {
    try {
      const payload = {
        type: 'livestream_notification',
        livestreamId,
        recipientId,
        eventType,
        message,
        timestamp: new Date().toISOString(),
      };

      await axios.post(`${this.messagingServiceUrl}/api/notifications`, payload, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      logger.info(`Livestream notification sent: ${eventType} - ${livestreamId}`);
    } catch (error) {
      logger.error(`Failed to send livestream notification: ${error.message}`, error);
    }
  }

  /**
   * Relaie un message de livestream au microservice de messagerie
   * @param {string} livestreamId ID du livestream
   * @param {string} userId ID de l'utilisateur qui a envoyé le message
   * @param {string} userName Nom de l'utilisateur qui a envoyé le message
   * @param {string} content Contenu du message
   * @returns {Promise<void>}
   */
  async relayLivestreamMessage(livestreamId, userId, userName, content) {
    try {
      const payload = {
        type: 'livestream_message',
        livestreamId,
        userId,
        userName,
        content,
        timestamp: new Date().toISOString(),
      };

      await axios.post(`${this.messagingServiceUrl}/api/messages/relay`, payload, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      logger.info(`Livestream message relayed: ${livestreamId} - ${userId}`);
    } catch (error) {
      logger.error(`Failed to relay livestream message: ${error.message}`, error);
    }
  }

  /**
   * Envoie une notification concernant un commentaire sur un article de blog
   * @param {string} blogPostId ID de l'article de blog
   * @param {string} userId ID de l'utilisateur qui a commenté
   * @param {string} userName Nom de l'utilisateur qui a commenté
   * @param {string} content Contenu du commentaire
   * @returns {Promise<void>}
   */
  async sendBlogCommentNotification(blogPostId, userId, userName, content) {
    try {
      const payload = {
        type: 'blog_comment',
        blogPostId,
        userId,
        userName,
        content,
        timestamp: new Date().toISOString(),
      };

      await axios.post(`${this.messagingServiceUrl}/api/notifications`, payload, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      logger.info(`Blog comment notification sent: ${blogPostId} - ${userId}`);
    } catch (error) {
      logger.error(`Failed to send blog comment notification: ${error.message}`, error);
    }
  }

  /**
   * Envoie une notification concernant un like sur un article de blog
   * @param {string} blogPostId ID de l'article de blog
   * @param {string} userId ID de l'utilisateur qui a liké
   * @param {'like'|'unlike'} action Action effectuée
   * @returns {Promise<void>}
   */
  async sendBlogLikeNotification(blogPostId, userId, action) {
    try {
      const payload = {
        type: 'blog_like',
        blogPostId,
        userId,
        action,
        timestamp: new Date().toISOString(),
      };

      await axios.post(`${this.messagingServiceUrl}/api/notifications`, payload, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      logger.info(`Blog like notification sent: ${blogPostId} - ${userId} - ${action}`);
    } catch (error) {
      logger.error(`Failed to send blog like notification: ${error.message}`, error);
    }
  }

  /**
   * Envoie une notification concernant un partage de contenu social
   * @param {string} contentId ID du contenu
   * @param {'livestream'|'blog'|'video'} contentType Type de contenu
   * @param {string} userId ID de l'utilisateur qui a partagé
   * @param {string} targetUserId ID de l'utilisateur cible
   * @param {string} message Message personnalisé
   * @returns {Promise<void>}
   */
  async sendContentShareNotification(contentId, contentType, userId, targetUserId, message) {
    try {
      const payload = {
        type: 'content_share',
        contentId,
        contentType,
        userId,
        targetUserId,
        message,
        timestamp: new Date().toISOString(),
      };

      await axios.post(`${this.messagingServiceUrl}/api/notifications`, payload, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      logger.info(`Content share notification sent: ${contentType} - ${contentId} - from ${userId} to ${targetUserId}`);
    } catch (error) {
      logger.error(`Failed to send content share notification: ${error.message}`, error);
    }
  }
}

module.exports = new MessagingIntegrationService();
