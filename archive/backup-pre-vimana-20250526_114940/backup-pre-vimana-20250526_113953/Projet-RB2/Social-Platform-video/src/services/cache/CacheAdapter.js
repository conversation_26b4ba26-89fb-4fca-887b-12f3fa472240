const { cacheService } = require('./index');
const { CacheTier } = require('./CacheConfig');
const logger = require('../../utils/logger');

/**
 * Adaptateur pour intégrer le service de cache avec les services existants
 */
class CacheAdapter {
  /**
   * Crée un wrapper autour d'une méthode de service pour la mise en cache
   * @param {Function} serviceMethod Méthode de service à wrapper
   * @param {Object} options Options de cache
   * @returns {Function} Méthode wrappée avec cache
   */
  static wrapServiceMethod(serviceMethod, options = {}) {
    const {
      keyPrefix,
      keyGenerator,
      ttl,
      tier = CacheTier.STANDARD,
      tags = [],
      invalidationEvents = [],
      bypassCache = false,
    } = options;

    return async function(...args) {
      // Si le cache est désactivé, appeler directement la méthode
      if (bypassCache) {
        return serviceMethod.apply(this, args);
      }

      // Générer la clé de cache
      let cacheKey;
      if (keyGenerator) {
        cacheKey = keyGenerator(...args);
      } else {
        // Clé par défaut basée sur le préfixe et les arguments
        cacheKey = `${keyPrefix}:${JSON.stringify(args)}`;
      }

      try {
        // Essayer de récupérer du cache
        return await cacheService.getOrSet(
          cacheKey,
          async () => serviceMethod.apply(this, args),
          { ttl, tier, tags }
        );
      } catch (error) {
        logger.error(`Cache error for key ${cacheKey}: ${error.message}`, error);
        // En cas d'erreur, appeler directement la méthode
        return serviceMethod.apply(this, args);
      }
    };
  }

  /**
   * Invalide le cache pour un tag spécifique
   * @param {string} tag Tag à invalider
   * @returns {number} Nombre d'entrées invalidées
   */
  static invalidateTag(tag) {
    try {
      return cacheService.invalidateByTag(tag);
    } catch (error) {
      logger.error(`Error invalidating cache tag ${tag}: ${error.message}`, error);
      return 0;
    }
  }

  /**
   * Invalide le cache pour plusieurs tags
   * @param {Array<string>} tags Tags à invalider
   * @returns {number} Nombre total d'entrées invalidées
   */
  static invalidateTags(tags) {
    try {
      return cacheService.invalidateByTags(tags);
    } catch (error) {
      logger.error(`Error invalidating cache tags: ${error.message}`, error);
      return 0;
    }
  }

  /**
   * Crée un middleware Express pour la mise en cache des réponses API
   * @param {Object} options Options de cache
   * @returns {Function} Middleware Express
   */
  static createApiCacheMiddleware(options = {}) {
    const {
      keyPrefix = 'api',
      ttl,
      tier = CacheTier.STANDARD,
      tags = [],
      methods = ['GET'],
      parameterBlacklist = ['token', 'apiKey', 'password'],
    } = options;

    return async (req, res, next) => {
      // Ne mettre en cache que les méthodes spécifiées
      if (!methods.includes(req.method)) {
        return next();
      }

      // Générer une clé de cache basée sur l'URL et les paramètres
      const url = req.originalUrl || req.url;
      
      // Filtrer les paramètres sensibles
      const filteredQuery = { ...req.query };
      parameterBlacklist.forEach(param => {
        delete filteredQuery[param];
      });

      const cacheKey = `${keyPrefix}:${req.method}:${url}:${JSON.stringify(filteredQuery)}`;
      
      try {
        // Essayer de récupérer du cache
        const cachedResponse = cacheService.get(cacheKey, { tier, tags });
        
        if (cachedResponse) {
          // Ajouter un en-tête pour indiquer que la réponse vient du cache
          res.setHeader('X-Cache', 'HIT');
          return res.status(cachedResponse.status).json(cachedResponse.data);
        }
        
        // Stocker la méthode json originale
        const originalJson = res.json;
        
        // Remplacer la méthode json pour intercepter la réponse
        res.json = function(data) {
          // Restaurer la méthode originale
          res.json = originalJson;
          
          // Mettre en cache la réponse
          cacheService.set(cacheKey, {
            status: res.statusCode,
            data,
          }, { ttl, tier, tags });
          
          // Ajouter un en-tête pour indiquer que la réponse ne vient pas du cache
          res.setHeader('X-Cache', 'MISS');
          
          // Appeler la méthode originale
          return originalJson.call(this, data);
        };
        
        next();
      } catch (error) {
        logger.error(`API cache middleware error: ${error.message}`, error);
        next();
      }
    };
  }
}

module.exports = CacheAdapter;
