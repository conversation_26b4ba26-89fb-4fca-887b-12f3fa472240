const { cacheService } = require('./index');
const { CacheTier } = require('./CacheConfig');
const logger = require('../../utils/logger');

/**
 * Utilitaires pour le cache
 */
class CacheUtils {
  /**
   * Génère une clé de cache pour un livestream
   * @param {string} livestreamId ID du livestream
   * @returns {string} Clé de cache
   */
  static generateLivestreamKey(livestreamId) {
    return `livestream:${livestreamId}`;
  }

  /**
   * Génère une clé de cache pour une liste de livestreams
   * @param {Object} filters Filtres appliqués
   * @returns {string} Clé de cache
   */
  static generateLivestreamsListKey(filters = {}) {
    return `livestreams:list:${JSON.stringify(filters)}`;
  }

  /**
   * Génère une clé de cache pour les messages d'un livestream
   * @param {string} livestreamId ID du livestream
   * @returns {string} Clé de cache
   */
  static generateLivestreamMessagesKey(livestreamId) {
    return `livestream:${livestreamId}:messages`;
  }

  /**
   * Génère une clé de cache pour un article de blog
   * @param {string} blogPostId ID de l'article de blog
   * @returns {string} Clé de cache
   */
  static generateBlogPostKey(blogPostId) {
    return `blog:post:${blogPostId}`;
  }

  /**
   * Génère une clé de cache pour une liste d'articles de blog
   * @param {Object} filters Filtres appliqués
   * @returns {string} Clé de cache
   */
  static generateBlogPostsListKey(filters = {}) {
    return `blog:posts:list:${JSON.stringify(filters)}`;
  }

  /**
   * Génère une clé de cache pour les commentaires d'un article de blog
   * @param {string} blogPostId ID de l'article de blog
   * @returns {string} Clé de cache
   */
  static generateBlogPostCommentsKey(blogPostId) {
    return `blog:post:${blogPostId}:comments`;
  }

  /**
   * Invalide le cache pour un livestream spécifique
   * @param {string} livestreamId ID du livestream
   */
  static invalidateLivestreamCache(livestreamId) {
    try {
      // Invalider le cache du livestream
      cacheService.invalidateByTags([
        `livestream:${livestreamId}`,
        'livestreams'
      ]);
      
      logger.info(`Invalidated cache for livestream ${livestreamId}`);
    } catch (error) {
      logger.error(`Error invalidating livestream cache: ${error.message}`, error);
    }
  }

  /**
   * Invalide le cache pour un article de blog spécifique
   * @param {string} blogPostId ID de l'article de blog
   */
  static invalidateBlogPostCache(blogPostId) {
    try {
      // Invalider le cache de l'article de blog
      cacheService.invalidateByTags([
        `blog:post:${blogPostId}`,
        'blog:posts'
      ]);
      
      logger.info(`Invalidated cache for blog post ${blogPostId}`);
    } catch (error) {
      logger.error(`Error invalidating blog post cache: ${error.message}`, error);
    }
  }

  /**
   * Précharge les données fréquemment utilisées dans le cache
   */
  static async preloadCommonData() {
    try {
      logger.info('Preloading common data into cache...');
      
      // Précharger la liste des livestreams en cours
      const LivestreamService = require('../LivestreamService');
      await cacheService.getOrSet(
        'livestreams:list:active',
        async () => LivestreamService.getLivestreams({ status: 'live' }),
        { 
          tier: CacheTier.FREQUENT,
          tags: ['livestreams', 'active']
        }
      );
      
      // Précharger la liste des articles de blog récents
      const BlogService = require('../BlogService');
      await cacheService.getOrSet(
        'blog:posts:list:recent',
        async () => {
          const posts = await BlogService.getBlogPosts();
          return posts.slice(0, 10); // 10 articles les plus récents
        },
        { 
          tier: CacheTier.STANDARD,
          tags: ['blog:posts', 'recent']
        }
      );
      
      logger.info('Common data preloaded into cache');
    } catch (error) {
      logger.error(`Error preloading common data: ${error.message}`, error);
    }
  }

  /**
   * Configure le cache pour une utilisation optimale
   * @param {Object} options Options de configuration
   */
  static configureCache(options = {}) {
    try {
      const {
        maxSize = 1000,
        ttlValues = {},
      } = options;
      
      // Configurer la taille maximale du cache
      cacheService.setMaxSize(maxSize);
      
      // Configurer les valeurs TTL personnalisées
      if (Object.keys(ttlValues).length > 0) {
        cacheService.setTTLValues(ttlValues);
      }
      
      logger.info('Cache configured successfully');
    } catch (error) {
      logger.error(`Error configuring cache: ${error.message}`, error);
    }
  }
}

module.exports = CacheUtils;
