/**
 * Configuration du cache pour différents types de données
 */

/**
 * Niveaux de cache avec différentes durées de vie
 */
export enum CacheTier {
  MICRO = 'micro',       // Très court (quelques secondes)
  FREQUENT = 'frequent', // Court (quelques minutes)
  STANDARD = 'standard', // Standard (environ une heure)
  STABLE = 'stable',     // Long (plusieurs heures)
  REFERENCE = 'reference' // Très long (un jour ou plus)
}

/**
 * Options pour les opérations de cache
 */
export interface CacheOptions {
  tier?: CacheTier;       // Niveau de cache prédéfini
  ttl?: number;           // Durée de vie en secondes (remplace tier si fourni)
  tags?: string[];        // Tags pour l'invalidation groupée
  compress?: boolean;     // Compresser les données volumineuses
  namespace?: string;     // Espace de noms pour isoler les clés
}

/**
 * Statistiques de performance du cache
 */
export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  setOperations: number;
  getOperations: number;
  invalidations: number;
  averageLookupTime: number;
}

/**
 * Configuration des durées de vie par défaut pour chaque niveau de cache (en secondes)
 */
export const DEFAULT_TTL_VALUES = {
  [CacheTier.MICRO]: 10,           // 10 secondes
  [CacheTier.FREQUENT]: 5 * 60,    // 5 minutes
  [CacheTier.STANDARD]: 60 * 60,   // 1 heure
  [CacheTier.STABLE]: 6 * 60 * 60, // 6 heures
  [CacheTier.REFERENCE]: 24 * 60 * 60, // 24 heures
};

/**
 * Configuration spécifique pour différents types de données
 */
export const CACHE_CONFIG = {
  // Données utilisateur
  userProfile: {
    tier: CacheTier.STANDARD,
    tags: ['user', 'profile'],
  },
  userPreferences: {
    tier: CacheTier.STABLE,
    tags: ['user', 'preferences'],
  },
  
  // Contenu vidéo
  videoMetadata: {
    tier: CacheTier.STANDARD,
    tags: ['video', 'metadata'],
  },
  videoThumbnails: {
    tier: CacheTier.STABLE,
    tags: ['video', 'thumbnails'],
    compress: true,
  },
  videoComments: {
    tier: CacheTier.FREQUENT,
    tags: ['video', 'comments', 'social'],
  },
  
  // Livestreams
  livestreamInfo: {
    tier: CacheTier.MICRO,
    tags: ['livestream', 'metadata'],
  },
  livestreamViewers: {
    tier: CacheTier.MICRO,
    tags: ['livestream', 'viewers', 'analytics'],
  },
  
  // Recommandations
  recommendedVideos: {
    tier: CacheTier.FREQUENT,
    tags: ['recommendations', 'video'],
  },
  recommendedCreators: {
    tier: CacheTier.STANDARD,
    tags: ['recommendations', 'creators'],
  },
  
  // Analytics
  analyticsData: {
    tier: CacheTier.STANDARD,
    tags: ['analytics'],
  },
  
  // Recherche
  searchResults: {
    tier: CacheTier.FREQUENT,
    tags: ['search'],
  },
  
  // Collections et stories
  collections: {
    tier: CacheTier.STANDARD,
    tags: ['collections', 'user'],
  },
  stories: {
    tier: CacheTier.FREQUENT,
    tags: ['stories', 'content'],
  },
};
