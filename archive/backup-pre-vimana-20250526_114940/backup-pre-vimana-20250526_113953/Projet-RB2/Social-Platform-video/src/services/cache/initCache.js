const { cacheService } = require('./index');
const { CacheTier } = require('./CacheConfig');
const CacheUtils = require('./CacheUtils');
const logger = require('../../utils/logger');

/**
 * Initialise le service de cache
 */
async function initializeCache() {
  try {
    logger.info('Initializing cache service...');
    
    // Configurer le cache
    CacheUtils.configureCache({
      maxSize: process.env.CACHE_MAX_SIZE ? parseInt(process.env.CACHE_MAX_SIZE, 10) : 1000,
      ttlValues: {
        [CacheTier.MICRO]: process.env.CACHE_TTL_MICRO ? parseInt(process.env.CACHE_TTL_MICRO, 10) : 10,
        [CacheTier.FREQUENT]: process.env.CACHE_TTL_FREQUENT ? parseInt(process.env.CACHE_TTL_FREQUENT, 10) : 5 * 60,
        [CacheTier.STANDARD]: process.env.CACHE_TTL_STANDARD ? parseInt(process.env.CACHE_TTL_STANDARD, 10) : 60 * 60,
        [CacheTier.STABLE]: process.env.CACHE_TTL_STABLE ? parseInt(process.env.CACHE_TTL_STABLE, 10) : 6 * 60 * 60,
        [CacheTier.REFERENCE]: process.env.CACHE_TTL_REFERENCE ? parseInt(process.env.CACHE_TTL_REFERENCE, 10) : 24 * 60 * 60,
      },
    });
    
    // Précharger les données communes
    await CacheUtils.preloadCommonData();
    
    // Afficher les statistiques initiales
    const stats = cacheService.getStats();
    logger.info(`Cache initialized with ${stats.hits} hits, ${stats.misses} misses, ${stats.hitRate * 100}% hit rate`);
    
    return true;
  } catch (error) {
    logger.error(`Error initializing cache: ${error.message}`, error);
    return false;
  }
}

module.exports = { initializeCache };
