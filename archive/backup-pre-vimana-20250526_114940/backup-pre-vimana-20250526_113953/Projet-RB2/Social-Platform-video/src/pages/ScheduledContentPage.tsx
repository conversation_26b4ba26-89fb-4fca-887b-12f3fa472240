import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Calendar,
  RefreshCw,
  ArrowLeft,
  AlertCircle,
  Loader2,
  Clock,
  Repeat,
  Grid
} from 'lucide-react';
import { ScheduledContentManager } from '../components/content-management/ScheduledContentManager';
import { ScheduleContentDialog } from '../components/content-management/ScheduleContentDialog';
import {
  getScheduledPosts,
  cancelScheduledPosts,
  publishScheduledPosts,
  schedulePosts,
  ScheduledPost,
  RecurrenceSettings
} from '../api/contentApi';
import { useToast } from '../hooks/useToast';

export default function ScheduledContentPage() {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [scheduledItems, setScheduledItems] = useState<ScheduledPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRescheduleDialogOpen, setIsRescheduleDialogOpen] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);

  // Fetch scheduled posts on mount
  useEffect(() => {
    fetchScheduledPosts();
  }, []);

  // Fetch scheduled posts
  const fetchScheduledPosts = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const posts = await getScheduledPosts(1, 100);
      setScheduledItems(posts);
    } catch (err) {
      console.error('Error fetching scheduled posts:', err);
      setError('Failed to load scheduled content. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle publish now
  const handlePublishNow = async (itemIds: string[]) => {
    try {
      await publishScheduledPosts(itemIds);
      showToast('Content published successfully', 'success');
      fetchScheduledPosts();
    } catch (err) {
      console.error('Error publishing content:', err);
      showToast('Failed to publish content', 'error');
      throw err;
    }
  };

  // Handle reschedule
  const handleReschedule = (itemId: string) => {
    setSelectedItemId(itemId);
    setIsRescheduleDialogOpen(true);
  };

  // Handle reschedule submit
  const handleRescheduleSubmit = async (
    itemIds: string[],
    scheduledDate: string,
    recurrence?: RecurrenceSettings
  ) => {
    try {
      // First cancel the current schedule
      await cancelScheduledPosts(itemIds);

      // Then reschedule with the new date and recurrence settings
      // In a real implementation, you would have a dedicated reschedule endpoint
      // that handles this in a single request
      await schedulePosts(itemIds, scheduledDate, recurrence);

      const recurrenceText = recurrence
        ? ` with ${recurrence.pattern} recurrence`
        : '';

      showToast(`Content rescheduled successfully${recurrenceText}`, 'success');
      fetchScheduledPosts();
    } catch (err) {
      console.error('Error rescheduling content:', err);
      showToast('Failed to reschedule content', 'error');
      throw err;
    }
  };

  // Handle cancel
  const handleCancel = async (itemIds: string[]) => {
    try {
      await cancelScheduledPosts(itemIds);
      showToast('Scheduled publication cancelled', 'success');
      fetchScheduledPosts();
    } catch (err) {
      console.error('Error cancelling scheduled content:', err);
      showToast('Failed to cancel scheduled publication', 'error');
      throw err;
    }
  };

  // Get selected item for rescheduling
  const getSelectedItemForReschedule = () => {
    if (!selectedItemId) return [];
    const item = scheduledItems.find(item => item.id === selectedItemId);
    return item ? [item] : [];
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <button
            type="button"
            onClick={() => navigate('/content')}
            className="mr-4 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
          >
            <ArrowLeft size={20} />
          </button>

          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Calendar size={24} className="mr-2 text-blue-500" />
              Scheduled Content
            </h1>
            <p className="text-gray-600 mt-1">
              Manage your scheduled content publications
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            type="button"
            onClick={() => navigate('/content/calendar')}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <Grid size={16} className="mr-1" />
            Calendar View
          </button>

          <button
            type="button"
            onClick={fetchScheduledPosts}
            className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            disabled={isLoading}
          >
            <RefreshCw size={16} className={`mr-1 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md flex items-center">
          <AlertCircle size={20} className="mr-2 flex-shrink-0" />
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-lg font-medium text-gray-900 flex items-center">
            <Clock size={20} className="mr-2 text-blue-500" />
            Publication Schedule
          </h2>

          <div className="text-sm text-gray-500">
            {scheduledItems.length} item{scheduledItems.length !== 1 ? 's' : ''} scheduled
          </div>
        </div>

        <ScheduledContentManager
          scheduledItems={scheduledItems}
          onPublishNow={handlePublishNow}
          onReschedule={handleReschedule}
          onCancel={handleCancel}
          onRefresh={fetchScheduledPosts}
          isLoading={isLoading}
        />
      </div>

      {/* Reschedule Dialog */}
      <ScheduleContentDialog
        isOpen={isRescheduleDialogOpen}
        onClose={() => setIsRescheduleDialogOpen(false)}
        onSchedule={handleRescheduleSubmit}
        selectedItems={getSelectedItemForReschedule()}
      />
    </div>
  );
}
