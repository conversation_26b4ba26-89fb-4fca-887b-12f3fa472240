import { useState, useEffect } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Sparkles, 
  TrendingUp, 
  History, 
  Heart, 
  Users, 
  Zap, 
  Settings, 
  Search, 
  Loader2, 
  AlertCircle 
} from 'lucide-react';
import { useRecommendationsStore } from '@/store/recommendations';
import { RecommendationCard } from '@/components/recommendations/RecommendationCard';
import { RecommendationPreferences } from '@/components/recommendations/RecommendationPreferences';
import { SearchResult } from '@/api/searchApi';

export function RecommendationsPage() {
  const {
    recommendations,
    isLoading,
    error,
    activeRecommendationType,
    fetchPersonalizedRecommendations,
    fetchTrendingRecommendations,
    fetchRecommendationsFromHistory,
    fetchRecommendationsFromLikes,
    fetchRecommendationsFromFollowing,
    fetchNewContentRecommendations,
    loadMoreRecommendations,
    setActiveRecommendationType,
  } = useRecommendationsStore();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  
  // Fetch initial recommendations on mount
  useEffect(() => {
    fetchPersonalizedRecommendations();
  }, [fetchPersonalizedRecommendations]);
  
  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveRecommendationType(value as any);
    
    switch (value) {
      case 'forYou':
        fetchPersonalizedRecommendations();
        break;
      case 'trending':
        fetchTrendingRecommendations();
        break;
      case 'basedOnHistory':
        fetchRecommendationsFromHistory();
        break;
      case 'basedOnLikes':
        fetchRecommendationsFromLikes();
        break;
      case 'fromCreatorsYouFollow':
        fetchRecommendationsFromFollowing();
        break;
      case 'newToYou':
        fetchNewContentRecommendations();
        break;
    }
  };
  
  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    
    // In a real implementation, this would call the search API
    // For now, we'll just simulate a search delay
    setTimeout(() => {
      setIsSearching(false);
    }, 1000);
  };
  
  // Handle load more
  const handleLoadMore = () => {
    loadMoreRecommendations(activeRecommendationType);
  };
  
  // Get current recommendations based on active type
  const getCurrentRecommendations = (): SearchResult[] => {
    return recommendations[activeRecommendationType] || [];
  };
  
  const currentRecommendations = getCurrentRecommendations();
  
  return (
    <div className="min-h-screen bg-gray-50 pb-16">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <h1 className="text-xl font-bold flex items-center">
              <Sparkles size={24} className="text-blue-500 mr-2" />
              Recommendations
            </h1>
            
            <form onSubmit={handleSearch} className="w-full md:w-64">
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search content..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  {isSearching ? (
                    <Loader2 size={16} className="text-gray-400 animate-spin" />
                  ) : (
                    <Search size={16} className="text-gray-400" />
                  )}
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <Tabs defaultValue="forYou" value={activeRecommendationType} onValueChange={handleTabChange}>
          <TabsList className="mb-6">
            <TabsTrigger value="forYou" className="flex items-center">
              <Sparkles size={16} className="mr-2" />
              For You
            </TabsTrigger>
            <TabsTrigger value="trending" className="flex items-center">
              <TrendingUp size={16} className="mr-2" />
              Trending
            </TabsTrigger>
            <TabsTrigger value="basedOnHistory" className="flex items-center">
              <History size={16} className="mr-2" />
              Based on History
            </TabsTrigger>
            <TabsTrigger value="basedOnLikes" className="flex items-center">
              <Heart size={16} className="mr-2" />
              Based on Likes
            </TabsTrigger>
            <TabsTrigger value="fromCreatorsYouFollow" className="flex items-center">
              <Users size={16} className="mr-2" />
              From Following
            </TabsTrigger>
            <TabsTrigger value="newToYou" className="flex items-center">
              <Zap size={16} className="mr-2" />
              New to You
            </TabsTrigger>
            <TabsTrigger value="preferences" className="flex items-center">
              <Settings size={16} className="mr-2" />
              Preferences
            </TabsTrigger>
          </TabsList>
          
          {/* For You Tab */}
          <TabsContent value="forYou">
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2 flex items-center">
                <Sparkles size={20} className="text-blue-500 mr-2" />
                Personalized Recommendations
              </h2>
              <p className="text-sm text-gray-500">
                Content tailored to your interests and viewing history
              </p>
            </div>
            
            {isLoading && currentRecommendations.length === 0 ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 size={32} className="animate-spin text-blue-500" />
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center h-64 text-red-500">
                <AlertCircle size={32} className="mb-2" />
                <p>{error}</p>
                <button
                  onClick={() => fetchPersonalizedRecommendations()}
                  className="mt-4 px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none"
                >
                  Try Again
                </button>
              </div>
            ) : currentRecommendations.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <p className="text-lg mb-2">No recommendations available</p>
                <p className="text-sm">Try adjusting your preferences or explore other categories</p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                  {currentRecommendations.map((item) => (
                    <RecommendationCard
                      key={item.id}
                      item={item}
                      showReason={true}
                      size="md"
                    />
                  ))}
                </div>
                
                <div className="mt-8 text-center">
                  <button
                    onClick={handleLoadMore}
                    disabled={isLoading}
                    className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 size={16} className="inline-block animate-spin mr-2" />
                        Loading...
                      </>
                    ) : (
                      'Load More'
                    )}
                  </button>
                </div>
              </>
            )}
          </TabsContent>
          
          {/* Trending Tab */}
          <TabsContent value="trending">
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2 flex items-center">
                <TrendingUp size={20} className="text-orange-500 mr-2" />
                Trending Content
              </h2>
              <p className="text-sm text-gray-500">
                Popular content that's gaining attention right now
              </p>
            </div>
            
            {/* Similar content structure as For You tab */}
            {isLoading && currentRecommendations.length === 0 ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 size={32} className="animate-spin text-blue-500" />
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center h-64 text-red-500">
                <AlertCircle size={32} className="mb-2" />
                <p>{error}</p>
                <button
                  onClick={() => fetchTrendingRecommendations()}
                  className="mt-4 px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none"
                >
                  Try Again
                </button>
              </div>
            ) : currentRecommendations.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <p className="text-lg mb-2">No trending content available</p>
                <p className="text-sm">Check back later for the latest trends</p>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                  {currentRecommendations.map((item) => (
                    <RecommendationCard
                      key={item.id}
                      item={item}
                      showReason={false}
                      size="md"
                    />
                  ))}
                </div>
                
                <div className="mt-8 text-center">
                  <button
                    onClick={handleLoadMore}
                    disabled={isLoading}
                    className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 size={16} className="inline-block animate-spin mr-2" />
                        Loading...
                      </>
                    ) : (
                      'Load More'
                    )}
                  </button>
                </div>
              </>
            )}
          </TabsContent>
          
          {/* Based on History Tab */}
          <TabsContent value="basedOnHistory">
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2 flex items-center">
                <History size={20} className="text-purple-500 mr-2" />
                Based on Your History
              </h2>
              <p className="text-sm text-gray-500">
                Recommendations based on content you've watched recently
              </p>
            </div>
            
            {/* Similar content structure as For You tab */}
            {/* Implementation similar to other tabs */}
          </TabsContent>
          
          {/* Based on Likes Tab */}
          <TabsContent value="basedOnLikes">
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2 flex items-center">
                <Heart size={20} className="text-red-500 mr-2" />
                Based on Your Likes
              </h2>
              <p className="text-sm text-gray-500">
                Content similar to what you've liked
              </p>
            </div>
            
            {/* Similar content structure as For You tab */}
            {/* Implementation similar to other tabs */}
          </TabsContent>
          
          {/* From Following Tab */}
          <TabsContent value="fromCreatorsYouFollow">
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2 flex items-center">
                <Users size={20} className="text-green-500 mr-2" />
                From Creators You Follow
              </h2>
              <p className="text-sm text-gray-500">
                Latest content from creators you follow
              </p>
            </div>
            
            {/* Similar content structure as For You tab */}
            {/* Implementation similar to other tabs */}
          </TabsContent>
          
          {/* New to You Tab */}
          <TabsContent value="newToYou">
            <div className="mb-6">
              <h2 className="text-lg font-semibold mb-2 flex items-center">
                <Zap size={20} className="text-yellow-500 mr-2" />
                New to You
              </h2>
              <p className="text-sm text-gray-500">
                Discover content you haven't seen before
              </p>
            </div>
            
            {/* Similar content structure as For You tab */}
            {/* Implementation similar to other tabs */}
          </TabsContent>
          
          {/* Preferences Tab */}
          <TabsContent value="preferences">
            <RecommendationPreferences />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
