import { useState } from 'react';
import { CommentsList } from '../components/social/comments/CommentsList';
import { useComments } from '../hooks/useComments';

export function CommentsExamplePage() {
  const [entityType, setEntityType] = useState<'video' | 'livestream' | 'blog'>('video');
  const [entityId, setEntityId] = useState('example-video-123');
  
  const {
    comments,
    totalCount,
    isLoading,
    error,
    addComment,
    likeComment,
    replyToComment,
    editComment,
    deleteComment,
    reportComment,
    loadMoreComments,
    refreshComments,
    hasMoreComments,
  } = useComments({
    entityId,
    entityType,
  });
  
  return (
    <div className="max-w-4xl mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Comments System Example</h1>
      
      {/* Entity type selector */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Entity Type
        </label>
        <div className="flex space-x-4">
          <button
            type="button"
            onClick={() => {
              setEntityType('video');
              setEntityId('example-video-123');
            }}
            className={`px-4 py-2 rounded-md ${
              entityType === 'video'
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Video
          </button>
          <button
            type="button"
            onClick={() => {
              setEntityType('livestream');
              setEntityId('example-livestream-456');
            }}
            className={`px-4 py-2 rounded-md ${
              entityType === 'livestream'
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Livestream
          </button>
          <button
            type="button"
            onClick={() => {
              setEntityType('blog');
              setEntityId('example-blog-789');
            }}
            className={`px-4 py-2 rounded-md ${
              entityType === 'blog'
                ? 'bg-green-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            Blog Post
          </button>
        </div>
      </div>
      
      {/* Mock content */}
      <div className="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold mb-4">
          {entityType === 'video' && 'Example Video Content'}
          {entityType === 'livestream' && 'Example Livestream Content'}
          {entityType === 'blog' && 'Example Blog Post Content'}
        </h2>
        
        <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-md mb-4">
          {entityType === 'video' && (
            <div className="flex items-center justify-center">
              <span className="text-gray-500">Video Player Placeholder</span>
            </div>
          )}
          {entityType === 'livestream' && (
            <div className="flex items-center justify-center">
              <span className="text-gray-500">Livestream Player Placeholder</span>
            </div>
          )}
          {entityType === 'blog' && (
            <div className="flex items-center justify-center">
              <span className="text-gray-500">Blog Post Image Placeholder</span>
            </div>
          )}
        </div>
        
        <p className="text-gray-700">
          This is an example {entityType} content to demonstrate the comments system.
          The comments below are mock data for demonstration purposes.
        </p>
      </div>
      
      {/* Comments section */}
      <CommentsList
        entityId={entityId}
        entityType={entityType}
        comments={comments}
        totalCount={totalCount}
        isLoading={isLoading}
        error={error}
        onAddComment={addComment}
        onLikeComment={likeComment}
        onReplyToComment={replyToComment}
        onEditComment={editComment}
        onDeleteComment={deleteComment}
        onReportComment={reportComment}
        onLoadMoreComments={loadMoreComments}
        onRefreshComments={refreshComments}
        hasMoreComments={hasMoreComments}
      />
      
      {/* Documentation */}
      <div className="mt-12 bg-gray-50 rounded-lg p-6 border border-gray-200">
        <h2 className="text-xl font-semibold mb-4">Comments System Documentation</h2>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-lg">Features</h3>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Rich text comments with emoji support</li>
              <li>Image attachments</li>
              <li>Nested replies (threaded conversations)</li>
              <li>Like/unlike comments</li>
              <li>Edit and delete your own comments</li>
              <li>Report inappropriate comments</li>
              <li>Sort by newest, oldest, or most liked</li>
              <li>Pagination with "Load more" functionality</li>
              <li>Real-time updates (simulated in this example)</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-lg">Components</h3>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li><code>CommentsList</code> - Main container component</li>
              <li><code>CommentItem</code> - Individual comment with replies</li>
              <li><code>CommentEditor</code> - Form for adding/editing comments</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-lg">Hooks</h3>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li><code>useComments</code> - Custom hook for managing comments state and API calls</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-lg">Integration</h3>
            <p className="mt-2">
              To integrate the comments system into your application:
            </p>
            <ol className="list-decimal pl-5 mt-2 space-y-1">
              <li>Import the <code>CommentsList</code> component</li>
              <li>Use the <code>useComments</code> hook with your entity ID and type</li>
              <li>Pass the hook's return values to the <code>CommentsList</code> component</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
