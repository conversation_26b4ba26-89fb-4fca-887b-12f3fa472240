import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  User, 
  Setting<PERSON>, 
  Save, 
  ArrowLeft, 
  Loader2, 
  AlertCircle, 
  CheckCircle
} from 'lucide-react';
import { BioEditor } from '../components/profile/BioEditor';
import { ProfilePictureUpload } from '../components/profile/ProfilePictureUpload';
import { CoverPhotoUpload } from '../components/profile/CoverPhotoUpload';
import { SocialLinksEditor, SocialLink } from '../components/profile/SocialLinksEditor';

interface UserProfile {
  id: string;
  username: string;
  displayName: string;
  bio: string;
  profilePictureUrl?: string;
  coverPhotoUrl?: string;
  socialLinks: SocialLink[];
  createdAt: string;
  updatedAt?: string;
}

export function ProfileEditPage() {
  const navigate = useNavigate();
  
  // Mock user profile data
  const [profile, setProfile] = useState<UserProfile>({
    id: 'user-1',
    username: 'johndo<PERSON>',
    displayName: '<PERSON>',
    bio: 'Wellness enthusiast and yoga instructor with 5 years of experience. Passionate about helping others find balance and peace in their lives.',
    profilePictureUrl: 'https://i.pravatar.cc/300?u=johndoe',
    coverPhotoUrl: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8eW9nYXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=1200&q=60',
    socialLinks: [
      { id: 'link-1', platform: 'instagram', url: 'https://instagram.com/johndoe', isVerified: true },
      { id: 'link-2', platform: 'website', url: 'https://johndoe.com' }
    ],
    createdAt: new Date().toISOString()
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  
  // Clear success message after 5 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [successMessage]);
  
  // Handle bio update
  const handleBioUpdate = async (newBio: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setProfile({
        ...profile,
        bio: newBio,
        updatedAt: new Date().toISOString()
      });
      
      setSuccessMessage('Bio updated successfully');
    } catch (err) {
      setError('Failed to update bio');
      console.error('Error updating bio:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle profile picture update
  const handleProfilePictureUpdate = async (imageFile: File) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Create object URL for preview
      const objectUrl = URL.createObjectURL(imageFile);
      
      setProfile({
        ...profile,
        profilePictureUrl: objectUrl,
        updatedAt: new Date().toISOString()
      });
      
      setSuccessMessage('Profile picture updated successfully');
    } catch (err) {
      setError('Failed to update profile picture');
      console.error('Error updating profile picture:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle profile picture removal
  const handleProfilePictureRemove = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setProfile({
        ...profile,
        profilePictureUrl: undefined,
        updatedAt: new Date().toISOString()
      });
      
      setSuccessMessage('Profile picture removed successfully');
    } catch (err) {
      setError('Failed to remove profile picture');
      console.error('Error removing profile picture:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle cover photo update
  const handleCoverPhotoUpdate = async (imageFile: File) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Create object URL for preview
      const objectUrl = URL.createObjectURL(imageFile);
      
      setProfile({
        ...profile,
        coverPhotoUrl: objectUrl,
        updatedAt: new Date().toISOString()
      });
      
      setSuccessMessage('Cover photo updated successfully');
    } catch (err) {
      setError('Failed to update cover photo');
      console.error('Error updating cover photo:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle cover photo removal
  const handleCoverPhotoRemove = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setProfile({
        ...profile,
        coverPhotoUrl: undefined,
        updatedAt: new Date().toISOString()
      });
      
      setSuccessMessage('Cover photo removed successfully');
    } catch (err) {
      setError('Failed to remove cover photo');
      console.error('Error removing cover photo:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle social links update
  const handleSocialLinksUpdate = async (links: SocialLink[]) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setProfile({
        ...profile,
        socialLinks: links,
        updatedAt: new Date().toISOString()
      });
      
      setSuccessMessage('Social links updated successfully');
    } catch (err) {
      setError('Failed to update social links');
      console.error('Error updating social links:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };
  
  // Navigate back to profile
  const handleBackToProfile = () => {
    navigate('/profile');
  };
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center">
            <button
              type="button"
              onClick={handleBackToProfile}
              className="mr-4 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
            >
              <ArrowLeft size={20} />
            </button>
            
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Edit Profile</h1>
              <p className="text-gray-600">Update your profile information</p>
            </div>
          </div>
          
          <div className="flex items-center">
            <div className="flex items-center mr-4">
              <User size={20} className="mr-2 text-gray-500" />
              <span className="font-medium">{profile.displayName}</span>
            </div>
            
            <button
              type="button"
              onClick={handleBackToProfile}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center"
            >
              <Save size={16} className="mr-2" />
              Done Editing
            </button>
          </div>
        </div>
        
        {/* Status messages */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-md flex items-center">
            <AlertCircle size={20} className="mr-2 flex-shrink-0" />
            <div>{error}</div>
          </div>
        )}
        
        {successMessage && (
          <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-md flex items-center">
            <CheckCircle size={20} className="mr-2 flex-shrink-0" />
            <div>{successMessage}</div>
          </div>
        )}
        
        {/* Cover photo */}
        <div className="mb-6">
          <CoverPhotoUpload
            currentImageUrl={profile.coverPhotoUrl}
            onSave={handleCoverPhotoUpdate}
            onRemove={handleCoverPhotoRemove}
          />
        </div>
        
        {/* Profile picture */}
        <div className="mb-6">
          <ProfilePictureUpload
            currentImageUrl={profile.profilePictureUrl}
            onSave={handleProfilePictureUpdate}
            onRemove={handleProfilePictureRemove}
            size="lg"
            showDefaultAvatars={true}
          />
        </div>
        
        {/* Bio */}
        <div className="mb-6">
          <BioEditor
            initialBio={profile.bio}
            onSave={handleBioUpdate}
            maxLength={500}
            showTemplates={true}
          />
        </div>
        
        {/* Social links */}
        <div className="mb-6">
          <SocialLinksEditor
            links={profile.socialLinks}
            onSave={handleSocialLinksUpdate}
          />
        </div>
      </div>
    </div>
  );
}
