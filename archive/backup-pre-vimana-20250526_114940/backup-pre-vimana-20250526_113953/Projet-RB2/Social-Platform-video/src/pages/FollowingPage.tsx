import { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { FollowersList } from '../components/follow/FollowersList';
import { useFollowStore } from '../store/follow';

export function FollowingPage() {
  const { userId } = useParams<{ userId: string }>();
  const { 
    following, 
    isLoading, 
    error, 
    fetchFollowing, 
    follow, 
    unfollow 
  } = useFollowStore();
  
  useEffect(() => {
    if (userId) {
      fetchFollowing(userId);
    }
  }, [userId, fetchFollowing]);
  
  const handleFollow = async (followUserId: string) => {
    await follow(followUserId);
  };
  
  const handleUnfollow = async (unfollowUserId: string) => {
    await unfollow(unfollowUserId);
  };
  
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b">
        <div className="flex items-center p-4">
          <Link to={`/profile/${userId}`} className="mr-4">
            <ArrowLeft size={24} />
          </Link>
          <h1 className="text-xl font-bold">Following</h1>
        </div>
      </div>
      
      {/* Content */}
      <div className="pb-16">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 size={32} className="animate-spin text-green-500" />
          </div>
        ) : error ? (
          <div className="p-8 text-center text-red-500">
            <p>Error: {error}</p>
            <button
              onClick={() => userId && fetchFollowing(userId)}
              className="mt-4 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              Try Again
            </button>
          </div>
        ) : (
          <FollowersList
            users={following}
            title="Following"
            emptyMessage="Not following anyone yet."
            onFollow={handleFollow}
            onUnfollow={handleUnfollow}
          />
        )}
      </div>
    </div>
  );
}
