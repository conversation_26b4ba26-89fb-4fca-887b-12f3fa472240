import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Loader2, Archive, RefreshCw, Trash2 } from 'lucide-react';
import { useContentStore } from '../store/content';

export function ArchivedPostsPage() {
  const { 
    archivedPosts, 
    isLoading, 
    error, 
    fetchArchivedPosts, 
    restorePost, 
    deletePost 
  } = useContentStore();
  
  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  
  useEffect(() => {
    fetchArchivedPosts(page, limit);
  }, [page, limit, fetchArchivedPosts]);
  
  const handleRestore = async (postId: string) => {
    try {
      await restorePost(postId);
    } catch (error) {
      console.error('Failed to restore post:', error);
    }
  };
  
  const handleDelete = async (postId: string) => {
    try {
      await deletePost(postId);
    } catch (error) {
      console.error('Failed to delete post:', error);
    }
  };
  
  const handleBatchRestore = async () => {
    for (const postId of selectedPosts) {
      try {
        await restorePost(postId);
      } catch (error) {
        console.error(`Failed to restore post ${postId}:`, error);
      }
    }
    setSelectedPosts([]);
  };
  
  const handleBatchDelete = async () => {
    for (const postId of selectedPosts) {
      try {
        await deletePost(postId);
      } catch (error) {
        console.error(`Failed to delete post ${postId}:`, error);
      }
    }
    setSelectedPosts([]);
  };
  
  const togglePostSelection = (postId: string) => {
    setSelectedPosts((prev) =>
      prev.includes(postId)
        ? prev.filter((id) => id !== postId)
        : [...prev, postId]
    );
  };
  
  const selectAllPosts = () => {
    if (selectedPosts.length === archivedPosts.length) {
      setSelectedPosts([]);
    } else {
      setSelectedPosts(archivedPosts.map((post) => post.id));
    }
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };
  
  return (
    <div className="min-h-screen bg-white pb-16">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b">
        <div className="flex items-center p-4">
          <Link to="/profile" className="mr-4">
            <ArrowLeft size={24} />
          </Link>
          <h1 className="text-xl font-bold">Archived Posts</h1>
        </div>
      </div>
      
      {/* Batch Actions */}
      {selectedPosts.length > 0 && (
        <div className="sticky top-16 z-10 bg-white border-b p-4 flex justify-between items-center">
          <div className="text-sm">
            <span className="font-medium">{selectedPosts.length}</span> posts selected
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={handleBatchRestore}
              className="px-3 py-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center text-sm"
            >
              <RefreshCw size={14} className="mr-1" />
              Restore Selected
            </button>
            
            <button
              onClick={handleBatchDelete}
              className="px-3 py-1.5 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center text-sm"
            >
              <Trash2 size={14} className="mr-1" />
              Delete Selected
            </button>
          </div>
        </div>
      )}
      
      {/* Content */}
      <div className="p-4">
        {isLoading && archivedPosts.length === 0 ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 size={32} className="animate-spin text-green-500" />
          </div>
        ) : error ? (
          <div className="p-8 text-center text-red-500">
            <p>Error: {error}</p>
            <button
              onClick={() => fetchArchivedPosts(page, limit)}
              className="mt-4 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              Try Again
            </button>
          </div>
        ) : archivedPosts.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <Archive size={48} className="mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">No archived posts</p>
            <p className="text-sm">When you archive posts, they'll appear here.</p>
          </div>
        ) : (
          <>
            <div className="mb-4 flex justify-between items-center">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={selectedPosts.length === archivedPosts.length && archivedPosts.length > 0}
                  onChange={selectAllPosts}
                  className="h-4 w-4 text-green-500 focus:ring-green-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-500">Select all</span>
              </div>
              
              <div className="text-sm text-gray-500">
                {archivedPosts.length} archived posts
              </div>
            </div>
            
            <div className="space-y-4">
              {archivedPosts.map((post) => (
                <div key={post.id} className="border rounded-lg overflow-hidden">
                  <div className="p-4 flex items-start">
                    <input
                      type="checkbox"
                      checked={selectedPosts.includes(post.id)}
                      onChange={() => togglePostSelection(post.id)}
                      className="h-4 w-4 mt-1 text-green-500 focus:ring-green-500 border-gray-300 rounded"
                    />
                    
                    <div className="ml-3 flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium">{post.title || 'Untitled Post'}</h3>
                          <p className="text-sm text-gray-500">
                            Archived on {formatDate(post.archivedAt || post.updatedAt)}
                          </p>
                        </div>
                        
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleRestore(post.id)}
                            className="p-1.5 text-gray-500 hover:text-green-500 focus:outline-none"
                            title="Restore"
                          >
                            <RefreshCw size={16} />
                          </button>
                          
                          <button
                            onClick={() => handleDelete(post.id)}
                            className="p-1.5 text-gray-500 hover:text-red-500 focus:outline-none"
                            title="Delete permanently"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </div>
                      
                      {post.content && (
                        <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                          {post.content}
                        </p>
                      )}
                      
                      {post.thumbnail && (
                        <img
                          src={post.thumbnail}
                          alt={post.title || 'Post thumbnail'}
                          className="mt-2 h-20 w-auto object-cover rounded"
                        />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Pagination */}
            {archivedPosts.length >= limit && (
              <div className="mt-6 flex justify-center">
                <button
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className="px-4 py-2 border border-gray-300 rounded-l-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={() => setPage(page + 1)}
                  disabled={archivedPosts.length < limit}
                  className="px-4 py-2 border border-gray-300 border-l-0 rounded-r-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
                >
                  Next
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
