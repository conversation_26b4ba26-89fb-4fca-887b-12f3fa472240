import { useNavigate } from 'react-router-dom';
import { StoryCreator } from '../components/stories/StoryCreator';
import { useStoriesStore } from '../store/stories';

export function CreateStoryPage() {
  const navigate = useNavigate();
  const { cancelCreatingStory } = useStoriesStore();
  
  const handleClose = () => {
    cancelCreatingStory();
    navigate('/');
  };
  
  return <StoryCreator onClose={handleClose} />;
}
