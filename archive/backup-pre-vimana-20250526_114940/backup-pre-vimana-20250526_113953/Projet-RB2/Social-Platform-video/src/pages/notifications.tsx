import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { NotificationItem } from '@/components/notifications/NotificationItem';
import { NotificationSettings } from '@/components/notifications/NotificationSettings';
import { useNotificationsStore } from '@/store/notifications';
import {
  Bell,
  Settings,
  Filter,
  Check,
  Trash2,
  ChevronDown,
  Loader2,
  Heart,
  MessageCircle,
  UserPlus,
  Share2,
  Users,
  DollarSign,
  Award,
  Video,
  BarChart2
} from 'lucide-react';
import { NotificationType } from '@/api/notificationsApi';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { cn } from '@/lib/utils';

export function Notifications() {
  const {
    notifications,
    unreadCount,
    totalCount,
    isLoading,
    error,
    activeFilter,
    statistics,
    fetchNotifications,
    fetchStatistics,
    markAllAsRead,
    removeAllNotifications,
    setActiveFilter,
  } = useNotificationsStore();

  const [activeTab, setActiveTab] = useState<'all' | 'settings' | 'analytics'>('all');
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);
  const [page, setPage] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Fetch notifications and statistics on mount
  useEffect(() => {
    fetchNotifications(1, 20, activeFilter === 'all' ? undefined : activeFilter);
    fetchStatistics();
  }, [activeFilter, fetchNotifications, fetchStatistics]);

  // Handle scroll to load more
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    // Load more when scrolled to bottom (with a 50px threshold)
    if (scrollTop + clientHeight >= scrollHeight - 50 && !isLoadingMore) {
      loadMoreNotifications();
    }
  };

  // Load more notifications
  const loadMoreNotifications = async () => {
    if (isLoading || isLoadingMore) return;

    const nextPage = page + 1;
    setIsLoadingMore(true);

    try {
      await fetchNotifications(nextPage, 20, activeFilter === 'all' ? undefined : activeFilter);
      setPage(nextPage);
    } catch (error) {
      console.error('Error loading more notifications:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Handle clear all notifications
  const handleClearAll = async () => {
    try {
      await removeAllNotifications();
    } catch (error) {
      console.error('Error clearing all notifications:', error);
    }
  };

  // Handle filter change
  const handleFilterChange = (filter: NotificationType | 'all' | 'unread') => {
    setActiveFilter(filter);
    setShowFilterDropdown(false);
    setPage(1);
  };

  // Get filter display name
  const getFilterDisplayName = (filter: NotificationType | 'all' | 'unread') => {
    switch (filter) {
      case 'all':
        return 'All Notifications';
      case 'unread':
        return 'Unread';
      case 'like':
        return 'Likes';
      case 'comment':
        return 'Comments';
      case 'follow':
        return 'Follows';
      case 'mention':
        return 'Mentions';
      case 'share':
        return 'Shares';
      case 'collaboration_request':
      case 'collaboration_accepted':
      case 'collaboration_declined':
        return 'Collaborations';
      case 'subscription':
        return 'Subscriptions';
      case 'tip':
      case 'payment':
      case 'payout':
        return 'Payments';
      case 'message':
        return 'Messages';
      case 'content_published':
      case 'content_trending':
        return 'Content';
      case 'achievement':
        return 'Achievements';
      case 'system':
        return 'System';
      default:
        return 'Notifications';
    }
  };

  // Get filter icon
  const getFilterIcon = (filter: NotificationType | 'all' | 'unread') => {
    switch (filter) {
      case 'like':
        return <Heart size={16} className="text-red-500" />;
      case 'comment':
      case 'mention':
      case 'message':
        return <MessageCircle size={16} className="text-blue-500" />;
      case 'follow':
        return <UserPlus size={16} className="text-green-500" />;
      case 'share':
        return <Share2 size={16} className="text-blue-500" />;
      case 'collaboration_request':
      case 'collaboration_accepted':
      case 'collaboration_declined':
      case 'subscription':
        return <Users size={16} className="text-indigo-500" />;
      case 'tip':
      case 'payment':
      case 'payout':
        return <DollarSign size={16} className="text-green-500" />;
      case 'content_published':
      case 'content_trending':
        return <Video size={16} className="text-red-500" />;
      case 'achievement':
        return <Award size={16} className="text-yellow-500" />;
      case 'unread':
        return <Bell size={16} className="text-blue-500" />;
      case 'system':
      case 'all':
      default:
        return <Bell size={16} className="text-gray-500" />;
    }
  };

  // Format number with K/M suffix
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Colors for charts
  const COLORS = ['#EF4444', '#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EC4899'];

  return (
    <div className="min-h-screen bg-gray-50 pb-16">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <h1 className="text-xl font-bold flex items-center">
            <Bell size={24} className="text-blue-500 mr-2" />
            Notifications
          </h1>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <Tabs defaultValue="all" value={activeTab} onValueChange={(value) => setActiveTab(value as 'all' | 'settings' | 'analytics')}>
          <TabsList className="grid grid-cols-3 mb-8">
            <TabsTrigger value="all" className="flex items-center">
              <Bell size={16} className="mr-2" />
              Notifications
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center">
              <BarChart2 size={16} className="mr-2" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center">
              <Settings size={16} className="mr-2" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {/* Filters */}
              <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                <div className="relative">
                  <button
                    onClick={() => setShowFilterDropdown(!showFilterDropdown)}
                    className="px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none flex items-center"
                  >
                    <Filter size={16} className="mr-1" />
                    {getFilterDisplayName(activeFilter)}
                    <ChevronDown size={14} className="ml-1" />
                  </button>

                  {showFilterDropdown && (
                    <div className="absolute left-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                      <div className="py-1">
                        <button
                          onClick={() => handleFilterChange('all')}
                          className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                            activeFilter === 'all' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          <Bell size={16} className="mr-2 text-gray-500" />
                          All Notifications
                        </button>
                        <button
                          onClick={() => handleFilterChange('unread')}
                          className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                            activeFilter === 'unread' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          <Bell size={16} className="mr-2 text-blue-500" />
                          Unread
                        </button>
                        <button
                          onClick={() => handleFilterChange('like')}
                          className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                            activeFilter === 'like' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          <Heart size={16} className="mr-2 text-red-500" />
                          Likes
                        </button>
                        <button
                          onClick={() => handleFilterChange('comment')}
                          className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                            activeFilter === 'comment' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          <MessageCircle size={16} className="mr-2 text-blue-500" />
                          Comments
                        </button>
                        <button
                          onClick={() => handleFilterChange('follow')}
                          className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                            activeFilter === 'follow' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          <UserPlus size={16} className="mr-2 text-green-500" />
                          Follows
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={handleMarkAllAsRead}
                    disabled={unreadCount === 0}
                    className="px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    <Check size={16} className="mr-1" />
                    Mark All Read
                  </button>
                  <button
                    onClick={handleClearAll}
                    disabled={totalCount === 0}
                    className="px-3 py-1.5 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    <Trash2 size={16} className="mr-1" />
                    Clear All
                  </button>
                </div>
              </div>

              {/* Notifications List */}
              <div
                className="max-h-screen overflow-y-auto"
                onScroll={handleScroll}
              >
                {isLoading && notifications.length === 0 ? (
                  <div className="flex justify-center items-center h-64">
                    <Loader2 size={32} className="animate-spin text-blue-500" />
                  </div>
                ) : error ? (
                  <div className="p-8 text-center text-red-500">
                    <p>Error: {error}</p>
                    <button
                      onClick={() => fetchNotifications()}
                      className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      Try Again
                    </button>
                  </div>
                ) : notifications.length === 0 ? (
                  <div className="p-16 text-center text-gray-500">
                    <Bell size={48} className="mx-auto mb-4 text-gray-300" />
                    <h3 className="text-lg font-medium mb-2">No notifications</h3>
                    <p className="text-sm">
                      {activeFilter !== 'all'
                        ? `You don't have any ${getFilterDisplayName(activeFilter).toLowerCase()} notifications.`
                        : "You're all caught up!"}
                    </p>
                    {activeFilter !== 'all' && (
                      <button
                        onClick={() => handleFilterChange('all')}
                        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      >
                        View All Notifications
                      </button>
                    )}
                  </div>
                ) : (
                  <>
                    {notifications.map((notification) => (
                      <NotificationItem
                        key={notification.id}
                        notification={notification}
                      />
                    ))}

                    {isLoadingMore && (
                      <div className="flex justify-center items-center py-4">
                        <Loader2 size={24} className="animate-spin text-blue-500" />
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="analytics">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h2 className="text-lg font-semibold mb-4">Notification Analytics</h2>

              {isLoading && !statistics ? (
                <div className="flex justify-center items-center h-64">
                  <Loader2 size={32} className="animate-spin text-blue-500" />
                </div>
              ) : error ? (
                <div className="p-4 text-center text-red-500">
                  <p>Error: {error}</p>
                  <button
                    onClick={() => fetchStatistics()}
                    className="mt-2 px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                  >
                    Try Again
                  </button>
                </div>
              ) : statistics ? (
                <div className="space-y-8">
                  {/* Overview Stats */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-blue-700 mb-1">Total Notifications</h3>
                      <p className="text-2xl font-bold text-blue-900">{formatNumber(statistics.totalCount)}</p>
                    </div>

                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-yellow-700 mb-1">Unread Notifications</h3>
                      <p className="text-2xl font-bold text-yellow-900">{formatNumber(statistics.unreadCount)}</p>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-green-700 mb-1">Most Common</h3>
                      <p className="text-2xl font-bold text-green-900 capitalize">
                        {Object.entries(statistics.typeCounts).sort((a, b) => b[1] - a[1])[0]?.[0]?.replace('_', ' ') || 'None'}
                      </p>
                    </div>

                    <div className="bg-purple-50 p-4 rounded-lg">
                      <h3 className="text-sm font-medium text-purple-700 mb-1">Weekly Average</h3>
                      <p className="text-2xl font-bold text-purple-900">
                        {statistics.weeklyStats.length > 0
                          ? Math.round(
                              statistics.weeklyStats.reduce((sum, item) => sum + item.count, 0) /
                                statistics.weeklyStats.length
                            )
                          : 0}
                      </p>
                    </div>
                  </div>

                  {/* Charts */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Weekly Trend */}
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Weekly Trend</h3>
                      <div className="h-64">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={statistics.weeklyStats}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" vertical={false} />
                            <XAxis
                              dataKey="date"
                              tick={{ fontSize: 12 }}
                              tickFormatter={(value) => {
                                const date = new Date(value);
                                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                              }}
                            />
                            <YAxis tick={{ fontSize: 12 }} />
                            <Tooltip
                              formatter={(value: number) => [formatNumber(value), 'Notifications']}
                              labelFormatter={(label) => {
                                const date = new Date(label);
                                return date.toLocaleDateString('en-US', {
                                  weekday: 'long',
                                  month: 'long',
                                  day: 'numeric',
                                });
                              }}
                            />
                            <Bar dataKey="count" fill="#3B82F6" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </div>

                    {/* Notification Types */}
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Notification Types</h3>
                      <div className="h-64">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={Object.entries(statistics.typeCounts).map(([type, count]) => ({
                                name: type,
                                value: count,
                              }))}
                              cx="50%"
                              cy="50%"
                              labelLine={false}
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                              label={({ name, percent }) =>
                                `${name.replace('_', ' ')}: ${(percent * 100).toFixed(0)}%`
                              }
                            >
                              {Object.entries(statistics.typeCounts).map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                              ))}
                            </Pie>
                            <Tooltip
                              formatter={(value: number) => [formatNumber(value), 'Notifications']}
                              labelFormatter={(label) => label.replace('_', ' ')}
                            />
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    </div>
                  </div>

                  {/* Type Breakdown */}
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Notification Type Breakdown</h3>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Type
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Count
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Percentage
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {Object.entries(statistics.typeCounts)
                            .sort((a, b) => b[1] - a[1])
                            .map(([type, count]) => (
                              <tr key={type}>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="flex items-center">
                                    {getFilterIcon(type as NotificationType)}
                                    <span className="ml-2 text-sm font-medium text-gray-900 capitalize">
                                      {type.replace('_', ' ')}
                                    </span>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {formatNumber(count)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="flex items-center">
                                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                                      <div
                                        className="bg-blue-600 h-2.5 rounded-full"
                                        style={{
                                          width: `${(count / statistics.totalCount) * 100}%`,
                                        }}
                                      ></div>
                                    </div>
                                    <span className="ml-2 text-sm text-gray-500">
                                      {((count / statistics.totalCount) * 100).toFixed(1)}%
                                    </span>
                                  </div>
                                </td>
                              </tr>
                            ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              ) : null}
            </div>
          </TabsContent>

          <TabsContent value="settings">
            <NotificationSettings />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}