import React, { useState    } from "react";
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>pinner } from 'react-icons/fa';
import VideoGrid from '../components/VideoGrid';
import VideoFilterSidebar from '../components/VideoFilterSidebar';
import { useVideos } from '../hooks/useVideos';

const VideoExplorePage: React.FC = () => {
  const [showFilters, setShowFilters] = useState(false);
  const { videos, loading, error    } = useVideos();
  const [searchQuery, setSearchQuery] = useState('');

  return (;
    <div className = "min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Header */}
        <div className = "mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <h1 className="text-3xl font-bold text-gray-900 mb-4 md:mb-0">
              Explorer les Vidéos;
            </h1>
            <div className="flex items-center space-x-4">
              <div className="relative flex-1 md:w-96">
                <input
                  type="text"
                  placeholder="Rechercher des vidéos..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  value={searchQuery
}
                  onChange = {(e) => setSearchQuery(e.target.value)
}
                />
                <FaSearch className = "absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>
              <button
                onClick={() => setShowFilters(!showFilters)
}
                className = "md:hidden inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50"
              >
                <FaFilter className="mr-2" />
                Filtres;
              </button>
            </div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-8">
          {/* Filters Sidebar */}
          <div
            className = {`${
              showFilters ? 'block' : 'hidden'
} md:block md:w-64 flex-shrink-0`}
          >
            <VideoFilterSidebar />
          </div>

          {/* Main Content */}
          <div className = "flex-1">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <FaSpinner className="animate-spin text-purple-600 text-4xl" />
              </div>
            ) : error ? (
              <div className="text-center py-12">
                <p className="text-red-500">
                  Une erreur est survenue lors du chargement des vidéos.
                </p>
              </div>
            ) : (
              <VideoGrid videos={videos
} />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default VideoExplorePage;