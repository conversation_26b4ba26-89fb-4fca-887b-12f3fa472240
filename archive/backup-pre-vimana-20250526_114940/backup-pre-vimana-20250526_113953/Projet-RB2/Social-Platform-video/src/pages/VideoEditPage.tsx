import { useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { VideoEditor } from '../components/video/VideoEditor';
import { useVideoEditorStore } from '../store/videoEditor';
import { getVideoDetails } from '../api/videoApi';

export function VideoEditPage() {
  const { videoId } = useParams<{ videoId: string }>();
  const navigate = useNavigate();
  const { isLoading, error, videoMetadata } = useVideoEditorStore();
  
  useEffect(() => {
    // Verify that the video exists and belongs to the current user
    const verifyVideoAccess = async () => {
      if (!videoId) {
        navigate('/profile');
        return;
      }
      
      try {
        const video = await getVideoDetails(videoId);
        
        // Check if the video belongs to the current user
        // This would typically involve checking the user ID against the current user
        // For now, we'll just check if the video exists
        if (!video) {
          navigate('/profile');
        }
      } catch (error) {
        console.error('Error verifying video access:', error);
        navigate('/profile');
      }
    };
    
    verifyVideoAccess();
  }, [videoId, navigate]);
  
  const handleSave = () => {
    // Navigate back to the video details page
    if (videoId) {
      navigate(`/videos/${videoId}`);
    } else {
      navigate('/profile');
    }
  };
  
  const handleCancel = () => {
    // Navigate back to the video details page
    if (videoId) {
      navigate(`/videos/${videoId}`);
    } else {
      navigate('/profile');
    }
  };
  
  if (!videoId) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <div className="text-center">
          <p className="text-red-500 mb-4">No video ID provided.</p>
          <Link
            to="/profile"
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          >
            Go to Profile
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50 pb-16">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center">
          <Link to={`/videos/${videoId}`} className="mr-4">
            <ArrowLeft size={24} />
          </Link>
          <h1 className="text-xl font-bold">Edit Video</h1>
        </div>
      </div>
      
      {/* Content */}
      <div className="container mx-auto px-4 py-6">
        {isLoading && !videoMetadata ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 size={32} className="animate-spin text-green-500" />
          </div>
        ) : error ? (
          <div className="p-8 text-center text-red-500">
            <p>Error: {error}</p>
            <Link
              to={`/videos/${videoId}`}
              className="mt-4 inline-block px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              Go Back
            </Link>
          </div>
        ) : (
          <VideoEditor
            videoId={videoId}
            onSave={handleSave}
            onCancel={handleCancel}
          />
        )}
      </div>
    </div>
  );
}
