import React, { useState, useEffect    } from "react";
import { useParams } from 'react-router-dom';
import { BlogPost, Comment } from '../types';
import { BlogApi } from '../api/blogApi';
import { FaHeart, FaComment, FaClock, FaShare, FaTwitter, FaLinkedin } from 'react-icons/fa';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

const BlogPostDetail: React.FC = () => {
  const { id    } = useParams<{ id: string }>();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  const [newComment, setNewComment] = useState('');
  const blogApi = new BlogApi();

  useEffect(() => {
    const fetchPost = async () => {
      if (id) {
        try {
          const postData = await blogApi.getPost(id);
          setPost(postData)
} catch(error) {
          console.error('Error fetching post:', error)
        } finally {
          setLoading(false)
        }
      }
    }

    fetchPost();
  }, [id]);

  const handleLike = async () => {
    if (post?.id) {
      try {
        await blogApi.likePost(post.id);
        setPost(prev => prev ? { ...prev, likes: prev.likes + 1 } : null);
      } catch(error) {
        console.error('Error liking post:', error)
      }
    }
  }

  const handleComment = async () => {
    if (post?.id && newComment.trim()) {
      try {
        const comment = await blogApi.addComment(post.id, {
          content: newComment,
          author: {
            id: 'current-user',
            name: 'Current User',
            avatar: '/avatars/default.jpg',
            bio: 'Member'
}
});

        setPost(prev =>
          prev;
            ? { ...prev, comments: [...prev.comments, comment] }
            : null;
        );
        setNewComment('');
      } catch(error) {
        console.error('Error adding comment:', error)
      }
    }
  }

  if (loading) {
    return (;
      <div className = "flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
}

  if (!post) {
    return (;
      <div className = "max-w-3xl mx-auto px-4 py-12">
        <p className="text-center text-gray-500">Article non trouvé</p>
      </div>
    )
}

  return (;
    <div className = "min-h-screen bg-gray-50">
      {/* Hero Image */}
      {post.imageUrl && (
        <div className = "h-96 w-full relative">
          <img
            src={post.imageUrl
}
            alt = {post.title
}
            className = "w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        </div>
      )
}

      <div className = "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Article Header */}
        <div className = "mb-8">
          <div className="flex items-center space-x-4 mb-6">
            <img
              src={post.author.avatar
}
              alt = {post.author.name
}
              className = "w-12 h-12 rounded-full"
            />
            <div>
              <h4 className="font-medium text-gray-900">{post.author.name
}</h4>
              <p className="text-sm text-gray-500">
                {format(new Date(post.createdAt), 'dd MMMM yyyy', { locale: fr })}
              </p>
            </div>
          </div>

          <h1 className = "text-4xl font-bold text-gray-900 mb-4">{post.title
}</h1>

          <div className = "flex flex-wrap items-center gap-4 text-sm text-gray-500">
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
              {post.category
}
            </span>
            <div className = "flex items-center">
              <FaClock className="mr-1" />
              {post.readTime
} min de lecture;
            </div>
            <div className = "flex items-center">
              <FaHeart className="mr-1 text-red-500" />
              {post.likes
} likes;
            </div>
            <div className = "flex items-center">
              <FaComment className="mr-1" />
              {post.comments.length
} commentaires;
            </div>
          </div>
        </div>

        {/* Article Content */}
        <div className = "prose prose-lg max-w-none mb-12">
          <p className="whitespace-pre-line">{post.content
}</p>
        </div>

        {/* Tags */}
        <div className = "flex flex-wrap gap-2 mb-12">
          {post.tags.map((tag) => (
            <span
              key={tag
}
              className = "bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm"
            >
              #{tag
}
            </span>
          ))}
        </div>

        {/* Social Share */}
        <div className = "border-t border-b border-gray-200 py-6 mb-12">
          <h3 className="text-lg font-semibold mb-4">Partager cet article</h3>
          <div className="flex space-x-4">
            <button className="flex items-center space-x-2 text-gray-600 hover:text-blue-600">
              <FaTwitter />
              <span>Twitter</span>
            </button>
            <button className="flex items-center space-x-2 text-gray-600 hover:text-blue-600">
              <FaLinkedin />
              <span>LinkedIn</span>
            </button>
            <button className="flex items-center space-x-2 text-gray-600 hover:text-blue-600">
              <FaShare />
              <span>Copier le lien</span>
            </button>
          </div>
        </div>

        {/* Comments Section */}
        <div>
          <h3 className = "text-2xl font-semibold mb-6">Commentaires</h3>

          {/* New Comment Form */}
          <div className = "mb-8">
            <textarea
              value={newComment
}
              onChange = {(e) => setNewComment(e.target.value)
}
              placeholder = "Ajouter un commentaire..."
              className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={4
}
            />
            <button
              onClick = {handleComment
}
              className = "mt-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Publier;
            </button>
          </div>

          {/* Comments List */}
          <div className = "space-y-6">
            {post.comments.map((comment) => (
              <div key={comment.id
} className = "bg-white p-6 rounded-lg shadow-sm">
                <div className="flex items-center space-x-4 mb-4">
                  <img
                    src={comment.author.avatar
}
                    alt = {comment.author.name
}
                    className = "w-10 h-10 rounded-full"
                  />
                  <div>
                    <h4 className="font-medium text-gray-900">
                      {comment.author.name
}
                    </h4>
                    <p className="text-sm text-gray-500">
                      {format(new Date(comment.createdAt), 'dd MMMM yyyy', {
                        locale: fr
})}
                    </p>
                  </div>
                </div>
                <p className = "text-gray-600">{comment.content
}</p>
                <div className = "mt-4 flex items-center space-x-4 text-sm text-gray-500">
                  <button className="flex items-center space-x-1">
                    <FaHeart />
                    <span>{comment.likes
}</span>
                  </button>
                  <button className = "flex items-center space-x-1">
                    <FaComment />
                    <span>Répondre</span>
                  </button>
                </div>
              </div>
            ))
}
          </div>
        </div>
      </div>
    </div>
  );
}

export default BlogPostDetail;