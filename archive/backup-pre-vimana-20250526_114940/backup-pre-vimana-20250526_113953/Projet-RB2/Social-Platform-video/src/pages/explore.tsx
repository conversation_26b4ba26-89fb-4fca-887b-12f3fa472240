import { useState, useEffect } from 'react';
import { Search, Filter, Grid, Users, Video, TrendingUp, Loader2 } from 'lucide-react';
import { Avatar } from '@/components/ui/avatar';
import { AdvancedSearch } from '@/components/search/AdvancedSearch';
import { useSearchStore } from '@/store/search';
import { SearchFilters, SearchResult } from '@/api/searchApi';
import { FollowButton } from '@/components/follow/FollowButton';

export function Explore() {
  const {
    results,
    totalResults,
    trendingContent,
    categories,
    isLoading,
    error,
    search,
    fetchTrendingContent,
    fetchCategories,
    loadNextPage,
  } = useSearchStore();

  const [activeTab, setActiveTab] = useState<'all' | 'videos' | 'users'>('all');
  const [searchPerformed, setSearchPerformed] = useState(false);

  // Fetch initial data on mount
  useEffect(() => {
    fetchTrendingContent();
    fetchCategories();
  }, []);

  const handleSearch = (filters: SearchFilters) => {
    // Add content type filter based on active tab
    if (activeTab === 'videos') {
      filters.type = 'video';
    } else if (activeTab === 'users') {
      filters.type = 'user';
    }

    search(filters);
    setSearchPerformed(true);
  };

  const handleTabChange = (tab: 'all' | 'videos' | 'users') => {
    setActiveTab(tab);

    if (searchPerformed) {
      // Re-run search with new content type filter
      const filters = { ...useSearchStore.getState().filters };

      if (tab === 'videos') {
        filters.type = 'video';
      } else if (tab === 'users') {
        filters.type = 'user';
      } else {
        delete filters.type;
      }

      search(filters);
    }
  };

  const handleLoadMore = () => {
    loadNextPage();
  };

  // Format number for display (e.g., 1.2K, 3.5M)
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-16">
      <div className="sticky top-0 bg-white p-4 shadow-sm z-10">
        <AdvancedSearch onSearch={handleSearch} />
      </div>

      {/* Content Tabs */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex">
            <button
              onClick={() => handleTabChange('all')}
              className={`px-4 py-3 flex items-center text-sm font-medium ${
                activeTab === 'all'
                  ? 'text-green-500 border-b-2 border-green-500'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Grid size={16} className="mr-2" />
              All
            </button>

            <button
              onClick={() => handleTabChange('videos')}
              className={`px-4 py-3 flex items-center text-sm font-medium ${
                activeTab === 'videos'
                  ? 'text-green-500 border-b-2 border-green-500'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Video size={16} className="mr-2" />
              Videos
            </button>

            <button
              onClick={() => handleTabChange('users')}
              className={`px-4 py-3 flex items-center text-sm font-medium ${
                activeTab === 'users'
                  ? 'text-green-500 border-b-2 border-green-500'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Users size={16} className="mr-2" />
              Users
            </button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* Search Results */}
        {searchPerformed ? (
          <div>
            {isLoading && results.length === 0 ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 size={32} className="animate-spin text-green-500" />
              </div>
            ) : error ? (
              <div className="p-8 text-center text-red-500">
                <p>Error: {error}</p>
                <button
                  onClick={() => search(useSearchStore.getState().filters)}
                  className="mt-4 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                >
                  Try Again
                </button>
              </div>
            ) : results.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <p className="text-lg font-medium mb-2">No results found</p>
                <p>Try adjusting your search filters or try a different search term.</p>
              </div>
            ) : (
              <div>
                <div className="mb-4 flex justify-between items-center">
                  <h2 className="text-lg font-semibold">
                    {totalResults} {totalResults === 1 ? 'result' : 'results'} found
                  </h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {results.map((result) => (
                    <SearchResultCard key={result.id} result={result} />
                  ))}
                </div>

                {/* Load More Button */}
                {results.length < totalResults && (
                  <div className="mt-8 text-center">
                    <button
                      onClick={handleLoadMore}
                      disabled={isLoading}
                      className="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 flex items-center mx-auto"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 size={16} className="animate-spin mr-2" />
                          Loading...
                        </>
                      ) : (
                        'Load More'
                      )}
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : (
          // Default Explore Content
          <div>
            {/* Categories */}
            <section className="mb-8">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Popular Categories</h2>
                <a href="#" className="text-sm text-green-500 hover:text-green-600">
                  View All
                </a>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {categories.slice(0, 8).map((category) => (
                  <button
                    key={category.id}
                    className="bg-white p-4 rounded-lg text-left hover:bg-gray-50 transition-colors shadow-sm border border-gray-100"
                    onClick={() => {
                      handleSearch({ categories: [category.id] });
                    }}
                  >
                    <div className="font-medium">{category.name}</div>
                    <div className="text-sm text-gray-500">{formatNumber(category.count)} posts</div>
                  </button>
                ))}
              </div>
            </section>

            {/* Trending Content */}
            <section>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold flex items-center">
                  <TrendingUp size={20} className="mr-2 text-green-500" />
                  Trending Now
                </h2>
                <a href="#" className="text-sm text-green-500 hover:text-green-600">
                  View All
                </a>
              </div>

              {isLoading && trendingContent.length === 0 ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 size={24} className="animate-spin text-green-500" />
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {trendingContent.slice(0, 6).map((content) => (
                    <SearchResultCard key={content.id} result={content} />
                  ))}
                </div>
              )}
            </section>
          </div>
        )}
      </div>
    </div>
  );
}

// Search Result Card Component
function SearchResultCard({ result }: { result: SearchResult }) {
  const handleFollow = async (userId: string) => {
    console.log('Follow user:', userId);
    // Implement follow functionality
  };

  const handleUnfollow = async (userId: string) => {
    console.log('Unfollow user:', userId);
    // Implement unfollow functionality
  };

  // Format duration in MM:SS format
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date);
  };

  if (result.type === 'user') {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Avatar src={result.creator.avatar} alt={result.creator.name} className="w-12 h-12" />
              <div>
                <h3 className="font-medium">{result.creator.name}</h3>
                <p className="text-sm text-gray-500">@{result.creator.username}</p>
              </div>
            </div>
            <FollowButton
              userId={result.creator.id}
              initialIsFollowing={false}
              onFollow={handleFollow}
              onUnfollow={handleUnfollow}
              size="sm"
            />
          </div>
          {result.description && (
            <p className="mt-3 text-sm text-gray-600 line-clamp-2">{result.description}</p>
          )}
          <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
            <span>{result.stats.views.toLocaleString()} followers</span>
            <span>{formatDate(result.createdAt)}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
      <div className="relative aspect-video bg-gray-100">
        <img
          src={result.thumbnailUrl}
          alt={result.title}
          className="w-full h-full object-cover"
        />
        {result.type === 'video' && result.duration && (
          <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-1.5 py-0.5 rounded">
            {formatDuration(result.duration)}
          </div>
        )}
        {result.isLive && (
          <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full flex items-center">
            <span className="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></span>
            LIVE
          </div>
        )}
      </div>
      <div className="p-3">
        <div className="flex space-x-3">
          <Avatar src={result.creator.avatar} alt={result.creator.name} className="w-8 h-8 mt-1" />
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-sm line-clamp-2">{result.title}</h3>
            <p className="text-xs text-gray-500 mt-1">{result.creator.name}</p>
            <div className="flex items-center text-xs text-gray-500 mt-1">
              <span>{result.stats.views.toLocaleString()} views</span>
              <span className="mx-1">•</span>
              <span>{formatDate(result.createdAt)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}