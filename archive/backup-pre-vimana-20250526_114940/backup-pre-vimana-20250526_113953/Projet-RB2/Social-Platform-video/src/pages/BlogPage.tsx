import React, { useState, useEffect    } from "react";
import { BlogPost, BlogFilter as BlogFilterType, BlogCategory, Tag } from '../types';
import { BlogApi } from '../api/blogApi';
import BlogCard from '../components/BlogCard';
import BlogFilter from '../components/BlogFilter';
// import { FaSpinner } from 'react-icons/fa';

const BlogPage: React.FC = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeFilter, setActiveFilter] = useState<BlogFilterType>({});
  const [showFilters, setShowFilters] = useState(false);
  
  const blogApi = new BlogApi();

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const [postsData, categoriesData, tagsData] = await Promise.all([
          blogApi.getPosts(),
          blogApi.getCategories(),
          blogApi.getTags()
]);

        setPosts(postsData);
        setCategories(categoriesData);
        setTags(tagsData)
      } catch(error) {
        console.error('Error fetching blog data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchInitialData();
  }, []);

  const handleFilterChange = async (filter: BlogFilterType) => {
    setLoading(true);
    try {
      const filteredPosts = await blogApi.getPosts(filter);
      setPosts(filteredPosts);
      setActiveFilter(filter)
} catch(error) {
      console.error('Error filtering posts:', error)
    } finally {
      setLoading(false)
    }
  }

  const handlePostSelect = (post: BlogPost) => {
    // Implementation would handle navigation to post detail page;
    console.log('Selected post:', post)
  }

  return (
    <div className = "min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-20">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            Blog de la Communauté
          </h1>
          <p className="text-lg md:text-xl text-blue-100 max-w-3xl">
            Découvrez les dernières actualités, conseils et expériences partagés par notre communauté de passionnés du bien-être et du développement personnel.
          </p>
        </div>
      </div>

      <div className = "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className = {`lg:w-64 ${showFilters ? 'block' : 'hidden lg:block'
}`}>
            <BlogFilter
              categories = {categories
}
              tags = {tags
}
              onFilterChange = {handleFilterChange
}
              initialFilter = {activeFilter
}
            />
          </div>

          {/* Main Content */}
          <div className = "flex-1">
            {/* Mobile Filter Toggle */}
            <div className = "lg:hidden mb-6">
              <button
                onClick={() => setShowFilters(!showFilters)
}
                className = "w-full px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm text-center"
              >
                {showFilters ? 'Masquer les filtres' : 'Afficher les filtres'
}
              </button>
            </div>

            {/* Loading State */}
            {loading && (
              <div className = "flex items-center justify-center py-12">
                {/* <FaSpinner className="animate-spin text-blue-600 text-4xl" /> */}
              </div>
            )}

            {/* Posts Grid */}
            {!loading && (
              <div className = "grid grid-cols-1 md:grid-cols-2 gap-6">
                {posts.map((post) => (
                  <BlogCard
                    key={post.id
}
                    post = {post
}
                    onSelect = {handlePostSelect
}
                  />
                ))}
              </div>
            )}

            {/* Empty State */}
            {!loading && posts.length === 0 && (
              <div className = "text-center py-12">
                <p className="text-gray-500">
                  Aucun article trouvé. Essayez de modifier vos critères de recherche.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default BlogPage;