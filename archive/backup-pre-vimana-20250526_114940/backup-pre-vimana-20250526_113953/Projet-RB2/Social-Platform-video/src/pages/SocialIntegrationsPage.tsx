import { useState, useEffect } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { SocialConnections } from '@/components/social/SocialConnections';
import { useSocialSharingStore } from '@/store/socialSharing';
import { 
  Share2, 
  Settings, 
  History, 
  HelpCircle, 
  Save, 
  Hash, 
  Plus, 
  X, 
  Loader2,
  Calendar,
  Link2,
  Check
} from 'lucide-react';

export function SocialIntegrationsPage() {
  const {
    crossPostSettings,
    isLoading,
    error,
    fetchCrossPostSettings,
    updateCrossPostSettings,
    fetchSharingHistory,
    sharingHistory,
  } = useSocialSharingStore();
  
  const [activeTab, setActiveTab] = useState<'connections' | 'settings' | 'history'>('connections');
  const [showHelp, setShowHelp] = useState(false);
  const [editedSettings, setEditedSettings] = useState<{
    defaultCaption: string;
    defaultHashtags: string[];
    platforms: {
      [key: string]: {
        enabled: boolean;
        customCaption?: boolean;
        schedulePost?: boolean;
        includeLink?: boolean;
        includeHashtags?: boolean;
      };
    };
  } | null>(null);
  const [newHashtag, setNewHashtag] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  
  // Fetch cross-post settings and sharing history on mount
  useEffect(() => {
    fetchCrossPostSettings();
    fetchSharingHistory();
  }, [fetchCrossPostSettings, fetchSharingHistory]);
  
  // Set edited settings when cross-post settings change
  useEffect(() => {
    if (crossPostSettings) {
      setEditedSettings({
        defaultCaption: crossPostSettings.defaultCaption,
        defaultHashtags: [...crossPostSettings.defaultHashtags],
        platforms: { ...crossPostSettings.platforms },
      });
    }
  }, [crossPostSettings]);
  
  // Handle adding a hashtag
  const handleAddHashtag = () => {
    if (newHashtag.trim() && editedSettings && !editedSettings.defaultHashtags.includes(newHashtag.trim())) {
      setEditedSettings({
        ...editedSettings,
        defaultHashtags: [...editedSettings.defaultHashtags, newHashtag.trim()],
      });
      setNewHashtag('');
    }
  };
  
  // Handle removing a hashtag
  const handleRemoveHashtag = (tag: string) => {
    if (editedSettings) {
      setEditedSettings({
        ...editedSettings,
        defaultHashtags: editedSettings.defaultHashtags.filter((t) => t !== tag),
      });
    }
  };
  
  // Handle platform setting change
  const handlePlatformSettingChange = (
    platform: string,
    setting: 'enabled' | 'customCaption' | 'schedulePost' | 'includeLink' | 'includeHashtags',
    value: boolean
  ) => {
    if (editedSettings) {
      setEditedSettings({
        ...editedSettings,
        platforms: {
          ...editedSettings.platforms,
          [platform]: {
            ...editedSettings.platforms[platform],
            [setting]: value,
          },
        },
      });
    }
  };
  
  // Handle saving settings
  const handleSaveSettings = async () => {
    if (!editedSettings) return;
    
    setIsSaving(true);
    
    try {
      await updateCrossPostSettings(editedSettings);
    } catch (error) {
      console.error('Error saving settings:', error);
    } finally {
      setIsSaving(false);
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    }).format(date);
  };
  
  return (
    <div className="min-h-screen bg-gray-50 pb-16">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <h1 className="text-xl font-bold flex items-center">
            <Share2 size={24} className="text-blue-500 mr-2" />
            Social Media Integrations
          </h1>
          
          <button
            onClick={() => setShowHelp(!showHelp)}
            className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 focus:outline-none"
            title="Help"
          >
            <HelpCircle size={20} />
          </button>
        </div>
      </div>
      
      {/* Help Panel */}
      {showHelp && (
        <div className="container mx-auto px-4 py-4 bg-blue-50 border-b border-blue-200">
          <div className="flex items-start">
            <div className="flex-1">
              <h3 className="text-lg font-medium text-blue-800 mb-2">Social Media Help</h3>
              <ul className="list-disc pl-5 text-sm text-blue-700 space-y-1">
                <li>Connect your social media accounts to share content directly from the platform.</li>
                <li>Configure cross-posting settings to automatically share to multiple platforms.</li>
                <li>View your sharing history and performance metrics.</li>
                <li>Set default captions and hashtags for your shared content.</li>
                <li>Schedule posts for optimal engagement times.</li>
              </ul>
            </div>
            <button
              onClick={() => setShowHelp(false)}
              className="text-blue-500 hover:text-blue-700 focus:outline-none"
            >
              &times;
            </button>
          </div>
        </div>
      )}
      
      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <Tabs defaultValue="connections" value={activeTab} onValueChange={(value) => setActiveTab(value as 'connections' | 'settings' | 'history')}>
          <TabsList className="grid grid-cols-3 mb-8">
            <TabsTrigger value="connections" className="flex items-center">
              <Share2 size={16} className="mr-2" />
              Connections
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center">
              <Settings size={16} className="mr-2" />
              Cross-Post Settings
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center">
              <History size={16} className="mr-2" />
              Sharing History
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="connections">
            <SocialConnections />
          </TabsContent>
          
          <TabsContent value="settings">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Cross-Post Settings</h2>
                
                <button
                  onClick={handleSaveSettings}
                  disabled={!editedSettings || isLoading || isSaving}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
                >
                  {isSaving ? (
                    <>
                      <Loader2 size={16} className="animate-spin mr-2" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save size={16} className="mr-2" />
                      Save Settings
                    </>
                  )}
                </button>
              </div>
              
              {isLoading && !editedSettings ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 size={24} className="animate-spin text-blue-500" />
                </div>
              ) : error ? (
                <div className="p-4 text-center text-red-500">
                  <p>Error: {error}</p>
                  <button
                    onClick={() => fetchCrossPostSettings()}
                    className="mt-2 px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                  >
                    Try Again
                  </button>
                </div>
              ) : editedSettings ? (
                <div className="space-y-6">
                  {/* Default Caption */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Default Caption
                    </label>
                    <textarea
                      value={editedSettings.defaultCaption}
                      onChange={(e) => setEditedSettings({
                        ...editedSettings,
                        defaultCaption: e.target.value,
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={3}
                      placeholder="Enter a default caption for your shared content..."
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      This caption will be used as a template when sharing content.
                    </p>
                  </div>
                  
                  {/* Default Hashtags */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                      <Hash size={14} className="mr-1" />
                      Default Hashtags
                    </label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {editedSettings.defaultHashtags.map((tag) => (
                        <div
                          key={tag}
                          className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-sm flex items-center"
                        >
                          #{tag}
                          <button
                            onClick={() => handleRemoveHashtag(tag)}
                            className="ml-1 text-gray-500 hover:text-gray-700"
                          >
                            <X size={14} />
                          </button>
                        </div>
                      ))}
                    </div>
                    <div className="flex">
                      <input
                        type="text"
                        value={newHashtag}
                        onChange={(e) => setNewHashtag(e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Add hashtag"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleAddHashtag();
                          }
                        }}
                      />
                      <button
                        onClick={handleAddHashtag}
                        className="px-3 py-2 bg-gray-100 text-gray-700 rounded-r-md border border-gray-300 border-l-0 hover:bg-gray-200 focus:outline-none flex items-center"
                      >
                        <Plus size={16} className="mr-1" />
                        Add
                      </button>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      These hashtags will be automatically added to your shared content.
                    </p>
                  </div>
                  
                  {/* Platform Settings */}
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Platform Settings</h3>
                    
                    <div className="space-y-4">
                      {Object.entries(editedSettings.platforms).map(([platform, settings]) => (
                        <div key={platform} className="bg-gray-50 p-4 rounded-md">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-sm font-medium capitalize">{platform}</h4>
                            <div className="flex items-center">
                              <input
                                type="checkbox"
                                id={`${platform}-enabled`}
                                checked={settings.enabled}
                                onChange={(e) => handlePlatformSettingChange(
                                  platform,
                                  'enabled',
                                  e.target.checked
                                )}
                                className="h-4 w-4 text-blue-500 focus:ring-blue-500 rounded"
                              />
                              <label htmlFor={`${platform}-enabled`} className="ml-2 text-sm text-gray-700">
                                Enabled
                              </label>
                            </div>
                          </div>
                          
                          {settings.enabled && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                              <div className="flex items-center">
                                <input
                                  type="checkbox"
                                  id={`${platform}-customCaption`}
                                  checked={settings.customCaption}
                                  onChange={(e) => handlePlatformSettingChange(
                                    platform,
                                    'customCaption',
                                    e.target.checked
                                  )}
                                  className="h-4 w-4 text-blue-500 focus:ring-blue-500 rounded"
                                />
                                <label htmlFor={`${platform}-customCaption`} className="ml-2 text-xs text-gray-700">
                                  Use custom caption
                                </label>
                              </div>
                              
                              <div className="flex items-center">
                                <input
                                  type="checkbox"
                                  id={`${platform}-schedulePost`}
                                  checked={settings.schedulePost}
                                  onChange={(e) => handlePlatformSettingChange(
                                    platform,
                                    'schedulePost',
                                    e.target.checked
                                  )}
                                  className="h-4 w-4 text-blue-500 focus:ring-blue-500 rounded"
                                />
                                <label htmlFor={`${platform}-schedulePost`} className="ml-2 text-xs text-gray-700 flex items-center">
                                  <Calendar size={12} className="mr-1" />
                                  Allow scheduling
                                </label>
                              </div>
                              
                              <div className="flex items-center">
                                <input
                                  type="checkbox"
                                  id={`${platform}-includeLink`}
                                  checked={settings.includeLink}
                                  onChange={(e) => handlePlatformSettingChange(
                                    platform,
                                    'includeLink',
                                    e.target.checked
                                  )}
                                  className="h-4 w-4 text-blue-500 focus:ring-blue-500 rounded"
                                />
                                <label htmlFor={`${platform}-includeLink`} className="ml-2 text-xs text-gray-700 flex items-center">
                                  <Link2 size={12} className="mr-1" />
                                  Include link
                                </label>
                              </div>
                              
                              <div className="flex items-center">
                                <input
                                  type="checkbox"
                                  id={`${platform}-includeHashtags`}
                                  checked={settings.includeHashtags}
                                  onChange={(e) => handlePlatformSettingChange(
                                    platform,
                                    'includeHashtags',
                                    e.target.checked
                                  )}
                                  className="h-4 w-4 text-blue-500 focus:ring-blue-500 rounded"
                                />
                                <label htmlFor={`${platform}-includeHashtags`} className="ml-2 text-xs text-gray-700 flex items-center">
                                  <Hash size={12} className="mr-1" />
                                  Include hashtags
                                </label>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : null}
            </div>
          </TabsContent>
          
          <TabsContent value="history">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h2 className="text-lg font-semibold mb-4">Sharing History</h2>
              
              {isLoading && sharingHistory.length === 0 ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 size={24} className="animate-spin text-blue-500" />
                </div>
              ) : error ? (
                <div className="p-4 text-center text-red-500">
                  <p>Error: {error}</p>
                  <button
                    onClick={() => fetchSharingHistory()}
                    className="mt-2 px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                  >
                    Try Again
                  </button>
                </div>
              ) : sharingHistory.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No sharing history available.</p>
                  <p className="text-sm mt-1">
                    Share content to social media to see your history here.
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Content
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Platform
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Metrics
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {sharingHistory.map((item) => (
                        <tr key={item.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {item.thumbnailUrl ? (
                                <img
                                  src={item.thumbnailUrl}
                                  alt={item.contentTitle}
                                  className="h-10 w-10 rounded-md object-cover mr-3"
                                />
                              ) : (
                                <div className="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center mr-3">
                                  <span className="text-xs text-gray-500 uppercase">{item.contentType.slice(0, 1)}</span>
                                </div>
                              )}
                              <div>
                                <p className="text-sm font-medium text-gray-900">{item.contentTitle}</p>
                                <p className="text-xs text-gray-500 capitalize">{item.contentType}</p>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-gray-900 capitalize">{item.platform}</span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              item.status === 'published'
                                ? 'bg-green-100 text-green-800'
                                : item.status === 'pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {item.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.publishedAt
                              ? formatDate(item.publishedAt)
                              : item.scheduledFor
                              ? `Scheduled: ${formatDate(item.scheduledFor)}`
                              : '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {item.metrics ? (
                              <div className="flex space-x-2 text-xs">
                                <span className="flex items-center text-gray-500">
                                  <Heart size={12} className="mr-1" />
                                  {item.metrics.likes}
                                </span>
                                <span className="flex items-center text-gray-500">
                                  <MessageCircle size={12} className="mr-1" />
                                  {item.metrics.comments}
                                </span>
                                <span className="flex items-center text-gray-500">
                                  <Share2 size={12} className="mr-1" />
                                  {item.metrics.shares}
                                </span>
                              </div>
                            ) : (
                              <span className="text-xs text-gray-500">No metrics</span>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
