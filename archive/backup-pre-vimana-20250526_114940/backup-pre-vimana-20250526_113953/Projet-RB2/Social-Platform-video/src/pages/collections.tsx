import { useState, useEffect } from 'react';
import { useCollectionsStore } from '../store/collections';
import { CollectionGrid } from '../components/collections/CollectionGrid';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Button } from '../components/ui/button';
import { CreateCollectionDialog } from '../components/collections/CreateCollectionDialog';
import { Loader2, Plus } from 'lucide-react';

export default function CollectionsPage() {
  const {
    fetchUserCollections,
    fetchPublicCollections,
    startCreatingCollection,
    collections,
    publicCollections,
    isLoading,
    error,
  } = useCollectionsStore();
  
  const [activeTab, setActiveTab] = useState<'my-collections' | 'public-collections'>('my-collections');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  
  // Fetch collections when the component mounts
  useEffect(() => {
    fetchUserCollections();
    fetchPublicCollections();
  }, [fetchUserCollections, fetchPublicCollections]);
  
  // Handle create collection
  const handleCreateCollection = () => {
    startCreatingCollection();
    setIsCreateDialogOpen(true);
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Collections</h1>
        
        <Button onClick={handleCreateCollection}>
          <Plus size={16} className="mr-2" />
          Create Collection
        </Button>
      </div>
      
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'my-collections' | 'public-collections')}>
        <TabsList className="mb-6">
          <TabsTrigger value="my-collections">My Collections</TabsTrigger>
          <TabsTrigger value="public-collections">Public Collections</TabsTrigger>
        </TabsList>
        
        <TabsContent value="my-collections">
          {isLoading && collections.length === 0 ? (
            <div className="flex justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            </div>
          ) : error ? (
            <div className="text-center py-12 bg-red-50 rounded-lg">
              <p className="text-red-500">{error}</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => fetchUserCollections()}
              >
                Try Again
              </Button>
            </div>
          ) : (
            <CollectionGrid
              collections={collections}
              isOwner={true}
              showCreateButton={true}
              emptyMessage="You haven't created any collections yet."
            />
          )}
        </TabsContent>
        
        <TabsContent value="public-collections">
          {isLoading && publicCollections.length === 0 ? (
            <div className="flex justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            </div>
          ) : error ? (
            <div className="text-center py-12 bg-red-50 rounded-lg">
              <p className="text-red-500">{error}</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => fetchPublicCollections()}
              >
                Try Again
              </Button>
            </div>
          ) : (
            <CollectionGrid
              collections={publicCollections}
              isOwner={false}
              emptyMessage="No public collections available."
            />
          )}
        </TabsContent>
      </Tabs>
      
      <CreateCollectionDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />
    </div>
  );
}
