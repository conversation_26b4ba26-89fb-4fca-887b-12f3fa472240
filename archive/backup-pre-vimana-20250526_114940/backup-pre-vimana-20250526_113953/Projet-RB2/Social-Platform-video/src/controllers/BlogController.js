const BlogService = require('../services/CachedBlogService');
const MessagingIntegrationService = require('../services/MessagingIntegrationService');
const logger = require('../utils/logger');

/**
 * Contrôleur pour la gestion des articles de blog
 */
class BlogController {
  /**
   * Récupère tous les articles de blog avec filtres optionnels
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async getBlogPosts(req, res) {
    try {
      const filters = req.query;
      const blogPosts = await BlogService.getBlogPosts(filters);
      res.status(200).json(blogPosts);
    } catch (error) {
      logger.error(`Error in getBlogPosts: ${error.message}`, error);
      res.status(500).json({ message: 'Error fetching blog posts' });
    }
  }

  /**
   * Récupère un article de blog par son ID
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async getBlogPostById(req, res) {
    try {
      const { id } = req.params;
      const blogPost = await BlogService.getBlogPostById(id);

      if (!blogPost) {
        return res.status(404).json({ message: 'Blog post not found' });
      }

      res.status(200).json(blogPost);
    } catch (error) {
      logger.error(`Error in getBlogPostById: ${error.message}`, error);
      res.status(500).json({ message: 'Error fetching blog post' });
    }
  }

  /**
   * Crée un nouvel article de blog
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async createBlogPost(req, res) {
    try {
      const blogPostData = req.body;
      const userId = req.user.id;
      const userName = `${req.user.firstName} ${req.user.lastName}`;

      // Ajouter l'ID et le nom de l'utilisateur comme auteur
      blogPostData.authorId = userId;
      blogPostData.authorName = userName;

      const createdBlogPost = await BlogService.createBlogPost(blogPostData);
      res.status(201).json(createdBlogPost);
    } catch (error) {
      logger.error(`Error in createBlogPost: ${error.message}`, error);
      res.status(500).json({ message: 'Error creating blog post' });
    }
  }

  /**
   * Met à jour un article de blog existant
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async updateBlogPost(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      const userId = req.user.id;

      // Vérifier que l'utilisateur est l'auteur de l'article
      const blogPost = await BlogService.getBlogPostById(id);

      if (!blogPost) {
        return res.status(404).json({ message: 'Blog post not found' });
      }

      if (blogPost.authorId !== userId && req.user.role !== 'ADMIN') {
        return res.status(403).json({ message: 'You are not authorized to update this blog post' });
      }

      const updatedBlogPost = await BlogService.updateBlogPost(id, updateData);
      res.status(200).json(updatedBlogPost);
    } catch (error) {
      logger.error(`Error in updateBlogPost: ${error.message}`, error);
      res.status(500).json({ message: 'Error updating blog post' });
    }
  }

  /**
   * Supprime un article de blog
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async deleteBlogPost(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      // Vérifier que l'utilisateur est l'auteur de l'article
      const blogPost = await BlogService.getBlogPostById(id);

      if (!blogPost) {
        return res.status(404).json({ message: 'Blog post not found' });
      }

      if (blogPost.authorId !== userId && req.user.role !== 'ADMIN') {
        return res.status(403).json({ message: 'You are not authorized to delete this blog post' });
      }

      await BlogService.deleteBlogPost(id);
      res.status(200).json({ message: 'Blog post deleted successfully' });
    } catch (error) {
      logger.error(`Error in deleteBlogPost: ${error.message}`, error);
      res.status(500).json({ message: 'Error deleting blog post' });
    }
  }

  /**
   * Récupère les commentaires d'un article de blog
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async getBlogPostComments(req, res) {
    try {
      const { id } = req.params;
      const comments = await BlogService.getBlogPostComments(id);
      res.status(200).json(comments);
    } catch (error) {
      logger.error(`Error in getBlogPostComments: ${error.message}`, error);
      res.status(500).json({ message: 'Error fetching blog post comments' });
    }
  }

  /**
   * Ajoute un commentaire à un article de blog
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async addBlogPostComment(req, res) {
    try {
      const { id } = req.params;
      const { content } = req.body;
      const userId = req.user.id;
      const userName = `${req.user.firstName} ${req.user.lastName}`;

      const comment = await BlogService.addBlogPostComment(id, {
        userId,
        userName,
        content,
      });

      // Envoyer une notification via le service de messagerie
      await MessagingIntegrationService.sendBlogCommentNotification(
        id,
        userId,
        userName,
        content
      );

      res.status(201).json(comment);
    } catch (error) {
      logger.error(`Error in addBlogPostComment: ${error.message}`, error);
      res.status(500).json({ message: 'Error adding blog post comment' });
    }
  }

  /**
   * Ajoute un like à un article de blog
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async likeBlogPost(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const result = await BlogService.toggleBlogPostLike(id, userId, 'like');

      // Envoyer une notification via le service de messagerie
      await MessagingIntegrationService.sendBlogLikeNotification(
        id,
        userId,
        'like'
      );

      res.status(200).json(result);
    } catch (error) {
      logger.error(`Error in likeBlogPost: ${error.message}`, error);
      res.status(500).json({ message: 'Error liking blog post' });
    }
  }

  /**
   * Retire un like d'un article de blog
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async unlikeBlogPost(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const result = await BlogService.toggleBlogPostLike(id, userId, 'unlike');

      // Envoyer une notification via le service de messagerie
      await MessagingIntegrationService.sendBlogLikeNotification(
        id,
        userId,
        'unlike'
      );

      res.status(200).json(result);
    } catch (error) {
      logger.error(`Error in unlikeBlogPost: ${error.message}`, error);
      res.status(500).json({ message: 'Error unliking blog post' });
    }
  }

  /**
   * Partage un article de blog avec un autre utilisateur
   * @param {Object} req Requête Express
   * @param {Object} res Réponse Express
   */
  async shareBlogPost(req, res) {
    try {
      const { id } = req.params;
      const { targetUserId, message } = req.body;
      const userId = req.user.id;

      // Vérifier que l'article existe
      const blogPost = await BlogService.getBlogPostById(id);

      if (!blogPost) {
        return res.status(404).json({ message: 'Blog post not found' });
      }

      // Envoyer une notification via le service de messagerie
      await MessagingIntegrationService.sendContentShareNotification(
        id,
        'blog',
        userId,
        targetUserId,
        message || `J'ai partagé avec vous l'article "${blogPost.title}"`
      );

      res.status(200).json({ message: 'Blog post shared successfully' });
    } catch (error) {
      logger.error(`Error in shareBlogPost: ${error.message}`, error);
      res.status(500).json({ message: 'Error sharing blog post' });
    }
  }
}

module.exports = new BlogController();
