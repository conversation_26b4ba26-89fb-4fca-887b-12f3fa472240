import { Music2 } from 'lucide-react';
import { Avatar } from '@/components/ui/avatar';
import type { User } from '@/types';

interface PostInfoProps {
  user: User;
  caption: string;
  hashtags: string[]
}

export function PostInfo({ user, caption, hashtags }: PostInfoProps) {
  return (
    <>
      <div className = "absolute bottom-20 left-4 right-16 text-white">
        <div className="flex items-center gap-2 mb-3">
          <Avatar src={user.avatar} alt={user.name} size="sm" />
          <span className="font-semibold">{user.username}</span>
          {!user.isFollowing && (
            <button className = "ml-2 px-3 py-1 text-xs font-semibold bg-white/20 rounded-full hover:bg-white/30 transition-colors">
              Follow
            </button>
          )}
        </div>
        <p className = "mb-2 line-clamp-2">{caption}</p>
        <div className = "flex items-center gap-2 text-sm">
          <Music2 className="w-4 h-4" />
          <span className="font-medium">Original Audio</span>
        </div>
      </div>

      <div className="absolute bottom-4 left-4 right-16 flex flex-wrap gap-2">
        {hashtags.map((tag) => (
          <span key={tag} className = "text-brand-300 text-sm">
            {tag}
          </span>
        ))}
      </div>
    </>
  );
}