import React, { ReactNode } from 'react';
import { useSelection } from './SelectionProvider';
import { X, Check, Trash2, Share2, FolderPlus, ChevronDown } from 'lucide-react';

// Type pour les actions de la barre d'outils
interface SelectionToolbarAction<T> {
  // Icône de l'action
  icon: ReactNode;
  // Libellé de l'action
  label: string;
  // Fonction exécutée au clic
  onClick: (selectedItems: T[]) => void;
  // Désactiver conditionnellement l'action
  disabled?: boolean;
  // Condition de visibilité de l'action
  show?: (selectedCount: number) => boolean;
  // Couleur du texte (pour les actions spéciales comme "Supprimer")
  textColor?: string;
}

// Props pour la barre d'outils de sélection
interface SelectionToolbarProps<T> {
  // Actions disponibles
  actions?: SelectionToolbarAction<T>[];
  // Titre personnalisé (affiche par défaut "X éléments sélectionnés")
  title?: (selectedCount: number) => ReactNode;
  // Classe CSS pour la barre d'outils
  className?: string;
  // Position de la barre d'outils
  position?: 'top' | 'bottom';
  // Est-ce que la barre d'outils est fixe
  fixed?: boolean;
  // Callback exécuté lorsque la sélection est effacée
  onClearSelection?: () => void;
}

// Actions par défaut pour la barre d'outils
const defaultActions = <T,>(): SelectionToolbarAction<T>[] => [
  {
    icon: <Trash2 size={18} />,
    label: 'Supprimer',
    onClick: () => console.log('Suppression non implémentée'),
    textColor: 'text-red-500',
    show: (count) => count > 0,
  },
  {
    icon: <Share2 size={18} />,
    label: 'Partager',
    onClick: () => console.log('Partage non implémenté'),
    show: (count) => count > 0,
  },
  {
    icon: <FolderPlus size={18} />,
    label: 'Ajouter à...',
    onClick: () => console.log('Ajout à une collection non implémenté'),
    show: (count) => count > 0,
  },
];

// Composant pour la barre d'outils de sélection
export function SelectionToolbar<T>({
  actions = defaultActions<T>(),
  title,
  className = '',
  position = 'bottom',
  fixed = true,
  onClearSelection,
}: SelectionToolbarProps<T>) {
  // Utiliser le contexte de sélection
  const { 
    selectedCount, 
    selectedItemsList, 
    clearSelection, 
    selectionMode,
    toggleSelectionMode
  } = useSelection<T>();

  // Ne pas afficher si aucun élément n'est sélectionné ou en mode sans sélection
  if (selectedCount === 0 || selectionMode === 'none') {
    return null;
  }

  // Gérer la suppression de la sélection
  const handleClearSelection = () => {
    clearSelection();
    if (onClearSelection) {
      onClearSelection();
    }
  };

  // Titre par défaut
  const defaultTitle = (
    <div className="flex items-center">
      <Check size={16} className="mr-2" />
      <span>{selectedCount} {selectedCount > 1 ? 'éléments sélectionnés' : 'élément sélectionné'}</span>
    </div>
  );

  // Classes pour la position
  const positionClasses = fixed
    ? position === 'top'
      ? 'top-0 left-0 right-0'
      : 'bottom-0 left-0 right-0'
    : '';

  return (
    <div 
      className={`
        bg-white border-t border-gray-200 shadow-lg p-3
        ${fixed ? 'fixed z-50' : 'sticky'} 
        ${positionClasses}
        transition-all duration-300 ease-in-out
        ${className}
      `}
    >
      <div className="container mx-auto flex flex-wrap justify-between items-center">
        {/* Titre et bouton de fermeture */}
        <div className="flex items-center">
          <button 
            onClick={handleClearSelection}
            className="mr-3 p-1 rounded-full hover:bg-gray-100"
            aria-label="Effacer la sélection"
          >
            <X size={18} />
          </button>
          
          {title ? title(selectedCount) : defaultTitle}
          
          {/* Menu de mode de sélection */}
          <div className="ml-4 flex items-center">
            <span className="text-sm text-gray-500 mr-1">Mode:</span>
            <div className="relative inline-block">
              <button className="flex items-center text-sm text-gray-800 hover:text-blue-500">
                {selectionMode === 'single' ? 'Simple' : 'Multiple'}
                <ChevronDown size={14} className="ml-1" />
              </button>
              {/* Menu déroulant non implémenté ici pour la simplicité */}
            </div>
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex items-center space-x-4">
          {actions
            .filter(action => !action.show || action.show(selectedCount))
            .map((action, index) => (
              <button
                key={index}
                onClick={() => action.onClick(selectedItemsList)}
                disabled={action.disabled}
                className={`
                  flex items-center px-3 py-1.5 rounded-md
                  text-sm ${action.textColor || 'text-gray-700'}
                  ${action.disabled 
                    ? 'opacity-50 cursor-not-allowed' 
                    : 'hover:bg-gray-100'}
                `}
              >
                <span className="mr-1.5">{action.icon}</span>
                <span>{action.label}</span>
              </button>
            ))}
        </div>
      </div>
    </div>
  );
} 