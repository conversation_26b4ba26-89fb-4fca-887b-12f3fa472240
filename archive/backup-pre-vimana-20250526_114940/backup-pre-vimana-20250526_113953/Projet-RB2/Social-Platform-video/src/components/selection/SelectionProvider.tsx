import React, { createContext, useState, useContext, ReactNode } from 'react';

// Types pour le contexte de sélection
export interface SelectionContextType<T> {
  // Les éléments sélectionnés (stockés par ID)
  selectedItems: Record<string, T>;
  // Mode de sélection (simple ou multiple)
  selectionMode: 'single' | 'multiple' | 'none';
  // Méthodes pour gérer la sélection
  toggleSelection: (id: string, item: T) => void;
  selectItem: (id: string, item: T) => void;
  unselectItem: (id: string) => void;
  clearSelection: () => void;
  toggleSelectionMode: (mode?: 'single' | 'multiple' | 'none') => void;
  // Helpers
  isSelected: (id: string) => boolean;
  hasSelection: boolean;
  selectedCount: number;
  selectedIds: string[];
  selectedItemsList: T[];
}

// Créer le contexte avec une valeur par défaut undefined
export const SelectionContext = createContext<SelectionContextType<any> | undefined>(undefined);

// Props pour le fournisseur de sélection
interface SelectionProviderProps<T> {
  children: ReactNode;
  initialMode?: 'single' | 'multiple' | 'none';
}

// Fournisseur de contexte de sélection
export function SelectionProvider<T>({ 
  children, 
  initialMode = 'none' 
}: SelectionProviderProps<T>) {
  // État pour gérer les éléments sélectionnés
  const [selectedItems, setSelectedItems] = useState<Record<string, T>>({});
  // État pour gérer le mode de sélection
  const [selectionMode, setSelectionMode] = useState<'single' | 'multiple' | 'none'>(initialMode);

  // Basculer la sélection d'un élément (sélectionner s'il n'est pas sélectionné, désélectionner sinon)
  const toggleSelection = (id: string, item: T) => {
    if (selectionMode === 'none') return;
    
    setSelectedItems(prev => {
      // Si l'élément est déjà sélectionné, le supprimer
      if (prev[id]) {
        const { [id]: removed, ...rest } = prev;
        return rest;
      }
      
      // En mode simple, on ne garde que l'élément nouvellement sélectionné
      if (selectionMode === 'single') {
        return { [id]: item };
      }
      
      // En mode multiple, on ajoute l'élément à la liste des éléments sélectionnés
      return { ...prev, [id]: item };
    });
  };

  // Sélectionner un élément spécifique
  const selectItem = (id: string, item: T) => {
    if (selectionMode === 'none') return;
    
    setSelectedItems(prev => {
      if (selectionMode === 'single') {
        return { [id]: item };
      }
      return { ...prev, [id]: item };
    });
  };

  // Désélectionner un élément spécifique
  const unselectItem = (id: string) => {
    setSelectedItems(prev => {
      if (!prev[id]) return prev;
      
      const { [id]: removed, ...rest } = prev;
      return rest;
    });
  };

  // Effacer toutes les sélections
  const clearSelection = () => {
    setSelectedItems({});
  };

  // Basculer le mode de sélection
  const toggleSelectionMode = (mode?: 'single' | 'multiple' | 'none') => {
    if (mode) {
      setSelectionMode(mode);
      // Si on passe en mode 'none', on efface la sélection
      if (mode === 'none') {
        clearSelection();
      }
    } else {
      // Si aucun mode n'est spécifié, basculer entre les modes
      setSelectionMode(prev => {
        if (prev === 'none') return 'single';
        if (prev === 'single') return 'multiple';
        return 'none';
      });
      
      // Si on bascule vers 'none', effacer la sélection
      if (selectionMode === 'multiple') {
        clearSelection();
      }
    }
  };

  // Vérifier si un élément est sélectionné
  const isSelected = (id: string) => !!selectedItems[id];

  // Calculer les valeurs dérivées
  const selectedCount = Object.keys(selectedItems).length;
  const hasSelection = selectedCount > 0;
  const selectedIds = Object.keys(selectedItems);
  const selectedItemsList = Object.values(selectedItems);

  // Valeur du contexte
  const value: SelectionContextType<T> = {
    selectedItems,
    selectionMode,
    toggleSelection,
    selectItem,
    unselectItem,
    clearSelection,
    toggleSelectionMode,
    isSelected,
    hasSelection,
    selectedCount,
    selectedIds,
    selectedItemsList,
  };

  return (
    <SelectionContext.Provider value={value}>
      {children}
    </SelectionContext.Provider>
  );
}

// Hook pour utiliser le contexte de sélection
export function useSelection<T>() {
  const context = useContext<SelectionContextType<T>>(SelectionContext as any);
  
  if (context === undefined) {
    throw new Error('useSelection must be used within a SelectionProvider');
  }
  
  return context;
} 