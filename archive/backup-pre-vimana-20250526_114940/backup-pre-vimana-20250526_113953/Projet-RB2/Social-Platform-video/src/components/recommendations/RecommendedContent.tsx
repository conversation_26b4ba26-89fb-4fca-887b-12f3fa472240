import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Loader2, ChevronRight, TrendingUp, Sparkles, RefreshCw } from 'lucide-react';
import { useRecommendationsStore } from '../../store/recommendations';
import { SearchResult } from '../../api/searchApi';
import { Avatar } from '../ui/avatar';

interface RecommendedContentProps {
  title?: string;
  subtitle?: string;
  limit?: number;
  showRefresh?: boolean;
  showViewAll?: boolean;
  viewAllLink?: string;
  className?: string;
}

export function RecommendedContent({
  title = 'Recommended for You',
  subtitle,
  limit = 6,
  showRefresh = true,
  showViewAll = true,
  viewAllLink = '/explore',
  className = '',
}: RecommendedContentProps) {
  const { 
    feedItems, 
    isLoading, 
    error, 
    fetchPersonalizedFeed 
  } = useRecommendationsStore();
  
  const [refreshing, setRefreshing] = useState(false);
  
  useEffect(() => {
    fetchPersonalizedFeed(1, limit);
  }, [limit]);
  
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchPersonalizedFeed(1, limit);
    setRefreshing(false);
  };
  
  // Format duration in MM:SS format
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else if (diffDays < 30) {
      return `${Math.floor(diffDays / 7)} weeks ago`;
    } else {
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
      }).format(date);
    }
  };
  
  return (
    <div className={`${className}`}>
      <div className="flex justify-between items-center mb-4">
        <div>
          <h2 className="text-lg font-semibold flex items-center">
            <Sparkles size={18} className="mr-2 text-green-500" />
            {title}
          </h2>
          {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
        </div>
        
        <div className="flex items-center space-x-2">
          {showRefresh && (
            <button
              onClick={handleRefresh}
              disabled={isLoading || refreshing}
              className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              title="Refresh recommendations"
            >
              <RefreshCw size={16} className={refreshing ? 'animate-spin' : ''} />
            </button>
          )}
          
          {showViewAll && (
            <Link
              to={viewAllLink}
              className="text-sm text-green-500 hover:text-green-600 flex items-center"
            >
              View All
              <ChevronRight size={16} />
            </Link>
          )}
        </div>
      </div>
      
      {isLoading && feedItems.length === 0 ? (
        <div className="flex justify-center items-center h-40">
          <Loader2 size={24} className="animate-spin text-green-500" />
        </div>
      ) : error ? (
        <div className="p-4 text-center text-red-500 bg-red-50 rounded-lg">
          <p>Error: {error}</p>
          <button
            onClick={handleRefresh}
            className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            Try Again
          </button>
        </div>
      ) : feedItems.length === 0 ? (
        <div className="p-8 text-center text-gray-500 bg-gray-50 rounded-lg">
          <p>No recommendations available.</p>
          <p className="text-sm mt-1">Try following more users or exploring content.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {feedItems.map((item) => (
            <ContentCard key={item.id} item={item} />
          ))}
        </div>
      )}
    </div>
  );
}

// Content Card Component
function ContentCard({ item }: { item: SearchResult }) {
  if (item.type === 'user') {
    return (
      <Link
        to={`/profile/${item.creator.id}`}
        className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow"
      >
        <div className="p-4">
          <div className="flex items-center space-x-3">
            <Avatar src={item.creator.avatar} alt={item.creator.name} className="w-12 h-12" />
            <div>
              <h3 className="font-medium">{item.creator.name}</h3>
              <p className="text-sm text-gray-500">@{item.creator.username}</p>
            </div>
          </div>
          {item.description && (
            <p className="mt-3 text-sm text-gray-600 line-clamp-2">{item.description}</p>
          )}
        </div>
      </Link>
    );
  }
  
  return (
    <Link
      to={`/${item.type}s/${item.id}`}
      className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-shadow"
    >
      <div className="relative aspect-video bg-gray-100">
        <img
          src={item.thumbnailUrl}
          alt={item.title}
          className="w-full h-full object-cover"
        />
        {item.type === 'video' && item.duration && (
          <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-1.5 py-0.5 rounded">
            {formatDuration(item.duration)}
          </div>
        )}
        {item.isLive && (
          <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full flex items-center">
            <span className="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></span>
            LIVE
          </div>
        )}
        {item.stats.views > 10000 && (
          <div className="absolute top-2 right-2 bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full flex items-center">
            <TrendingUp size={12} className="mr-1" />
            Trending
          </div>
        )}
      </div>
      <div className="p-3">
        <div className="flex space-x-3">
          <Avatar src={item.creator.avatar} alt={item.creator.name} className="w-8 h-8 mt-1" />
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-sm line-clamp-2">{item.title}</h3>
            <p className="text-xs text-gray-500 mt-1">{item.creator.name}</p>
            <div className="flex items-center text-xs text-gray-500 mt-1">
              <span>{item.stats.views.toLocaleString()} views</span>
              <span className="mx-1">•</span>
              <span>{formatDate(item.createdAt)}</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}

// Helper function to format duration
function formatDuration(seconds: number) {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}
