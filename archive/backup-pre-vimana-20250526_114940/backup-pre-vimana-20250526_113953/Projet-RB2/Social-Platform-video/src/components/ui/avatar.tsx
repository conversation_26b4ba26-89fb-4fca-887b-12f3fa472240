import { cn } from '@/lib/utils';

interface AvatarProps extends React.HTMLAttributes<HTMLDivElement> {
  src: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg'
}

export function Avatar({ src, alt, size = 'md', className, ...props }: AvatarProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-16 h-16'
  };
  return (
    <div
      className={cn(
        'relative rounded-full overflow-hidden bg-gray-200',
        sizeClasses[size],
        className
      )}
      {...props}
    >
      <img
        src = {src}
        alt = {alt}
        className = "w-full h-full object-cover"
        onError={(e) => {
          const target = e.target as HTMLImageElement;
          target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(
            alt
          )
}&background=random`;
        }}
      />
    </div>
  );
}