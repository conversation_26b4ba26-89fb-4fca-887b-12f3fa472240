import React from 'react';
import { Link } from 'react-router-dom';
import { Video } from '../types';
import { FaPlay, FaHeart, FaComment, FaClock } from 'react-icons/fa';
import { formatDistanceToNow } from 'date-fns';
import { fr } from 'date-fns/locale';

interface VideoGridProps {
  videos: Video[]
}

const VideoGrid: React.FC<VideoGridProps> = ({ videos }) => {
  return (;
    <div className = "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {videos.map((video) => (
        <Link
          key={video.id
}
          to = {`/videos/${video.id
}`}
          className = "group bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200"
        >
          {/* Thumbnail */}
          <div className = "relative aspect-w-16 aspect-h-9">
            <img
              src={video.thumbnailUrl
}
              alt = {video.title
}
              className = "w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-200 flex items-center justify-center">
              <FaPlay className="text-white text-4xl opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
            </div>
            {video.duration && (
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-sm px-2 py-1 rounded">
                {video.duration
}
              </div>
            )}
          </div>

          {/* Content */}
          <div className = "p-4">
            <div className="flex items-start space-x-3">
              {/* Author Avatar */}
              <img
                src = {video.author.avatar
}
                alt = {video.author.name
}
                className = "w-10 h-10 rounded-full"
              />
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-medium text-gray-900 truncate">
                  {video.title
}
                </h3>
                <p className = "text-sm text-gray-500">{video.author.name
}</p>
              </div>
            </div>

            {/* Video Stats */}
            <div className = "mt-4 flex items-center text-sm text-gray-500 space-x-4">
              <div className="flex items-center">
                <FaHeart className="mr-1 text-red-500" />
                {video.likes
}
              </div>
              <div className = "flex items-center">
                <FaComment className="mr-1" />
                {video.comments
}
              </div>
              <div className="flex items-center">
                <FaClock className="mr-1" />
                {formatDistanceToNow(new Date(video.createdAt), {
                  addSuffix: true,
                  locale: fr
})}
              </div>
            </div>

            {/* Tags */}
            {video.tags && video.tags.length > 0 && (
              <div className = "mt-3 flex flex-wrap gap-2">
                {video.tags.map((tag) => (
                  <span
                    key={tag
}
                    className = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                  >
                    #{tag
}
                  </span>
                ))}
              </div>
            )}
          </div>
        </Link>
      ))}
    </div>
  );
}

export default VideoGrid;