import React, { useState, useEffect    } from "react";
import {
  Box,
  Container,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Avatar,
  Button,
  TextField,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Favorite as FavoriteIcon,
  Comment as CommentIcon,
  Share as ShareIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { api, ENDPOINTS } from '../../services/api';

interface Post {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  image?: string;
  likes: number;
  comments: Comment[];
  timestamp: string;
  liked: boolean
}

interface Comment {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  content: string;
  timestamp: string
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other    } = props;

  return (;
    <div
      role = "tabpanel"
      hidden={value !== index
}
      id = {`simple-tabpanel-${index
}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx = {{ p: 3
}}>{children}</Box>}
    </div>
  );
}

export function SocialContainer() {
  const [posts, setPosts] = useState<Post[]>([]);
  const [newPost, setNewPost] = useState('');
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [commentDialogOpen, setCommentDialogOpen] = useState(false);
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [newComment, setNewComment] = useState('');

  useEffect(() => {
    fetchPosts()
  }, []);

  const fetchPosts = async () => {
    try {
      const response = await api.get(`${ENDPOINTS.social.base
}${ENDPOINTS.social.posts}`);
      setPosts(response.data);
    } catch(error) {
      console.error('Error fetching posts:', error)
    }
  }

  const handleCreatePost = async () => {
    if (!newPost.trim() && !selectedImage) { { { {return }}}};

    try {
      const formData = new FormData();
      formData.append('content', newPost);
      if (selectedImage) {
        formData.append('image', selectedImage)
      }

      await api.post(`${ENDPOINTS.social.base}${ENDPOINTS.social.posts}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
}
});

      setNewPost('');
      setSelectedImage(null);
      fetchPosts();
    } catch(error) {
      console.error('Error creating post:', error)
    }
  }

  const handleLike = async (postId: string) => {
    try {
      await api.post(`${ENDPOINTS.social.base
}${ENDPOINTS.social.likes}/${postId}`);
      setPosts((prevPosts) =>
        prevPosts.map((post) =>
          post.id = postId;
            ? { ...post, likes: post.liked ? post.likes - 1 : post.likes + 1, liked: !post.liked }
            : post;
        )
      );
    } catch(error) {
      console.error('Error liking post:', error)
    }
  }

  const handleComment = async () => {
    if (!selectedPost || !newComment.trim()) { { { {return }}}};

    try {
      await api.post(`${ENDPOINTS.social.base}${ENDPOINTS.social.comments}/${selectedPost.id}`, {
        content: newComment
});

      setNewComment('');
      setCommentDialogOpen(false);
      fetchPosts();
    } catch(error) {
      console.error('Error commenting on post:', error)
    }
  }

  const handleShare = async (postId: string) => {
    try {
      await api.post(`${ENDPOINTS.social.base
}${ENDPOINTS.social.shares}/${postId}`);
      // Handle successful share (e.g., show success message)
    } catch(error) {
      console.error('Error sharing post:', error)
    }
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }

  return (;
    <Container maxWidth="md">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant = "h4" component="h1" gutterBottom>
          Community;
        </Typography>

        <Paper sx={{ mb: 3
}}>
          <Tabs value = {tabValue
} onChange = {handleTabChange
} centered>
            <Tab label = "Feed" />
            <Tab label="Trending" />
            <Tab label="My Posts" />
          </Tabs>
        </Paper>

        <Grid container spacing={3
}>
          <Grid item xs = {12
}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                <Avatar />
                <TextField
                  fullWidth;
                  multiline;
                  rows = {2
}
                  placeholder = "Share your wellness journey..."
                  value={newPost
}
                  onChange = {(e) => setNewPost(e.target.value)
}
                />
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                <Button
                  component = "label"
                  startIcon={<AddIcon />
}
                >
                  Add Image;
                  <input
                    type = "file"
                    hidden;
                    accept="image/*"
                    onChange={(e) => setSelectedImage(e.target.files?.[0] || null)
}
                  />
                </Button>
                <Button variant = "contained" onClick={handleCreatePost
}>
                  Post;
                </Button>
              </Box>
              {selectedImage && (
                <Typography variant = "caption" display="block" sx={{ mt: 1
}}>
                  Selected image: {selectedImage.name}
                </Typography>
              )}
            </Paper>
          </Grid>

          <Grid item xs = {12
}>
            <TabPanel value = {tabValue
} index = {0
}>
              {posts.map((post) => (
                <Card key = {post.id
} sx = {{ mb: 3
}}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar src = {post.userAvatar
} sx = {{ mr: 2
}} />
                      <Box>
                        <Typography variant = "subtitle1">{post.userName
}</Typography>
                        <Typography variant = "caption" color="text.secondary">
                          {new Date(post.timestamp).toLocaleString()
}
                        </Typography>
                      </Box>
                    </Box>
                    <Typography variant = "body1" sx={{ mb: 2
}}>
                      {post.content}
                    </Typography>
                    {post.image && (
                      <CardMedia
                        component = "img"
                        image={post.image
}
                        alt="Post image"
                        sx={{ borderRadius: 1, mb: 2 }}
                      />
                    )}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Button
                        startIcon = {<FavoriteIcon color={post.liked ? 'error' : 'inherit'
} />}
                        onClick = {() => handleLike(post.id)
}
                      >
                        {post.likes}
                      </Button>
                      <Button
                        startIcon = {<CommentIcon />
}
                        onClick = {() => {
                          setSelectedPost(post);
                          setCommentDialogOpen(true)
}}
                      >
                        {post.comments.length}
                      </Button>
                      <Button
                        startIcon = {<ShareIcon />
}
                        onClick = {() => handleShare(post.id)
}
                      >
                        Share;
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </TabPanel>

            <TabPanel value = {tabValue
} index = {1
}>
              <Typography variant = "h6" sx={{ mb: 2
}}>
                Trending Posts;
              </Typography>
              {/* Trending posts content */}
            </TabPanel>

            <TabPanel value = {tabValue
} index = {2
}>
              <Typography variant = "h6" sx={{ mb: 2
}}>
                My Posts;
              </Typography>
              {/* User's posts content */}
            </TabPanel>
          </Grid>
        </Grid>

        <Dialog
          open = {commentDialogOpen
}
          onClose = {() => setCommentDialogOpen(false)
}
          maxWidth="sm"
          fullWidth;
        >
          <DialogTitle>Comments</DialogTitle>
          <DialogContent>
            {selectedPost && (
              <>
                <List>
                  {selectedPost.comments.map((comment, index) => (
                    <React.Fragment key = {comment.id
}>
                      <ListItem alignItems = "flex-start">
                        <ListItemAvatar>
                          <Avatar src={comment.userAvatar
} />
                        </ListItemAvatar>
                        <ListItemText
                          primary = {comment.userName
}
                          secondary = {
                            <>
                              <Typography component="span" variant="body2">
                                {comment.content
}
                              </Typography>
                              <br />
                              <Typography component = "span" variant="caption" color="text.secondary">
                                {new Date(comment.timestamp).toLocaleString()
}
                              </Typography>
                            </>
                          }
                        />
                      </ListItem>
                      {index < selectedPost.comments.length - 1 && <Divider variant = "inset" />
}
                    </React.Fragment>
                  ))}
                </List>
                <Box sx = {{ mt: 2
}}>
                  <TextField
                    fullWidth;
                    multiline;
                    rows = {2
}
                    placeholder = "Write a comment..."
                    value={newComment
}
                    onChange = {(e) => setNewComment(e.target.value)
}
                  />
                </Box>
              </>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick = {() => setCommentDialogOpen(false)
}>Cancel</Button>
            <Button onClick = {handleComment
} variant = "contained">
              Comment;
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
}
