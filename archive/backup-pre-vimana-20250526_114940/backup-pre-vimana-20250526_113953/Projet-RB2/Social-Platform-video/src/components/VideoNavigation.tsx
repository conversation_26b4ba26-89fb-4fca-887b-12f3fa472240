import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FaVideo, FaUpload, FaPlay, FaList } from 'react-icons/fa';
import { useAuth } from '../../../hooks/useAuth';

const VideoNavigation: React.FC = () => {
  const location = useLocation();
  const { isAuthenticated
   } = useAuth();

  const isActive = (path: string) => location.pathname = path;

  return (;
    <nav className="bg-white shadow-sm mb-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-8">
            {/* Logo et titre */}
            <Link to = "/videos" className="flex items-center text-purple-600">
              <FaVideo className="h-6 w-6 mr-2" />
              <span className="font-semibold text-lg">Vidéos</span>
            </Link>

            {/* Navigation principale */}
            <div className = "hidden md:flex items-center space-x-4">
              <Link
                to="/videos/explore"
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  isActive('/videos/explore')
                    ? 'text-purple-600 bg-purple-50'
                    : 'text-gray-700 hover:text-purple-600 hover:bg-purple-50'
}`}
              >
                Explorer;
              </Link>

              {isAuthenticated && (
                <>
                  <Link
                    to = "/dashboard/videos/my-videos"
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      isActive('/dashboard/videos/my-videos')
                        ? 'text-purple-600 bg-purple-50'
                        : 'text-gray-700 hover:text-purple-600 hover:bg-purple-50'
}`}
                  >
                    Mes Vidéos;
                  </Link>
                  <Link
                    to = "/dashboard/videos/live"
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      isActive('/dashboard/videos/live')
                        ? 'text-purple-600 bg-purple-50'
                        : 'text-gray-700 hover:text-purple-600 hover:bg-purple-50'
}`}
                  >
                    Live;
                  </Link>
                </>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className = "flex items-center space-x-4">
            {isAuthenticated ? (
              <Link
                to="/dashboard/videos/upload"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
              >
                <FaUpload className="mr-2" />
                Publier;
              </Link>
            ) : (
              <Link
                to="/login"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-purple-600 bg-white hover:bg-purple-50 border-purple-600"
              >
                Se connecter;
              </Link>
            )
}
          </div>
        </div>
      </div>

      {/* Menu mobile */}
      <div className = "md:hidden border-t border-gray-200">
        <div className="grid grid-cols-3 divide-x divide-gray-200">
          <Link
            to="/videos/explore"
            className={`text-center py-3 ${
              isActive('/videos/explore')
                ? 'text-purple-600 bg-purple-50'
                : 'text-gray-700'
}`}
          >
            <FaPlay className = "h-5 w-5 mx-auto mb-1" />
            <span className="text-xs">Explorer</span>
          </Link>

          {isAuthenticated ? (
            <>
              <Link
                to="/dashboard/videos/my-videos"
                className={`text-center py-3 ${
                  isActive('/dashboard/videos/my-videos')
                    ? 'text-purple-600 bg-purple-50'
                    : 'text-gray-700'
}`}
              >
                <FaList className = "h-5 w-5 mx-auto mb-1" />
                <span className="text-xs">Mes Vidéos</span>
              </Link>
              <Link
                to="/dashboard/videos/upload"
                className={`text-center py-3 ${
                  isActive('/dashboard/videos/upload')
                    ? 'text-purple-600 bg-purple-50'
                    : 'text-gray-700'
}`}
              >
                <FaUpload className = "h-5 w-5 mx-auto mb-1" />
                <span className="text-xs">Publier</span>
              </Link>
            </>
          ) : (
            <Link
              to="/login"
              className="col-span-2 text-center py-3 text-purple-600"
            >
              Se connecter;
            </Link>
          )
}
        </div>
      </div>
    </nav>
  );
}

export default VideoNavigation;