import { useState, useEffect } from 'react';
import { 
  Search, 
  UserPlus, 
  Users, 
  Loader2, 
  AlertCircle, 
  RefreshCw,
  ChevronDown,
  Filter,
  X
} from 'lucide-react';
import { FollowButton } from './FollowButton';
import { User } from './FollowSuggestions';

interface FollowListProps {
  title: string;
  users: User[];
  isLoading: boolean;
  error: string | null;
  onFollow: (userId: string) => Promise<void>;
  onUnfollow: (userId: string) => Promise<void>;
  onRefresh: () => Promise<void>;
  onSearch: (query: string) => void;
  onLoadMore?: () => Promise<void>;
  hasMore?: boolean;
  type: 'followers' | 'following';
  showMutualFollowers?: boolean;
  emptyStateMessage?: string;
  className?: string;
}

type SortOption = 'recent' | 'alphabetical';
type FilterOption = 'all' | 'verified' | 'mutual';

export function FollowList({
  title,
  users,
  isLoading,
  error,
  onFollow,
  onUnfollow,
  onRefresh,
  onSearch,
  onLoadMore,
  hasMore = false,
  type,
  showMutualFollowers = true,
  emptyStateMessage,
  className = '',
}: FollowListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [sortBy, setSortBy] = useState<SortOption>('recent');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [showFilters, setShowFilters] = useState(false);
  
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };
  
  // Handle search submit
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchQuery);
  };
  
  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefresh();
    } catch (error) {
      console.error('Error refreshing list:', error);
    } finally {
      setIsRefreshing(false);
    }
  };
  
  // Handle load more
  const handleLoadMore = async () => {
    if (!onLoadMore || isLoadingMore) return;
    
    setIsLoadingMore(true);
    try {
      await onLoadMore();
    } catch (error) {
      console.error('Error loading more users:', error);
    } finally {
      setIsLoadingMore(false);
    }
  };
  
  // Handle sort change
  const handleSortChange = (option: SortOption) => {
    setSortBy(option);
  };
  
  // Handle filter change
  const handleFilterChange = (option: FilterOption) => {
    setFilterBy(option);
  };
  
  // Toggle filters visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };
  
  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
    onSearch('');
  };
  
  // Render mutual followers text
  const renderMutualFollowers = (user: User) => {
    if (!showMutualFollowers || !user.mutualFollowers || user.mutualFollowers === 0) {
      return null;
    }
    
    if (user.mutualFollowerNames && user.mutualFollowerNames.length > 0) {
      const firstFollower = user.mutualFollowerNames[0];
      
      if (user.mutualFollowers === 1) {
        return (
          <span className="text-xs text-gray-500">
            Followed by {firstFollower}
          </span>
        );
      } else {
        const othersCount = user.mutualFollowers - 1;
        return (
          <span className="text-xs text-gray-500">
            Followed by {firstFollower} and {othersCount} {othersCount === 1 ? 'other' : 'others'}
          </span>
        );
      }
    } else {
      return (
        <span className="text-xs text-gray-500">
          {user.mutualFollowers} mutual {user.mutualFollowers === 1 ? 'follower' : 'followers'}
        </span>
      );
    }
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold flex items-center mb-4">
          {type === 'followers' ? (
            <UserPlus size={18} className="mr-2 text-gray-500" />
          ) : (
            <Users size={18} className="mr-2 text-gray-500" />
          )}
          {title}
        </h3>
        
        {/* Search form */}
        <form onSubmit={handleSearchSubmit} className="relative">
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearchChange}
            placeholder={`Search ${type}...`}
            className="w-full p-2 pl-9 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          
          {searchQuery && (
            <button
              type="button"
              onClick={clearSearch}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X size={16} />
            </button>
          )}
        </form>
        
        {/* Filters toggle */}
        <div className="mt-3 flex justify-between items-center">
          <button
            type="button"
            onClick={toggleFilters}
            className="text-sm text-gray-600 flex items-center"
          >
            <Filter size={14} className="mr-1" />
            Filters
            <ChevronDown size={14} className={`ml-1 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
          
          <button
            type="button"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
          >
            <RefreshCw size={14} className={`mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
        
        {/* Filters */}
        {showFilters && (
          <div className="mt-3 pt-3 border-t border-gray-200 grid grid-cols-2 gap-3">
            {/* Sort options */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Sort by
              </label>
              <select
                value={sortBy}
                onChange={(e) => handleSortChange(e.target.value as SortOption)}
                className="w-full text-sm border border-gray-300 rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="recent">Most recent</option>
                <option value="alphabetical">Alphabetical</option>
              </select>
            </div>
            
            {/* Filter options */}
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Filter by
              </label>
              <select
                value={filterBy}
                onChange={(e) => handleFilterChange(e.target.value as FilterOption)}
                className="w-full text-sm border border-gray-300 rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All {type}</option>
                <option value="verified">Verified only</option>
                <option value="mutual">Mutual connections</option>
              </select>
            </div>
          </div>
        )}
      </div>
      
      {/* Content */}
      <div className="p-4">
        {/* Loading state */}
        {isLoading && users.length === 0 && (
          <div className="py-8 flex flex-col items-center justify-center">
            <Loader2 size={32} className="text-gray-400 animate-spin mb-4" />
            <p className="text-gray-500">Loading {type}...</p>
          </div>
        )}
        
        {/* Error state */}
        {error && (
          <div className="py-6 flex flex-col items-center justify-center">
            <AlertCircle size={32} className="text-red-500 mb-4" />
            <p className="text-red-500 mb-2">{error}</p>
            <button
              type="button"
              onClick={handleRefresh}
              className="text-blue-600 hover:text-blue-800"
            >
              Try again
            </button>
          </div>
        )}
        
        {/* Empty state */}
        {!isLoading && !error && users.length === 0 && (
          <div className="py-8 flex flex-col items-center justify-center">
            {type === 'followers' ? (
              <UserPlus size={32} className="text-gray-400 mb-4" />
            ) : (
              <Users size={32} className="text-gray-400 mb-4" />
            )}
            <p className="text-gray-500 mb-2">
              {emptyStateMessage || `No ${type} found`}
            </p>
            {searchQuery && (
              <button
                type="button"
                onClick={clearSearch}
                className="text-blue-600 hover:text-blue-800"
              >
                Clear search
              </button>
            )}
          </div>
        )}
        
        {/* User list */}
        {!isLoading && !error && users.length > 0 && (
          <div className="space-y-4">
            {users.map(user => (
              <div key={user.id} className="flex items-center py-2">
                {/* Avatar */}
                <div className="mr-3 flex-shrink-0">
                  {user.avatar ? (
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                      {user.name.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                
                {/* User info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center">
                    <p className="font-medium text-gray-900 truncate">
                      {user.name}
                    </p>
                    {user.isVerified && (
                      <span className="ml-1 text-blue-500">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                          <path fillRule="evenodd" d="M8.603 3.799A4.49 4.49 0 0112 2.25c1.357 0 2.573.6 3.397 1.549a4.49 4.49 0 013.498 1.307 4.491 4.491 0 011.307 3.497A4.49 4.49 0 0121.75 12a4.49 4.49 0 01-1.549 3.397 4.491 4.491 0 01-1.307 3.497 4.491 4.491 0 01-3.497 1.307A4.49 4.49 0 0112 21.75a4.49 4.49 0 01-3.397-1.549 4.49 4.49 0 01-3.498-1.306 4.491 4.491 0 01-1.307-3.498A4.49 4.49 0 012.25 12c0-1.357.6-2.573 1.549-3.397a4.49 4.49 0 011.307-3.497 4.49 4.49 0 013.497-1.307zm7.007 6.387a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
                        </svg>
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-500 truncate">@{user.username}</p>
                  {user.bio && (
                    <p className="text-sm text-gray-600 line-clamp-1">{user.bio}</p>
                  )}
                  {renderMutualFollowers(user)}
                </div>
                
                {/* Follow button (not shown for current user) */}
                <div className="ml-4">
                  <FollowButton
                    userId={user.id}
                    userName={user.name}
                    isFollowing={user.isFollowing}
                    size="sm"
                    variant="outline"
                    onFollow={onFollow}
                    onUnfollow={onUnfollow}
                  />
                </div>
              </div>
            ))}
            
            {/* Load more button */}
            {hasMore && onLoadMore && (
              <div className="pt-4 flex justify-center">
                <button
                  type="button"
                  onClick={handleLoadMore}
                  disabled={isLoadingMore}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 flex items-center"
                >
                  {isLoadingMore ? (
                    <>
                      <Loader2 size={16} className="mr-2 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    'Load more'
                  )}
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
