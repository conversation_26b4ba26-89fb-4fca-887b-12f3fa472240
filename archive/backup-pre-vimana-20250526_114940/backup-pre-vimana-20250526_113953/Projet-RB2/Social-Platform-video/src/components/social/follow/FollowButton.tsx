import { useState } from 'react';
import { <PERSON>r<PERSON><PERSON>, <PERSON>r<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Off } from 'lucide-react';

interface FollowButtonProps {
  userId: string;
  userName: string;
  isFollowing: boolean;
  isNotificationsEnabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'outline' | 'minimal';
  showNotificationToggle?: boolean;
  onFollow: (userId: string) => Promise<void>;
  onUnfollow: (userId: string) => Promise<void>;
  onToggleNotifications?: (userId: string, enabled: boolean) => Promise<void>;
  className?: string;
}

export function FollowButton({
  userId,
  userName,
  isFollowing,
  isNotificationsEnabled = true,
  size = 'md',
  variant = 'primary',
  showNotificationToggle = false,
  onFollow,
  onUnfollow,
  onToggleNotifications,
  className = '',
}: FollowButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [showNotificationOptions, setShowNotificationOptions] = useState(false);
  const [following, setFollowing] = useState(isFollowing);
  const [notificationsEnabled, setNotificationsEnabled] = useState(isNotificationsEnabled);
  
  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-1 text-xs',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
  };
  
  // Variant classes
  const variantClasses = {
    primary: following
      ? 'bg-gray-200 text-gray-800 hover:bg-gray-300'
      : 'bg-green-500 text-white hover:bg-green-600',
    secondary: following
      ? 'bg-gray-100 text-gray-800 hover:bg-gray-200'
      : 'bg-blue-500 text-white hover:bg-blue-600',
    outline: following
      ? 'border border-gray-300 text-gray-700 hover:bg-gray-50'
      : 'border border-green-500 text-green-500 hover:bg-green-50',
    minimal: following
      ? 'text-gray-700 hover:bg-gray-100'
      : 'text-green-500 hover:bg-green-50',
  };
  
  // Handle follow/unfollow
  const handleToggleFollow = async () => {
    setIsLoading(true);
    
    try {
      if (following) {
        await onUnfollow(userId);
      } else {
        await onFollow(userId);
      }
      
      setFollowing(!following);
    } catch (error) {
      console.error('Error toggling follow:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle toggle notifications
  const handleToggleNotifications = async () => {
    if (!onToggleNotifications) return;
    
    try {
      await onToggleNotifications(userId, !notificationsEnabled);
      setNotificationsEnabled(!notificationsEnabled);
    } catch (error) {
      console.error('Error toggling notifications:', error);
    }
  };
  
  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={handleToggleFollow}
        disabled={isLoading}
        className={`
          rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2
          transition-colors duration-200 flex items-center justify-center
          ${sizeClasses[size]} ${variantClasses[variant]} ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}
        `}
        aria-label={following ? `Unfollow ${userName}` : `Follow ${userName}`}
      >
        {isLoading ? (
          <Loader2 size={size === 'sm' ? 14 : size === 'md' ? 16 : 18} className="animate-spin mr-1" />
        ) : following ? (
          <UserMinus size={size === 'sm' ? 14 : size === 'md' ? 16 : 18} className="mr-1" />
        ) : (
          <UserPlus size={size === 'sm' ? 14 : size === 'md' ? 16 : 18} className="mr-1" />
        )}
        
        {following ? 'Following' : 'Follow'}
      </button>
      
      {/* Notification toggle (only shown for followed users) */}
      {following && showNotificationToggle && (
        <button
          type="button"
          onClick={handleToggleNotifications}
          className={`
            ml-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
            transition-colors duration-200
            ${sizeClasses[size]} border border-gray-300 text-gray-700 hover:bg-gray-50
          `}
          aria-label={notificationsEnabled ? 'Turn off notifications' : 'Turn on notifications'}
        >
          {notificationsEnabled ? (
            <Bell size={size === 'sm' ? 14 : size === 'md' ? 16 : 18} />
          ) : (
            <BellOff size={size === 'sm' ? 14 : size === 'md' ? 16 : 18} />
          )}
        </button>
      )}
    </div>
  );
}
