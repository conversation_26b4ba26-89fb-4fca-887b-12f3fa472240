import { useState, useEffect } from 'react';
import { 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin, 
  Youtube, 
  RefreshCw, 
  Link2, 
  ExternalLink, 
  Trash2, 
  <PERSON>ader2, 
  <PERSON>, 
  BarChart2
} from 'lucide-react';
import { useSocialSharingStore } from '../../store/socialSharing';
import { SocialPlatform, SocialConnection } from '../../api/socialSharingApi';

interface SocialConnectionsProps {
  className?: string;
}

export function SocialConnections({ className = '' }: SocialConnectionsProps) {
  const {
    socialConnections,
    isLoading,
    isSyncing,
    error,
    fetchSocialConnections,
    connectPlatform,
    disconnectPlatform,
    syncPlatform,
    getAuthUrl,
  } = useSocialSharingStore();
  
  const [connectingPlatform, setConnectingPlatform] = useState<SocialPlatform | null>(null);
  const [showDisconnectConfirm, setShowDisconnectConfirm] = useState<SocialPlatform | null>(null);
  
  // Fetch social connections on mount
  useEffect(() => {
    fetchSocialConnections();
  }, [fetchSocialConnections]);
  
  // <PERSON>le connecting to a platform
  const handleConnect = async (platform: SocialPlatform) => {
    setConnectingPlatform(platform);
    
    try {
      // Get the auth URL
      const redirectUri = `${window.location.origin}/social-auth-callback`;
      const authUrl = await getAuthUrl(platform, redirectUri);
      
      // Open the auth URL in a popup window
      const width = 600;
      const height = 700;
      const left = window.innerWidth / 2 - width / 2;
      const top = window.innerHeight / 2 - height / 2;
      
      const popup = window.open(
        authUrl,
        `Connect to ${platform}`,
        `width=${width},height=${height},left=${left},top=${top}`
      );
      
      // Listen for messages from the popup
      const handleMessage = async (event: MessageEvent) => {
        // Verify origin
        if (event.origin !== window.location.origin) return;
        
        // Check if the message contains an auth code
        if (event.data && event.data.type === 'social-auth' && event.data.platform === platform && event.data.code) {
          // Connect with the auth code
          await connectPlatform(platform, event.data.code);
          
          // Close the popup
          if (popup) popup.close();
          
          // Remove the event listener
          window.removeEventListener('message', handleMessage);
        }
      };
      
      window.addEventListener('message', handleMessage);
    } catch (error) {
      console.error(`Error connecting to ${platform}:`, error);
    } finally {
      setConnectingPlatform(null);
    }
  };
  
  // Handle disconnecting from a platform
  const handleDisconnect = async (platform: SocialPlatform) => {
    try {
      await disconnectPlatform(platform);
      setShowDisconnectConfirm(null);
    } catch (error) {
      console.error(`Error disconnecting from ${platform}:`, error);
    }
  };
  
  // Handle syncing a platform
  const handleSync = async (platform: SocialPlatform) => {
    try {
      await syncPlatform(platform);
    } catch (error) {
      console.error(`Error syncing ${platform}:`, error);
    }
  };
  
  // Get platform icon
  const getPlatformIcon = (platform: SocialPlatform, size: number = 20) => {
    switch (platform) {
      case 'facebook':
        return <Facebook size={size} className="text-blue-600" />;
      case 'twitter':
        return <Twitter size={size} className="text-blue-400" />;
      case 'instagram':
        return <Instagram size={size} className="text-pink-500" />;
      case 'tiktok':
        return <span className="text-black font-bold">TT</span>;
      case 'youtube':
        return <Youtube size={size} className="text-red-600" />;
      case 'linkedin':
        return <Linkedin size={size} className="text-blue-700" />;
      case 'pinterest':
        return <span className="text-red-600 font-bold">P</span>;
      case 'reddit':
        return <span className="text-orange-600 font-bold">R</span>;
      default:
        return null;
    }
  };
  
  // Format number with K/M suffix
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    }).format(date);
  };
  
  // Available platforms
  const availablePlatforms: SocialPlatform[] = [
    'facebook',
    'twitter',
    'instagram',
    'tiktok',
    'youtube',
    'linkedin',
    'pinterest',
    'reddit',
  ];
  
  // Get connected and not connected platforms
  const connectedPlatforms = socialConnections.filter((conn) => conn.isConnected);
  const notConnectedPlatforms = availablePlatforms.filter(
    (platform) => !socialConnections.some((conn) => conn.platform === platform && conn.isConnected)
  );
  
  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Social Media Connections</h2>
        
        <button
          onClick={() => fetchSocialConnections()}
          disabled={isLoading}
          className="p-1.5 text-gray-500 hover:text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none"
          title="Refresh connections"
        >
          <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
        </button>
      </div>
      
      {error && (
        <div className="p-4 bg-red-50 text-red-700 rounded-md">
          <p>{error}</p>
          <button
            onClick={() => fetchSocialConnections()}
            className="mt-2 text-sm underline"
          >
            Try Again
          </button>
        </div>
      )}
      
      {/* Connected Platforms */}
      <div>
        <h3 className="text-lg font-medium mb-4">Connected Accounts</h3>
        
        {isLoading && connectedPlatforms.length === 0 ? (
          <div className="flex justify-center items-center h-32">
            <Loader2 size={24} className="animate-spin text-blue-500" />
          </div>
        ) : connectedPlatforms.length === 0 ? (
          <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
            <p className="text-gray-500">No social media accounts connected.</p>
            <p className="text-sm text-gray-400 mt-1">
              Connect your accounts to share content directly from the platform.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {connectedPlatforms.map((connection) => (
              <div
                key={connection.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-4"
              >
                <div className="flex justify-between">
                  <div className="flex items-start">
                    <div className="h-12 w-12 rounded-full overflow-hidden bg-gray-100 flex items-center justify-center mr-4">
                      {getPlatformIcon(connection.platform, 24)}
                    </div>
                    
                    <div>
                      <h4 className="text-lg font-medium capitalize">{connection.platform}</h4>
                      <p className="text-sm text-gray-500">@{connection.username}</p>
                      <div className="flex items-center mt-1 text-xs text-gray-500">
                        <span>Last synced: {formatDate(connection.lastSyncedAt)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleSync(connection.platform)}
                      disabled={isSyncing}
                      className="p-1.5 text-gray-500 hover:text-blue-500 rounded-md hover:bg-gray-100 focus:outline-none"
                      title="Sync account"
                    >
                      <RefreshCw size={16} className={isSyncing ? 'animate-spin' : ''} />
                    </button>
                    
                    <a
                      href={connection.profileUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-1.5 text-gray-500 hover:text-blue-500 rounded-md hover:bg-gray-100 focus:outline-none"
                      title="View profile"
                    >
                      <ExternalLink size={16} />
                    </a>
                    
                    <button
                      onClick={() => setShowDisconnectConfirm(connection.platform)}
                      className="p-1.5 text-gray-500 hover:text-red-500 rounded-md hover:bg-gray-100 focus:outline-none"
                      title="Disconnect account"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 mt-4">
                  <div className="bg-gray-50 p-2 rounded-md text-center">
                    <div className="text-sm text-gray-500 flex items-center justify-center">
                      <Users size={14} className="mr-1" />
                      Followers
                    </div>
                    <div className="text-lg font-semibold">
                      {formatNumber(connection.followerCount)}
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 p-2 rounded-md text-center">
                    <div className="text-sm text-gray-500 flex items-center justify-center">
                      <Users size={14} className="mr-1" />
                      Following
                    </div>
                    <div className="text-lg font-semibold">
                      {formatNumber(connection.followingCount)}
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 p-2 rounded-md text-center">
                    <div className="text-sm text-gray-500 flex items-center justify-center">
                      <BarChart2 size={14} className="mr-1" />
                      Posts
                    </div>
                    <div className="text-lg font-semibold">
                      {formatNumber(connection.postCount)}
                    </div>
                  </div>
                </div>
                
                {/* Disconnect Confirmation */}
                {showDisconnectConfirm === connection.platform && (
                  <div className="mt-4 p-3 bg-red-50 rounded-md border border-red-200">
                    <p className="text-sm text-red-700">
                      Are you sure you want to disconnect your {connection.platform} account?
                    </p>
                    <div className="flex justify-end mt-2 space-x-2">
                      <button
                        onClick={() => setShowDisconnectConfirm(null)}
                        className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={() => handleDisconnect(connection.platform)}
                        className="px-3 py-1 text-xs bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none"
                      >
                        Disconnect
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Available Platforms */}
      {notConnectedPlatforms.length > 0 && (
        <div>
          <h3 className="text-lg font-medium mb-4">Available Platforms</h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {notConnectedPlatforms.map((platform) => (
              <button
                key={platform}
                onClick={() => handleConnect(platform)}
                disabled={connectingPlatform === platform}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
              >
                <div className="flex flex-col items-center">
                  <div className="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center mb-2">
                    {getPlatformIcon(platform, 24)}
                  </div>
                  
                  <h4 className="text-sm font-medium capitalize">{platform}</h4>
                  
                  <div className="mt-2 flex items-center text-xs text-blue-500">
                    {connectingPlatform === platform ? (
                      <Loader2 size={14} className="animate-spin mr-1" />
                    ) : (
                      <Link2 size={14} className="mr-1" />
                    )}
                    <span>
                      {connectingPlatform === platform ? 'Connecting...' : 'Connect'}
                    </span>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
