import { useState } from 'react';
import { 
  Flag, 
  AlertTriangle, 
  Check, 
  Loader2, 
  X,
  Upload,
  Info
} from 'lucide-react';

export type ReportType = 'user' | 'post' | 'comment' | 'video' | 'livestream';

interface ReportContentDialogProps {
  contentId: string;
  contentType: ReportType;
  contentPreview?: string;
  contentOwner?: string;
  contentOwnerAvatar?: string;
  isOpen: boolean;
  onClose: () => void;
  onReport: (contentId: string, contentType: ReportType, reason: string, details?: string, evidence?: File[]) => Promise<void>;
}

export function ReportContentDialog({
  contentId,
  contentType,
  contentPreview,
  contentOwner,
  contentOwnerAvatar,
  isOpen,
  onClose,
  onReport,
}: ReportContentDialogProps) {
  const [reason, setReason] = useState('');
  const [details, setDetails] = useState('');
  const [evidence, setEvidence] = useState<File[]>([]);
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  if (!isOpen) return null;
  
  // Get content type label
  const getContentTypeLabel = () => {
    switch (contentType) {
      case 'user':
        return 'user';
      case 'post':
        return 'post';
      case 'comment':
        return 'comment';
      case 'video':
        return 'video';
      case 'livestream':
        return 'livestream';
      default:
        return 'content';
    }
  };
  
  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;
    
    // Convert FileList to array and add to evidence
    const newFiles = Array.from(files);
    
    // Validate file types (only images)
    const invalidFiles = newFiles.filter(
      file => !file.type.startsWith('image/')
    );
    
    if (invalidFiles.length > 0) {
      setError('Only image files are allowed');
      return;
    }
    
    // Validate file size (max 5MB per file)
    const largeFiles = newFiles.filter(
      file => file.size > 5 * 1024 * 1024
    );
    
    if (largeFiles.length > 0) {
      setError('Files must be less than 5MB');
      return;
    }
    
    setEvidence([...evidence, ...newFiles]);
    setError(null);
  };
  
  // Remove evidence
  const removeEvidence = (index: number) => {
    setEvidence(evidence.filter((_, i) => i !== index));
  };
  
  // Go to next step
  const goToNextStep = () => {
    if (step === 1 && !reason) {
      setError('Please select a reason for reporting');
      return;
    }
    
    setError(null);
    setStep(step + 1);
  };
  
  // Go to previous step
  const goToPreviousStep = () => {
    setError(null);
    setStep(step - 1);
  };
  
  // Handle report submission
  const handleSubmit = async () => {
    if (!reason) {
      setError('Please select a reason for reporting');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      await onReport(contentId, contentType, reason, details, evidence.length > 0 ? evidence : undefined);
      setSuccess(`Thank you for reporting this ${getContentTypeLabel()}. We'll review it as soon as possible.`);
      
      // Reset form
      setReason('');
      setDetails('');
      setEvidence([]);
      setStep(1);
      
      // Close dialog after a delay
      setTimeout(() => {
        onClose();
      }, 3000);
    } catch (err) {
      setError('Failed to submit report. Please try again.');
      console.error('Error submitting report:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6 relative">
        {/* Close button */}
        <button
          type="button"
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
          aria-label="Close"
        >
          <X size={20} />
        </button>
        
        <div className="text-center mb-6">
          <div className="mx-auto w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-4">
            <Flag size={24} className="text-orange-600" />
          </div>
          
          <h3 className="text-xl font-bold mb-2">
            Report {getContentTypeLabel()}
          </h3>
          
          <p className="text-gray-600">
            Help us understand what's happening and how we can fix it.
          </p>
        </div>
        
        {/* Content preview */}
        {contentPreview && (
          <div className="mb-6 p-3 bg-gray-50 rounded-md">
            {contentOwner && (
              <div className="flex items-center mb-2">
                {/* Avatar */}
                <div className="mr-2 flex-shrink-0">
                  {contentOwnerAvatar ? (
                    <img
                      src={contentOwnerAvatar}
                      alt={contentOwner}
                      className="w-6 h-6 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 text-xs">
                      {contentOwner.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                
                {/* User name */}
                <p className="text-sm font-medium">{contentOwner}</p>
              </div>
            )}
            
            <div className="text-sm text-gray-700 line-clamp-3">
              {contentPreview}
            </div>
          </div>
        )}
        
        {/* Step 1: Select reason */}
        {step === 1 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Why are you reporting this {getContentTypeLabel()}?
            </label>
            
            <div className="space-y-2 mb-6">
              {[
                { value: 'spam', label: 'Spam or scam' },
                { value: 'harassment', label: 'Harassment or bullying' },
                { value: 'hate_speech', label: 'Hate speech or symbols' },
                { value: 'violence', label: 'Violence or dangerous organizations' },
                { value: 'nudity', label: 'Nudity or sexual content' },
                { value: 'misinformation', label: 'False information' },
                { value: 'intellectual_property', label: 'Intellectual property violation' },
                { value: 'other', label: 'Something else' },
              ].map(option => (
                <div key={option.value} className="flex items-center">
                  <input
                    type="radio"
                    id={`reason-${option.value}`}
                    name="report-reason"
                    value={option.value}
                    checked={reason === option.value}
                    onChange={() => setReason(option.value)}
                    className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                  />
                  <label
                    htmlFor={`reason-${option.value}`}
                    className="ml-2 block text-sm text-gray-700"
                  >
                    {option.label}
                  </label>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Step 2: Additional details */}
        {step === 2 && (
          <div>
            <label htmlFor="report-details" className="block text-sm font-medium text-gray-700 mb-2">
              Additional details (optional)
            </label>
            
            <textarea
              id="report-details"
              value={details}
              onChange={(e) => setDetails(e.target.value)}
              placeholder="Please provide any additional information that might help us understand the issue..."
              className="w-full min-h-[100px] p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none"
              maxLength={1000}
            />
            
            <p className="mt-1 text-xs text-gray-500 text-right">
              {details.length}/1000
            </p>
            
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Evidence (optional)
              </label>
              
              <div className="border-2 border-dashed border-gray-300 rounded-md p-4 text-center">
                <input
                  type="file"
                  id="evidence-upload"
                  onChange={handleFileSelect}
                  className="hidden"
                  accept="image/*"
                  multiple
                />
                
                <label
                  htmlFor="evidence-upload"
                  className="cursor-pointer flex flex-col items-center"
                >
                  <Upload size={24} className="text-gray-400 mb-2" />
                  <span className="text-sm text-gray-600">
                    Click to upload screenshots or evidence
                  </span>
                  <span className="text-xs text-gray-500 mt-1">
                    (Max 5MB per file, images only)
                  </span>
                </label>
              </div>
              
              {/* Evidence preview */}
              {evidence.length > 0 && (
                <div className="mt-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">
                    Uploaded evidence ({evidence.length})
                  </p>
                  
                  <div className="flex flex-wrap gap-2">
                    {evidence.map((file, index) => (
                      <div
                        key={index}
                        className="relative w-16 h-16 border border-gray-200 rounded-md overflow-hidden group"
                      >
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`Evidence ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                        <button
                          type="button"
                          onClick={() => removeEvidence(index)}
                          className="absolute top-0 right-0 p-1 bg-red-500 text-white rounded-bl-md opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X size={12} />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* Step 3: Confirmation */}
        {step === 3 && (
          <div>
            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-2">Report summary</h4>
              
              <div className="space-y-3 text-sm">
                <div>
                  <span className="font-medium">Content type:</span>{' '}
                  <span className="capitalize">{getContentTypeLabel()}</span>
                </div>
                
                <div>
                  <span className="font-medium">Reason:</span>{' '}
                  <span>
                    {reason === 'spam' && 'Spam or scam'}
                    {reason === 'harassment' && 'Harassment or bullying'}
                    {reason === 'hate_speech' && 'Hate speech or symbols'}
                    {reason === 'violence' && 'Violence or dangerous organizations'}
                    {reason === 'nudity' && 'Nudity or sexual content'}
                    {reason === 'misinformation' && 'False information'}
                    {reason === 'intellectual_property' && 'Intellectual property violation'}
                    {reason === 'other' && 'Something else'}
                  </span>
                </div>
                
                {details && (
                  <div>
                    <span className="font-medium">Additional details:</span>
                    <p className="mt-1 text-gray-600 whitespace-pre-wrap">{details}</p>
                  </div>
                )}
                
                {evidence.length > 0 && (
                  <div>
                    <span className="font-medium">Evidence:</span>{' '}
                    <span>{evidence.length} file(s) attached</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex items-start p-3 bg-blue-50 rounded-md mb-6 text-sm">
              <Info size={16} className="text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
              <p className="text-blue-800">
                Our team will review your report and take appropriate action according to our community guidelines. You may not receive a direct response, but we appreciate your help in keeping our platform safe.
              </p>
            </div>
          </div>
        )}
        
        {/* Error message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
            <AlertTriangle size={16} className="mr-2 flex-shrink-0" />
            {error}
          </div>
        )}
        
        {/* Success message */}
        {success && (
          <div className="mb-4 p-3 bg-green-50 text-green-700 rounded-md text-sm flex items-center">
            <Check size={16} className="mr-2 flex-shrink-0" />
            {success}
          </div>
        )}
        
        {/* Navigation buttons */}
        <div className="flex space-x-3">
          {step > 1 ? (
            <button
              type="button"
              onClick={goToPreviousStep}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              disabled={isLoading}
            >
              Back
            </button>
          ) : (
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              disabled={isLoading}
            >
              Cancel
            </button>
          )}
          
          {step < 3 ? (
            <button
              type="button"
              onClick={goToNextStep}
              className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
              disabled={step === 1 && !reason}
            >
              Next
            </button>
          ) : (
            <button
              type="button"
              onClick={handleSubmit}
              className="flex-1 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 flex items-center justify-center"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 size={16} className="animate-spin mr-2" />
                  Submitting...
                </>
              ) : (
                <>
                  <Flag size={16} className="mr-2" />
                  Submit Report
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
