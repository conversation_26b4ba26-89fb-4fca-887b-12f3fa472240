import { useState, useRef, useEffect } from 'react';
import { 
  Send, 
  Image, 
  Smile, 
  X, 
  Loader2, 
  AlertCircle,
  Info
} from 'lucide-react';

interface CommentEditorProps {
  onSubmit: (text: string, attachments?: File[]) => Promise<void>;
  placeholder?: string;
  maxLength?: number;
  showAttachments?: boolean;
  autoFocus?: boolean;
  initialValue?: string;
  isReply?: boolean;
  onCancel?: () => void;
}

export function CommentEditor({
  onSubmit,
  placeholder = 'Write a comment...',
  maxLength = 1000,
  showAttachments = true,
  autoFocus = false,
  initialValue = '',
  isReply = false,
  onCancel
}: CommentEditorProps) {
  const [text, setText] = useState(initialValue);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const emojiPickerRef = useRef<HTMLDivElement>(null);
  
  // Auto-resize textarea as content grows
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [text]);
  
  // Auto-focus textarea if specified
  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [autoFocus]);
  
  // Handle click outside of emoji picker
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiPickerRef.current && 
        !emojiPickerRef.current.contains(event.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;
    
    // Convert FileList to array and add to attachments
    const newFiles = Array.from(files);
    
    // Validate file types (only images)
    const invalidFiles = newFiles.filter(
      file => !file.type.startsWith('image/')
    );
    
    if (invalidFiles.length > 0) {
      setError('Only image files are allowed');
      return;
    }
    
    // Validate file size (max 5MB per file)
    const largeFiles = newFiles.filter(
      file => file.size > 5 * 1024 * 1024
    );
    
    if (largeFiles.length > 0) {
      setError('Files must be less than 5MB');
      return;
    }
    
    setAttachments([...attachments, ...newFiles]);
    setError(null);
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Remove an attachment
  const removeAttachment = (index: number) => {
    setAttachments(attachments.filter((_, i) => i !== index));
  };
  
  // Add an emoji to the text
  const addEmoji = (emoji: string) => {
    setText(prev => prev + emoji);
    setShowEmojiPicker(false);
    
    // Focus back on textarea
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!text.trim() && attachments.length === 0) {
      return;
    }
    
    if (text.length > maxLength) {
      setError(`Comment is too long (maximum ${maxLength} characters)`);
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      await onSubmit(text, attachments.length > 0 ? attachments : undefined);
      
      // Reset form
      setText('');
      setAttachments([]);
    } catch (err) {
      setError('Failed to post comment. Please try again.');
      console.error('Error posting comment:', err);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle cancel button click
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };
  
  return (
    <div className={`bg-white rounded-lg ${isReply ? 'pl-8' : ''}`}>
      <form onSubmit={handleSubmit} className="space-y-3">
        {/* Text input */}
        <div className="relative">
          <textarea
            ref={textareaRef}
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder={placeholder}
            className="w-full min-h-[60px] p-3 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
            maxLength={maxLength}
            disabled={isSubmitting}
          />
          
          <div className="absolute bottom-2 right-2 flex items-center space-x-1">
            <button
              type="button"
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
              className="p-1 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
              disabled={isSubmitting}
            >
              <Smile size={18} />
            </button>
          </div>
          
          {/* Character counter */}
          <div className="text-xs text-gray-500 text-right mt-1">
            {text.length}/{maxLength}
          </div>
          
          {/* Emoji picker */}
          {showEmojiPicker && (
            <div 
              ref={emojiPickerRef}
              className="absolute bottom-12 right-0 bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-10"
            >
              <div className="grid grid-cols-8 gap-1">
                {['😀', '😂', '😍', '🥰', '😎', '🙌', '👍', '🔥', 
                  '❤️', '🎉', '✨', '💯', '🙏', '👏', '🤔', '😊',
                  '🥺', '😭', '😅', '🤣', '😘', '💪', '🤩', '😴'].map(emoji => (
                  <button
                    key={emoji}
                    type="button"
                    onClick={() => addEmoji(emoji)}
                    className="w-8 h-8 flex items-center justify-center hover:bg-gray-100 rounded"
                  >
                    {emoji}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* Attachments */}
        {showAttachments && (
          <div>
            {/* Attachment preview */}
            {attachments.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {attachments.map((file, index) => (
                  <div 
                    key={index}
                    className="relative w-16 h-16 border border-gray-200 rounded-md overflow-hidden group"
                  >
                    <img
                      src={URL.createObjectURL(file)}
                      alt={`Attachment ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => removeAttachment(index)}
                      className="absolute top-0 right-0 p-1 bg-red-500 text-white rounded-bl-md opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X size={12} />
                    </button>
                  </div>
                ))}
              </div>
            )}
            
            {/* File input */}
            <div className="flex items-center mt-2">
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
                multiple
                disabled={isSubmitting}
              />
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
                disabled={isSubmitting}
              >
                <Image size={18} />
              </button>
            </div>
          </div>
        )}
        
        {/* Error message */}
        {error && (
          <div className="text-red-500 text-sm flex items-center">
            <AlertCircle size={14} className="mr-1" />
            {error}
          </div>
        )}
        
        {/* Submit button */}
        <div className="flex justify-between">
          {isReply && onCancel && (
            <button
              type="button"
              onClick={handleCancel}
              className="px-3 py-1 text-gray-600 hover:text-gray-800 rounded-md"
              disabled={isSubmitting}
            >
              Cancel
            </button>
          )}
          
          <div className={isReply && onCancel ? '' : 'ml-auto'}>
            <button
              type="submit"
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
              disabled={isSubmitting || (!text.trim() && attachments.length === 0)}
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Posting...
                </>
              ) : (
                <>
                  <Send size={16} className="mr-2" />
                  {isReply ? 'Reply' : 'Post'}
                </>
              )}
            </button>
          </div>
        </div>
        
        {/* Community guidelines reminder */}
        <div className="text-xs text-gray-500 flex items-start mt-1">
          <Info size={12} className="mr-1 mt-0.5 flex-shrink-0" />
          <span>
            Please follow our community guidelines. Be respectful and avoid posting offensive content.
          </span>
        </div>
      </form>
    </div>
  );
}
