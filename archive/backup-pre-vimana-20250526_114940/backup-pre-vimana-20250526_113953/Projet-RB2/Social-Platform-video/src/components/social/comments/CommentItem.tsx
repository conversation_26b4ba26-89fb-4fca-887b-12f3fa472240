import { useState } from 'react';
import { 
  Heart, 
  MessageCircle, 
  MoreHorizontal, 
  Flag, 
  Trash2, 
  <PERSON>, 
  Co<PERSON>, 
  AlertTriangle,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { CommentEditor } from './CommentEditor';
import { formatDistanceToNow } from 'date-fns';

export interface Comment {
  id: string;
  content: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  createdAt: string;
  updatedAt?: string;
  likes: number;
  liked: boolean;
  attachments?: {
    id: string;
    url: string;
    type: string;
  }[];
  replies?: Comment[];
  replyCount: number;
  isEdited: boolean;
  isDeleted: boolean;
  isAuthor: boolean;
}

interface CommentItemProps {
  comment: Comment;
  onLike: (commentId: string) => Promise<void>;
  onReply: (commentId: string, content: string, attachments?: File[]) => Promise<void>;
  onEdit: (commentId: string, content: string, attachments?: File[]) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  onReport: (commentId: string, reason: string) => Promise<void>;
  showReplies?: boolean;
  isReply?: boolean;
  depth?: number;
  maxDepth?: number;
}

export function CommentItem({
  comment,
  onLike,
  onReply,
  onEdit,
  onDelete,
  onReport,
  showReplies = true,
  isReply = false,
  depth = 0,
  maxDepth = 3
}: CommentItemProps) {
  const [isReplying, setIsReplying] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportReason, setReportReason] = useState('');
  const [showMenu, setShowMenu] = useState(false);
  const [showRepliesList, setShowRepliesList] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState(false);
  
  // Format date
  const formattedDate = formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true });
  
  // Handle like
  const handleLike = async () => {
    try {
      await onLike(comment.id);
    } catch (error) {
      console.error('Error liking comment:', error);
    }
  };
  
  // Handle reply
  const handleReply = async (content: string, attachments?: File[]) => {
    try {
      await onReply(comment.id, content, attachments);
      setIsReplying(false);
    } catch (error) {
      console.error('Error replying to comment:', error);
    }
  };
  
  // Handle edit
  const handleEdit = async (content: string, attachments?: File[]) => {
    try {
      await onEdit(comment.id, content, attachments);
      setIsEditing(false);
    } catch (error) {
      console.error('Error editing comment:', error);
    }
  };
  
  // Handle delete
  const handleDelete = async () => {
    try {
      await onDelete(comment.id);
      setConfirmDelete(false);
    } catch (error) {
      console.error('Error deleting comment:', error);
    }
  };
  
  // Handle report
  const handleReport = async () => {
    if (!reportReason.trim()) return;
    
    try {
      await onReport(comment.id, reportReason);
      setShowReportModal(false);
      setReportReason('');
    } catch (error) {
      console.error('Error reporting comment:', error);
    }
  };
  
  // Toggle menu
  const toggleMenu = () => {
    setShowMenu(!showMenu);
  };
  
  // Toggle replies
  const toggleReplies = () => {
    setShowRepliesList(!showRepliesList);
  };
  
  return (
    <div className={`py-3 ${isReply ? 'pl-6 border-l border-gray-200' : ''}`}>
      {/* Comment content */}
      <div className="flex">
        {/* User avatar */}
        <div className="mr-3 flex-shrink-0">
          {comment.userAvatar ? (
            <img
              src={comment.userAvatar}
              alt={comment.userName}
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
              {comment.userName.charAt(0).toUpperCase()}
            </div>
          )}
        </div>
        
        {/* Comment body */}
        <div className="flex-1 min-w-0">
          {/* Comment header */}
          <div className="flex items-center mb-1">
            <span className="font-medium text-gray-900">{comment.userName}</span>
            <span className="text-xs text-gray-500 ml-2">{formattedDate}</span>
            {comment.isEdited && (
              <span className="text-xs text-gray-500 ml-2">(edited)</span>
            )}
          </div>
          
          {/* Comment text */}
          {isEditing ? (
            <CommentEditor
              onSubmit={handleEdit}
              initialValue={comment.content}
              showAttachments={false}
              autoFocus={true}
              isReply={true}
              onCancel={() => setIsEditing(false)}
            />
          ) : (
            <div className="text-gray-800 whitespace-pre-wrap break-words">
              {comment.isDeleted ? (
                <span className="text-gray-400 italic">This comment has been deleted</span>
              ) : (
                comment.content
              )}
            </div>
          )}
          
          {/* Attachments */}
          {!comment.isDeleted && comment.attachments && comment.attachments.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              {comment.attachments.map((attachment) => (
                <div 
                  key={attachment.id}
                  className="relative w-24 h-24 border border-gray-200 rounded-md overflow-hidden"
                >
                  <img
                    src={attachment.url}
                    alt="Attachment"
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          )}
          
          {/* Comment actions */}
          {!comment.isDeleted && (
            <div className="flex items-center mt-2 text-sm">
              {/* Like button */}
              <button
                type="button"
                onClick={handleLike}
                className={`flex items-center mr-4 ${
                  comment.liked ? 'text-red-500' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Heart size={16} className={comment.liked ? 'fill-red-500' : ''} />
                <span className="ml-1">{comment.likes > 0 ? comment.likes : ''}</span>
              </button>
              
              {/* Reply button */}
              {showReplies && depth < maxDepth && (
                <button
                  type="button"
                  onClick={() => setIsReplying(!isReplying)}
                  className="flex items-center mr-4 text-gray-500 hover:text-gray-700"
                >
                  <MessageCircle size={16} />
                  <span className="ml-1">Reply</span>
                </button>
              )}
              
              {/* More options button */}
              <div className="relative ml-auto">
                <button
                  type="button"
                  onClick={toggleMenu}
                  className="flex items-center text-gray-500 hover:text-gray-700"
                >
                  <MoreHorizontal size={16} />
                </button>
                
                {/* Dropdown menu */}
                {showMenu && (
                  <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                    <div className="py-1">
                      {/* Copy link */}
                      <button
                        type="button"
                        onClick={() => {
                          navigator.clipboard.writeText(`${window.location.href}#comment-${comment.id}`);
                          setShowMenu(false);
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <Copy size={14} className="mr-2" />
                        Copy link
                      </button>
                      
                      {/* Edit (only for author) */}
                      {comment.isAuthor && (
                        <button
                          type="button"
                          onClick={() => {
                            setIsEditing(true);
                            setShowMenu(false);
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          <Edit size={14} className="mr-2" />
                          Edit
                        </button>
                      )}
                      
                      {/* Delete (only for author) */}
                      {comment.isAuthor && (
                        <button
                          type="button"
                          onClick={() => {
                            setConfirmDelete(true);
                            setShowMenu(false);
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                        >
                          <Trash2 size={14} className="mr-2" />
                          Delete
                        </button>
                      )}
                      
                      {/* Report (only for non-authors) */}
                      {!comment.isAuthor && (
                        <button
                          type="button"
                          onClick={() => {
                            setShowReportModal(true);
                            setShowMenu(false);
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-orange-600 hover:bg-gray-100"
                        >
                          <Flag size={14} className="mr-2" />
                          Report
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Reply form */}
      {isReplying && (
        <div className="mt-3 ml-12">
          <CommentEditor
            onSubmit={handleReply}
            placeholder="Write a reply..."
            showAttachments={true}
            autoFocus={true}
            isReply={true}
            onCancel={() => setIsReplying(false)}
          />
        </div>
      )}
      
      {/* Replies */}
      {showReplies && comment.replies && comment.replies.length > 0 && (
        <div className="mt-3 ml-12">
          {/* Toggle replies button */}
          <button
            type="button"
            onClick={toggleReplies}
            className="flex items-center text-sm text-gray-600 mb-3"
          >
            {showRepliesList ? (
              <>
                <ChevronUp size={16} className="mr-1" />
                Hide replies ({comment.replies.length})
              </>
            ) : (
              <>
                <ChevronDown size={16} className="mr-1" />
                Show replies ({comment.replies.length})
              </>
            )}
          </button>
          
          {/* Replies list */}
          {showRepliesList && (
            <div className="space-y-3">
              {comment.replies.map((reply) => (
                <CommentItem
                  key={reply.id}
                  comment={reply}
                  onLike={onLike}
                  onReply={onReply}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onReport={onReport}
                  showReplies={true}
                  isReply={true}
                  depth={depth + 1}
                  maxDepth={maxDepth}
                />
              ))}
            </div>
          )}
        </div>
      )}
      
      {/* Show reply count if there are replies but they're not shown */}
      {showReplies && !showRepliesList && comment.replyCount > 0 && !comment.replies && (
        <div className="mt-2 ml-12">
          <button
            type="button"
            className="text-sm text-blue-600 hover:text-blue-800"
            onClick={toggleReplies}
          >
            View {comment.replyCount} {comment.replyCount === 1 ? 'reply' : 'replies'}
          </button>
        </div>
      )}
      
      {/* Delete confirmation modal */}
      {confirmDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium mb-4">Delete Comment</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this comment? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setConfirmDelete(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Report modal */}
      {showReportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium mb-4">Report Comment</h3>
            <p className="text-gray-600 mb-4">
              Please tell us why you're reporting this comment.
            </p>
            <div className="mb-4">
              <select
                value={reportReason}
                onChange={(e) => setReportReason(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select a reason</option>
                <option value="harassment">Harassment or bullying</option>
                <option value="hate_speech">Hate speech</option>
                <option value="misinformation">Misinformation</option>
                <option value="spam">Spam</option>
                <option value="violence">Violence</option>
                <option value="other">Other</option>
              </select>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowReportModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleReport}
                disabled={!reportReason}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50"
              >
                Report
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
