import { useState, useEffect } from 'react';
import { 
  Globe, 
  Instagram, 
  Facebook, 
  Twitter, 
  Linkedin, 
  Youtube, 
  Link as LinkIcon, 
  Plus, 
  Trash2, 
  Check, 
  X, 
  Edit, 
  Loader2, 
  AlertCircle,
  ExternalLink
} from 'lucide-react';

export interface SocialLink {
  id: string;
  platform: string;
  url: string;
  isVerified?: boolean;
}

interface SocialLinksEditorProps {
  links: SocialLink[];
  onSave: (links: SocialLink[]) => Promise<void>;
  maxLinks?: number;
  readOnly?: boolean;
  className?: string;
}

// Supported platforms with their icons and validation patterns
const platforms = [
  { 
    id: 'website', 
    name: 'Website', 
    icon: <Globe size={18} />,
    placeholder: 'https://yourwebsite.com',
    pattern: /^https?:\/\/.+\..+/
  },
  { 
    id: 'instagram', 
    name: 'Instagram', 
    icon: <Instagram size={18} />,
    placeholder: 'https://instagram.com/username',
    pattern: /^https?:\/\/(www\.)?instagram\.com\/.+/
  },
  { 
    id: 'facebook', 
    name: 'Facebook', 
    icon: <Facebook size={18} />,
    placeholder: 'https://facebook.com/username',
    pattern: /^https?:\/\/(www\.)?facebook\.com\/.+/
  },
  { 
    id: 'twitter', 
    name: 'Twitter', 
    icon: <Twitter size={18} />,
    placeholder: 'https://twitter.com/username',
    pattern: /^https?:\/\/(www\.)?twitter\.com\/.+/
  },
  { 
    id: 'linkedin', 
    name: 'LinkedIn', 
    icon: <Linkedin size={18} />,
    placeholder: 'https://linkedin.com/in/username',
    pattern: /^https?:\/\/(www\.)?linkedin\.com\/.+/
  },
  { 
    id: 'youtube', 
    name: 'YouTube', 
    icon: <Youtube size={18} />,
    placeholder: 'https://youtube.com/c/channelname',
    pattern: /^https?:\/\/(www\.)?youtube\.com\/.+/
  },
  { 
    id: 'other', 
    name: 'Other', 
    icon: <LinkIcon size={18} />,
    placeholder: 'https://example.com',
    pattern: /^https?:\/\/.+\..+/
  }
];

export function SocialLinksEditor({
  links,
  onSave,
  maxLinks = 7,
  readOnly = false,
  className = ''
}: SocialLinksEditorProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedLinks, setEditedLinks] = useState<SocialLink[]>([...links]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  
  // Update edited links when props change
  useEffect(() => {
    if (!isEditing) {
      setEditedLinks([...links]);
    }
  }, [links, isEditing]);
  
  // Start editing
  const handleEdit = () => {
    if (readOnly) return;
    setIsEditing(true);
    setError(null);
    setValidationErrors({});
  };
  
  // Cancel editing
  const handleCancel = () => {
    setIsEditing(false);
    setEditedLinks([...links]);
    setError(null);
    setValidationErrors({});
  };
  
  // Add new link
  const handleAddLink = () => {
    if (editedLinks.length >= maxLinks) {
      setError(`You can add up to ${maxLinks} social links`);
      return;
    }
    
    const newLink: SocialLink = {
      id: `link-${Date.now()}`,
      platform: 'website',
      url: ''
    };
    
    setEditedLinks([...editedLinks, newLink]);
  };
  
  // Remove link
  const handleRemoveLink = (id: string) => {
    setEditedLinks(editedLinks.filter(link => link.id !== id));
    
    // Clear validation error for removed link
    if (validationErrors[id]) {
      const newValidationErrors = { ...validationErrors };
      delete newValidationErrors[id];
      setValidationErrors(newValidationErrors);
    }
  };
  
  // Update link platform
  const handlePlatformChange = (id: string, platform: string) => {
    setEditedLinks(editedLinks.map(link => 
      link.id === id ? { ...link, platform } : link
    ));
    
    // Clear validation error when platform changes
    if (validationErrors[id]) {
      const newValidationErrors = { ...validationErrors };
      delete newValidationErrors[id];
      setValidationErrors(newValidationErrors);
    }
  };
  
  // Update link URL
  const handleUrlChange = (id: string, url: string) => {
    setEditedLinks(editedLinks.map(link => 
      link.id === id ? { ...link, url } : link
    ));
    
    // Clear validation error when URL changes
    if (validationErrors[id]) {
      const newValidationErrors = { ...validationErrors };
      delete newValidationErrors[id];
      setValidationErrors(newValidationErrors);
    }
  };
  
  // Validate links
  const validateLinks = () => {
    const errors: Record<string, string> = {};
    let isValid = true;
    
    editedLinks.forEach(link => {
      // Skip empty URLs (they'll be filtered out)
      if (!link.url.trim()) return;
      
      const platform = platforms.find(p => p.id === link.platform);
      if (!platform) {
        errors[link.id] = 'Invalid platform';
        isValid = false;
        return;
      }
      
      if (!platform.pattern.test(link.url)) {
        errors[link.id] = `Invalid ${platform.name} URL format`;
        isValid = false;
      }
    });
    
    setValidationErrors(errors);
    return isValid;
  };
  
  // Save links
  const handleSave = async () => {
    // Validate links
    if (!validateLinks()) {
      return;
    }
    
    // Filter out links with empty URLs
    const linksToSave = editedLinks.filter(link => link.url.trim() !== '');
    
    setIsLoading(true);
    setError(null);
    
    try {
      await onSave(linksToSave);
      setIsEditing(false);
    } catch (err) {
      setError('Failed to save social links. Please try again.');
      console.error('Error saving social links:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Get platform icon
  const getPlatformIcon = (platformId: string) => {
    const platform = platforms.find(p => p.id === platformId);
    return platform?.icon || <LinkIcon size={18} />;
  };
  
  // Get platform name
  const getPlatformName = (platformId: string) => {
    const platform = platforms.find(p => p.id === platformId);
    return platform?.name || 'Other';
  };
  
  // Get platform placeholder
  const getPlatformPlaceholder = (platformId: string) => {
    const platform = platforms.find(p => p.id === platformId);
    return platform?.placeholder || 'https://example.com';
  };
  
  // Format URL for display
  const formatUrl = (url: string) => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname + (urlObj.pathname !== '/' ? urlObj.pathname : '');
    } catch (e) {
      return url;
    }
  };
  
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="font-semibold">Social Links</h3>
        
        {!isEditing && !readOnly && (
          <button
            type="button"
            onClick={handleEdit}
            className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100"
            aria-label="Edit social links"
          >
            <Edit size={16} />
          </button>
        )}
      </div>
      
      <div className="p-4">
        {isEditing ? (
          <div className="space-y-4">
            {/* Links editor */}
            <div className="space-y-3">
              {editedLinks.map(link => (
                <div key={link.id} className="flex items-start space-x-2">
                  {/* Platform selector */}
                  <select
                    value={link.platform}
                    onChange={(e) => handlePlatformChange(link.id, e.target.value)}
                    className="p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    disabled={isLoading}
                  >
                    {platforms.map(platform => (
                      <option key={platform.id} value={platform.id}>
                        {platform.name}
                      </option>
                    ))}
                  </select>
                  
                  {/* URL input */}
                  <div className="flex-1">
                    <input
                      type="text"
                      value={link.url}
                      onChange={(e) => handleUrlChange(link.id, e.target.value)}
                      placeholder={getPlatformPlaceholder(link.platform)}
                      className={`w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                        validationErrors[link.id] ? 'border-red-300' : 'border-gray-300'
                      }`}
                      disabled={isLoading}
                    />
                    
                    {validationErrors[link.id] && (
                      <p className="text-xs text-red-500 mt-1">
                        {validationErrors[link.id]}
                      </p>
                    )}
                  </div>
                  
                  {/* Remove button */}
                  <button
                    type="button"
                    onClick={() => handleRemoveLink(link.id)}
                    className="p-2 text-gray-500 hover:text-red-500 hover:bg-gray-100 rounded-md"
                    disabled={isLoading}
                    aria-label="Remove link"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              ))}
            </div>
            
            {/* Add link button */}
            <div>
              <button
                type="button"
                onClick={handleAddLink}
                className="flex items-center text-blue-600 hover:text-blue-800"
                disabled={isLoading || editedLinks.length >= maxLinks}
              >
                <Plus size={16} className="mr-1" />
                Add Link
              </button>
              
              {editedLinks.length >= maxLinks && (
                <p className="text-xs text-gray-500 mt-1">
                  You've reached the maximum of {maxLinks} social links.
                </p>
              )}
            </div>
            
            {/* Error message */}
            {error && (
              <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
                <AlertCircle size={16} className="mr-2 flex-shrink-0" />
                {error}
              </div>
            )}
            
            {/* Action buttons */}
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={handleCancel}
                className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                disabled={isLoading}
              >
                Cancel
              </button>
              
              <button
                type="button"
                onClick={handleSave}
                className="px-3 py-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 size={16} className="mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Check size={16} className="mr-2" />
                    Save
                  </>
                )}
              </button>
            </div>
          </div>
        ) : (
          <div>
            {links.length === 0 ? (
              <p className="text-gray-400 italic">
                {readOnly ? 'No social links available' : 'Add your social media links to connect with others'}
              </p>
            ) : (
              <div className="space-y-2">
                {links.map(link => (
                  <a
                    key={link.id}
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-2 hover:bg-gray-50 rounded-md group"
                  >
                    <div className="mr-3 text-gray-500">
                      {getPlatformIcon(link.platform)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900">
                        {getPlatformName(link.platform)}
                        {link.isVerified && (
                          <span className="ml-1 text-blue-500 text-xs">(Verified)</span>
                        )}
                      </div>
                      <div className="text-sm text-gray-500 truncate">
                        {formatUrl(link.url)}
                      </div>
                    </div>
                    
                    <div className="text-gray-400 opacity-0 group-hover:opacity-100">
                      <ExternalLink size={14} />
                    </div>
                  </a>
                ))}
              </div>
            )}
            
            {!readOnly && (
              <button
                type="button"
                onClick={handleEdit}
                className="mt-4 text-sm text-blue-600 hover:text-blue-800 flex items-center"
              >
                <Edit size={14} className="mr-1" />
                {links.length === 0 ? 'Add Social Links' : 'Edit Social Links'}
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
