import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Camera, X, Save, Loader2, Link as LinkIcon } from 'lucide-react';
import { Avatar } from '../ui/avatar';
import { ImageCropper } from './ImageCropper';

const profileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(50, 'Name is too long'),
  username: z.string().min(3, 'Username must be at least 3 characters').max(30, 'Username is too long')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),
  bio: z.string().max(160, 'Bio cannot exceed 160 characters').optional(),
  website: z.string().url('Please enter a valid URL').or(z.literal('')).optional(),
  location: z.string().max(100, 'Location is too long').optional(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

interface SocialLink {
  platform: string;
  url: string;
}

interface ProfileEditorProps {
  initialData: ProfileFormData & {
    avatar: string;
    coverPhoto: string;
    socialLinks: SocialLink[];
  };
  onSave: (data: ProfileFormData & { avatar: string; coverPhoto: string; socialLinks: SocialLink[] }) => Promise<void>;
  onCancel: () => void;
}

export function ProfileEditor({ initialData, onSave, onCancel }: ProfileEditorProps) {
  const [avatar, setAvatar] = useState(initialData.avatar);
  const [coverPhoto, setCoverPhoto] = useState(initialData.coverPhoto);
  const [socialLinks, setSocialLinks] = useState<SocialLink[]>(initialData.socialLinks);
  const [showAvatarCropper, setShowAvatarCropper] = useState(false);
  const [showCoverCropper, setShowCoverCropper] = useState(false);
  const [newAvatarFile, setNewAvatarFile] = useState<File | null>(null);
  const [newCoverFile, setNewCoverFile] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newSocialPlatform, setNewSocialPlatform] = useState('');
  const [newSocialUrl, setNewSocialUrl] = useState('');
  
  const { 
    register, 
    handleSubmit, 
    watch,
    formState: { errors } 
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: initialData.name,
      username: initialData.username,
      bio: initialData.bio || '',
      website: initialData.website || '',
      location: initialData.location || '',
    }
  });
  
  const bioValue = watch('bio') || '';
  
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setNewAvatarFile(e.target.files[0]);
      setShowAvatarCropper(true);
    }
  };
  
  const handleCoverChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setNewCoverFile(e.target.files[0]);
      setShowCoverCropper(true);
    }
  };
  
  const handleAvatarCrop = (croppedImage: string) => {
    setAvatar(croppedImage);
    setShowAvatarCropper(false);
  };
  
  const handleCoverCrop = (croppedImage: string) => {
    setCoverPhoto(croppedImage);
    setShowCoverCropper(false);
  };
  
  const addSocialLink = () => {
    if (newSocialPlatform && newSocialUrl) {
      setSocialLinks([...socialLinks, { platform: newSocialPlatform, url: newSocialUrl }]);
      setNewSocialPlatform('');
      setNewSocialUrl('');
    }
  };
  
  const removeSocialLink = (index: number) => {
    setSocialLinks(socialLinks.filter((_, i) => i !== index));
  };
  
  const onSubmit = async (data: ProfileFormData) => {
    setIsSubmitting(true);
    try {
      await onSave({
        ...data,
        avatar,
        coverPhoto,
        socialLinks,
      });
    } catch (error) {
      console.error('Failed to save profile:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-2xl font-bold mb-6">Edit Profile</h2>
      
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Cover Photo */}
        <div className="relative mb-6">
          <div 
            className="h-48 rounded-lg bg-gray-200 bg-cover bg-center"
            style={{ backgroundImage: `url(${coverPhoto})` }}
          >
            <div className="absolute bottom-4 right-4">
              <label className="cursor-pointer bg-white rounded-full p-2 shadow-md hover:bg-gray-100">
                <Camera size={20} />
                <input 
                  type="file" 
                  accept="image/*" 
                  className="hidden" 
                  onChange={handleCoverChange}
                />
              </label>
            </div>
          </div>
        </div>
        
        {/* Avatar */}
        <div className="relative mb-6 flex justify-center">
          <div className="absolute -top-16">
            <div className="relative">
              <Avatar 
                src={avatar} 
                alt="Profile" 
                className="w-32 h-32 border-4 border-white shadow-md"
              />
              <label className="absolute bottom-0 right-0 cursor-pointer bg-white rounded-full p-2 shadow-md hover:bg-gray-100">
                <Camera size={16} />
                <input 
                  type="file" 
                  accept="image/*" 
                  className="hidden" 
                  onChange={handleAvatarChange}
                />
              </label>
            </div>
          </div>
        </div>
        
        {/* Spacer for avatar */}
        <div className="h-16"></div>
        
        {/* Basic Info */}
        <div className="space-y-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              {...register('name')}
              className={`w-full px-3 py-2 border ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              } rounded-md focus:outline-none focus:ring-2 focus:ring-green-500`}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
            <div className="flex">
              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
                @
              </span>
              <input
                {...register('username')}
                className={`flex-1 px-3 py-2 border ${
                  errors.username ? 'border-red-500' : 'border-gray-300'
                } rounded-r-md focus:outline-none focus:ring-2 focus:ring-green-500`}
              />
            </div>
            {errors.username && (
              <p className="mt-1 text-sm text-red-500">{errors.username.message}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Bio</label>
            <textarea
              {...register('bio')}
              rows={3}
              className={`w-full px-3 py-2 border ${
                errors.bio ? 'border-red-500' : 'border-gray-300'
              } rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 resize-none`}
            />
            <div className="mt-1 flex justify-between items-center">
              <p className={`text-xs ${bioValue.length > 160 ? 'text-red-500' : 'text-gray-500'}`}>
                {bioValue.length}/160
              </p>
              {errors.bio && (
                <p className="text-sm text-red-500">{errors.bio.message}</p>
              )}
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Website</label>
            <input
              {...register('website')}
              type="url"
              placeholder="https://example.com"
              className={`w-full px-3 py-2 border ${
                errors.website ? 'border-red-500' : 'border-gray-300'
              } rounded-md focus:outline-none focus:ring-2 focus:ring-green-500`}
            />
            {errors.website && (
              <p className="mt-1 text-sm text-red-500">{errors.website.message}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
            <input
              {...register('location')}
              className={`w-full px-3 py-2 border ${
                errors.location ? 'border-red-500' : 'border-gray-300'
              } rounded-md focus:outline-none focus:ring-2 focus:ring-green-500`}
            />
            {errors.location && (
              <p className="mt-1 text-sm text-red-500">{errors.location.message}</p>
            )}
          </div>
        </div>
        
        {/* Social Links */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">Social Links</h3>
          
          <div className="space-y-2 mb-4">
            {socialLinks.map((link, index) => (
              <div key={index} className="flex items-center">
                <div className="flex-1 flex items-center border border-gray-300 rounded-md overflow-hidden">
                  <span className="bg-gray-100 px-3 py-2 text-gray-700 border-r border-gray-300">
                    {link.platform}
                  </span>
                  <span className="px-3 py-2 flex-1 truncate">{link.url}</span>
                </div>
                <button
                  type="button"
                  onClick={() => removeSocialLink(index)}
                  className="ml-2 text-red-500 hover:text-red-700"
                >
                  <X size={18} />
                </button>
              </div>
            ))}
          </div>
          
          <div className="flex items-center space-x-2">
            <select
              value={newSocialPlatform}
              onChange={(e) => setNewSocialPlatform(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="">Select platform</option>
              <option value="Twitter">Twitter</option>
              <option value="Instagram">Instagram</option>
              <option value="Facebook">Facebook</option>
              <option value="LinkedIn">LinkedIn</option>
              <option value="YouTube">YouTube</option>
              <option value="TikTok">TikTok</option>
            </select>
            
            <div className="flex-1 flex items-center">
              <span className="bg-gray-100 p-2 border border-gray-300 rounded-l-md">
                <LinkIcon size={18} className="text-gray-500" />
              </span>
              <input
                type="url"
                value={newSocialUrl}
                onChange={(e) => setNewSocialUrl(e.target.value)}
                placeholder="https://example.com/profile"
                className="flex-1 px-3 py-2 border-y border-r border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-green-500"
              />
            </div>
            
            <button
              type="button"
              onClick={addSocialLink}
              disabled={!newSocialPlatform || !newSocialUrl}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              Add
            </button>
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex justify-end space-x-4 mt-8">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:bg-green-300 disabled:cursor-not-allowed flex items-center"
          >
            {isSubmitting ? (
              <>
                <Loader2 size={18} className="animate-spin mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save size={18} className="mr-2" />
                Save Profile
              </>
            )}
          </button>
        </div>
      </form>
      
      {/* Image Croppers */}
      {showAvatarCropper && newAvatarFile && (
        <ImageCropper
          image={URL.createObjectURL(newAvatarFile)}
          onCrop={handleAvatarCrop}
          onCancel={() => setShowAvatarCropper(false)}
          aspectRatio={1}
          circular
        />
      )}
      
      {showCoverCropper && newCoverFile && (
        <ImageCropper
          image={URL.createObjectURL(newCoverFile)}
          onCrop={handleCoverCrop}
          onCancel={() => setShowCoverCropper(false)}
          aspectRatio={3}
        />
      )}
    </div>
  );
}
