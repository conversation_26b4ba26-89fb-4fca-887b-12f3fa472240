import { useState, useRef } from 'react';
import { VideoPlayer } from './video-player';
import { PostActions } from './post/post-actions';
import { PostInfo } from './post/post-info';
// import type { Post } from '@/types'; // Temporarily comment out to use 'any'
// import { SearchResult } from '@/api/searchApi'; // Temporarily comment out
import { usePostsStore } from '@/store/posts';

interface PostCardProps {
  post: any; // Changed type to any temporarily
  compact?: boolean;
}

export function PostCard({ post, compact }: PostCardProps) {
  const toggleLike = usePostsStore((state) => state.toggleLike);
  const [showControls, setShowControls] = useState(true);
  const doubleTapRef = useRef<NodeJS.Timeout>();

  const handleDoubleTap = () => {
    if (doubleTapRef.current) {
      clearTimeout(doubleTapRef.current);
      toggleLike(post.id);
      setShowControls(false);
      setTimeout(() => setShowControls(true), 1000)
    } else {
      doubleTapRef.current = setTimeout(() => {
        doubleTapRef.current = undefined
      }, 300);
    }
  }

  return (
    <div className = "snap-start h-screen relative bg-black" onClick={handleDoubleTap
}>
      <VideoPlayer
        src = {post.videoUrl
}
        className = "h-screen"
        showControls={showControls
}
        poster = {`https://api.thumbalizr.com/?url=${encodeURIComponent(post.videoUrl)
}&width=720`}
      />

      {/* Overlay gradient for better text visibility */}
      <div className = "absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/60 pointer-events-none" />

      <PostInfo
        user={post.user
}
        caption = {post.caption
}
        hashtags = {post.hashtags || []
}
      />

      <PostActions
        postId = {post.id
}
        likes = {post.likes
}
        comments = {post.comments
}
        hasLiked = {post.hasLiked
}
        onLike = {toggleLike
}
      />
    </div>
  );
}