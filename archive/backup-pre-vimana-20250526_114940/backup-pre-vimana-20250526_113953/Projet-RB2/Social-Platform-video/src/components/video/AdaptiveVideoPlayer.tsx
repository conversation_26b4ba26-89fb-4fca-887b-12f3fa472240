import { useState, useEffect, useRef } from 'react';
import { 
  VideoQuality, 
  VideoFormat, 
  VideoManifest, 
  createAdaptivePlayerConfig,
  setupAdaptiveQualitySwitching,
  determineOptimalQuality
} from '../../services/video-optimization/adaptive-streaming';
import { getBandwidthEstimate } from '../../services/video-optimization/bandwidth-detection';
import { Settings, ChevronUp, ChevronDown, Play, Pause, Volume2, VolumeX, Maximize, Minimize, SkipForward, SkipBack } from 'lucide-react';
import { Slider } from '../ui/slider';
import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';

interface AdaptiveVideoPlayerProps {
  manifest: VideoManifest;
  autoPlay?: boolean;
  muted?: boolean;
  loop?: boolean;
  poster?: string;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onQualityChange?: (quality: VideoQuality) => void;
  className?: string;
}

export function AdaptiveVideoPlayer({
  manifest,
  autoPlay = false,
  muted = false,
  loop = false,
  poster,
  onPlay,
  onPause,
  onEnded,
  onTimeUpdate,
  onQualityChange,
  className = '',
}: AdaptiveVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const controlsTimeoutRef = useRef<number | null>(null);
  
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isMuted, setIsMuted] = useState(muted);
  const [volume, setVolume] = useState(1);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [buffered, setBuffered] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [selectedQuality, setSelectedQuality] = useState<VideoQuality>(manifest.defaultQuality);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Initialize player
  useEffect(() => {
    if (!videoRef.current) return;
    
    const video = videoRef.current;
    
    // Set up event listeners
    const onLoadedMetadata = () => {
      setDuration(video.duration);
      setIsLoading(false);
    };
    
    const onTimeUpdateHandler = () => {
      setCurrentTime(video.currentTime);
      onTimeUpdate?.(video.currentTime, video.duration);
    };
    
    const onProgress = () => {
      if (video.buffered.length > 0) {
        setBuffered(video.buffered.end(video.buffered.length - 1));
      }
    };
    
    const onPlayHandler = () => {
      setIsPlaying(true);
      onPlay?.();
    };
    
    const onPauseHandler = () => {
      setIsPlaying(false);
      onPause?.();
    };
    
    const onEndedHandler = () => {
      setIsPlaying(false);
      onEnded?.();
    };
    
    const onErrorHandler = () => {
      setError('Error loading video');
      setIsLoading(false);
    };
    
    // Add event listeners
    video.addEventListener('loadedmetadata', onLoadedMetadata);
    video.addEventListener('timeupdate', onTimeUpdateHandler);
    video.addEventListener('progress', onProgress);
    video.addEventListener('play', onPlayHandler);
    video.addEventListener('pause', onPauseHandler);
    video.addEventListener('ended', onEndedHandler);
    video.addEventListener('error', onErrorHandler);
    
    // Set up adaptive streaming
    const cleanupAdaptiveStreaming = setupAdaptiveQualitySwitching(
      video,
      manifest,
      createAdaptivePlayerConfig(manifest, {
        initialQuality: selectedQuality,
      })
    );
    
    // Set initial source
    updateVideoSource(selectedQuality);
    
    // Clean up
    return () => {
      video.removeEventListener('loadedmetadata', onLoadedMetadata);
      video.removeEventListener('timeupdate', onTimeUpdateHandler);
      video.removeEventListener('progress', onProgress);
      video.removeEventListener('play', onPlayHandler);
      video.removeEventListener('pause', onPauseHandler);
      video.removeEventListener('ended', onEndedHandler);
      video.removeEventListener('error', onErrorHandler);
      
      cleanupAdaptiveStreaming();
    };
  }, [manifest]);
  
  // Update video source when quality changes
  useEffect(() => {
    updateVideoSource(selectedQuality);
    onQualityChange?.(selectedQuality);
  }, [selectedQuality]);
  
  // Handle auto-hide controls
  useEffect(() => {
    if (!showControls) return;
    
    const hideControls = () => {
      setShowControls(false);
    };
    
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    
    if (isPlaying) {
      controlsTimeoutRef.current = window.setTimeout(hideControls, 3000);
    }
    
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls, isPlaying]);
  
  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);
  
  // Update video source
  const updateVideoSource = (quality: VideoQuality) => {
    if (!videoRef.current) return;
    
    const video = videoRef.current;
    const currentTime = video.currentTime;
    const wasPlaying = !video.paused;
    
    // Find the stream with the selected quality
    let stream;
    
    if (quality === VideoQuality.AUTO) {
      // For AUTO, determine the optimal quality based on bandwidth
      const bandwidth = getBandwidthEstimate();
      const optimalQuality = determineOptimalQuality(
        manifest.streams,
        bandwidth
      );
      stream = manifest.streams.find((s) => s.quality === optimalQuality);
    } else {
      stream = manifest.streams.find((s) => s.quality === quality);
    }
    
    // If the selected quality is not available, use the default
    if (!stream) {
      stream = manifest.streams.find((s) => s.quality === manifest.defaultQuality);
    }
    
    if (!stream) return;
    
    // Update the video source
    video.src = stream.url;
    
    // Restore playback state
    video.currentTime = currentTime;
    
    if (wasPlaying) {
      video.play().catch(() => {
        // Autoplay might be blocked
        setIsPlaying(false);
      });
    }
  };
  
  // Play/pause
  const togglePlay = () => {
    if (!videoRef.current) return;
    
    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play().catch(() => {
        // Autoplay might be blocked
        setIsPlaying(false);
      });
    }
  };
  
  // Mute/unmute
  const toggleMute = () => {
    if (!videoRef.current) return;
    
    const newMutedState = !isMuted;
    videoRef.current.muted = newMutedState;
    setIsMuted(newMutedState);
  };
  
  // Set volume
  const handleVolumeChange = (value: number[]) => {
    if (!videoRef.current) return;
    
    const newVolume = value[0];
    videoRef.current.volume = newVolume;
    setVolume(newVolume);
    
    if (newVolume === 0) {
      videoRef.current.muted = true;
      setIsMuted(true);
    } else if (isMuted) {
      videoRef.current.muted = false;
      setIsMuted(false);
    }
  };
  
  // Seek
  const handleSeek = (value: number[]) => {
    if (!videoRef.current) return;
    
    const newTime = value[0];
    videoRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };
  
  // Toggle fullscreen
  const toggleFullscreen = () => {
    if (!containerRef.current) return;
    
    if (!isFullscreen) {
      containerRef.current.requestFullscreen().catch((err) => {
        console.error('Error attempting to enable fullscreen:', err);
      });
    } else {
      document.exitFullscreen().catch((err) => {
        console.error('Error attempting to exit fullscreen:', err);
      });
    }
  };
  
  // Format time (seconds to MM:SS)
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  // Skip forward/backward
  const skip = (seconds: number) => {
    if (!videoRef.current) return;
    
    const newTime = Math.min(
      Math.max(0, videoRef.current.currentTime + seconds),
      videoRef.current.duration
    );
    
    videoRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };
  
  // Show controls when mouse moves
  const handleMouseMove = () => {
    setShowControls(true);
  };
  
  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden bg-black ${className}`}
      onMouseMove={handleMouseMove}
    >
      {/* Video element */}
      <video
        ref={videoRef}
        className="w-full h-full"
        poster={poster}
        autoPlay={autoPlay}
        muted={muted}
        loop={loop}
        playsInline
      />
      
      {/* Loading indicator */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white"></div>
        </div>
      )}
      
      {/* Error message */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-red-500 text-white p-4 rounded-md">
            {error}
          </div>
        </div>
      )}
      
      {/* Controls */}
      {showControls && (
        <div className="absolute inset-0 flex flex-col justify-end bg-gradient-to-t from-black/70 to-transparent">
          {/* Progress bar */}
          <div className="px-4 pb-1">
            <div className="relative h-1 bg-gray-600 rounded-full">
              {/* Buffered */}
              <div
                className="absolute h-full bg-gray-400 rounded-full"
                style={{ width: `${(buffered / duration) * 100}%` }}
              ></div>
              
              {/* Progress */}
              <Slider
                value={[currentTime]}
                min={0}
                max={duration}
                step={0.01}
                onValueChange={handleSeek}
                className="absolute inset-0"
              />
            </div>
          </div>
          
          {/* Control buttons */}
          <div className="px-4 py-2 flex items-center">
            {/* Play/Pause */}
            <Button
              variant="ghost"
              size="icon"
              onClick={togglePlay}
              className="text-white hover:bg-white/20"
            >
              {isPlaying ? <Pause size={20} /> : <Play size={20} />}
            </Button>
            
            {/* Skip backward */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => skip(-10)}
              className="text-white hover:bg-white/20"
            >
              <SkipBack size={20} />
            </Button>
            
            {/* Skip forward */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => skip(10)}
              className="text-white hover:bg-white/20"
            >
              <SkipForward size={20} />
            </Button>
            
            {/* Volume */}
            <div className="relative flex items-center ml-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleMute}
                className="text-white hover:bg-white/20"
              >
                {isMuted || volume === 0 ? <VolumeX size={20} /> : <Volume2 size={20} />}
              </Button>
              
              <div className="w-24 hidden sm:block">
                <Slider
                  value={[isMuted ? 0 : volume]}
                  min={0}
                  max={1}
                  step={0.01}
                  onValueChange={handleVolumeChange}
                />
              </div>
            </div>
            
            {/* Time */}
            <div className="ml-auto mr-2 text-white text-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
            
            {/* Quality settings */}
            <DropdownMenu open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/20"
                >
                  <Settings size={20} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Quality</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setSelectedQuality(VideoQuality.AUTO)}
                  className={selectedQuality === VideoQuality.AUTO ? 'bg-gray-100' : ''}
                >
                  Auto
                </DropdownMenuItem>
                {manifest.streams
                  .map((stream) => stream.quality)
                  .filter((quality) => quality !== VideoQuality.AUTO)
                  .sort((a, b) => {
                    // Sort in descending order (highest quality first)
                    const aValue = parseInt(a.replace('p', ''));
                    const bValue = parseInt(b.replace('p', ''));
                    return bValue - aValue;
                  })
                  .map((quality) => (
                    <DropdownMenuItem
                      key={quality}
                      onClick={() => setSelectedQuality(quality as VideoQuality)}
                      className={selectedQuality === quality ? 'bg-gray-100' : ''}
                    >
                      {quality}
                    </DropdownMenuItem>
                  ))}
              </DropdownMenuContent>
            </DropdownMenu>
            
            {/* Fullscreen */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleFullscreen}
              className="text-white hover:bg-white/20"
            >
              {isFullscreen ? <Minimize size={20} /> : <Maximize size={20} />}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
