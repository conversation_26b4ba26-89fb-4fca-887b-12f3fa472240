import { useState, useEffect } from 'react';
import { 
  Save, 
  Clock, 
  Calendar, 
  Trash2, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  ChevronDown,
  ChevronUp,
  Edit,
  Eye
} from 'lucide-react';

interface Draft {
  id: string;
  title: string;
  thumbnailUrl: string;
  lastSaved: Date;
  scheduledPublishDate?: Date;
  videoId: string;
  metadata: Record<string, any>;
}

interface DraftSavingProps {
  videoId: string;
  videoTitle: string;
  thumbnailUrl: string;
  metadata: Record<string, any>;
  onSaveDraft: (draft: Omit<Draft, 'id' | 'lastSaved'>) => Promise<Draft>;
  onLoadDraft: (draftId: string) => Promise<void>;
  onDeleteDraft: (draftId: string) => Promise<void>;
  onSchedulePublish: (date: Date) => Promise<void>;
  existingDrafts?: Draft[];
}

export function DraftSaving({
  videoId,
  videoTitle,
  thumbnailUrl,
  metadata,
  onSaveDraft,
  onLoadDraft,
  onDeleteDraft,
  onSchedulePublish,
  existingDrafts = []
}: DraftSavingProps) {
  const [drafts, setDrafts] = useState<Draft[]>(existingDrafts);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [showDrafts, setShowDrafts] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [scheduledDate, setScheduledDate] = useState<Date | null>(null);
  const [showScheduler, setShowScheduler] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [autoSaveInterval, setAutoSaveInterval] = useState<NodeJS.Timeout | null>(null);
  
  // Initialize auto-save
  useEffect(() => {
    if (autoSaveEnabled) {
      const interval = setInterval(() => {
        handleSaveDraft();
      }, 5 * 60 * 1000); // Auto-save every 5 minutes
      
      setAutoSaveInterval(interval);
      
      return () => {
        if (interval) clearInterval(interval);
      };
    } else if (autoSaveInterval) {
      clearInterval(autoSaveInterval);
      setAutoSaveInterval(null);
    }
  }, [autoSaveEnabled, videoId, metadata]);
  
  // Save draft
  const handleSaveDraft = async () => {
    setIsSaving(true);
    setError(null);
    setSuccess(null);
    
    try {
      const draftData = {
        videoId,
        title: videoTitle,
        thumbnailUrl,
        metadata,
      };
      
      const savedDraft = await onSaveDraft(draftData);
      
      // Update drafts list
      setDrafts(prevDrafts => {
        const existingIndex = prevDrafts.findIndex(d => d.id === savedDraft.id);
        
        if (existingIndex >= 0) {
          // Update existing draft
          const updatedDrafts = [...prevDrafts];
          updatedDrafts[existingIndex] = savedDraft;
          return updatedDrafts;
        } else {
          // Add new draft
          return [...prevDrafts, savedDraft];
        }
      });
      
      setLastSaved(new Date());
      setSuccess('Draft saved successfully');
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError('Failed to save draft');
    } finally {
      setIsSaving(false);
    }
  };
  
  // Load draft
  const handleLoadDraft = async (draftId: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      await onLoadDraft(draftId);
      setShowDrafts(false);
    } catch (err) {
      setError('Failed to load draft');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Delete draft
  const handleDeleteDraft = async (draftId: string) => {
    setIsDeleting(true);
    setError(null);
    
    try {
      await onDeleteDraft(draftId);
      
      // Update drafts list
      setDrafts(prevDrafts => prevDrafts.filter(d => d.id !== draftId));
    } catch (err) {
      setError('Failed to delete draft');
    } finally {
      setIsDeleting(false);
    }
  };
  
  // Schedule publish
  const handleSchedulePublish = async () => {
    if (!scheduledDate) return;
    
    try {
      await onSchedulePublish(scheduledDate);
      setSuccess('Publishing scheduled successfully');
      setShowScheduler(false);
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError('Failed to schedule publishing');
    }
  };
  
  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };
  
  // Get minimum date for scheduling (now + 5 minutes)
  const getMinScheduleDate = () => {
    const now = new Date();
    now.setMinutes(now.getMinutes() + 5);
    return now.toISOString().slice(0, 16); // Format for datetime-local input
  };
  
  return (
    <div className="w-full bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold">Save & Publish</h3>
        <p className="text-sm text-gray-500">
          Save your progress or schedule publishing
        </p>
      </div>
      
      <div className="p-4">
        {/* Auto-save toggle */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="auto-save"
              checked={autoSaveEnabled}
              onChange={() => setAutoSaveEnabled(!autoSaveEnabled)}
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            />
            <label htmlFor="auto-save" className="ml-2 block text-sm text-gray-700">
              Auto-save every 5 minutes
            </label>
          </div>
          
          {lastSaved && (
            <div className="text-xs text-gray-500 flex items-center">
              <Clock size={12} className="mr-1" />
              Last saved: {formatDate(lastSaved)}
            </div>
          )}
        </div>
        
        {/* Save button */}
        <button
          type="button"
          onClick={handleSaveDraft}
          disabled={isSaving}
          className="w-full mb-3 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 flex items-center justify-center"
        >
          {isSaving ? (
            <>
              <Loader2 size={16} className="mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save size={16} className="mr-2" />
              Save Draft
            </>
          )}
        </button>
        
        {/* Schedule publishing button */}
        <button
          type="button"
          onClick={() => setShowScheduler(!showScheduler)}
          className="w-full mb-3 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center"
        >
          <Calendar size={16} className="mr-2" />
          Schedule Publishing
        </button>
        
        {/* Scheduler */}
        {showScheduler && (
          <div className="mb-4 p-3 border border-gray-200 rounded-md">
            <h4 className="text-sm font-medium mb-2">Schedule Publication</h4>
            <div className="mb-3">
              <label htmlFor="schedule-date" className="block text-xs text-gray-500 mb-1">
                Date and Time
              </label>
              <input
                type="datetime-local"
                id="schedule-date"
                min={getMinScheduleDate()}
                value={scheduledDate?.toISOString().slice(0, 16) || ''}
                onChange={(e) => setScheduledDate(e.target.value ? new Date(e.target.value) : null)}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button
              type="button"
              onClick={handleSchedulePublish}
              disabled={!scheduledDate}
              className="w-full px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              Confirm Schedule
            </button>
          </div>
        )}
        
        {/* Manage drafts button */}
        <button
          type="button"
          onClick={() => setShowDrafts(!showDrafts)}
          className="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center justify-center"
        >
          {showDrafts ? (
            <>
              <ChevronUp size={16} className="mr-2" />
              Hide Drafts
            </>
          ) : (
            <>
              <ChevronDown size={16} className="mr-2" />
              Manage Drafts ({drafts.length})
            </>
          )}
        </button>
        
        {/* Drafts list */}
        {showDrafts && (
          <div className="mt-4 space-y-3">
            <h4 className="text-sm font-medium">Saved Drafts</h4>
            
            {drafts.length === 0 ? (
              <p className="text-sm text-gray-500 text-center py-4">
                No drafts saved yet
              </p>
            ) : (
              <div className="max-h-60 overflow-y-auto">
                {drafts.map(draft => (
                  <div 
                    key={draft.id}
                    className="p-3 border border-gray-200 rounded-md flex items-start"
                  >
                    <div className="h-12 w-20 bg-gray-200 rounded overflow-hidden mr-3 flex-shrink-0">
                      {draft.thumbnailUrl ? (
                        <img 
                          src={draft.thumbnailUrl} 
                          alt={draft.title} 
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center bg-gray-200">
                          <Eye size={16} className="text-gray-500" />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm truncate">{draft.title}</div>
                      <div className="text-xs text-gray-500 flex items-center">
                        <Clock size={10} className="mr-1" />
                        {formatDate(draft.lastSaved)}
                      </div>
                      {draft.scheduledPublishDate && (
                        <div className="text-xs text-blue-600 flex items-center mt-1">
                          <Calendar size={10} className="mr-1" />
                          Scheduled: {formatDate(draft.scheduledPublishDate)}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex space-x-1 ml-2">
                      <button
                        type="button"
                        onClick={() => handleLoadDraft(draft.id)}
                        disabled={isLoading}
                        className="p-1 text-blue-600 hover:text-blue-800 rounded"
                        title="Load draft"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        type="button"
                        onClick={() => handleDeleteDraft(draft.id)}
                        disabled={isDeleting}
                        className="p-1 text-red-600 hover:text-red-800 rounded"
                        title="Delete draft"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
        
        {/* Status messages */}
        {error && (
          <div className="mt-4 p-3 bg-red-50 text-red-700 rounded-md text-sm flex items-center">
            <AlertCircle size={16} className="mr-2 flex-shrink-0" />
            {error}
          </div>
        )}
        
        {success && (
          <div className="mt-4 p-3 bg-green-50 text-green-700 rounded-md text-sm flex items-center">
            <CheckCircle size={16} className="mr-2 flex-shrink-0" />
            {success}
          </div>
        )}
      </div>
    </div>
  );
}
