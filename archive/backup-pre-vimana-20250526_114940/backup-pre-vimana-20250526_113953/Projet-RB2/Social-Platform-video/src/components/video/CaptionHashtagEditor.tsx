import { useState, useEffect, useRef } from 'react';
import { 
  Hash, 
  Type, 
  Smile, 
  Trash2, 
  Plus, 
  Info, 
  ChevronDown, 
  ChevronUp,
  AtSign
} from 'lucide-react';

interface Hashtag {
  id: string;
  text: string;
}

interface Mention {
  id: string;
  username: string;
  displayName: string;
}

interface CaptionHashtagEditorProps {
  initialCaption?: string;
  initialHashtags?: Hashtag[];
  initialMentions?: Mention[];
  suggestedHashtags?: Hashtag[];
  suggestedMentions?: Mention[];
  maxCaptionLength?: number;
  maxHashtags?: number;
  onChange: (data: { 
    caption: string; 
    hashtags: Hashtag[]; 
    mentions: Mention[] 
  }) => void;
}

export function CaptionHashtagEditor({
  initialCaption = '',
  initialHashtags = [],
  initialMentions = [],
  suggestedHashtags = [],
  suggestedMentions = [],
  maxCaptionLength = 2200,
  maxHashtags = 30,
  onChange
}: CaptionHashtagEditorProps) {
  const [caption, setCaption] = useState(initialCaption);
  const [hashtags, setHashtags] = useState<Hashtag[]>(initialHashtags);
  const [mentions, setMentions] = useState<Mention[]>(initialMentions);
  const [newHashtag, setNewHashtag] = useState('');
  const [showSuggestedHashtags, setShowSuggestedHashtags] = useState(false);
  const [showSuggestedMentions, setShowSuggestedMentions] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [mentionSearchTerm, setMentionSearchTerm] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  
  const hashtagInputRef = useRef<HTMLInputElement>(null);
  const mentionInputRef = useRef<HTMLInputElement>(null);
  const emojiPickerRef = useRef<HTMLDivElement>(null);
  
  // Filter suggested hashtags based on search term
  const filteredSuggestedHashtags = suggestedHashtags.filter(
    hashtag => hashtag.text.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // Filter suggested mentions based on search term
  const filteredSuggestedMentions = suggestedMentions.filter(
    mention => 
      mention.username.toLowerCase().includes(mentionSearchTerm.toLowerCase()) ||
      mention.displayName.toLowerCase().includes(mentionSearchTerm.toLowerCase())
  );
  
  // Update parent component when data changes
  useEffect(() => {
    onChange({ caption, hashtags, mentions });
  }, [caption, hashtags, mentions, onChange]);
  
  // Handle caption change
  const handleCaptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newCaption = e.target.value;
    if (newCaption.length <= maxCaptionLength) {
      setCaption(newCaption);
    }
  };
  
  // Add a new hashtag
  const addHashtag = (text: string) => {
    if (!text.trim()) return;
    
    // Remove # if present at the beginning
    const cleanText = text.trim().startsWith('#') ? text.trim().substring(1) : text.trim();
    
    // Check if hashtag already exists
    if (hashtags.some(tag => tag.text.toLowerCase() === cleanText.toLowerCase())) {
      return;
    }
    
    // Check if we've reached the maximum number of hashtags
    if (hashtags.length >= maxHashtags) {
      return;
    }
    
    const newTag: Hashtag = {
      id: Date.now().toString(),
      text: cleanText
    };
    
    setHashtags([...hashtags, newTag]);
    setNewHashtag('');
    setSearchTerm('');
    setShowSuggestedHashtags(false);
    
    // Focus back on the input
    if (hashtagInputRef.current) {
      hashtagInputRef.current.focus();
    }
  };
  
  // Remove a hashtag
  const removeHashtag = (id: string) => {
    setHashtags(hashtags.filter(tag => tag.id !== id));
  };
  
  // Add a mention
  const addMention = (mention: Mention) => {
    // Check if mention already exists
    if (mentions.some(m => m.username === mention.username)) {
      return;
    }
    
    setMentions([...mentions, mention]);
    setMentionSearchTerm('');
    setShowSuggestedMentions(false);
    
    // Focus back on the input
    if (mentionInputRef.current) {
      mentionInputRef.current.focus();
    }
  };
  
  // Remove a mention
  const removeMention = (id: string) => {
    setMentions(mentions.filter(mention => mention.id !== id));
  };
  
  // Add an emoji to the caption
  const addEmoji = (emoji: string) => {
    setCaption(prev => prev + emoji);
    setShowEmojiPicker(false);
  };
  
  // Handle click outside of emoji picker
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        emojiPickerRef.current && 
        !emojiPickerRef.current.contains(event.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  return (
    <div className="w-full bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold">Caption & Hashtags</h3>
        <p className="text-sm text-gray-500">
          Add a caption and hashtags to your video
        </p>
      </div>
      
      <div className="p-4">
        {/* Caption textarea */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Caption
          </label>
          <div className="relative">
            <textarea
              value={caption}
              onChange={handleCaptionChange}
              placeholder="Write a caption for your video..."
              className="w-full min-h-[120px] p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
            <div className="absolute bottom-3 right-3 flex items-center space-x-2">
              <button
                type="button"
                onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                className="p-1 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
              >
                <Smile size={18} />
              </button>
              <span className="text-xs text-gray-500">
                {caption.length}/{maxCaptionLength}
              </span>
            </div>
            
            {/* Emoji picker */}
            {showEmojiPicker && (
              <div 
                ref={emojiPickerRef}
                className="absolute bottom-12 right-0 bg-white border border-gray-200 rounded-lg shadow-lg p-2 z-10"
              >
                <div className="grid grid-cols-8 gap-1">
                  {['😀', '😂', '😍', '🥰', '😎', '🙌', '👍', '🔥', 
                    '❤️', '🎉', '✨', '💯', '🙏', '👏', '🤔', '😊',
                    '🥺', '😭', '😅', '🤣', '😘', '💪', '🤩', '😴'].map(emoji => (
                    <button
                      key={emoji}
                      onClick={() => addEmoji(emoji)}
                      className="w-8 h-8 flex items-center justify-center hover:bg-gray-100 rounded"
                    >
                      {emoji}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Hashtags */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Hashtags
          </label>
          <div className="flex flex-wrap gap-2 mb-2">
            {hashtags.map(tag => (
              <div 
                key={tag.id}
                className="flex items-center bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm"
              >
                <Hash size={14} className="mr-1" />
                {tag.text}
                <button
                  type="button"
                  onClick={() => removeHashtag(tag.id)}
                  className="ml-1 text-green-700 hover:text-green-900"
                >
                  <Trash2 size={14} />
                </button>
              </div>
            ))}
          </div>
          
          <div className="relative">
            <div className="flex">
              <div className="relative flex-1">
                <input
                  ref={hashtagInputRef}
                  type="text"
                  value={newHashtag}
                  onChange={(e) => {
                    setNewHashtag(e.target.value);
                    setSearchTerm(e.target.value);
                    setShowSuggestedHashtags(true);
                  }}
                  onFocus={() => setShowSuggestedHashtags(true)}
                  placeholder="Add a hashtag..."
                  className="w-full p-2 pr-8 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
                <Hash size={16} className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>
              <button
                type="button"
                onClick={() => addHashtag(newHashtag)}
                disabled={!newHashtag.trim()}
                className="px-3 py-2 bg-green-500 text-white rounded-r-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
              >
                <Plus size={16} />
              </button>
            </div>
            
            {/* Suggested hashtags */}
            {showSuggestedHashtags && filteredSuggestedHashtags.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
                {filteredSuggestedHashtags.map(tag => (
                  <button
                    key={tag.id}
                    type="button"
                    onClick={() => addHashtag(tag.text)}
                    className="w-full text-left px-3 py-2 hover:bg-gray-100 flex items-center"
                  >
                    <Hash size={14} className="mr-2 text-gray-500" />
                    <span>{tag.text}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
          
          <p className="mt-1 text-xs text-gray-500">
            {hashtags.length}/{maxHashtags} hashtags used
          </p>
        </div>
        
        {/* Advanced options toggle */}
        <button
          type="button"
          onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
          className="flex items-center text-sm text-gray-600 mb-3"
        >
          {showAdvancedOptions ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          <span className="ml-1">Advanced options</span>
        </button>
        
        {/* Mentions (only shown when advanced options are enabled) */}
        {showAdvancedOptions && (
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Mentions
            </label>
            <div className="flex flex-wrap gap-2 mb-2">
              {mentions.map(mention => (
                <div 
                  key={mention.id}
                  className="flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm"
                >
                  <AtSign size={14} className="mr-1" />
                  {mention.username}
                  <button
                    type="button"
                    onClick={() => removeMention(mention.id)}
                    className="ml-1 text-blue-700 hover:text-blue-900"
                  >
                    <Trash2 size={14} />
                  </button>
                </div>
              ))}
            </div>
            
            <div className="relative">
              <div className="flex">
                <div className="relative flex-1">
                  <input
                    ref={mentionInputRef}
                    type="text"
                    value={mentionSearchTerm}
                    onChange={(e) => {
                      setMentionSearchTerm(e.target.value);
                      setShowSuggestedMentions(true);
                    }}
                    onFocus={() => setShowSuggestedMentions(true)}
                    placeholder="Mention someone..."
                    className="w-full p-2 pr-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                  <AtSign size={16} className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
              </div>
              
              {/* Suggested mentions */}
              {showSuggestedMentions && filteredSuggestedMentions.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
                  {filteredSuggestedMentions.map(mention => (
                    <button
                      key={mention.id}
                      type="button"
                      onClick={() => addMention(mention)}
                      className="w-full text-left px-3 py-2 hover:bg-gray-100 flex items-center"
                    >
                      <AtSign size={14} className="mr-2 text-gray-500" />
                      <div>
                        <div className="font-medium">{mention.username}</div>
                        <div className="text-xs text-gray-500">{mention.displayName}</div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* Tips */}
        <div className="mt-4 bg-blue-50 p-3 rounded-md">
          <div className="flex items-start">
            <Info size={16} className="text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Tips for better engagement:</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>Use relevant hashtags to increase discoverability</li>
                <li>Keep your caption engaging and authentic</li>
                <li>Mention collaborators or featured accounts</li>
                <li>Use emojis to add personality to your caption</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
