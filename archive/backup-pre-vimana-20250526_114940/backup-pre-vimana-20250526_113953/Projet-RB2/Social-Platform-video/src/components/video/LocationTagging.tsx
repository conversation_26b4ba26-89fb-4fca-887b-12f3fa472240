import { useState, useEffect, useRef } from 'react';
import { 
  MapPin, 
  Search, 
  X, 
  Loader2, 
  ChevronDown, 
  ChevronUp,
  Plus,
  Star,
  Navigation
} from 'lucide-react';

interface Location {
  id: string;
  name: string;
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  type: 'city' | 'place' | 'custom';
  popularity?: number; // 1-100 scale for popularity
}

interface LocationTaggingProps {
  initialLocation?: Location | null;
  popularLocations?: Location[];
  onLocationSelect: (location: Location | null) => void;
  onCreateCustomLocation?: (name: string) => Promise<Location>;
}

export function LocationTagging({
  initialLocation = null,
  popularLocations = [],
  onLocationSelect,
  onCreateCustomLocation
}: LocationTaggingProps) {
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(initialLocation);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Location[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [userLocation, setUserLocation] = useState<{latitude: number; longitude: number} | null>(null);
  const [isLoadingUserLocation, setIsLoadingUserLocation] = useState(false);
  const [customLocationName, setCustomLocationName] = useState('');
  const [isCreatingCustomLocation, setIsCreatingCustomLocation] = useState(false);
  const [showCreateCustom, setShowCreateCustom] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchResultsRef = useRef<HTMLDivElement>(null);
  
  // Mock function to search locations - in a real app, this would call an API
  const searchLocations = async (query: string) => {
    setIsSearching(true);
    setError(null);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Filter popular locations based on query
      const results = popularLocations.filter(
        location => location.name.toLowerCase().includes(query.toLowerCase()) ||
                   location.address.toLowerCase().includes(query.toLowerCase())
      );
      
      setSearchResults(results);
    } catch (err) {
      setError('Failed to search locations');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };
  
  // Get user's current location
  const getUserLocation = () => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by your browser');
      return;
    }
    
    setIsLoadingUserLocation(true);
    setError(null);
    
    navigator.geolocation.getCurrentPosition(
      (position) => {
        setUserLocation({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude
        });
        setIsLoadingUserLocation(false);
        
        // In a real app, you would use these coordinates to search for nearby locations
        // For this example, we'll just filter the popular locations to simulate this
        const nearbyLocations = popularLocations.slice(0, 3); // Just take the first 3 for demo
        setSearchResults(nearbyLocations);
        setShowResults(true);
      },
      (error) => {
        setError(`Error getting location: ${error.message}`);
        setIsLoadingUserLocation(false);
      }
    );
  };
  
  // Create a custom location
  const createCustomLocation = async () => {
    if (!customLocationName.trim()) return;
    
    setIsCreatingCustomLocation(true);
    setError(null);
    
    try {
      if (onCreateCustomLocation) {
        const newLocation = await onCreateCustomLocation(customLocationName);
        setSelectedLocation(newLocation);
        onLocationSelect(newLocation);
        setCustomLocationName('');
        setShowCreateCustom(false);
      } else {
        // Fallback if no creation function is provided
        const newLocation: Location = {
          id: Date.now().toString(),
          name: customLocationName,
          address: 'Custom location',
          coordinates: userLocation || { latitude: 0, longitude: 0 },
          type: 'custom'
        };
        
        setSelectedLocation(newLocation);
        onLocationSelect(newLocation);
        setCustomLocationName('');
        setShowCreateCustom(false);
      }
    } catch (err) {
      setError('Failed to create custom location');
    } finally {
      setIsCreatingCustomLocation(false);
    }
  };
  
  // Handle location selection
  const handleLocationSelect = (location: Location) => {
    setSelectedLocation(location);
    onLocationSelect(location);
    setShowResults(false);
    setSearchQuery('');
  };
  
  // Clear selected location
  const clearLocation = () => {
    setSelectedLocation(null);
    onLocationSelect(null);
  };
  
  // Search when query changes
  useEffect(() => {
    if (searchQuery.trim().length >= 2) {
      searchLocations(searchQuery);
      setShowResults(true);
    } else {
      setSearchResults([]);
      setShowResults(false);
    }
  }, [searchQuery]);
  
  // Handle click outside of search results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchResultsRef.current && 
        !searchResultsRef.current.contains(event.target as Node) &&
        !searchInputRef.current?.contains(event.target as Node)
      ) {
        setShowResults(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  return (
    <div className="w-full bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold">Location</h3>
        <p className="text-sm text-gray-500">
          Tag your video with a location
        </p>
      </div>
      
      <div className="p-4">
        {/* Selected location display */}
        {selectedLocation && (
          <div className="mb-4 p-3 bg-blue-50 rounded-md flex items-start">
            <MapPin className="text-blue-500 mt-0.5 mr-2 flex-shrink-0" size={18} />
            <div className="flex-1">
              <div className="font-medium">{selectedLocation.name}</div>
              <div className="text-sm text-gray-600">{selectedLocation.address}</div>
            </div>
            <button
              type="button"
              onClick={clearLocation}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={18} />
            </button>
          </div>
        )}
        
        {/* Location search */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Search for a location
          </label>
          <div className="relative">
            <div className="flex">
              <div className="relative flex-1">
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => {
                    if (searchQuery.trim().length >= 2 || searchResults.length > 0) {
                      setShowResults(true);
                    }
                  }}
                  placeholder="Search for a location..."
                  className="w-full p-2 pl-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
                <Search size={16} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>
              <button
                type="button"
                onClick={getUserLocation}
                disabled={isLoadingUserLocation}
                className="ml-2 px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
                title="Use current location"
              >
                {isLoadingUserLocation ? (
                  <Loader2 size={16} className="animate-spin" />
                ) : (
                  <Navigation size={16} />
                )}
              </button>
            </div>
            
            {/* Search results */}
            {showResults && (searchResults.length > 0 || isSearching) && (
              <div 
                ref={searchResultsRef}
                className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto"
              >
                {isSearching ? (
                  <div className="p-3 text-center text-gray-500">
                    <Loader2 size={20} className="animate-spin mx-auto mb-2" />
                    <p>Searching locations...</p>
                  </div>
                ) : searchResults.length === 0 ? (
                  <div className="p-3 text-center text-gray-500">
                    <p>No locations found</p>
                  </div>
                ) : (
                  <>
                    {searchResults.map(location => (
                      <button
                        key={location.id}
                        type="button"
                        onClick={() => handleLocationSelect(location)}
                        className="w-full text-left px-3 py-2 hover:bg-gray-100 flex items-start"
                      >
                        <MapPin size={16} className="mt-0.5 mr-2 text-gray-500 flex-shrink-0" />
                        <div className="flex-1">
                          <div className="font-medium">{location.name}</div>
                          <div className="text-xs text-gray-500">{location.address}</div>
                        </div>
                        {location.popularity && location.popularity > 70 && (
                          <Star size={14} className="text-yellow-500 ml-1 flex-shrink-0" />
                        )}
                      </button>
                    ))}
                    
                    {/* Create custom location option */}
                    <div className="border-t border-gray-200 mt-1 pt-1">
                      <button
                        type="button"
                        onClick={() => setShowCreateCustom(!showCreateCustom)}
                        className="w-full text-left px-3 py-2 text-green-600 hover:bg-gray-100 flex items-center"
                      >
                        <Plus size={16} className="mr-2" />
                        Create custom location
                      </button>
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
        
        {/* Create custom location */}
        {showCreateCustom && (
          <div className="mb-4 p-3 border border-gray-200 rounded-md">
            <h4 className="text-sm font-medium mb-2">Create Custom Location</h4>
            <div className="flex mb-2">
              <input
                type="text"
                value={customLocationName}
                onChange={(e) => setCustomLocationName(e.target.value)}
                placeholder="Enter location name..."
                className="flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
              <button
                type="button"
                onClick={createCustomLocation}
                disabled={!customLocationName.trim() || isCreatingCustomLocation}
                className="px-3 py-2 bg-green-500 text-white rounded-r-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
              >
                {isCreatingCustomLocation ? (
                  <Loader2 size={16} className="animate-spin" />
                ) : (
                  <Plus size={16} />
                )}
              </button>
            </div>
            <p className="text-xs text-gray-500">
              Create a custom location if you can't find what you're looking for
            </p>
          </div>
        )}
        
        {/* Error message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
            {error}
          </div>
        )}
        
        {/* Popular locations */}
        <div className="mt-4">
          <h4 className="text-sm font-medium mb-2">Popular Locations</h4>
          <div className="grid grid-cols-2 gap-2">
            {popularLocations.slice(0, 4).map(location => (
              <button
                key={location.id}
                type="button"
                onClick={() => handleLocationSelect(location)}
                className="p-2 border border-gray-200 rounded-md hover:bg-gray-50 text-left flex items-start"
              >
                <MapPin size={14} className="mt-0.5 mr-1 text-gray-500 flex-shrink-0" />
                <div>
                  <div className="text-sm font-medium truncate">{location.name}</div>
                  <div className="text-xs text-gray-500 truncate">{location.address}</div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
