import { useState, useEffect, useRef } from 'react';
import { 
  MapPin, 
  Search, 
  X, 
  ChevronDown, 
  ChevronUp, 
  Loader2, 
  Navigation,
  Globe,
  Home
} from 'lucide-react';

export interface Location {
  id: string;
  name: string;
  address?: string;
  city?: string;
  region?: string;
  country?: string;
  postalCode?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  type: 'city' | 'region' | 'country' | 'point' | 'address';
  distance?: number; // in kilometers
}

interface LocationFilterProps {
  selectedLocation?: Location | null;
  onLocationChange: (location: Location | null) => void;
  onRadiusChange?: (radius: number) => void;
  radius?: number;
  popularLocations?: Location[];
  isExpanded?: boolean;
  onToggleExpand?: () => void;
  showRadiusSelector?: boolean;
  useCurrentLocation?: boolean;
  isLoading?: boolean;
}

export function LocationFilter({
  selectedLocation,
  onLocationChange,
  onRadiusChange,
  radius = 50,
  popularLocations = [],
  isExpanded = true,
  onToggleExpand,
  showRadiusSelector = true,
  useCurrentLocation = true,
  isLoading = false
}: LocationFilterProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Location[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchResultsRef = useRef<HTMLDivElement>(null);
  
  // Mock function to search locations - in a real app, this would call an API
  const searchLocations = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }
    
    setIsSearching(true);
    setError(null);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock search results
      const results: Location[] = [
        {
          id: 'loc-1',
          name: `${query} City`,
          city: `${query} City`,
          region: 'Region',
          country: 'Country',
          type: 'city',
          coordinates: {
            latitude: 48.8566,
            longitude: 2.3522
          }
        },
        {
          id: 'loc-2',
          name: `${query} Region`,
          region: `${query} Region`,
          country: 'Country',
          type: 'region',
          coordinates: {
            latitude: 48.8566,
            longitude: 2.3522
          }
        },
        {
          id: 'loc-3',
          name: `${query} Point of Interest`,
          address: '123 Main St',
          city: 'City',
          region: 'Region',
          country: 'Country',
          type: 'point',
          coordinates: {
            latitude: 48.8566,
            longitude: 2.3522
          }
        }
      ];
      
      setSearchResults(results);
    } catch (err) {
      setError('Failed to search locations');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };
  
  // Get user's current location
  const getUserLocation = () => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by your browser');
      return;
    }
    
    setIsLoadingLocation(true);
    setError(null);
    
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          // In a real app, you would use a reverse geocoding service to get the location details
          // For this example, we'll create a mock location
          const userLocation: Location = {
            id: 'current-location',
            name: 'Current Location',
            type: 'point',
            coordinates: {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude
            }
          };
          
          onLocationChange(userLocation);
        } catch (err) {
          setError('Error getting location details');
        } finally {
          setIsLoadingLocation(false);
        }
      },
      (error) => {
        setError(`Error getting location: ${error.message}`);
        setIsLoadingLocation(false);
      }
    );
  };
  
  // Handle location selection
  const handleLocationSelect = (location: Location) => {
    onLocationChange(location);
    setShowResults(false);
    setSearchQuery('');
  };
  
  // Clear selected location
  const clearLocation = () => {
    onLocationChange(null);
  };
  
  // Handle radius change
  const handleRadiusChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    if (onRadiusChange) {
      onRadiusChange(value);
    }
  };
  
  // Toggle expanded state
  const toggleExpanded = () => {
    if (onToggleExpand) {
      onToggleExpand();
    }
  };
  
  // Search when query changes
  useEffect(() => {
    const delaySearch = setTimeout(() => {
      if (searchQuery.trim().length >= 2) {
        searchLocations(searchQuery);
      } else {
        setSearchResults([]);
      }
    }, 300);
    
    return () => clearTimeout(delaySearch);
  }, [searchQuery]);
  
  // Handle click outside of search results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchResultsRef.current && 
        !searchResultsRef.current.contains(event.target as Node) &&
        !searchInputRef.current?.contains(event.target as Node)
      ) {
        setShowResults(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Format location name
  const formatLocationName = (location: Location) => {
    if (location.type === 'city') {
      return `${location.city}, ${location.country}`;
    } else if (location.type === 'region') {
      return `${location.region}, ${location.country}`;
    } else if (location.type === 'country') {
      return location.country || location.name;
    } else if (location.type === 'point') {
      return location.name;
    } else {
      return location.address || location.name;
    }
  };
  
  // Get location icon
  const getLocationIcon = (locationType: Location['type']) => {
    switch (locationType) {
      case 'city':
        return <Home size={16} className="text-blue-500" />;
      case 'country':
        return <Globe size={16} className="text-green-500" />;
      case 'region':
        return <MapPin size={16} className="text-purple-500" />;
      default:
        return <MapPin size={16} className="text-red-500" />;
    }
  };
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div 
        className="p-4 border-b border-gray-200 flex justify-between items-center cursor-pointer"
        onClick={toggleExpanded}
      >
        <h3 className="font-semibold flex items-center">
          <MapPin size={18} className="mr-2 text-gray-500" />
          Location
        </h3>
        {onToggleExpand && (
          <div>
            {isExpanded ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
          </div>
        )}
      </div>
      
      {isExpanded && (
        <div className="p-4">
          {/* Selected location display */}
          {selectedLocation && (
            <div className="mb-4 p-3 bg-blue-50 rounded-md flex items-start">
              <MapPin className="text-blue-500 mt-0.5 mr-2 flex-shrink-0" size={18} />
              <div className="flex-1">
                <div className="font-medium">{selectedLocation.name}</div>
                {selectedLocation.address && (
                  <div className="text-sm text-gray-600">{selectedLocation.address}</div>
                )}
                {!selectedLocation.address && selectedLocation.city && (
                  <div className="text-sm text-gray-600">
                    {[
                      selectedLocation.city,
                      selectedLocation.region,
                      selectedLocation.country
                    ].filter(Boolean).join(', ')}
                  </div>
                )}
              </div>
              <button
                type="button"
                onClick={clearLocation}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={18} />
              </button>
            </div>
          )}
          
          {/* Location search */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search for a location
            </label>
            <div className="relative">
              <div className="flex">
                <div className="relative flex-1">
                  <input
                    ref={searchInputRef}
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onFocus={() => {
                      if (searchQuery.trim().length >= 2 || searchResults.length > 0) {
                        setShowResults(true);
                      }
                    }}
                    placeholder="Search for a location..."
                    className="w-full p-2 pl-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                  <Search size={16} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  
                  {searchQuery && (
                    <button
                      type="button"
                      onClick={() => setSearchQuery('')}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <X size={16} />
                    </button>
                  )}
                </div>
                
                {useCurrentLocation && (
                  <button
                    type="button"
                    onClick={getUserLocation}
                    disabled={isLoadingLocation}
                    className="ml-2 px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 flex items-center"
                    title="Use current location"
                  >
                    {isLoadingLocation ? (
                      <Loader2 size={16} className="animate-spin" />
                    ) : (
                      <Navigation size={16} />
                    )}
                  </button>
                )}
              </div>
              
              {/* Search results */}
              {showResults && (searchResults.length > 0 || isSearching) && (
                <div 
                  ref={searchResultsRef}
                  className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto"
                >
                  {isSearching ? (
                    <div className="p-3 text-center text-gray-500">
                      <Loader2 size={20} className="animate-spin mx-auto mb-2" />
                      <p>Searching locations...</p>
                    </div>
                  ) : searchResults.length === 0 ? (
                    <div className="p-3 text-center text-gray-500">
                      <p>No locations found</p>
                    </div>
                  ) : (
                    <>
                      {searchResults.map(location => (
                        <button
                          key={location.id}
                          type="button"
                          onClick={() => handleLocationSelect(location)}
                          className="w-full text-left px-3 py-2 hover:bg-gray-100 flex items-start"
                        >
                          <div className="mt-0.5 mr-2 text-gray-500 flex-shrink-0">
                            {getLocationIcon(location.type)}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium">{location.name}</div>
                            <div className="text-xs text-gray-500">
                              {formatLocationName(location)}
                            </div>
                          </div>
                        </button>
                      ))}
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
          
          {/* Radius selector */}
          {showRadiusSelector && selectedLocation && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search radius: {radius} km
              </label>
              <input
                type="range"
                min="1"
                max="500"
                step="1"
                value={radius}
                onChange={handleRadiusChange}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>1 km</span>
                <span>250 km</span>
                <span>500 km</span>
              </div>
            </div>
          )}
          
          {/* Error message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
              {error}
            </div>
          )}
          
          {/* Popular locations */}
          {popularLocations.length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">Popular Locations</h4>
              <div className="grid grid-cols-2 gap-2">
                {popularLocations.map(location => (
                  <button
                    key={location.id}
                    type="button"
                    onClick={() => handleLocationSelect(location)}
                    className="p-2 border border-gray-200 rounded-md hover:bg-gray-50 text-left flex items-start"
                  >
                    <MapPin size={14} className="mt-0.5 mr-1 text-gray-500 flex-shrink-0" />
                    <div>
                      <div className="text-sm font-medium truncate">{location.name}</div>
                      <div className="text-xs text-gray-500 truncate">
                        {formatLocationName(location)}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
