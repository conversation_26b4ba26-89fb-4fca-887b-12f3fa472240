import { useState } from 'react';
import {
  ArrowUpDown,
  ChevronDown,
  ChevronUp,
  Flame,
  Clock,
  ThumbsUp,
  Eye,
  Star,
  BarChart2,
  Calendar,
  TrendingUp,
  MessageSquare
} from 'lucide-react';

export type SortOption = {
  id: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
};

interface SortOptionsProps {
  options: SortOption[];
  selectedOption: string;
  onOptionChange: (optionId: string) => void;
  label?: string;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
  className?: string;
  variant?: 'dropdown' | 'card';
}

export function SortOptions({
  options,
  selectedOption,
  onOptionChange,
  label = 'Sort by',
  isExpanded = true,
  onToggleExpand,
  className = '',
  variant = 'dropdown'
}: SortOptionsProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Get the currently selected option
  const currentOption = options.find(option => option.id === selectedOption) || options[0];

  // Toggle dropdown
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // Select an option
  const selectOption = (optionId: string) => {
    onOptionChange(optionId);
    setIsOpen(false);
  };

  // Toggle expanded state
  const toggleExpanded = () => {
    if (onToggleExpand) {
      onToggleExpand();
    }
  };

  // Dropdown variant (simple dropdown)
  if (variant === 'dropdown') {
    return (
      <div className={`relative ${className}`}>
        <button
          type="button"
          onClick={toggleDropdown}
          className="flex items-center justify-between w-full px-3 py-2 text-sm bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500"
        >
          <div className="flex items-center">
            {currentOption.icon}
            <span className="ml-2">{currentOption.label}</span>
          </div>
          <ChevronDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </button>

        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
            <ul className="py-1">
              {options.map(option => (
                <li key={option.id}>
                  <button
                    type="button"
                    onClick={() => selectOption(option.id)}
                    className={`flex items-center w-full px-4 py-2 text-sm hover:bg-gray-100 ${
                      option.id === selectedOption ? 'bg-green-50 text-green-700' : 'text-gray-700'
                    }`}
                  >
                    {option.icon}
                    <div className="ml-2">
                      <div>{option.label}</div>
                      {option.description && (
                        <div className="text-xs text-gray-500">{option.description}</div>
                      )}
                    </div>
                  </button>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  }

  // Card variant (expandable card)
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div
        className="p-4 border-b border-gray-200 flex justify-between items-center cursor-pointer"
        onClick={toggleExpanded}
      >
        <h3 className="font-semibold flex items-center">
          <ArrowUpDown size={18} className="mr-2 text-gray-500" />
          {label}
        </h3>
        {onToggleExpand && (
          <div>
            {isExpanded ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
          </div>
        )}
      </div>

      {isExpanded && (
        <div className="p-4">
          <div className="relative">
            <button
              type="button"
              onClick={toggleDropdown}
              className="w-full flex items-center justify-between p-2 border border-gray-300 rounded-md bg-white text-left focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <div className="flex items-center">
                {currentOption.icon}
                <span className="ml-2">{currentOption.label}</span>
              </div>
              <ChevronDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
            </button>

            {isOpen && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
                {options.map(option => (
                  <button
                    key={option.id}
                    type="button"
                    onClick={() => selectOption(option.id)}
                    className={`w-full text-left px-3 py-2 flex items-center ${
                      option.id === selectedOption
                        ? 'bg-green-50 text-green-700'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    {option.icon}
                    <div className="ml-2">
                      <div className="font-medium">{option.label}</div>
                      {option.description && (
                        <div className="text-xs text-gray-500">{option.description}</div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Predefined sort options for videos
export const videoSortOptions: SortOption[] = [
  {
    id: 'trending',
    label: 'Trending',
    description: 'Videos gaining popularity right now',
    icon: <Flame size={16} className="text-orange-500" />
  },
  {
    id: 'latest',
    label: 'Latest',
    description: 'Most recently published',
    icon: <Clock size={16} className="text-blue-500" />
  },
  {
    id: 'popular',
    label: 'Most Popular',
    description: 'Highest engagement overall',
    icon: <ThumbsUp size={16} className="text-green-500" />
  },
  {
    id: 'views',
    label: 'Most Viewed',
    description: 'Highest view count',
    icon: <Eye size={16} className="text-purple-500" />
  },
  {
    id: 'comments',
    label: 'Most Commented',
    description: 'Highest comment count',
    icon: <MessageSquare size={16} className="text-indigo-500" />
  },
  {
    id: 'rating',
    label: 'Top Rated',
    description: 'Highest user ratings',
    icon: <Star size={16} className="text-yellow-500" />
  }
];

// Predefined sort options for retreats
export const retreatSortOptions: SortOption[] = [
  {
    id: 'recommended',
    label: 'Recommended',
    description: 'Personalized for you',
    icon: <Star size={16} className="text-yellow-500" />
  },
  {
    id: 'popular',
    label: 'Most Popular',
    description: 'Highest bookings',
    icon: <ThumbsUp size={16} className="text-green-500" />
  },
  {
    id: 'price_low',
    label: 'Price: Low to High',
    description: 'Most affordable first',
    icon: <BarChart2 size={16} className="text-blue-500" />
  },
  {
    id: 'price_high',
    label: 'Price: High to Low',
    description: 'Premium options first',
    icon: <BarChart2 size={16} className="text-purple-500" />
  },
  {
    id: 'upcoming',
    label: 'Upcoming',
    description: 'Starting soon',
    icon: <Calendar size={16} className="text-orange-500" />
  },
  {
    id: 'trending',
    label: 'Trending',
    description: 'Growing in popularity',
    icon: <TrendingUp size={16} className="text-red-500" />
  }
];

// Predefined sort options for users/professionals
export const userSortOptions: SortOption[] = [
  {
    id: 'recommended',
    label: 'Recommended',
    description: 'Personalized for you',
    icon: <Star size={16} className="text-yellow-500" />
  },
  {
    id: 'popular',
    label: 'Most Popular',
    description: 'Highest follower count',
    icon: <ThumbsUp size={16} className="text-green-500" />
  },
  {
    id: 'recent',
    label: 'Recently Active',
    description: 'Active in the last week',
    icon: <Clock size={16} className="text-blue-500" />
  },
  {
    id: 'trending',
    label: 'Trending',
    description: 'Growing in popularity',
    icon: <TrendingUp size={16} className="text-red-500" />
  }
];
