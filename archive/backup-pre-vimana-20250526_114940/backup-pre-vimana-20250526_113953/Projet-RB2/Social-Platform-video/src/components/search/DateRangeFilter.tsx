import { useState, useEffect } from 'react';
import { 
  Calendar, 
  ChevronDown, 
  ChevronUp, 
  X, 
  ChevronLeft, 
  ChevronRight,
  Check
} from 'lucide-react';
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addMonths, subMonths, isSameMonth, isSameDay, addDays, parseISO } from 'date-fns';

export interface DateRange {
  startDate: Date | null;
  endDate: Date | null;
}

interface DateRangeFilterProps {
  selectedRange: DateRange;
  onRangeChange: (range: DateRange) => void;
  presetRanges?: {
    label: string;
    value: () => DateRange;
  }[];
  isExpanded?: boolean;
  onToggleExpand?: () => void;
  minDate?: Date;
  maxDate?: Date;
}

export function DateRangeFilter({
  selectedRange,
  onRangeChange,
  presetRanges,
  isExpanded = true,
  onToggleExpand,
  minDate,
  maxDate
}: DateRangeFilterProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [hoverDate, setHoverDate] = useState<Date | null>(null);
  const [isSelectingStartDate, setIsSelectingStartDate] = useState(true);
  const [showCalendar, setShowCalendar] = useState(false);
  
  // Toggle expanded state
  const toggleExpanded = () => {
    if (onToggleExpand) {
      onToggleExpand();
    }
  };
  
  // Toggle calendar visibility
  const toggleCalendar = () => {
    setShowCalendar(!showCalendar);
    if (!showCalendar) {
      // Reset to current month when opening calendar
      setCurrentMonth(new Date());
    }
  };
  
  // Navigate to previous month
  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };
  
  // Navigate to next month
  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };
  
  // Clear date range
  const clearDateRange = () => {
    onRangeChange({ startDate: null, endDate: null });
    setIsSelectingStartDate(true);
  };
  
  // Handle date selection
  const handleDateClick = (day: Date) => {
    if (isSelectingStartDate) {
      // Selecting start date
      onRangeChange({
        startDate: day,
        endDate: null
      });
      setIsSelectingStartDate(false);
    } else {
      // Selecting end date
      if (selectedRange.startDate && day < selectedRange.startDate) {
        // If selected end date is before start date, swap them
        onRangeChange({
          startDate: day,
          endDate: selectedRange.startDate
        });
      } else {
        onRangeChange({
          ...selectedRange,
          endDate: day
        });
      }
      setIsSelectingStartDate(true);
      setShowCalendar(false);
    }
  };
  
  // Handle mouse enter on date
  const handleDateMouseEnter = (day: Date) => {
    setHoverDate(day);
  };
  
  // Handle mouse leave on date
  const handleDateMouseLeave = () => {
    setHoverDate(null);
  };
  
  // Check if a date is in the selected range
  const isInRange = (day: Date) => {
    if (!selectedRange.startDate) return false;
    if (!selectedRange.endDate && !hoverDate) return false;
    
    const endDate = selectedRange.endDate || hoverDate;
    if (!endDate) return false;
    
    return day >= selectedRange.startDate && day <= endDate;
  };
  
  // Check if a date is the start of the range
  const isRangeStart = (day: Date) => {
    if (!selectedRange.startDate) return false;
    return isSameDay(day, selectedRange.startDate);
  };
  
  // Check if a date is the end of the range
  const isRangeEnd = (day: Date) => {
    if (selectedRange.endDate) {
      return isSameDay(day, selectedRange.endDate);
    }
    if (!isSelectingStartDate && hoverDate) {
      return isSameDay(day, hoverDate);
    }
    return false;
  };
  
  // Format date range for display
  const formatDateRange = () => {
    if (!selectedRange.startDate) {
      return 'Select dates';
    }
    
    const startDateStr = format(selectedRange.startDate, 'MMM d, yyyy');
    
    if (!selectedRange.endDate) {
      return `From ${startDateStr}`;
    }
    
    const endDateStr = format(selectedRange.endDate, 'MMM d, yyyy');
    return `${startDateStr} - ${endDateStr}`;
  };
  
  // Apply a preset range
  const applyPresetRange = (getRangeFn: () => DateRange) => {
    const range = getRangeFn();
    onRangeChange(range);
    setIsSelectingStartDate(true);
    setShowCalendar(false);
  };
  
  // Render calendar days
  const renderDays = () => {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(monthStart);
    const startDate = startOfWeek(monthStart);
    const endDate = endOfWeek(monthEnd);
    
    const dateFormat = 'EEEE';
    const days = [];
    let day = startDate;
    
    // Render day headers (Sun, Mon, etc.)
    const dayHeaders = [];
    for (let i = 0; i < 7; i++) {
      dayHeaders.push(
        <div key={`header-${i}`} className="text-center text-xs font-medium text-gray-500 uppercase">
          {format(addDays(startDate, i), 'EEEEE')}
        </div>
      );
    }
    days.push(<div key="header" className="grid grid-cols-7 mb-1">{dayHeaders}</div>);
    
    // Render calendar days
    let formattedDate = '';
    let rows = [];
    
    while (day <= endDate) {
      let rowDays = [];
      
      for (let i = 0; i < 7; i++) {
        formattedDate = format(day, 'd');
        const cloneDay = day;
        const isToday = isSameDay(day, new Date());
        const isDisabled = (minDate && day < minDate) || (maxDate && day > maxDate);
        
        rowDays.push(
          <div
            key={`day-${day.toISOString()}`}
            className={`
              relative p-2 text-center cursor-pointer
              ${!isSameMonth(day, monthStart) ? 'text-gray-400' : ''}
              ${isToday ? 'font-bold' : ''}
              ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}
              ${isRangeStart(day) || isRangeEnd(day) ? 'bg-green-500 text-white hover:bg-green-600' : ''}
              ${isInRange(day) && !isRangeStart(day) && !isRangeEnd(day) ? 'bg-green-100' : ''}
            `}
            onClick={() => !isDisabled && handleDateClick(cloneDay)}
            onMouseEnter={() => !isDisabled && handleDateMouseEnter(cloneDay)}
            onMouseLeave={handleDateMouseLeave}
          >
            {formattedDate}
          </div>
        );
        
        day = addDays(day, 1);
      }
      
      rows.push(
        <div key={`row-${day.toISOString()}`} className="grid grid-cols-7">
          {rowDays}
        </div>
      );
    }
    
    days.push(<div key="days" className="mt-1">{rows}</div>);
    
    return days;
  };
  
  // Default preset ranges if none provided
  const defaultPresetRanges = [
    {
      label: 'Today',
      value: () => {
        const today = new Date();
        return { startDate: today, endDate: today };
      }
    },
    {
      label: 'Yesterday',
      value: () => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return { startDate: yesterday, endDate: yesterday };
      }
    },
    {
      label: 'This Week',
      value: () => {
        const today = new Date();
        const startOfWeekDate = startOfWeek(today);
        return { startDate: startOfWeekDate, endDate: today };
      }
    },
    {
      label: 'Last 7 Days',
      value: () => {
        const today = new Date();
        const last7Days = new Date();
        last7Days.setDate(last7Days.getDate() - 6);
        return { startDate: last7Days, endDate: today };
      }
    },
    {
      label: 'This Month',
      value: () => {
        const today = new Date();
        return { startDate: startOfMonth(today), endDate: today };
      }
    },
    {
      label: 'Last 30 Days',
      value: () => {
        const today = new Date();
        const last30Days = new Date();
        last30Days.setDate(last30Days.getDate() - 29);
        return { startDate: last30Days, endDate: today };
      }
    }
  ];
  
  const rangesToUse = presetRanges || defaultPresetRanges;
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div 
        className="p-4 border-b border-gray-200 flex justify-between items-center cursor-pointer"
        onClick={toggleExpanded}
      >
        <h3 className="font-semibold flex items-center">
          <Calendar size={18} className="mr-2 text-gray-500" />
          Date Range
        </h3>
        {onToggleExpand && (
          <div>
            {isExpanded ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
          </div>
        )}
      </div>
      
      {isExpanded && (
        <div className="p-4">
          {/* Date range display */}
          <div className="relative mb-4">
            <button
              type="button"
              onClick={toggleCalendar}
              className="w-full flex items-center justify-between p-2 border border-gray-300 rounded-md bg-white text-left focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <div className="flex items-center">
                <Calendar size={16} className="mr-2 text-gray-500" />
                <span>{formatDateRange()}</span>
              </div>
              <ChevronDown size={16} className={`transition-transform ${showCalendar ? 'rotate-180' : ''}`} />
            </button>
            
            {selectedRange.startDate && (
              <button
                type="button"
                onClick={clearDateRange}
                className="absolute right-10 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X size={16} />
              </button>
            )}
          </div>
          
          {/* Calendar */}
          {showCalendar && (
            <div className="mb-4 border border-gray-200 rounded-md p-3">
              {/* Month navigation */}
              <div className="flex justify-between items-center mb-2">
                <button
                  type="button"
                  onClick={prevMonth}
                  className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
                >
                  <ChevronLeft size={20} />
                </button>
                <h4 className="font-medium">
                  {format(currentMonth, 'MMMM yyyy')}
                </h4>
                <button
                  type="button"
                  onClick={nextMonth}
                  className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full"
                >
                  <ChevronRight size={20} />
                </button>
              </div>
              
              {/* Calendar grid */}
              <div className="mt-2">
                {renderDays()}
              </div>
              
              {/* Selection instructions */}
              <div className="mt-2 text-xs text-gray-500 text-center">
                {isSelectingStartDate ? 'Select start date' : 'Select end date'}
              </div>
            </div>
          )}
          
          {/* Preset ranges */}
          <div>
            <h4 className="text-sm font-medium mb-2">Preset Ranges</h4>
            <div className="grid grid-cols-2 gap-2">
              {rangesToUse.map((range, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => applyPresetRange(range.value)}
                  className="p-2 border border-gray-200 rounded-md hover:bg-gray-50 text-left text-sm"
                >
                  {range.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
