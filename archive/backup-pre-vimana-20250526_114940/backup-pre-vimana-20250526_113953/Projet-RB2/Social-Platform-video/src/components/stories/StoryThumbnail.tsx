import { useState, useEffect } from 'react';
import { Plus } from 'lucide-react';
import { Story } from '../../api/storiesApi';
import { useStoriesStore } from '../../store/stories';

interface StoryThumbnailProps {
  story?: Story;
  isCreateButton?: boolean;
  size?: 'sm' | 'md' | 'lg';
  onCreateClick?: () => void;
}

export function StoryThumbnail({
  story,
  isCreateButton = false,
  size = 'md',
  onCreateClick,
}: StoryThumbnailProps) {
  const { openStory, startCreatingStory } = useStoriesStore();
  const [progress, setProgress] = useState<number[]>([]);
  
  // Calculate the size based on the prop
  const sizeClass = {
    sm: 'w-16 h-16',
    md: 'w-20 h-20',
    lg: 'w-24 h-24',
  }[size];
  
  // Calculate the border size based on the prop
  const borderSize = {
    sm: 'border-2',
    md: 'border-2',
    lg: 'border-3',
  }[size];
  
  // Calculate the text size based on the prop
  const textSize = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  }[size];
  
  // Calculate the plus icon size based on the prop
  const plusSize = {
    sm: 16,
    md: 20,
    lg: 24,
  }[size];
  
  // Initialize progress for each story item
  useEffect(() => {
    if (story) {
      setProgress(story.items.map(() => 0));
    }
  }, [story]);
  
  const handleClick = () => {
    if (isCreateButton) {
      if (onCreateClick) {
        onCreateClick();
      } else {
        startCreatingStory();
      }
    } else if (story) {
      openStory(story.id);
    }
  };
  
  // Get the thumbnail image
  const getThumbnail = () => {
    if (isCreateButton) {
      return null;
    }
    
    if (!story || story.items.length === 0) {
      return null;
    }
    
    // Use the first unseen item as the thumbnail, or the last item if all are seen
    const unseenItem = story.items.find((item) => !item.seen);
    const thumbnailItem = unseenItem || story.items[story.items.length - 1];
    
    return thumbnailItem.thumbnailUrl || thumbnailItem.url;
  };
  
  const thumbnail = getThumbnail();
  
  // Create story ring segments
  const createRingSegments = () => {
    if (!story || story.items.length === 0) {
      return null;
    }
    
    const segmentCount = story.items.length;
    const segmentAngle = 360 / segmentCount;
    const gap = 2; // Gap in degrees between segments
    const adjustedSegmentAngle = segmentAngle - gap;
    
    return (
      <svg className="absolute inset-0 w-full h-full -rotate-90">
        {story.items.map((item, index) => {
          const startAngle = index * segmentAngle;
          const endAngle = startAngle + adjustedSegmentAngle;
          
          // Convert angles to radians and calculate coordinates
          const startRad = (startAngle * Math.PI) / 180;
          const endRad = (endAngle * Math.PI) / 180;
          
          const x1 = 50 + 45 * Math.cos(startRad);
          const y1 = 50 + 45 * Math.sin(startRad);
          const x2 = 50 + 45 * Math.cos(endRad);
          const y2 = 50 + 45 * Math.sin(endRad);
          
          // Determine if this segment should use the large arc flag
          const largeArcFlag = adjustedSegmentAngle > 180 ? 1 : 0;
          
          // Create the path for the segment
          const d = `
            M 50 50
            L ${x1} ${y1}
            A 45 45 0 ${largeArcFlag} 1 ${x2} ${y2}
            L 50 50
          `;
          
          return (
            <path
              key={index}
              d={d}
              fill="none"
              stroke={item.seen ? '#D1D5DB' : '#10B981'}
              strokeWidth="2"
              strokeLinecap="round"
            />
          );
        })}
      </svg>
    );
  };
  
  return (
    <div className="flex flex-col items-center">
      <button
        onClick={handleClick}
        className={`relative ${sizeClass} rounded-full overflow-hidden focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2`}
      >
        {/* Story ring */}
        {!isCreateButton && story && (
          <div
            className={`absolute inset-0 rounded-full ${
              story.hasUnseen ? 'bg-gradient-to-tr from-green-500 to-blue-500' : 'bg-gray-300'
            }`}
          >
            {createRingSegments()}
          </div>
        )}
        
        {/* Create button or story thumbnail */}
        <div
          className={`absolute inset-1 rounded-full overflow-hidden ${
            isCreateButton ? 'bg-gray-100' : 'bg-white'
          } ${borderSize} border-white flex items-center justify-center`}
        >
          {isCreateButton ? (
            <div className="bg-green-500 rounded-full p-1 text-white">
              <Plus size={plusSize} />
            </div>
          ) : (
            thumbnail && (
              <img
                src={thumbnail}
                alt={`${story?.user.name}'s story`}
                className="w-full h-full object-cover"
              />
            )
          )}
        </div>
      </button>
      
      {/* Username or "Your Story" text */}
      <span className={`mt-1 ${textSize} text-center truncate max-w-[80px]`}>
        {isCreateButton
          ? 'Your Story'
          : story?.user.name === 'You'
          ? 'Your Story'
          : story?.user.name}
      </span>
    </div>
  );
}
