import { useState, useRef, useEffect } from 'react';
import { X, Camera, Video, Image, Clock, Loader2, Check, Trash2 } from 'lucide-react';
import { useStoriesStore } from '../../store/stories';

interface StoryCreatorProps {
  onClose?: () => void;
}

export function StoryCreator({ onClose }: StoryCreatorProps) {
  const { createNewStory, cancelCreatingStory } = useStoriesStore();
  
  const [activeTab, setActiveTab] = useState<'camera' | 'gallery'>('camera');
  const [mediaType, setMediaType] = useState<'image' | 'video'>('image');
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [previewMedia, setPreviewMedia] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [expiresIn, setExpiresIn] = useState(24); // Default 24 hours
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const recordingIntervalRef = useRef<number | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  
  // Initialize camera when component mounts
  useEffect(() => {
    if (activeTab === 'camera') {
      initCamera();
    }
    
    return () => {
      stopCamera();
    };
  }, [activeTab]);
  
  // Update recording time
  useEffect(() => {
    if (isRecording) {
      recordingIntervalRef.current = window.setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } else if (recordingIntervalRef.current) {
      clearInterval(recordingIntervalRef.current);
    }
    
    return () => {
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
      }
    };
  }, [isRecording]);
  
  // Initialize camera
  const initCamera = async () => {
    try {
      const constraints = {
        video: {
          facingMode: 'user',
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
        audio: mediaType === 'video',
      };
      
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
    }
  };
  
  // Stop camera
  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
    }
    
    if (recordingIntervalRef.current) {
      clearInterval(recordingIntervalRef.current);
    }
    
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }
  };
  
  // Capture image from camera
  const captureImage = () => {
    if (videoRef.current && canvasRef.current && streamRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        
        // Convert canvas to blob
        canvas.toBlob((blob) => {
          if (blob) {
            const file = new File([blob], `story-image-${Date.now()}.jpg`, {
              type: 'image/jpeg',
            });
            
            setSelectedFile(file);
            setPreviewMedia(URL.createObjectURL(blob));
          }
        }, 'image/jpeg', 0.9);
      }
    }
  };
  
  // Start recording video
  const startRecording = () => {
    if (streamRef.current) {
      chunksRef.current = [];
      const mediaRecorder = new MediaRecorder(streamRef.current);
      
      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunksRef.current.push(e.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'video/mp4' });
        const file = new File([blob], `story-video-${Date.now()}.mp4`, {
          type: 'video/mp4',
        });
        
        setSelectedFile(file);
        setPreviewMedia(URL.createObjectURL(blob));
        setDuration(recordingTime);
      };
      
      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setIsRecording(true);
      setRecordingTime(0);
    }
  };
  
  // Stop recording video
  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };
  
  // Handle file selection from gallery
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    
    if (files && files.length > 0) {
      const file = files[0];
      const isVideo = file.type.startsWith('video/');
      
      setMediaType(isVideo ? 'video' : 'image');
      setSelectedFile(file);
      setPreviewMedia(URL.createObjectURL(file));
      
      // Get video duration if it's a video
      if (isVideo) {
        const video = document.createElement('video');
        video.preload = 'metadata';
        
        video.onloadedmetadata = () => {
          setDuration(Math.round(video.duration));
          URL.revokeObjectURL(video.src);
        };
        
        video.src = URL.createObjectURL(file);
      }
    }
  };
  
  // Upload story
  const uploadStory = async () => {
    if (!selectedFile) return;
    
    setIsUploading(true);
    setUploadProgress(0);
    
    try {
      // Simulate upload progress
      const interval = setInterval(() => {
        setUploadProgress((prev) => {
          const newProgress = prev + 10;
          if (newProgress >= 100) {
            clearInterval(interval);
            return 100;
          }
          return newProgress;
        });
      }, 300);
      
      // Create story
      await createNewStory({
        type: mediaType,
        file: selectedFile,
        duration: mediaType === 'video' ? duration : undefined,
        expiresIn,
      });
      
      clearInterval(interval);
      
      // Close the creator
      if (onClose) {
        onClose();
      } else {
        cancelCreatingStory();
      }
    } catch (error) {
      console.error('Error creating story:', error);
      setIsUploading(false);
    }
  };
  
  // Format time in MM:SS format
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Reset preview and selected file
  const resetPreview = () => {
    if (previewMedia) {
      URL.revokeObjectURL(previewMedia);
    }
    
    setPreviewMedia(null);
    setSelectedFile(null);
    setDuration(0);
  };
  
  return (
    <div className="fixed inset-0 z-50 bg-black flex flex-col">
      {/* Header */}
      <div className="p-4 flex justify-between items-center">
        <button
          onClick={() => {
            if (onClose) {
              onClose();
            } else {
              cancelCreatingStory();
            }
          }}
          className="text-white p-2 rounded-full hover:bg-gray-800 focus:outline-none"
          aria-label="Close"
        >
          <X size={24} />
        </button>
        
        <h2 className="text-white text-lg font-semibold">Create Story</h2>
        
        <div className="w-10"></div> {/* Spacer for alignment */}
      </div>
      
      {/* Main content */}
      <div className="flex-1 flex flex-col">
        {/* Preview or Camera */}
        <div className="flex-1 flex items-center justify-center bg-gray-900">
          {previewMedia ? (
            // Media preview
            <>
              {mediaType === 'image' ? (
                <img
                  src={previewMedia}
                  alt="Story preview"
                  className="max-w-full max-h-full object-contain"
                />
              ) : (
                <video
                  src={previewMedia}
                  className="max-w-full max-h-full object-contain"
                  controls
                  autoPlay
                  loop
                  muted
                />
              )}
              
              <button
                onClick={resetPreview}
                className="absolute bottom-4 left-4 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 focus:outline-none"
                aria-label="Delete"
              >
                <Trash2 size={20} />
              </button>
            </>
          ) : (
            // Camera or gallery view
            activeTab === 'camera' ? (
              <>
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className="w-full h-full object-cover"
                />
                <canvas ref={canvasRef} className="hidden" />
                
                {isRecording && (
                  <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-3 py-1 rounded-full flex items-center">
                    <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                    {formatTime(recordingTime)}
                  </div>
                )}
              </>
            ) : (
              <div className="text-center p-8">
                <Image size={48} className="text-gray-400 mx-auto mb-4" />
                <p className="text-gray-300 mb-4">Select a photo or video from your gallery</p>
                <label className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 cursor-pointer">
                  Browse Files
                  <input
                    type="file"
                    accept={mediaType === 'image' ? 'image/*' : 'video/*'}
                    className="hidden"
                    onChange={handleFileSelect}
                  />
                </label>
              </div>
            )
          )}
        </div>
        
        {/* Controls */}
        <div className="bg-gray-800 p-4">
          {previewMedia ? (
            // Upload controls
            <div className="flex flex-col space-y-4">
              {/* Expiration time selector */}
              <div className="flex items-center justify-between">
                <div className="flex items-center text-white">
                  <Clock size={18} className="mr-2" />
                  <span>Story expires after</span>
                </div>
                
                <select
                  value={expiresIn}
                  onChange={(e) => setExpiresIn(parseInt(e.target.value))}
                  className="bg-gray-700 text-white px-3 py-1 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value={6}>6 hours</option>
                  <option value={12}>12 hours</option>
                  <option value={24}>24 hours</option>
                  <option value={48}>48 hours</option>
                </select>
              </div>
              
              {/* Upload button */}
              <button
                onClick={uploadStory}
                disabled={isUploading}
                className="w-full py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:bg-green-400 flex items-center justify-center"
              >
                {isUploading ? (
                  <>
                    <Loader2 size={20} className="animate-spin mr-2" />
                    Uploading... {uploadProgress}%
                  </>
                ) : (
                  <>
                    <Check size={20} className="mr-2" />
                    Share to Story
                  </>
                )}
              </button>
            </div>
          ) : (
            // Camera/gallery controls
            <div className="flex flex-col space-y-4">
              {/* Tabs */}
              <div className="flex border-b border-gray-700">
                <button
                  onClick={() => setActiveTab('camera')}
                  className={`flex-1 py-2 text-sm font-medium flex items-center justify-center ${
                    activeTab === 'camera'
                      ? 'text-green-500 border-b-2 border-green-500'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <Camera size={18} className="mr-2" />
                  Camera
                </button>
                
                <button
                  onClick={() => setActiveTab('gallery')}
                  className={`flex-1 py-2 text-sm font-medium flex items-center justify-center ${
                    activeTab === 'gallery'
                      ? 'text-green-500 border-b-2 border-green-500'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <Image size={18} className="mr-2" />
                  Gallery
                </button>
              </div>
              
              {/* Media type selector */}
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => setMediaType('image')}
                  className={`px-4 py-2 rounded-full flex items-center ${
                    mediaType === 'image'
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  <Camera size={18} className="mr-2" />
                  Photo
                </button>
                
                <button
                  onClick={() => setMediaType('video')}
                  className={`px-4 py-2 rounded-full flex items-center ${
                    mediaType === 'video'
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  <Video size={18} className="mr-2" />
                  Video
                </button>
              </div>
              
              {/* Capture button */}
              {activeTab === 'camera' && (
                <div className="flex justify-center">
                  {mediaType === 'image' ? (
                    <button
                      onClick={captureImage}
                      className="w-16 h-16 rounded-full border-4 border-white flex items-center justify-center focus:outline-none"
                      aria-label="Take photo"
                    >
                      <div className="w-12 h-12 bg-white rounded-full"></div>
                    </button>
                  ) : (
                    <button
                      onClick={isRecording ? stopRecording : startRecording}
                      className={`w-16 h-16 rounded-full flex items-center justify-center focus:outline-none ${
                        isRecording
                          ? 'bg-red-500 border-4 border-white'
                          : 'border-4 border-white'
                      }`}
                      aria-label={isRecording ? 'Stop recording' : 'Start recording'}
                    >
                      {isRecording ? (
                        <div className="w-8 h-8 bg-white rounded-sm"></div>
                      ) : (
                        <div className="w-12 h-12 bg-red-500 rounded-full"></div>
                      )}
                    </button>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
