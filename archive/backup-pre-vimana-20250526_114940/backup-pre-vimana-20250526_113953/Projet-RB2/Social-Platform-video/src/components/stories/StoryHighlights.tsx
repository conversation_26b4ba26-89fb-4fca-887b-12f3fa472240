import { useState, useEffect } from 'react';
import { Plus, Pencil, MoreHorizontal, X } from 'lucide-react';
import { useStoriesStore } from '../../store/stories';
import { StoryThumbnail } from './StoryThumbnail';
import { Avatar } from '../ui/avatar';
import { Button } from '../ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../ui/dialog';
import { Input } from '../ui/input';
import { Label } from '../ui/label';

interface StoryHighlight {
  id: string;
  title: string;
  coverUrl: string;
  storyIds: string[];
  createdAt: string;
}

interface StoryHighlightsProps {
  userId: string;
  isOwnProfile?: boolean;
}

export function StoryHighlights({ userId, isOwnProfile = false }: StoryHighlightsProps) {
  const { getUserStories, getArchivedStories } = useStoriesStore();
  
  const [highlights, setHighlights] = useState<StoryHighlight[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedHighlight, setSelectedHighlight] = useState<StoryHighlight | null>(null);
  const [newHighlightTitle, setNewHighlightTitle] = useState('');
  const [selectedStories, setSelectedStories] = useState<string[]>([]);
  const [archivedStories, setArchivedStories] = useState<any[]>([]);
  const [selectedCoverUrl, setSelectedCoverUrl] = useState('');
  
  // Fetch highlights and archived stories
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // In a real app, you would fetch highlights from the API
        // For now, we'll use mock data
        const mockHighlights: StoryHighlight[] = [
          {
            id: '1',
            title: 'Yoga Sessions',
            coverUrl: 'https://images.unsplash.com/photo-1545389336-cf090694435e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=764&q=80',
            storyIds: ['story1', 'story2'],
            createdAt: new Date().toISOString(),
          },
          {
            id: '2',
            title: 'Meditation',
            coverUrl: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
            storyIds: ['story3', 'story4'],
            createdAt: new Date().toISOString(),
          },
          {
            id: '3',
            title: 'Wellness Tips',
            coverUrl: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1220&q=80',
            storyIds: ['story5'],
            createdAt: new Date().toISOString(),
          },
        ];
        
        setHighlights(mockHighlights);
        
        if (isOwnProfile) {
          // Fetch archived stories for the user to create highlights
          const archived = await getArchivedStories();
          setArchivedStories(archived);
        }
      } catch (error) {
        console.error('Error fetching highlights:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [userId, isOwnProfile, getArchivedStories]);
  
  // Open create dialog
  const handleCreateHighlight = () => {
    setNewHighlightTitle('');
    setSelectedStories([]);
    setSelectedCoverUrl('');
    setIsCreateDialogOpen(true);
  };
  
  // Open edit dialog
  const handleEditHighlight = (highlight: StoryHighlight) => {
    setSelectedHighlight(highlight);
    setNewHighlightTitle(highlight.title);
    setSelectedStories(highlight.storyIds);
    setSelectedCoverUrl(highlight.coverUrl);
    setIsEditDialogOpen(true);
  };
  
  // Save new highlight
  const handleSaveNewHighlight = () => {
    if (!newHighlightTitle.trim() || selectedStories.length === 0) return;
    
    // In a real app, you would send this to the API
    const newHighlight: StoryHighlight = {
      id: `highlight-${Date.now()}`,
      title: newHighlightTitle,
      coverUrl: selectedCoverUrl || (archivedStories.find(story => story.id === selectedStories[0])?.items[0]?.url || ''),
      storyIds: selectedStories,
      createdAt: new Date().toISOString(),
    };
    
    setHighlights([...highlights, newHighlight]);
    setIsCreateDialogOpen(false);
  };
  
  // Update existing highlight
  const handleUpdateHighlight = () => {
    if (!selectedHighlight || !newHighlightTitle.trim() || selectedStories.length === 0) return;
    
    const updatedHighlight: StoryHighlight = {
      ...selectedHighlight,
      title: newHighlightTitle,
      coverUrl: selectedCoverUrl || selectedHighlight.coverUrl,
      storyIds: selectedStories,
    };
    
    setHighlights(highlights.map(h => h.id === selectedHighlight.id ? updatedHighlight : h));
    setIsEditDialogOpen(false);
  };
  
  // Delete highlight
  const handleDeleteHighlight = () => {
    if (!selectedHighlight) return;
    
    setHighlights(highlights.filter(h => h.id !== selectedHighlight.id));
    setIsEditDialogOpen(false);
  };
  
  // Toggle story selection
  const toggleStorySelection = (storyId: string) => {
    if (selectedStories.includes(storyId)) {
      setSelectedStories(selectedStories.filter(id => id !== storyId));
    } else {
      setSelectedStories([...selectedStories, storyId]);
    }
  };
  
  // Set cover image
  const setCoverImage = (url: string) => {
    setSelectedCoverUrl(url);
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center py-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></div>
      </div>
    );
  }
  
  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold mb-3">Highlights</h3>
      
      <div className="flex space-x-4 overflow-x-auto pb-2">
        {isOwnProfile && (
          <button
            onClick={handleCreateHighlight}
            className="flex flex-col items-center"
          >
            <div className="w-16 h-16 rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center mb-1">
              <Plus size={24} className="text-gray-400" />
            </div>
            <span className="text-xs text-gray-500">New</span>
          </button>
        )}
        
        {highlights.map((highlight) => (
          <div key={highlight.id} className="flex flex-col items-center">
            <button
              className="relative"
              onClick={() => {
                // In a real app, you would open the highlight stories
                console.log('View highlight:', highlight.id);
              }}
            >
              <div className="w-16 h-16 rounded-full border-2 border-gray-200 overflow-hidden">
                <img
                  src={highlight.coverUrl}
                  alt={highlight.title}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {isOwnProfile && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEditHighlight(highlight);
                  }}
                  className="absolute -right-1 -bottom-1 bg-white rounded-full p-1 shadow-md"
                >
                  <Pencil size={12} className="text-gray-600" />
                </button>
              )}
            </button>
            <span className="text-xs mt-1 max-w-16 truncate">{highlight.title}</span>
          </div>
        ))}
      </div>
      
      {/* Create Highlight Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Highlight</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={newHighlightTitle}
                onChange={(e) => setNewHighlightTitle(e.target.value)}
                placeholder="Highlight title"
              />
            </div>
            
            <div className="space-y-2">
              <Label>Cover Image</Label>
              <div className="grid grid-cols-4 gap-2">
                {archivedStories.slice(0, 8).map((story) => (
                  story.items[0] && (
                    <button
                      key={story.id}
                      onClick={() => setCoverImage(story.items[0].url)}
                      className={`relative rounded-md overflow-hidden h-16 ${
                        selectedCoverUrl === story.items[0].url ? 'ring-2 ring-green-500' : ''
                      }`}
                    >
                      <img
                        src={story.items[0].url}
                        alt="Cover option"
                        className="w-full h-full object-cover"
                      />
                    </button>
                  )
                ))}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Select Stories</Label>
              <div className="max-h-60 overflow-y-auto space-y-2 border rounded-md p-2">
                {archivedStories.length === 0 ? (
                  <p className="text-sm text-gray-500">No archived stories available</p>
                ) : (
                  archivedStories.map((story) => (
                    <div
                      key={story.id}
                      className={`flex items-center p-2 rounded-md ${
                        selectedStories.includes(story.id) ? 'bg-gray-100' : ''
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={selectedStories.includes(story.id)}
                        onChange={() => toggleStorySelection(story.id)}
                        className="mr-2"
                      />
                      <div className="w-10 h-10 rounded-md overflow-hidden mr-2">
                        <img
                          src={story.items[0]?.url || ''}
                          alt="Story thumbnail"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="text-sm">
                        <p className="font-medium">Story from {new Date(story.createdAt).toLocaleDateString()}</p>
                        <p className="text-gray-500">{story.items.length} items</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveNewHighlight} disabled={!newHighlightTitle.trim() || selectedStories.length === 0}>
              Create Highlight
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Highlight Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Highlight</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-title">Title</Label>
              <Input
                id="edit-title"
                value={newHighlightTitle}
                onChange={(e) => setNewHighlightTitle(e.target.value)}
                placeholder="Highlight title"
              />
            </div>
            
            <div className="space-y-2">
              <Label>Cover Image</Label>
              <div className="grid grid-cols-4 gap-2">
                {archivedStories.slice(0, 8).map((story) => (
                  story.items[0] && (
                    <button
                      key={story.id}
                      onClick={() => setCoverImage(story.items[0].url)}
                      className={`relative rounded-md overflow-hidden h-16 ${
                        selectedCoverUrl === story.items[0].url ? 'ring-2 ring-green-500' : ''
                      }`}
                    >
                      <img
                        src={story.items[0].url}
                        alt="Cover option"
                        className="w-full h-full object-cover"
                      />
                    </button>
                  )
                ))}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Select Stories</Label>
              <div className="max-h-60 overflow-y-auto space-y-2 border rounded-md p-2">
                {archivedStories.length === 0 ? (
                  <p className="text-sm text-gray-500">No archived stories available</p>
                ) : (
                  archivedStories.map((story) => (
                    <div
                      key={story.id}
                      className={`flex items-center p-2 rounded-md ${
                        selectedStories.includes(story.id) ? 'bg-gray-100' : ''
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={selectedStories.includes(story.id)}
                        onChange={() => toggleStorySelection(story.id)}
                        className="mr-2"
                      />
                      <div className="w-10 h-10 rounded-md overflow-hidden mr-2">
                        <img
                          src={story.items[0]?.url || ''}
                          alt="Story thumbnail"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="text-sm">
                        <p className="font-medium">Story from {new Date(story.createdAt).toLocaleDateString()}</p>
                        <p className="text-gray-500">{story.items.length} items</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
          
          <DialogFooter className="flex justify-between">
            <Button variant="destructive" onClick={handleDeleteHighlight}>
              Delete
            </Button>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateHighlight} disabled={!newHighlightTitle.trim() || selectedStories.length === 0}>
                Save Changes
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
