export interface Video {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl: string;
  duration: string;
  author: User;
  likes: number;
  comments: number;
  views: number;
  createdAt: string;
  tags?: string[];
  category: string
}

export interface User {
  id: string;
  name: string;
  username: string;
  avatar: string;
  bio?: string;
  followersCount: number;
  followingCount: number;
  isFollowing: boolean;
}

export interface Comment {
  id: string;
  content: string;
  author: User;
  createdAt: string;
  likes: number;
  replies?: Comment[]
}

export interface VideoFilter {
  category?: string;
  duration?: string;
  sortBy?: 'recent' | 'popular' | 'liked' | 'commented';
  dateRange?: 'all' | 'today' | 'week' | 'month' | 'year';
  tags?: string[];
  searchQuery?: string
}

export interface VideoStats {
  views: number;
  likes: number;
  comments: number;
  shares: number
}

export interface VideoCategory {
  id: string;
  name: string;
  description: string;
  imageUrl?: string;
  videoCount: number
}

export interface Post {
  id: string;
  videoUrl: string;
  user: User;
  caption: string;
  hashtags?: string[];
  likes: number;
  comments: number;
  hasLiked: boolean;
  createdAt: string;
  // Add any other relevant fields for a Post
}
