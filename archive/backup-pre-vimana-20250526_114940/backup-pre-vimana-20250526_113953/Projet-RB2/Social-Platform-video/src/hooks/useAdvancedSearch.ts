import { useState, useEffect, useCallback } from 'react';
import { Category } from '../components/search/CategoryFilter';
import { Location } from '../components/search/LocationFilter';
import { DateRange } from '../components/search/DateRangeFilter';
import { SortOption } from '../components/search/SortOptions';

export interface SearchFilters {
  query: string;
  categories: string[];
  location: Location | null;
  radius: number;
  dateRange: DateRange;
  sortBy: string;
  page: number;
  limit: number;
  [key: string]: any; // Allow for additional custom filters
}

export interface SearchResult<T> {
  items: T[];
  totalCount: number;
  page: number;
  totalPages: number;
  isLoading: boolean;
  error: string | null;
}

interface UseAdvancedSearchOptions<T> {
  initialFilters?: Partial<SearchFilters>;
  searchFunction: (filters: SearchFilters) => Promise<{ items: T[]; totalCount: number }>;
  debounceTime?: number;
  autoSearch?: boolean;
}

export function useAdvancedSearch<T>({
  initialFilters = {},
  searchFunction,
  debounceTime = 300,
  autoSearch = true
}: UseAdvancedSearchOptions<T>) {
  // Default filters
  const defaultFilters: SearchFilters = {
    query: '',
    categories: [],
    location: null,
    radius: 50,
    dateRange: { startDate: null, endDate: null },
    sortBy: 'relevance',
    page: 1,
    limit: 20
  };
  
  // Merge default filters with initial filters
  const [filters, setFilters] = useState<SearchFilters>({
    ...defaultFilters,
    ...initialFilters
  });
  
  // Search results state
  const [results, setResults] = useState<SearchResult<T>>({
    items: [],
    totalCount: 0,
    page: 1,
    totalPages: 0,
    isLoading: false,
    error: null
  });
  
  // Track if this is the initial load
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  
  // Perform search
  const search = useCallback(async (searchFilters: SearchFilters = filters) => {
    setResults(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const { items, totalCount } = await searchFunction(searchFilters);
      const totalPages = Math.ceil(totalCount / searchFilters.limit);
      
      setResults({
        items,
        totalCount,
        page: searchFilters.page,
        totalPages,
        isLoading: false,
        error: null
      });
    } catch (error) {
      console.error('Search error:', error);
      setResults(prev => ({
        ...prev,
        isLoading: false,
        error: 'An error occurred while searching. Please try again.'
      }));
    }
  }, [filters, searchFunction]);
  
  // Update filters and trigger search
  const updateFilters = useCallback((newFilters: Partial<SearchFilters>, shouldSearch = true) => {
    // If updating page, don't reset to page 1
    const resetPage = !('page' in newFilters);
    
    setFilters(prevFilters => {
      const updatedFilters = {
        ...prevFilters,
        ...newFilters,
        // Reset to page 1 when changing filters (unless explicitly changing page)
        ...(resetPage ? { page: 1 } : {})
      };
      
      return updatedFilters;
    });
    
    // Set initial load to false after first filter update
    if (isInitialLoad) {
      setIsInitialLoad(false);
    }
  }, [isInitialLoad]);
  
  // Reset filters to initial state
  const resetFilters = useCallback(() => {
    setFilters({
      ...defaultFilters,
      ...initialFilters
    });
  }, [initialFilters]);
  
  // Load next page
  const loadNextPage = useCallback(() => {
    if (results.page < results.totalPages) {
      updateFilters({ page: results.page + 1 });
    }
  }, [results.page, results.totalPages, updateFilters]);
  
  // Load previous page
  const loadPreviousPage = useCallback(() => {
    if (results.page > 1) {
      updateFilters({ page: results.page - 1 });
    }
  }, [results.page, updateFilters]);
  
  // Update query
  const updateQuery = useCallback((query: string) => {
    updateFilters({ query });
  }, [updateFilters]);
  
  // Update categories
  const updateCategories = useCallback((categories: string[]) => {
    updateFilters({ categories });
  }, [updateFilters]);
  
  // Update location
  const updateLocation = useCallback((location: Location | null) => {
    updateFilters({ location });
  }, [updateFilters]);
  
  // Update radius
  const updateRadius = useCallback((radius: number) => {
    updateFilters({ radius });
  }, [updateFilters]);
  
  // Update date range
  const updateDateRange = useCallback((dateRange: DateRange) => {
    updateFilters({ dateRange });
  }, [updateFilters]);
  
  // Update sort option
  const updateSortBy = useCallback((sortBy: string) => {
    updateFilters({ sortBy });
  }, [updateFilters]);
  
  // Update results per page
  const updateLimit = useCallback((limit: number) => {
    updateFilters({ limit, page: 1 });
  }, [updateFilters]);
  
  // Debounced search effect
  useEffect(() => {
    if (!autoSearch && isInitialLoad) {
      return;
    }
    
    const handler = setTimeout(() => {
      search(filters);
    }, debounceTime);
    
    return () => {
      clearTimeout(handler);
    };
  }, [filters, search, debounceTime, autoSearch, isInitialLoad]);
  
  // Manually trigger search
  const executeSearch = useCallback(() => {
    search(filters);
    setIsInitialLoad(false);
  }, [filters, search]);
  
  return {
    filters,
    results,
    updateFilters,
    resetFilters,
    loadNextPage,
    loadPreviousPage,
    updateQuery,
    updateCategories,
    updateLocation,
    updateRadius,
    updateDateRange,
    updateSortBy,
    updateLimit,
    executeSearch,
    isInitialLoad
  };
}

// Mock search function for videos
export const mockVideoSearch = async (filters: SearchFilters) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  // Generate mock results
  const mockItems = Array.from({ length: filters.limit }).map((_, index) => {
    const itemIndex = (filters.page - 1) * filters.limit + index + 1;
    
    return {
      id: `video-${itemIndex}`,
      title: `Video ${itemIndex} ${filters.query ? `matching "${filters.query}"` : ''}`,
      description: `This is a sample video description for video ${itemIndex}.`,
      thumbnailUrl: `https://picsum.photos/seed/video${itemIndex}/300/200`,
      duration: Math.floor(Math.random() * 600) + 60, // 1-10 minutes in seconds
      views: Math.floor(Math.random() * 1000000),
      likes: Math.floor(Math.random() * 50000),
      comments: Math.floor(Math.random() * 5000),
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(), // Random date in last 30 days
      creator: {
        id: `user-${Math.floor(Math.random() * 100) + 1}`,
        name: `Creator ${Math.floor(Math.random() * 100) + 1}`,
        avatarUrl: `https://i.pravatar.cc/150?u=user-${Math.floor(Math.random() * 100) + 1}`
      },
      categories: filters.categories.length > 0 
        ? filters.categories 
        : [`category-${Math.floor(Math.random() * 10) + 1}`]
    };
  });
  
  // Apply filters for demonstration
  let filteredItems = [...mockItems];
  
  // Filter by query
  if (filters.query) {
    filteredItems = filteredItems.filter(item => 
      item.title.toLowerCase().includes(filters.query.toLowerCase()) ||
      item.description.toLowerCase().includes(filters.query.toLowerCase())
    );
  }
  
  // Filter by categories
  if (filters.categories.length > 0) {
    filteredItems = filteredItems.filter(item => 
      item.categories.some(category => filters.categories.includes(category))
    );
  }
  
  // Filter by date range
  if (filters.dateRange.startDate || filters.dateRange.endDate) {
    filteredItems = filteredItems.filter(item => {
      const itemDate = new Date(item.createdAt);
      
      if (filters.dateRange.startDate && filters.dateRange.endDate) {
        return itemDate >= filters.dateRange.startDate && itemDate <= filters.dateRange.endDate;
      } else if (filters.dateRange.startDate) {
        return itemDate >= filters.dateRange.startDate;
      } else if (filters.dateRange.endDate) {
        return itemDate <= filters.dateRange.endDate;
      }
      
      return true;
    });
  }
  
  // Sort results
  switch (filters.sortBy) {
    case 'latest':
      filteredItems.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      break;
    case 'popular':
      filteredItems.sort((a, b) => b.views - a.views);
      break;
    case 'trending':
      // For trending, we'll use a combination of recency and views
      filteredItems.sort((a, b) => {
        const aScore = b.views / (Date.now() - new Date(a.createdAt).getTime());
        const bScore = a.views / (Date.now() - new Date(b.createdAt).getTime());
        return bScore - aScore;
      });
      break;
    case 'views':
      filteredItems.sort((a, b) => b.views - a.views);
      break;
    case 'likes':
      filteredItems.sort((a, b) => b.likes - a.likes);
      break;
    case 'comments':
      filteredItems.sort((a, b) => b.comments - a.comments);
      break;
    default:
      // Default to relevance (no specific sorting)
      break;
  }
  
  return {
    items: filteredItems,
    totalCount: 100 // Mock total count
  };
};
