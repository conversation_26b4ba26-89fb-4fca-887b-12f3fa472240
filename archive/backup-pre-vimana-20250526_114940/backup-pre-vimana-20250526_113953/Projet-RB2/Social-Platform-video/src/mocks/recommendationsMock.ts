import { RecommendationItem } from '../api/recommendationApi';

// Helper function to generate a random number between min and max
const randomNumber = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// Helper function to generate a random date within the last 30 days
const randomDate = () => {
  const now = new Date();
  const daysAgo = randomNumber(0, 30);
  now.setDate(now.getDate() - daysAgo);
  return now.toISOString();
};

// Sample creators
const creators = [
  {
    id: 'creator1',
    name: '<PERSON>',
    username: 'yogamaster',
    avatarUrl: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400',
    isVerified: true,
  },
  {
    id: 'creator2',
    name: '<PERSON>',
    username: 'meditationguide',
    avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
    isVerified: false,
  },
  {
    id: 'creator3',
    name: '<PERSON>',
    username: 'mindfulem<PERSON>',
    avatarUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',
    isVerified: true,
  },
  {
    id: 'creator4',
    name: '<PERSON> Johnson',
    username: 'naturelover',
    avatarUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400',
    isVerified: false,
  },
  {
    id: 'creator5',
    name: 'Olivia Brown',
    username: 'wellnessguru',
    avatarUrl: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400',
    isVerified: true,
  },
];

// Sample categories
const categories = [
  'Yoga',
  'Meditation',
  'Fitness',
  'Nutrition',
  'Wellness',
  'Mindfulness',
  'Travel',
  'Nature',
  'Spirituality',
  'Health',
];

// Sample tags
const tags = [
  'beginner',
  'advanced',
  'relaxation',
  'stress-relief',
  'morning-routine',
  'evening-routine',
  'quick-session',
  'deep-practice',
  'outdoor',
  'indoor',
  'vegan',
  'gluten-free',
  'weight-loss',
  'strength',
  'flexibility',
];

// Sample recommendation reasons
const recommendationReasons = [
  'Because you watched "Morning Yoga Routine"',
  'Popular in your area',
  'Trending in Wellness',
  'Based on your interests',
  'Because you liked similar content',
  'New from a creator you follow',
  'Recommended for beginners',
  'Highly rated by users like you',
  'Because you searched for "meditation"',
  'You might have missed this',
];

// Generate a random video recommendation
const generateVideoRecommendation = (id: string): RecommendationItem => {
  const creator = creators[randomNumber(0, creators.length - 1)];
  const duration = randomNumber(120, 3600); // 2 minutes to 1 hour
  const views = randomNumber(100, 1000000);
  const likes = Math.floor(views * (randomNumber(1, 10) / 100)); // 1-10% of views
  const comments = Math.floor(likes * (randomNumber(1, 20) / 100)); // 1-20% of likes
  const isNew = Math.random() > 0.8; // 20% chance of being new
  const isTrending = Math.random() > 0.9; // 10% chance of trending
  const isSponsored = Math.random() > 0.95; // 5% chance of being sponsored
  
  // Generate 2-4 random tags
  const videoTags = [];
  const numTags = randomNumber(2, 4);
  for (let i = 0; i < numTags; i++) {
    const tag = tags[randomNumber(0, tags.length - 1)];
    if (!videoTags.includes(tag)) {
      videoTags.push(tag);
    }
  }
  
  return {
    id,
    type: 'video',
    title: `${categories[randomNumber(0, categories.length - 1)]} Session: ${videoTags.join(' & ')} Practice`,
    description: `A ${duration < 600 ? 'short' : duration < 1800 ? 'medium' : 'long'} ${categories[randomNumber(0, categories.length - 1)].toLowerCase()} session focusing on ${videoTags.join(' and ')}.`,
    thumbnailUrl: `https://picsum.photos/seed/${id}/640/360`,
    duration,
    createdAt: randomDate(),
    views,
    likes,
    comments,
    creator,
    tags: videoTags,
    category: categories[randomNumber(0, categories.length - 1)],
    relevanceScore: randomNumber(70, 100),
    isNew,
    isTrending,
    isSponsored,
    recommendationReason: recommendationReasons[randomNumber(0, recommendationReasons.length - 1)],
  };
};

// Generate a random post recommendation
const generatePostRecommendation = (id: string): RecommendationItem => {
  const creator = creators[randomNumber(0, creators.length - 1)];
  const views = randomNumber(50, 500000);
  const likes = Math.floor(views * (randomNumber(1, 15) / 100)); // 1-15% of views
  const comments = Math.floor(likes * (randomNumber(5, 30) / 100)); // 5-30% of likes
  const isNew = Math.random() > 0.7; // 30% chance of being new
  const isTrending = Math.random() > 0.85; // 15% chance of trending
  const isSponsored = Math.random() > 0.95; // 5% chance of being sponsored
  
  // Generate 1-3 random tags
  const postTags = [];
  const numTags = randomNumber(1, 3);
  for (let i = 0; i < numTags; i++) {
    const tag = tags[randomNumber(0, tags.length - 1)];
    if (!postTags.includes(tag)) {
      postTags.push(tag);
    }
  }
  
  return {
    id,
    type: 'post',
    title: `${numTags > 1 ? postTags.join(' & ') + ' Tips' : postTags[0] + ' Guide'}: ${categories[randomNumber(0, categories.length - 1)]} Insights`,
    description: `Learn about ${postTags.join(', ')} in this informative post about ${categories[randomNumber(0, categories.length - 1)].toLowerCase()}.`,
    thumbnailUrl: `https://picsum.photos/seed/${id}/640/360`,
    createdAt: randomDate(),
    views,
    likes,
    comments,
    creator,
    tags: postTags,
    category: categories[randomNumber(0, categories.length - 1)],
    relevanceScore: randomNumber(70, 100),
    isNew,
    isTrending,
    isSponsored,
    recommendationReason: recommendationReasons[randomNumber(0, recommendationReasons.length - 1)],
  };
};

// Generate a random livestream recommendation
const generateLivestreamRecommendation = (id: string): RecommendationItem => {
  const creator = creators[randomNumber(0, creators.length - 1)];
  const views = randomNumber(10, 10000);
  const likes = Math.floor(views * (randomNumber(5, 20) / 100)); // 5-20% of views
  const comments = Math.floor(likes * (randomNumber(10, 50) / 100)); // 10-50% of likes
  const isNew = true; // Livestreams are always new
  const isTrending = Math.random() > 0.7; // 30% chance of trending
  const isSponsored = Math.random() > 0.9; // 10% chance of being sponsored
  
  // Generate 1-2 random tags
  const streamTags = [];
  const numTags = randomNumber(1, 2);
  for (let i = 0; i < numTags; i++) {
    const tag = tags[randomNumber(0, tags.length - 1)];
    if (!streamTags.includes(tag)) {
      streamTags.push(tag);
    }
  }
  
  return {
    id,
    type: 'livestream',
    title: `LIVE: ${creator.name}'s ${categories[randomNumber(0, categories.length - 1)]} Session`,
    description: `Join ${creator.name} for a live ${categories[randomNumber(0, categories.length - 1)].toLowerCase()} session focusing on ${streamTags.join(' and ')}.`,
    thumbnailUrl: `https://picsum.photos/seed/${id}/640/360`,
    createdAt: new Date().toISOString(), // Livestreams are happening now
    views,
    likes,
    comments,
    creator,
    tags: streamTags,
    category: categories[randomNumber(0, categories.length - 1)],
    relevanceScore: randomNumber(80, 100),
    isNew,
    isTrending,
    isSponsored,
    recommendationReason: 'Live now',
  };
};

// Generate a set of recommendations
export const generateRecommendations = (count: number): RecommendationItem[] => {
  const recommendations: RecommendationItem[] = [];
  
  for (let i = 0; i < count; i++) {
    const type = Math.random();
    let recommendation;
    
    if (type < 0.7) {
      // 70% chance of video
      recommendation = generateVideoRecommendation(`video-${i}`);
    } else if (type < 0.9) {
      // 20% chance of post
      recommendation = generatePostRecommendation(`post-${i}`);
    } else {
      // 10% chance of livestream
      recommendation = generateLivestreamRecommendation(`livestream-${i}`);
    }
    
    recommendations.push(recommendation);
  }
  
  return recommendations;
};

// Generate mock recommendation preferences
export const mockRecommendationPreferences = {
  categories: ['Yoga', 'Meditation', 'Wellness'],
  excludedTags: ['weight-loss', 'advanced'],
  excludedCreators: ['creator4'],
  contentPreferences: {
    preferredDuration: 'medium' as 'short' | 'medium' | 'long',
    preferredLanguages: ['en', 'fr'],
    preferNewContent: true,
    preferFollowingContent: true,
  },
};
