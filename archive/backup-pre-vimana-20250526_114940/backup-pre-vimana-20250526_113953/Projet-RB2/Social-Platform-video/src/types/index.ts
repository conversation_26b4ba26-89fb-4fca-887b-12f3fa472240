export interface User {
  id: string;
  username: string;
  name: string;
  avatar: string;
  bio?: string;
  followersCount: number;
  followingCount: number;
  isFollowing: boolean
}

export interface Post {
  id: string;
  user: User;
  videoUrl: string;
  caption: string;
  hashtags: string[];
  likes: number;
  comments: number;
  hasLiked: boolean;
  createdAt: string;
  location?: string
}

export interface Video {
  id: string;
  title: string;
  description?: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration?: string;
  user?: User;
  views?: number;
  likes?: number;
  comments?: number;
  shares?: number;
  createdAt?: string;
  tags?: string[];
  category?: string;
  isPrivate?: boolean;
  isLive?: boolean;
}

export interface Livestream {
  id: string;
  title: string;
  description?: string;
  streamUrl: string;
  thumbnailUrl?: string;
  user?: User;
  status: 'live' | 'scheduled' | 'ended';
  scheduledFor?: string;
  startedAt?: string;
  endedAt?: string;
  duration?: string;
  viewerCount?: number;
  totalViews?: number;
  comments?: number;
  tags?: string[];
  category?: string;
  isPrivate?: boolean;
}

export interface Comment {
  id: string;
  user: User;
  content: string;
  likes: number;
  createdAt: string;
  hasLiked: boolean
}