import { BrowserRouter as Router } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AppRoutes } from './routes';
import { Navigation } from './components/navigation';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className = "min-h-screen bg-gray-50">
          <AppRoutes />
          <Navigation />
        </div>
      </Router>
    </QueryClientProvider>
  );
}

export default App;