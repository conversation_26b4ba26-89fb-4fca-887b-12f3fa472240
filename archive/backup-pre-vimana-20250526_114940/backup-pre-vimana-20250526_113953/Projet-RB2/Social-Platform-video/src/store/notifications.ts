import { create } from 'zustand';
import {
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  deleteAllNotifications,
  getNotificationSettings,
  updateNotificationSettings,
  updateNotificationTypeSettings,
  registerPushToken,
  unregisterPushToken,
  testPushNotification,
  getNotificationStatistics,
  initializeNotificationsSocket,
  disconnectNotificationsSocket,
  subscribeToNotifications,
  subscribeToNotificationCount,
  Notification,
  NotificationType,
  NotificationSettings,
} from '../api/notificationsApi';

interface NotificationsState {
  // Notifications
  notifications: Notification[];
  totalCount: number;
  unreadCount: number;
  currentPage: number;
  totalPages: number;
  
  // Settings
  notificationSettings: NotificationSettings | null;
  
  // Statistics
  statistics: {
    totalCount: number;
    unreadCount: number;
    typeCounts: {
      [key in NotificationType]?: number;
    };
    weeklyStats: Array<{
      date: string;
      count: number;
    }>;
  } | null;
  
  // UI state
  activeFilter: NotificationType | 'all' | 'unread';
  isSocketConnected: boolean;
  isSettingsOpen: boolean;
  
  // Status
  isLoading: boolean;
  error: string | null;
  
  // Actions - Notifications
  fetchNotifications: (page?: number, limit?: number, filter?: NotificationType | 'unread') => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  removeNotification: (notificationId: string) => Promise<void>;
  removeAllNotifications: () => Promise<void>;
  
  // Actions - Settings
  fetchNotificationSettings: () => Promise<void>;
  updateSettings: (settings: Partial<NotificationSettings>) => Promise<void>;
  updateTypeSettings: (type: NotificationType, settings: {
    enabled?: boolean;
    push?: boolean;
    email?: boolean;
    inApp?: boolean;
  }) => Promise<void>;
  
  // Actions - Push Notifications
  registerPushToken: (token: string, deviceType: 'ios' | 'android' | 'web') => Promise<void>;
  unregisterPushToken: (token: string) => Promise<void>;
  sendTestPushNotification: () => Promise<void>;
  
  // Actions - Statistics
  fetchStatistics: () => Promise<void>;
  
  // Actions - Socket
  initializeSocket: (token: string) => void;
  disconnectSocket: () => void;
  
  // UI Actions
  setActiveFilter: (filter: NotificationType | 'all' | 'unread') => void;
  setSettingsOpen: (isOpen: boolean) => void;
  
  // Add notification (used by socket)
  addNotification: (notification: Notification) => void;
  updateNotificationCount: (counts: { total: number; unread: number }) => void;
}

export const useNotificationsStore = create<NotificationsState>((set, get) => ({
  // Notifications
  notifications: [],
  totalCount: 0,
  unreadCount: 0,
  currentPage: 1,
  totalPages: 1,
  
  // Settings
  notificationSettings: null,
  
  // Statistics
  statistics: null,
  
  // UI state
  activeFilter: 'all',
  isSocketConnected: false,
  isSettingsOpen: false,
  
  // Status
  isLoading: false,
  error: null,
  
  // Actions - Notifications
  fetchNotifications: async (page = 1, limit = 20, filter) => {
    set({ isLoading: true, error: null });
    
    try {
      const { notifications, totalCount, unreadCount, page: currentPage, totalPages } = 
        await getNotifications(page, limit, filter === 'all' ? undefined : filter);
      
      set({
        notifications,
        totalCount,
        unreadCount,
        currentPage,
        totalPages,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch notifications',
      });
    }
  },
  
  markAsRead: async (notificationId) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedNotification = await markNotificationAsRead(notificationId);
      
      set((state) => ({
        notifications: state.notifications.map((notification) =>
          notification.id === notificationId ? updatedNotification : notification
        ),
        unreadCount: Math.max(0, state.unreadCount - 1),
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to mark notification as read',
      });
    }
  },
  
  markAllAsRead: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const { count } = await markAllNotificationsAsRead();
      
      set((state) => ({
        notifications: state.notifications.map((notification) => ({
          ...notification,
          isRead: true,
        })),
        unreadCount: 0,
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to mark all notifications as read',
      });
    }
  },
  
  removeNotification: async (notificationId) => {
    set({ isLoading: true, error: null });
    
    try {
      await deleteNotification(notificationId);
      
      set((state) => {
        const notification = state.notifications.find((n) => n.id === notificationId);
        const unreadCount = notification && !notification.isRead
          ? state.unreadCount - 1
          : state.unreadCount;
        
        return {
          notifications: state.notifications.filter((n) => n.id !== notificationId),
          totalCount: state.totalCount - 1,
          unreadCount: Math.max(0, unreadCount),
          isLoading: false,
        };
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to delete notification',
      });
    }
  },
  
  removeAllNotifications: async () => {
    set({ isLoading: true, error: null });
    
    try {
      await deleteAllNotifications();
      
      set({
        notifications: [],
        totalCount: 0,
        unreadCount: 0,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to delete all notifications',
      });
    }
  },
  
  // Actions - Settings
  fetchNotificationSettings: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const settings = await getNotificationSettings();
      set({ notificationSettings: settings, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch notification settings',
      });
    }
  },
  
  updateSettings: async (settings) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedSettings = await updateNotificationSettings(settings);
      set({ notificationSettings: updatedSettings, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to update notification settings',
      });
    }
  },
  
  updateTypeSettings: async (type, settings) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedSettings = await updateNotificationTypeSettings(type, settings);
      set({ notificationSettings: updatedSettings, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to update ${type} notification settings`,
      });
    }
  },
  
  // Actions - Push Notifications
  registerPushToken: async (token, deviceType) => {
    set({ isLoading: true, error: null });
    
    try {
      await registerPushToken(token, deviceType);
      set({ isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to register push token',
      });
    }
  },
  
  unregisterPushToken: async (token) => {
    set({ isLoading: true, error: null });
    
    try {
      await unregisterPushToken(token);
      set({ isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to unregister push token',
      });
    }
  },
  
  sendTestPushNotification: async () => {
    set({ isLoading: true, error: null });
    
    try {
      await testPushNotification();
      set({ isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to send test push notification',
      });
    }
  },
  
  // Actions - Statistics
  fetchStatistics: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const statistics = await getNotificationStatistics();
      set({ statistics, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch notification statistics',
      });
    }
  },
  
  // Actions - Socket
  initializeSocket: (token) => {
    try {
      const socket = initializeNotificationsSocket(token);
      
      // Subscribe to new notifications
      const unsubscribeNotifications = subscribeToNotifications((notification) => {
        get().addNotification(notification);
      });
      
      // Subscribe to notification count updates
      const unsubscribeCount = subscribeToNotificationCount((counts) => {
        get().updateNotificationCount(counts);
      });
      
      // Set socket connected state
      set({ isSocketConnected: true });
      
      // Clean up on unmount
      return () => {
        unsubscribeNotifications();
        unsubscribeCount();
        disconnectNotificationsSocket();
        set({ isSocketConnected: false });
      };
    } catch (error) {
      console.error('Error initializing socket:', error);
      set({ isSocketConnected: false });
    }
  },
  
  disconnectSocket: () => {
    disconnectNotificationsSocket();
    set({ isSocketConnected: false });
  },
  
  // UI Actions
  setActiveFilter: (filter) => {
    set({ activeFilter: filter });
    get().fetchNotifications(1, 20, filter === 'all' ? undefined : filter);
  },
  
  setSettingsOpen: (isOpen) => {
    set({ isSettingsOpen: isOpen });
    
    if (isOpen && !get().notificationSettings) {
      get().fetchNotificationSettings();
    }
  },
  
  // Add notification (used by socket)
  addNotification: (notification) => {
    set((state) => {
      // Check if notification already exists
      const exists = state.notifications.some((n) => n.id === notification.id);
      
      if (exists) {
        return state;
      }
      
      // Check if notification should be added based on active filter
      const shouldAdd = 
        state.activeFilter === 'all' || 
        (state.activeFilter === 'unread' && !notification.isRead) ||
        state.activeFilter === notification.type;
      
      return {
        notifications: shouldAdd 
          ? [notification, ...state.notifications]
          : state.notifications,
        totalCount: state.totalCount + 1,
        unreadCount: state.unreadCount + (notification.isRead ? 0 : 1),
      };
    });
  },
  
  updateNotificationCount: (counts) => {
    set({
      totalCount: counts.total,
      unreadCount: counts.unread,
    });
  },
}));
