import { create } from 'zustand';
import {
  getAnalyticsOverview,
  getViewsData,
  getEngagementData,
  getAudienceDemographics,
  getContentPerformance,
  getRevenueData,
  getContentItemAnalytics,
  exportAnalyticsData,
  AnalyticsOverview,
  ViewsData,
  EngagementData,
  AudienceDemographics,
  ContentPerformanceItem,
  RevenueData,
} from '../api/analyticsApi';

interface AnalyticsState {
  // Data
  overview: AnalyticsOverview | null;
  viewsData: ViewsData[];
  engagementData: EngagementData[];
  audienceDemographics: AudienceDemographics | null;
  contentPerformance: ContentPerformanceItem[];
  revenueData: RevenueData[];
  
  // Content item analytics
  selectedContentId: string | null;
  selectedContentType: 'video' | 'post' | 'story' | null;
  contentItemAnalytics: {
    views: ViewsData[];
    engagement: EngagementData[];
    audience: AudienceDemographics | null;
    watchTimeDistribution?: Array<{ segment: string; percentage: number }>;
    referrers: Array<{ source: string; percentage: number }>;
  } | null;
  
  // Filters
  timeRange: 'day' | 'week' | 'month' | 'year';
  granularity: 'hour' | 'day' | 'week' | 'month';
  contentTypeFilter: 'all' | 'video' | 'post' | 'story';
  sortBy: 'views' | 'likes' | 'comments' | 'shares' | 'completionRate';
  
  // Status
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchOverview: () => Promise<void>;
  fetchViewsData: () => Promise<void>;
  fetchEngagementData: () => Promise<void>;
  fetchAudienceDemographics: () => Promise<void>;
  fetchContentPerformance: () => Promise<void>;
  fetchRevenueData: () => Promise<void>;
  fetchContentItemAnalytics: (contentId: string, contentType: 'video' | 'post' | 'story') => Promise<void>;
  exportData: (dataType: 'overview' | 'views' | 'engagement' | 'audience' | 'content' | 'revenue', format: 'csv' | 'json' | 'pdf') => Promise<Blob>;
  
  // Filter actions
  setTimeRange: (timeRange: 'day' | 'week' | 'month' | 'year') => void;
  setGranularity: (granularity: 'hour' | 'day' | 'week' | 'month') => void;
  setContentTypeFilter: (contentType: 'all' | 'video' | 'post' | 'story') => void;
  setSortBy: (sortBy: 'views' | 'likes' | 'comments' | 'shares' | 'completionRate') => void;
  
  // Reset
  resetAnalytics: () => void;
}

export const useAnalyticsStore = create<AnalyticsState>((set, get) => ({
  // Data
  overview: null,
  viewsData: [],
  engagementData: [],
  audienceDemographics: null,
  contentPerformance: [],
  revenueData: [],
  
  // Content item analytics
  selectedContentId: null,
  selectedContentType: null,
  contentItemAnalytics: null,
  
  // Filters
  timeRange: 'week',
  granularity: 'day',
  contentTypeFilter: 'all',
  sortBy: 'views',
  
  // Status
  isLoading: false,
  error: null,
  
  // Actions
  fetchOverview: async () => {
    const { timeRange } = get();
    set({ isLoading: true, error: null });
    
    try {
      const overview = await getAnalyticsOverview(timeRange);
      set({ overview, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch analytics overview',
      });
    }
  },
  
  fetchViewsData: async () => {
    const { timeRange, granularity } = get();
    set({ isLoading: true, error: null });
    
    try {
      const viewsData = await getViewsData(timeRange, granularity);
      set({ viewsData, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch views data',
      });
    }
  },
  
  fetchEngagementData: async () => {
    const { timeRange, granularity } = get();
    set({ isLoading: true, error: null });
    
    try {
      const engagementData = await getEngagementData(timeRange, granularity);
      set({ engagementData, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch engagement data',
      });
    }
  },
  
  fetchAudienceDemographics: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const audienceDemographics = await getAudienceDemographics();
      set({ audienceDemographics, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch audience demographics',
      });
    }
  },
  
  fetchContentPerformance: async () => {
    const { timeRange, contentTypeFilter, sortBy } = get();
    set({ isLoading: true, error: null });
    
    try {
      const contentType = contentTypeFilter === 'all' ? undefined : contentTypeFilter;
      const contentPerformance = await getContentPerformance(timeRange, contentType as any, 10, sortBy);
      set({ contentPerformance, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch content performance',
      });
    }
  },
  
  fetchRevenueData: async () => {
    const { timeRange, granularity } = get();
    set({ isLoading: true, error: null });
    
    try {
      const revenueData = await getRevenueData(timeRange, granularity as 'day' | 'week' | 'month');
      set({ revenueData, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch revenue data',
      });
    }
  },
  
  fetchContentItemAnalytics: async (contentId: string, contentType: 'video' | 'post' | 'story') => {
    set({ 
      isLoading: true, 
      error: null,
      selectedContentId: contentId,
      selectedContentType: contentType
    });
    
    try {
      const analytics = await getContentItemAnalytics(contentId, contentType);
      set({ contentItemAnalytics: analytics, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch content item analytics',
      });
    }
  },
  
  exportData: async (dataType, format) => {
    const { timeRange } = get();
    set({ isLoading: true, error: null });
    
    try {
      const blob = await exportAnalyticsData(dataType, timeRange, format);
      set({ isLoading: false });
      return blob;
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to export analytics data',
      });
      throw error;
    }
  },
  
  // Filter actions
  setTimeRange: (timeRange) => {
    set({ timeRange });
    
    // Adjust granularity based on time range
    const { granularity } = get();
    if (timeRange === 'day' && granularity === 'week') {
      set({ granularity: 'hour' });
    } else if (timeRange === 'year' && granularity === 'hour') {
      set({ granularity: 'month' });
    }
    
    // Refresh data with new time range
    get().fetchOverview();
    get().fetchViewsData();
    get().fetchEngagementData();
    get().fetchContentPerformance();
    get().fetchRevenueData();
  },
  
  setGranularity: (granularity) => {
    set({ granularity });
    
    // Refresh data with new granularity
    get().fetchViewsData();
    get().fetchEngagementData();
    get().fetchRevenueData();
  },
  
  setContentTypeFilter: (contentTypeFilter) => {
    set({ contentTypeFilter });
    
    // Refresh content performance with new filter
    get().fetchContentPerformance();
  },
  
  setSortBy: (sortBy) => {
    set({ sortBy });
    
    // Refresh content performance with new sort
    get().fetchContentPerformance();
  },
  
  // Reset
  resetAnalytics: () => {
    set({
      overview: null,
      viewsData: [],
      engagementData: [],
      audienceDemographics: null,
      contentPerformance: [],
      revenueData: [],
      selectedContentId: null,
      selectedContentType: null,
      contentItemAnalytics: null,
      timeRange: 'week',
      granularity: 'day',
      contentTypeFilter: 'all',
      sortBy: 'views',
      isLoading: false,
      error: null,
    });
  },
}));
