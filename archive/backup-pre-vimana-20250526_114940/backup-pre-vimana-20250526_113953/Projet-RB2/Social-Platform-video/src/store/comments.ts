import { create } from 'zustand';
import { Comment } from '../components/comments/CommentItem';
import { 
  getComments, 
  createComment, 
  toggleCommentLike, 
  deleteComment, 
  getCommentReplies 
} from '../api/commentApi';

interface CommentsState {
  comments: Record<string, Comment[]>; // postId -> comments
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchComments: (postId: string) => Promise<void>;
  addComment: (postId: string, content: string, parentId?: string) => Promise<void>;
  likeComment: (commentId: string, postId: string) => Promise<void>;
  removeComment: (commentId: string, postId: string) => Promise<void>;
  fetchReplies: (commentId: string, postId: string) => Promise<void>;
}

export const useCommentsStore = create<CommentsState>((set, get) => ({
  comments: {},
  isLoading: false,
  error: null,
  
  fetchComments: async (postId: string) => {
    set({ isLoading: true, error: null });
    try {
      const comments = await getComments(postId);
      set((state) => ({
        comments: {
          ...state.comments,
          [postId]: comments,
        },
        isLoading: false,
      }));
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch comments' 
      });
    }
  },
  
  addComment: async (postId: string, content: string, parentId?: string) => {
    set({ isLoading: true, error: null });
    try {
      const newComment = await createComment({
        postId,
        content,
        parentId,
      });
      
      set((state) => {
        const currentComments = state.comments[postId] || [];
        
        // If it's a reply to a comment
        if (parentId) {
          // Find the parent comment and add the reply
          const updatedComments = currentComments.map((comment) => {
            if (comment.id === parentId) {
              return {
                ...comment,
                replies: [...(comment.replies || []), newComment],
              };
            }
            return comment;
          });
          
          return {
            comments: {
              ...state.comments,
              [postId]: updatedComments,
            },
            isLoading: false,
          };
        }
        
        // If it's a top-level comment
        return {
          comments: {
            ...state.comments,
            [postId]: [...currentComments, newComment],
          },
          isLoading: false,
        };
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to add comment' 
      });
    }
  },
  
  likeComment: async (commentId: string, postId: string) => {
    try {
      const { likes, isLiked } = await toggleCommentLike(commentId);
      
      set((state) => {
        const updateComment = (comment: Comment): Comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              likes,
              isLiked,
            };
          }
          
          if (comment.replies) {
            return {
              ...comment,
              replies: comment.replies.map(updateComment),
            };
          }
          
          return comment;
        };
        
        const currentComments = state.comments[postId] || [];
        const updatedComments = currentComments.map(updateComment);
        
        return {
          comments: {
            ...state.comments,
            [postId]: updatedComments,
          },
        };
      });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to like comment' 
      });
    }
  },
  
  removeComment: async (commentId: string, postId: string) => {
    try {
      await deleteComment(commentId);
      
      set((state) => {
        const removeCommentFromList = (comments: Comment[]): Comment[] => {
          return comments
            .filter((comment) => comment.id !== commentId)
            .map((comment) => {
              if (comment.replies) {
                return {
                  ...comment,
                  replies: removeCommentFromList(comment.replies),
                };
              }
              return comment;
            });
        };
        
        const currentComments = state.comments[postId] || [];
        const updatedComments = removeCommentFromList(currentComments);
        
        return {
          comments: {
            ...state.comments,
            [postId]: updatedComments,
          },
        };
      });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to remove comment' 
      });
    }
  },
  
  fetchReplies: async (commentId: string, postId: string) => {
    try {
      const replies = await getCommentReplies(commentId);
      
      set((state) => {
        const updateCommentWithReplies = (comment: Comment): Comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              replies,
            };
          }
          
          if (comment.replies) {
            return {
              ...comment,
              replies: comment.replies.map(updateCommentWithReplies),
            };
          }
          
          return comment;
        };
        
        const currentComments = state.comments[postId] || [];
        const updatedComments = currentComments.map(updateCommentWithReplies);
        
        return {
          comments: {
            ...state.comments,
            [postId]: updatedComments,
          },
        };
      });
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch replies' 
      });
    }
  },
}));
