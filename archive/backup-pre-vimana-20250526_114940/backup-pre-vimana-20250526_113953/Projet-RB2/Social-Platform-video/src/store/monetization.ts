import { create } from 'zustand';
import {
  getSubscriptionPlans,
  createSubscriptionPlan,
  updateSubscriptionPlan,
  deleteSubscriptionPlan,
  getSubscribers,
  subscribeToCreator,
  cancelSubscription,
  sendTip,
  getTransactions,
  getPaymentMethods,
  addPaymentMethod,
  deletePaymentMethod,
  setDefaultPaymentMethod,
  getPayoutMethods,
  addPayoutMethod,
  deletePayoutMethod,
  setDefaultPayoutMethod,
  requestPayout,
  getEarningsSummary,
  SubscriptionPlan,
  Subscriber,
  Transaction,
  PaymentMethod,
  PayoutMethod,
  EarningsSummary,
} from '../api/monetizationApi';

interface MonetizationState {
  // Subscription plans
  subscriptionPlans: SubscriptionPlan[];
  creatorSubscriptionPlans: SubscriptionPlan[];
  
  // Subscribers
  subscribers: Subscriber[];
  subscribersCount: number;
  subscribersPage: number;
  subscribersTotalPages: number;
  
  // Transactions
  transactions: Transaction[];
  transactionsCount: number;
  transactionsPage: number;
  transactionsTotalPages: number;
  
  // Payment methods
  paymentMethods: PaymentMethod[];
  
  // Payout methods
  payoutMethods: PayoutMethod[];
  
  // Earnings
  earningsSummary: EarningsSummary | null;
  
  // UI state
  isCreatingPlan: boolean;
  isEditingPlan: SubscriptionPlan | null;
  isAddingPaymentMethod: boolean;
  isAddingPayoutMethod: boolean;
  selectedPaymentMethodId: string | null;
  
  // Status
  isLoading: boolean;
  error: string | null;
  
  // Actions - Subscription Plans
  fetchSubscriptionPlans: (creatorId?: string) => Promise<void>;
  createPlan: (plan: Omit<SubscriptionPlan, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updatePlan: (planId: string, updates: Partial<Omit<SubscriptionPlan, 'id' | 'createdAt' | 'updatedAt'>>) => Promise<void>;
  deletePlan: (planId: string) => Promise<void>;
  
  // Actions - Subscribers
  fetchSubscribers: (page?: number, limit?: number) => Promise<void>;
  subscribe: (creatorId: string, planId: string, paymentMethodId: string) => Promise<void>;
  cancelSubscribe: (subscriptionId: string, reason?: string) => Promise<void>;
  
  // Actions - Tips
  sendTip: (creatorId: string, amount: number, currency: string, paymentMethodId: string, message?: string, contentId?: string, contentType?: 'video' | 'post' | 'livestream') => Promise<void>;
  
  // Actions - Transactions
  fetchTransactions: (type?: 'subscription' | 'tip' | 'purchase' | 'payout', status?: 'pending' | 'completed' | 'failed' | 'refunded', page?: number, limit?: number) => Promise<void>;
  
  // Actions - Payment Methods
  fetchPaymentMethods: () => Promise<void>;
  addPaymentMethod: (type: 'card' | 'paypal' | 'bank_account', details: any) => Promise<void>;
  removePaymentMethod: (paymentMethodId: string) => Promise<void>;
  setDefaultPayment: (paymentMethodId: string) => Promise<void>;
  
  // Actions - Payout Methods
  fetchPayoutMethods: () => Promise<void>;
  addPayoutMethod: (type: 'bank_account' | 'paypal', details: any) => Promise<void>;
  removePayoutMethod: (payoutMethodId: string) => Promise<void>;
  setDefaultPayout: (payoutMethodId: string) => Promise<void>;
  requestPayout: (amount: number, currency: string, payoutMethodId: string) => Promise<void>;
  
  // Actions - Earnings
  fetchEarningsSummary: (timeRange?: 'week' | 'month' | 'year' | 'all') => Promise<void>;
  
  // UI Actions
  setCreatingPlan: (isCreating: boolean) => void;
  setEditingPlan: (plan: SubscriptionPlan | null) => void;
  setAddingPaymentMethod: (isAdding: boolean) => void;
  setAddingPayoutMethod: (isAdding: boolean) => void;
  setSelectedPaymentMethod: (paymentMethodId: string | null) => void;
}

export const useMonetizationStore = create<MonetizationState>((set, get) => ({
  // Subscription plans
  subscriptionPlans: [],
  creatorSubscriptionPlans: [],
  
  // Subscribers
  subscribers: [],
  subscribersCount: 0,
  subscribersPage: 1,
  subscribersTotalPages: 1,
  
  // Transactions
  transactions: [],
  transactionsCount: 0,
  transactionsPage: 1,
  transactionsTotalPages: 1,
  
  // Payment methods
  paymentMethods: [],
  
  // Payout methods
  payoutMethods: [],
  
  // Earnings
  earningsSummary: null,
  
  // UI state
  isCreatingPlan: false,
  isEditingPlan: null,
  isAddingPaymentMethod: false,
  isAddingPayoutMethod: false,
  selectedPaymentMethodId: null,
  
  // Status
  isLoading: false,
  error: null,
  
  // Actions - Subscription Plans
  fetchSubscriptionPlans: async (creatorId) => {
    set({ isLoading: true, error: null });
    
    try {
      const plans = await getSubscriptionPlans(creatorId);
      
      if (creatorId) {
        set({ creatorSubscriptionPlans: plans, isLoading: false });
      } else {
        set({ subscriptionPlans: plans, isLoading: false });
      }
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch subscription plans',
      });
    }
  },
  
  createPlan: async (plan) => {
    set({ isLoading: true, error: null });
    
    try {
      await createSubscriptionPlan(plan);
      await get().fetchSubscriptionPlans();
      set({ isCreatingPlan: false, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to create subscription plan',
      });
    }
  },
  
  updatePlan: async (planId, updates) => {
    set({ isLoading: true, error: null });
    
    try {
      await updateSubscriptionPlan(planId, updates);
      await get().fetchSubscriptionPlans();
      set({ isEditingPlan: null, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to update subscription plan',
      });
    }
  },
  
  deletePlan: async (planId) => {
    set({ isLoading: true, error: null });
    
    try {
      await deleteSubscriptionPlan(planId);
      await get().fetchSubscriptionPlans();
      set({ isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to delete subscription plan',
      });
    }
  },
  
  // Actions - Subscribers
  fetchSubscribers: async (page = 1, limit = 20) => {
    set({ isLoading: true, error: null });
    
    try {
      const { subscribers, totalCount, page: currentPage, totalPages } = await getSubscribers(page, limit);
      
      set({
        subscribers,
        subscribersCount: totalCount,
        subscribersPage: currentPage,
        subscribersTotalPages: totalPages,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch subscribers',
      });
    }
  },
  
  subscribe: async (creatorId, planId, paymentMethodId) => {
    set({ isLoading: true, error: null });
    
    try {
      await subscribeToCreator(creatorId, planId, paymentMethodId);
      set({ isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to subscribe to creator',
      });
    }
  },
  
  cancelSubscribe: async (subscriptionId, reason) => {
    set({ isLoading: true, error: null });
    
    try {
      await cancelSubscription(subscriptionId, reason);
      set({ isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to cancel subscription',
      });
    }
  },
  
  // Actions - Tips
  sendTip: async (creatorId, amount, currency, paymentMethodId, message, contentId, contentType) => {
    set({ isLoading: true, error: null });
    
    try {
      await sendTip(creatorId, amount, currency, paymentMethodId, message, contentId, contentType);
      set({ isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to send tip',
      });
    }
  },
  
  // Actions - Transactions
  fetchTransactions: async (type, status, page = 1, limit = 20) => {
    set({ isLoading: true, error: null });
    
    try {
      const { transactions, totalCount, page: currentPage, totalPages } = await getTransactions(type, status, page, limit);
      
      set({
        transactions,
        transactionsCount: totalCount,
        transactionsPage: currentPage,
        transactionsTotalPages: totalPages,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch transactions',
      });
    }
  },
  
  // Actions - Payment Methods
  fetchPaymentMethods: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const paymentMethods = await getPaymentMethods();
      set({ paymentMethods, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch payment methods',
      });
    }
  },
  
  addPaymentMethod: async (type, details) => {
    set({ isLoading: true, error: null });
    
    try {
      await addPaymentMethod(type, details);
      await get().fetchPaymentMethods();
      set({ isAddingPaymentMethod: false, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to add payment method',
      });
    }
  },
  
  removePaymentMethod: async (paymentMethodId) => {
    set({ isLoading: true, error: null });
    
    try {
      await deletePaymentMethod(paymentMethodId);
      await get().fetchPaymentMethods();
      set({ isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to remove payment method',
      });
    }
  },
  
  setDefaultPayment: async (paymentMethodId) => {
    set({ isLoading: true, error: null });
    
    try {
      await setDefaultPaymentMethod(paymentMethodId);
      await get().fetchPaymentMethods();
      set({ isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to set default payment method',
      });
    }
  },
  
  // Actions - Payout Methods
  fetchPayoutMethods: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const payoutMethods = await getPayoutMethods();
      set({ payoutMethods, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch payout methods',
      });
    }
  },
  
  addPayoutMethod: async (type, details) => {
    set({ isLoading: true, error: null });
    
    try {
      await addPayoutMethod(type, details);
      await get().fetchPayoutMethods();
      set({ isAddingPayoutMethod: false, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to add payout method',
      });
    }
  },
  
  removePayoutMethod: async (payoutMethodId) => {
    set({ isLoading: true, error: null });
    
    try {
      await deletePayoutMethod(payoutMethodId);
      await get().fetchPayoutMethods();
      set({ isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to remove payout method',
      });
    }
  },
  
  setDefaultPayout: async (payoutMethodId) => {
    set({ isLoading: true, error: null });
    
    try {
      await setDefaultPayoutMethod(payoutMethodId);
      await get().fetchPayoutMethods();
      set({ isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to set default payout method',
      });
    }
  },
  
  requestPayout: async (amount, currency, payoutMethodId) => {
    set({ isLoading: true, error: null });
    
    try {
      await requestPayout(amount, currency, payoutMethodId);
      await get().fetchTransactions();
      await get().fetchEarningsSummary();
      set({ isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to request payout',
      });
    }
  },
  
  // Actions - Earnings
  fetchEarningsSummary: async (timeRange = 'month') => {
    set({ isLoading: true, error: null });
    
    try {
      const earningsSummary = await getEarningsSummary(timeRange);
      set({ earningsSummary, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch earnings summary',
      });
    }
  },
  
  // UI Actions
  setCreatingPlan: (isCreating) => {
    set({ isCreatingPlan: isCreating });
  },
  
  setEditingPlan: (plan) => {
    set({ isEditingPlan: plan });
  },
  
  setAddingPaymentMethod: (isAdding) => {
    set({ isAddingPaymentMethod: isAdding });
  },
  
  setAddingPayoutMethod: (isAdding) => {
    set({ isAddingPayoutMethod: isAdding });
  },
  
  setSelectedPaymentMethod: (paymentMethodId) => {
    set({ selectedPaymentMethodId: paymentMethodId });
  },
}));
