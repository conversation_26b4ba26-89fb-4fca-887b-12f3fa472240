import { create } from 'zustand';
import { 
  searchContent, 
  getTrendingContent, 
  getCategories, 
  getPopularTags, 
  getRelatedContent,
  searchUsers,
  SearchFilters,
  SearchResult
} from '../api/searchApi';

interface SearchState {
  // Search results
  results: SearchResult[];
  totalResults: number;
  currentPage: number;
  totalPages: number;
  
  // Trending content
  trendingContent: SearchResult[];
  
  // Categories and tags
  categories: Array<{ id: string; name: string; count: number }>;
  popularTags: Array<{ tag: string; count: number }>;
  
  // Related content
  relatedContent: SearchResult[];
  
  // User search results
  userResults: Array<{
    id: string;
    name: string;
    username: string;
    avatar: string;
    bio?: string;
    isFollowing: boolean;
    stats: {
      followers: number;
      following: number;
      posts: number;
    };
  }>;
  
  // Current search filters
  filters: SearchFilters;
  
  // Status
  isLoading: boolean;
  error: string | null;
  
  // Actions
  search: (filters: SearchFilters, page?: number, limit?: number) => Promise<void>;
  fetchTrendingContent: (type?: 'video' | 'post' | 'livestream', category?: string, limit?: number) => Promise<void>;
  fetchCategories: () => Promise<void>;
  fetchPopularTags: (limit?: number) => Promise<void>;
  fetchRelatedContent: (contentId: string, contentType: 'video' | 'post' | 'livestream', limit?: number) => Promise<void>;
  searchForUsers: (query: string, page?: number, limit?: number) => Promise<void>;
  setFilters: (filters: Partial<SearchFilters>) => void;
  resetFilters: () => void;
  loadNextPage: () => Promise<void>;
}

export const useSearchStore = create<SearchState>((set, get) => ({
  // Search results
  results: [],
  totalResults: 0,
  currentPage: 1,
  totalPages: 0,
  
  // Trending content
  trendingContent: [],
  
  // Categories and tags
  categories: [],
  popularTags: [],
  
  // Related content
  relatedContent: [],
  
  // User search results
  userResults: [],
  
  // Current search filters
  filters: {},
  
  // Status
  isLoading: false,
  error: null,
  
  // Actions
  search: async (filters: SearchFilters, page = 1, limit = 20) => {
    set({ isLoading: true, error: null, filters });
    
    try {
      const response = await searchContent(filters, page, limit);
      
      set({
        results: response.results,
        totalResults: response.totalResults,
        currentPage: response.page,
        totalPages: response.totalPages,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to search content',
      });
    }
  },
  
  fetchTrendingContent: async (type, category, limit = 10) => {
    set({ isLoading: true, error: null });
    
    try {
      const trending = await getTrendingContent(type, category, limit);
      
      set({
        trendingContent: trending,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch trending content',
      });
    }
  },
  
  fetchCategories: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const categories = await getCategories();
      
      set({
        categories,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch categories',
      });
    }
  },
  
  fetchPopularTags: async (limit = 20) => {
    set({ isLoading: true, error: null });
    
    try {
      const tags = await getPopularTags(limit);
      
      set({
        popularTags: tags,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch popular tags',
      });
    }
  },
  
  fetchRelatedContent: async (contentId, contentType, limit = 10) => {
    set({ isLoading: true, error: null });
    
    try {
      const related = await getRelatedContent(contentId, contentType, limit);
      
      set({
        relatedContent: related,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch related content',
      });
    }
  },
  
  searchForUsers: async (query, page = 1, limit = 20) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await searchUsers(query, page, limit);
      
      set({
        userResults: response.users,
        totalResults: response.totalResults,
        currentPage: response.page,
        totalPages: response.totalPages,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to search users',
      });
    }
  },
  
  setFilters: (filters: Partial<SearchFilters>) => {
    set((state) => ({
      filters: {
        ...state.filters,
        ...filters,
      },
    }));
  },
  
  resetFilters: () => {
    set({ filters: {} });
  },
  
  loadNextPage: async () => {
    const { currentPage, totalPages, filters } = get();
    
    if (currentPage >= totalPages) return;
    
    set({ isLoading: true, error: null });
    
    try {
      const nextPage = currentPage + 1;
      const response = await searchContent(filters, nextPage);
      
      set((state) => ({
        results: [...state.results, ...response.results],
        totalResults: response.totalResults,
        currentPage: response.page,
        totalPages: response.totalPages,
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load more results',
      });
    }
  },
}));
