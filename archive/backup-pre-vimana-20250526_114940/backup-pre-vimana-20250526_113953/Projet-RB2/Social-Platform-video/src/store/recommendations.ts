import { create } from 'zustand';
import { 
  getPersonalized<PERSON>eed, 
  getSimilar<PERSON>ontent, 
  getRecommendedUsers, 
  getInterestBasedContent,
  getUserInterests,
  updateUserInterests,
  getTrendingTopics,
  getTopicContent
} from '../api/recommendationsApi';
import { SearchResult } from '../api/searchApi';

interface UserInterest {
  id: string;
  name: string;
  score: number;
}

interface TrendingTopic {
  id: string;
  name: string;
  count: number;
  trend: number;
}

interface RecommendedUser {
  id: string;
  name: string;
  username: string;
  avatar: string;
  bio?: string;
  isFollowing: boolean;
  stats: {
    followers: number;
    following: number;
    posts: number;
  };
  matchScore: number;
  matchReason: string;
}

interface RecommendationsState {
  // Personalized feed
  feedItems: SearchResult[];
  feedTotalResults: number;
  feedCurrentPage: number;
  feedTotalPages: number;
  
  // Similar content
  similarContent: SearchResult[];
  
  // Recommended users
  recommendedUsers: RecommendedUser[];
  
  // Interest-based content
  interestBasedContent: SearchResult[];
  interestBasedTotalResults: number;
  interestBasedCurrentPage: number;
  interestBasedTotalPages: number;
  
  // User interests
  userInterests: UserInterest[];
  
  // Trending topics
  trendingTopics: TrendingTopic[];
  
  // Topic content
  topicContent: SearchResult[];
  topicContentTotalResults: number;
  topicContentCurrentPage: number;
  topicContentTotalPages: number;
  currentTopicId: string | null;
  
  // Status
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchPersonalizedFeed: (page?: number, limit?: number) => Promise<void>;
  fetchSimilarContent: (contentId: string, contentType: 'video' | 'post' | 'livestream', limit?: number) => Promise<void>;
  fetchRecommendedUsers: (limit?: number) => Promise<void>;
  fetchInterestBasedContent: (interests: string[], page?: number, limit?: number) => Promise<void>;
  fetchUserInterests: () => Promise<void>;
  saveUserInterests: (interests: Array<{ id: string; score: number }>) => Promise<void>;
  fetchTrendingTopics: (limit?: number) => Promise<void>;
  fetchTopicContent: (topicId: string, page?: number, limit?: number) => Promise<void>;
  loadMoreFeedItems: () => Promise<void>;
  loadMoreInterestBasedContent: () => Promise<void>;
  loadMoreTopicContent: () => Promise<void>;
}

export const useRecommendationsStore = create<RecommendationsState>((set, get) => ({
  // Personalized feed
  feedItems: [],
  feedTotalResults: 0,
  feedCurrentPage: 1,
  feedTotalPages: 0,
  
  // Similar content
  similarContent: [],
  
  // Recommended users
  recommendedUsers: [],
  
  // Interest-based content
  interestBasedContent: [],
  interestBasedTotalResults: 0,
  interestBasedCurrentPage: 1,
  interestBasedTotalPages: 0,
  
  // User interests
  userInterests: [],
  
  // Trending topics
  trendingTopics: [],
  
  // Topic content
  topicContent: [],
  topicContentTotalResults: 0,
  topicContentCurrentPage: 1,
  topicContentTotalPages: 0,
  currentTopicId: null,
  
  // Status
  isLoading: false,
  error: null,
  
  // Actions
  fetchPersonalizedFeed: async (page = 1, limit = 10) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await getPersonalizedFeed(page, limit);
      
      set({
        feedItems: page === 1 ? response.results : [...get().feedItems, ...response.results],
        feedTotalResults: response.totalResults,
        feedCurrentPage: response.page,
        feedTotalPages: response.totalPages,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch personalized feed',
      });
    }
  },
  
  fetchSimilarContent: async (contentId, contentType, limit = 10) => {
    set({ isLoading: true, error: null });
    
    try {
      const similarContent = await getSimilarContent(contentId, contentType, limit);
      
      set({
        similarContent,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch similar content',
      });
    }
  },
  
  fetchRecommendedUsers: async (limit = 10) => {
    set({ isLoading: true, error: null });
    
    try {
      const recommendedUsers = await getRecommendedUsers(limit);
      
      set({
        recommendedUsers,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch recommended users',
      });
    }
  },
  
  fetchInterestBasedContent: async (interests, page = 1, limit = 10) => {
    set({ isLoading: true, error: null });
    
    try {
      const response = await getInterestBasedContent(interests, page, limit);
      
      set({
        interestBasedContent: page === 1 ? response.results : [...get().interestBasedContent, ...response.results],
        interestBasedTotalResults: response.totalResults,
        interestBasedCurrentPage: response.page,
        interestBasedTotalPages: response.totalPages,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch interest-based content',
      });
    }
  },
  
  fetchUserInterests: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const userInterests = await getUserInterests();
      
      set({
        userInterests,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch user interests',
      });
    }
  },
  
  saveUserInterests: async (interests) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedInterests = await updateUserInterests(interests);
      
      set({
        userInterests: updatedInterests,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to save user interests',
      });
    }
  },
  
  fetchTrendingTopics: async (limit = 10) => {
    set({ isLoading: true, error: null });
    
    try {
      const trendingTopics = await getTrendingTopics(limit);
      
      set({
        trendingTopics,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch trending topics',
      });
    }
  },
  
  fetchTopicContent: async (topicId, page = 1, limit = 10) => {
    set({ isLoading: true, error: null, currentTopicId: topicId });
    
    try {
      const response = await getTopicContent(topicId, page, limit);
      
      set({
        topicContent: page === 1 ? response.results : [...get().topicContent, ...response.results],
        topicContentTotalResults: response.totalResults,
        topicContentCurrentPage: response.page,
        topicContentTotalPages: response.totalPages,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch topic content',
      });
    }
  },
  
  loadMoreFeedItems: async () => {
    const { feedCurrentPage, feedTotalPages } = get();
    
    if (feedCurrentPage >= feedTotalPages) return;
    
    await get().fetchPersonalizedFeed(feedCurrentPage + 1);
  },
  
  loadMoreInterestBasedContent: async () => {
    const { interestBasedCurrentPage, interestBasedTotalPages, userInterests } = get();
    
    if (interestBasedCurrentPage >= interestBasedTotalPages) return;
    
    const interestIds = userInterests.map((interest) => interest.id);
    await get().fetchInterestBasedContent(interestIds, interestBasedCurrentPage + 1);
  },
  
  loadMoreTopicContent: async () => {
    const { topicContentCurrentPage, topicContentTotalPages, currentTopicId } = get();
    
    if (topicContentCurrentPage >= topicContentTotalPages || !currentTopicId) return;
    
    await get().fetchTopicContent(currentTopicId, topicContentCurrentPage + 1);
  },
}));
