import { create } from 'zustand';
import { 
  getProfile, 
  updateProfile, 
  uploadProfileImage, 
  getUserPosts, 
  getUserSavedPosts, 
  getUserLikedPosts,
  ProfileData,
  ProfileUpdateData,
  SocialLink
} from '../api/profileApi';

interface ProfileState {
  profile: ProfileData | null;
  userPosts: any[];
  savedPosts: any[];
  likedPosts: any[];
  isLoading: boolean;
  isEditing: boolean;
  error: string | null;
  
  // Actions
  fetchProfile: (userId: string) => Promise<void>;
  updateUserProfile: (userId: string, data: ProfileUpdateData) => Promise<void>;
  uploadImage: (userId: string, imageType: 'avatar' | 'cover', imageData: string) => Promise<string>;
  fetchUserPosts: (userId: string, page?: number, limit?: number) => Promise<void>;
  fetchSavedPosts: (page?: number, limit?: number) => Promise<void>;
  fetchLikedPosts: (page?: number, limit?: number) => Promise<void>;
  setIsEditing: (isEditing: boolean) => void;
}

export const useProfileStore = create<ProfileState>((set, get) => ({
  profile: null,
  userPosts: [],
  savedPosts: [],
  likedPosts: [],
  isLoading: false,
  isEditing: false,
  error: null,
  
  fetchProfile: async (userId: string) => {
    set({ isLoading: true, error: null });
    try {
      const profile = await getProfile(userId);
      set({ profile, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch profile' 
      });
    }
  },
  
  updateUserProfile: async (userId: string, data: ProfileUpdateData) => {
    set({ isLoading: true, error: null });
    try {
      const updatedProfile = await updateProfile(userId, data);
      set({ 
        profile: updatedProfile, 
        isLoading: false,
        isEditing: false
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to update profile' 
      });
    }
  },
  
  uploadImage: async (userId: string, imageType: 'avatar' | 'cover', imageData: string) => {
    try {
      const { url } = await uploadProfileImage(userId, imageType, imageData);
      return url;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  },
  
  fetchUserPosts: async (userId: string, page = 1, limit = 10) => {
    set({ isLoading: true, error: null });
    try {
      const posts = await getUserPosts(userId, page, limit);
      set({ userPosts: posts, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch user posts' 
      });
    }
  },
  
  fetchSavedPosts: async (page = 1, limit = 10) => {
    set({ isLoading: true, error: null });
    try {
      const posts = await getUserSavedPosts(page, limit);
      set({ savedPosts: posts, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch saved posts' 
      });
    }
  },
  
  fetchLikedPosts: async (page = 1, limit = 10) => {
    set({ isLoading: true, error: null });
    try {
      const posts = await getUserLikedPosts(page, limit);
      set({ likedPosts: posts, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch liked posts' 
      });
    }
  },
  
  setIsEditing: (isEditing: boolean) => {
    set({ isEditing });
  },
}));
