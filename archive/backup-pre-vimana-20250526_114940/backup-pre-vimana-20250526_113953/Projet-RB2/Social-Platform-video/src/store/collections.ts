import { create } from 'zustand';
import {
  Collection,
  CollectionItem,
  User,
  CreateCollectionData,
  UpdateCollectionData,
  getUserCollections,
  getCollection,
  createCollection,
  updateCollection,
  deleteCollection,
  addToCollection,
  removeFromCollection,
  getCollectionItems,
  addCollaborator,
  removeCollaborator,
  getCollaborators,
  getShareableLink,
  getPublicCollections,
  reorderCollectionItems,
} from '../api/collectionsApi';

interface CollectionsState {
  // Collections data
  collections: Collection[];
  currentCollection: Collection | null;
  collectionItems: CollectionItem[];
  collaborators: User[];
  publicCollections: Collection[];
  
  // Pagination
  totalItems: number;
  currentPage: number;
  totalPages: number;
  
  // UI state
  isCreatingCollection: boolean;
  isAddingToCollection: boolean;
  selectedContentId: string | null;
  selectedContentType: 'video' | 'post' | 'livestream' | null;
  shareableLink: string | null;
  
  // Status
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchUserCollections: () => Promise<void>;
  fetchCollection: (collectionId: string) => Promise<void>;
  fetchCollectionItems: (collectionId: string, page?: number, limit?: number) => Promise<void>;
  fetchCollaborators: (collectionId: string) => Promise<void>;
  fetchPublicCollections: (page?: number, limit?: number) => Promise<void>;
  
  createNewCollection: (data: CreateCollectionData) => Promise<Collection>;
  updateExistingCollection: (collectionId: string, data: UpdateCollectionData) => Promise<void>;
  deleteExistingCollection: (collectionId: string) => Promise<void>;
  
  addItemToCollection: (collectionId: string, contentId: string, contentType: 'video' | 'post' | 'livestream', notes?: string) => Promise<void>;
  removeItemFromCollection: (collectionId: string, itemId: string) => Promise<void>;
  reorderItems: (collectionId: string, itemIds: string[]) => Promise<void>;
  
  addCollaboratorToCollection: (collectionId: string, userId: string) => Promise<void>;
  removeCollaboratorFromCollection: (collectionId: string, userId: string) => Promise<void>;
  
  generateShareableLink: (collectionId: string) => Promise<void>;
  
  // UI actions
  startCreatingCollection: () => void;
  cancelCreatingCollection: () => void;
  startAddingToCollection: (contentId: string, contentType: 'video' | 'post' | 'livestream') => void;
  cancelAddingToCollection: () => void;
  clearCurrentCollection: () => void;
}

export const useCollectionsStore = create<CollectionsState>((set, get) => ({
  // Collections data
  collections: [],
  currentCollection: null,
  collectionItems: [],
  collaborators: [],
  publicCollections: [],
  
  // Pagination
  totalItems: 0,
  currentPage: 1,
  totalPages: 1,
  
  // UI state
  isCreatingCollection: false,
  isAddingToCollection: false,
  selectedContentId: null,
  selectedContentType: null,
  shareableLink: null,
  
  // Status
  isLoading: false,
  error: null,
  
  // Actions
  fetchUserCollections: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const collections = await getUserCollections();
      set({ collections, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch collections',
      });
    }
  },
  
  fetchCollection: async (collectionId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const collection = await getCollection(collectionId);
      set({ currentCollection: collection, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to fetch collection ${collectionId}`,
      });
    }
  },
  
  fetchCollectionItems: async (collectionId: string, page = 1, limit = 20) => {
    set({ isLoading: true, error: null });
    
    try {
      const { items, totalItems, page: currentPage, totalPages } = await getCollectionItems(
        collectionId,
        page,
        limit
      );
      
      set({
        collectionItems: items,
        totalItems,
        currentPage,
        totalPages,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to fetch items for collection ${collectionId}`,
      });
    }
  },
  
  fetchCollaborators: async (collectionId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const collaborators = await getCollaborators(collectionId);
      set({ collaborators, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to fetch collaborators for collection ${collectionId}`,
      });
    }
  },
  
  fetchPublicCollections: async (page = 1, limit = 20) => {
    set({ isLoading: true, error: null });
    
    try {
      const { collections, totalCollections, page: currentPage, totalPages } = await getPublicCollections(
        page,
        limit
      );
      
      set({
        publicCollections: collections,
        totalItems: totalCollections,
        currentPage,
        totalPages,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch public collections',
      });
    }
  },
  
  createNewCollection: async (data: CreateCollectionData) => {
    set({ isLoading: true, error: null });
    
    try {
      const newCollection = await createCollection(data);
      
      set((state) => ({
        collections: [...state.collections, newCollection],
        isLoading: false,
        isCreatingCollection: false,
      }));
      
      return newCollection;
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to create collection',
      });
      throw error;
    }
  },
  
  updateExistingCollection: async (collectionId: string, data: UpdateCollectionData) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCollection = await updateCollection(collectionId, data);
      
      set((state) => ({
        collections: state.collections.map((c) =>
          c.id === collectionId ? updatedCollection : c
        ),
        currentCollection: state.currentCollection?.id === collectionId
          ? updatedCollection
          : state.currentCollection,
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to update collection ${collectionId}`,
      });
      throw error;
    }
  },
  
  deleteExistingCollection: async (collectionId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      await deleteCollection(collectionId);
      
      set((state) => ({
        collections: state.collections.filter((c) => c.id !== collectionId),
        currentCollection: state.currentCollection?.id === collectionId
          ? null
          : state.currentCollection,
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to delete collection ${collectionId}`,
      });
      throw error;
    }
  },
  
  addItemToCollection: async (
    collectionId: string,
    contentId: string,
    contentType: 'video' | 'post' | 'livestream',
    notes?: string
  ) => {
    set({ isLoading: true, error: null });
    
    try {
      const newItem = await addToCollection(collectionId, contentId, contentType, notes);
      
      set((state) => ({
        collectionItems: [...state.collectionItems, newItem],
        isLoading: false,
        isAddingToCollection: false,
        selectedContentId: null,
        selectedContentType: null,
      }));
      
      // Update the item count in the collection
      if (get().currentCollection?.id === collectionId) {
        set((state) => ({
          currentCollection: state.currentCollection
            ? { ...state.currentCollection, itemCount: (state.currentCollection.itemCount || 0) + 1 }
            : null,
        }));
      }
      
      // Also update in the collections list
      set((state) => ({
        collections: state.collections.map((c) =>
          c.id === collectionId
            ? { ...c, itemCount: (c.itemCount || 0) + 1 }
            : c
        ),
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to add item to collection ${collectionId}`,
      });
      throw error;
    }
  },
  
  removeItemFromCollection: async (collectionId: string, itemId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      await removeFromCollection(collectionId, itemId);
      
      set((state) => ({
        collectionItems: state.collectionItems.filter((item) => item.id !== itemId),
        isLoading: false,
      }));
      
      // Update the item count in the collection
      if (get().currentCollection?.id === collectionId) {
        set((state) => ({
          currentCollection: state.currentCollection
            ? { ...state.currentCollection, itemCount: Math.max(0, (state.currentCollection.itemCount || 0) - 1) }
            : null,
        }));
      }
      
      // Also update in the collections list
      set((state) => ({
        collections: state.collections.map((c) =>
          c.id === collectionId
            ? { ...c, itemCount: Math.max(0, (c.itemCount || 0) - 1) }
            : c
        ),
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to remove item from collection ${collectionId}`,
      });
      throw error;
    }
  },
  
  reorderItems: async (collectionId: string, itemIds: string[]) => {
    set({ isLoading: true, error: null });
    
    try {
      await reorderCollectionItems(collectionId, itemIds);
      
      // Reorder the items in the state
      const reorderedItems = [...get().collectionItems];
      const itemsMap = new Map(reorderedItems.map((item) => [item.id, item]));
      
      const newItems = itemIds
        .map((id) => itemsMap.get(id))
        .filter((item): item is CollectionItem => !!item);
      
      set({
        collectionItems: newItems,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to reorder items in collection ${collectionId}`,
      });
      throw error;
    }
  },
  
  addCollaboratorToCollection: async (collectionId: string, userId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const newCollaborator = await addCollaborator(collectionId, userId);
      
      set((state) => ({
        collaborators: [...state.collaborators, newCollaborator],
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to add collaborator to collection ${collectionId}`,
      });
      throw error;
    }
  },
  
  removeCollaboratorFromCollection: async (collectionId: string, userId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      await removeCollaborator(collectionId, userId);
      
      set((state) => ({
        collaborators: state.collaborators.filter((c) => c.id !== userId),
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to remove collaborator from collection ${collectionId}`,
      });
      throw error;
    }
  },
  
  generateShareableLink: async (collectionId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      const shareableLink = await getShareableLink(collectionId);
      set({ shareableLink, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : `Failed to generate shareable link for collection ${collectionId}`,
      });
      throw error;
    }
  },
  
  // UI actions
  startCreatingCollection: () => {
    set({ isCreatingCollection: true });
  },
  
  cancelCreatingCollection: () => {
    set({ isCreatingCollection: false });
  },
  
  startAddingToCollection: (contentId: string, contentType: 'video' | 'post' | 'livestream') => {
    set({
      isAddingToCollection: true,
      selectedContentId: contentId,
      selectedContentType: contentType,
    });
  },
  
  cancelAddingToCollection: () => {
    set({
      isAddingToCollection: false,
      selectedContentId: null,
      selectedContentType: null,
    });
  },
  
  clearCurrentCollection: () => {
    set({
      currentCollection: null,
      collectionItems: [],
      collaborators: [],
      shareableLink: null,
    });
  },
}));
