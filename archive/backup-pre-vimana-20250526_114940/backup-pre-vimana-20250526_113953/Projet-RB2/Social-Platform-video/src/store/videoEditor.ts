import { create } from 'zustand';
import { 
  VideoMetadata, 
  VideoEditOptions,
  getVideoDetails,
  updateVideoMetadata,
  applyVideoEdits,
  checkVideoProcessingStatus,
  generateVideoThumbnail,
  getAudioTracks,
  getVideoFilters
} from '../api/videoApi';

interface TextOverlay {
  id: string;
  text: string;
  position: { x: number; y: number };
  fontSize: number;
  color: string;
  startTime?: number;
  endTime?: number;
}

interface AudioTrack {
  id: string;
  name: string;
  duration: number;
  url: string;
  volume: number;
  startTime: number;
}

interface VideoFilter {
  id: string;
  name: string;
  preview: string;
  values: {
    brightness?: number;
    contrast?: number;
    saturation?: number;
    blur?: number;
    sepia?: number;
  };
}

interface VideoEditorState {
  // Video data
  videoId: string | null;
  videoMetadata: VideoMetadata | null;
  videoUrl: string | null;
  
  // Edit state
  trimRange: { start: number; end: number } | null;
  selectedFilter: VideoFilter | null;
  customFilterValues: {
    brightness: number;
    contrast: number;
    saturation: number;
    blur: number;
    sepia: number;
  };
  textOverlays: TextOverlay[];
  selectedAudioTrack: AudioTrack | null;
  
  // Available options
  availableFilters: VideoFilter[];
  availableAudioTracks: AudioTrack[];
  
  // Processing state
  isProcessing: boolean;
  processingJobId: string | null;
  processingProgress: number;
  
  // UI state
  currentTab: 'trim' | 'filters' | 'text' | 'audio';
  previewWithEdits: boolean;
  
  // Status
  isLoading: boolean;
  error: string | null;
  
  // Actions
  loadVideo: (videoId: string) => Promise<void>;
  setTrimRange: (start: number, end: number) => void;
  setSelectedFilter: (filterId: string | null) => void;
  updateCustomFilterValues: (values: Partial<VideoEditorState['customFilterValues']>) => void;
  addTextOverlay: (text: string) => void;
  updateTextOverlay: (id: string, updates: Partial<Omit<TextOverlay, 'id'>>) => void;
  removeTextOverlay: (id: string) => void;
  setSelectedAudioTrack: (trackId: string | null) => void;
  updateAudioTrackSettings: (updates: Partial<Pick<AudioTrack, 'volume' | 'startTime'>>) => void;
  setCurrentTab: (tab: VideoEditorState['currentTab']) => void;
  togglePreviewWithEdits: () => void;
  applyEdits: () => Promise<void>;
  generateThumbnail: (timeInSeconds: number) => Promise<string>;
  checkProcessingStatus: () => Promise<void>;
  resetEditor: () => void;
}

export const useVideoEditorStore = create<VideoEditorState>((set, get) => ({
  // Video data
  videoId: null,
  videoMetadata: null,
  videoUrl: null,
  
  // Edit state
  trimRange: null,
  selectedFilter: null,
  customFilterValues: {
    brightness: 100,
    contrast: 100,
    saturation: 100,
    blur: 0,
    sepia: 0,
  },
  textOverlays: [],
  selectedAudioTrack: null,
  
  // Available options
  availableFilters: [],
  availableAudioTracks: [],
  
  // Processing state
  isProcessing: false,
  processingJobId: null,
  processingProgress: 0,
  
  // UI state
  currentTab: 'trim',
  previewWithEdits: true,
  
  // Status
  isLoading: false,
  error: null,
  
  // Actions
  loadVideo: async (videoId: string) => {
    set({ isLoading: true, error: null, videoId });
    
    try {
      // Load video metadata
      const metadata = await getVideoDetails(videoId);
      
      // Set initial trim range to full video
      const trimRange = {
        start: 0,
        end: metadata.duration,
      };
      
      // Load available filters and audio tracks
      const [filters, audioTracks] = await Promise.all([
        getVideoFilters(),
        getAudioTracks(),
      ]);
      
      // Map to our internal format
      const availableFilters = filters.map(filter => ({
        id: filter.id,
        name: filter.name,
        preview: filter.preview,
        values: {}, // This would be populated with actual filter values from the API
      }));
      
      const availableAudioTracks = audioTracks.map(track => ({
        ...track,
        volume: 100,
        startTime: 0,
      }));
      
      set({
        videoMetadata: metadata,
        videoUrl: metadata.videoUrl,
        trimRange,
        availableFilters,
        availableAudioTracks,
        isLoading: false,
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load video',
      });
    }
  },
  
  setTrimRange: (start: number, end: number) => {
    set({ trimRange: { start, end } });
  },
  
  setSelectedFilter: (filterId: string | null) => {
    const { availableFilters } = get();
    
    if (!filterId) {
      set({ selectedFilter: null });
      return;
    }
    
    const filter = availableFilters.find(f => f.id === filterId) || null;
    set({ selectedFilter: filter });
  },
  
  updateCustomFilterValues: (values: Partial<VideoEditorState['customFilterValues']>) => {
    set(state => ({
      customFilterValues: {
        ...state.customFilterValues,
        ...values,
      },
    }));
  },
  
  addTextOverlay: (text: string) => {
    const newOverlay: TextOverlay = {
      id: Date.now().toString(),
      text,
      position: { x: 50, y: 50 }, // Center by default
      fontSize: 24,
      color: '#ffffff',
    };
    
    set(state => ({
      textOverlays: [...state.textOverlays, newOverlay],
    }));
  },
  
  updateTextOverlay: (id: string, updates: Partial<Omit<TextOverlay, 'id'>>) => {
    set(state => ({
      textOverlays: state.textOverlays.map(overlay =>
        overlay.id === id ? { ...overlay, ...updates } : overlay
      ),
    }));
  },
  
  removeTextOverlay: (id: string) => {
    set(state => ({
      textOverlays: state.textOverlays.filter(overlay => overlay.id !== id),
    }));
  },
  
  setSelectedAudioTrack: (trackId: string | null) => {
    const { availableAudioTracks } = get();
    
    if (!trackId) {
      set({ selectedAudioTrack: null });
      return;
    }
    
    const track = availableAudioTracks.find(t => t.id === trackId) || null;
    set({ selectedAudioTrack: track });
  },
  
  updateAudioTrackSettings: (updates: Partial<Pick<AudioTrack, 'volume' | 'startTime'>>) => {
    set(state => {
      if (!state.selectedAudioTrack) return state;
      
      return {
        selectedAudioTrack: {
          ...state.selectedAudioTrack,
          ...updates,
        },
      };
    });
  },
  
  setCurrentTab: (tab: VideoEditorState['currentTab']) => {
    set({ currentTab: tab });
  },
  
  togglePreviewWithEdits: () => {
    set(state => ({ previewWithEdits: !state.previewWithEdits }));
  },
  
  applyEdits: async () => {
    const {
      videoId,
      trimRange,
      selectedFilter,
      customFilterValues,
      textOverlays,
      selectedAudioTrack,
    } = get();
    
    if (!videoId) return;
    
    set({ isProcessing: true, error: null });
    
    try {
      // Prepare edit options
      const editOptions: VideoEditOptions = {};
      
      // Add trim range if set
      if (trimRange) {
        editOptions.trim = trimRange;
      }
      
      // Add filters
      const filterValues = selectedFilter?.values || customFilterValues;
      if (Object.keys(filterValues).length > 0) {
        editOptions.filters = filterValues;
      }
      
      // Add text overlays
      if (textOverlays.length > 0) {
        editOptions.textOverlays = textOverlays.map(({ id, ...rest }) => rest);
      }
      
      // Add audio track
      if (selectedAudioTrack) {
        editOptions.audioTrack = {
          url: selectedAudioTrack.url,
          volume: selectedAudioTrack.volume,
          startTime: selectedAudioTrack.startTime,
        };
      }
      
      // Apply edits
      const { jobId } = await applyVideoEdits(videoId, editOptions);
      
      set({
        processingJobId: jobId,
        processingProgress: 0,
      });
      
      // Start polling for status
      await get().checkProcessingStatus();
    } catch (error) {
      set({
        isProcessing: false,
        error: error instanceof Error ? error.message : 'Failed to apply edits',
      });
    }
  },
  
  generateThumbnail: async (timeInSeconds: number) => {
    const { videoId } = get();
    
    if (!videoId) {
      throw new Error('No video loaded');
    }
    
    try {
      const { thumbnailUrl } = await generateVideoThumbnail(videoId, timeInSeconds);
      return thumbnailUrl;
    } catch (error) {
      throw error;
    }
  },
  
  checkProcessingStatus: async () => {
    const { processingJobId } = get();
    
    if (!processingJobId) return;
    
    try {
      const status = await checkVideoProcessingStatus(processingJobId);
      
      set({
        processingProgress: status.progress || 0,
      });
      
      if (status.status === 'completed' && status.result) {
        set({
          isProcessing: false,
          videoUrl: status.result.videoUrl,
          processingJobId: null,
        });
      } else if (status.status === 'failed') {
        set({
          isProcessing: false,
          error: 'Video processing failed',
          processingJobId: null,
        });
      } else if (status.status === 'pending' || status.status === 'processing') {
        // Poll again in 2 seconds
        setTimeout(() => {
          get().checkProcessingStatus();
        }, 2000);
      }
    } catch (error) {
      set({
        isProcessing: false,
        error: error instanceof Error ? error.message : 'Failed to check processing status',
        processingJobId: null,
      });
    }
  },
  
  resetEditor: () => {
    set({
      trimRange: null,
      selectedFilter: null,
      customFilterValues: {
        brightness: 100,
        contrast: 100,
        saturation: 100,
        blur: 0,
        sepia: 0,
      },
      textOverlays: [],
      selectedAudioTrack: null,
      currentTab: 'trim',
      previewWithEdits: true,
      isProcessing: false,
      processingJobId: null,
      processingProgress: 0,
      error: null,
    });
  },
}));
