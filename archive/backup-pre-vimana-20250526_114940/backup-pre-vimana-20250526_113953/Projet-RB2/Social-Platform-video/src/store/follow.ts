import { create } from 'zustand';
import { User } from '../components/follow/FollowersList';
import { 
  getFollowers, 
  getFollowing, 
  followUser, 
  unfollowUser, 
  getFollowSuggestions,
  blockUser,
  unblockUser,
  getBlockedUsers
} from '../api/followApi';

interface FollowState {
  followers: User[];
  following: User[];
  suggestions: User[];
  blockedUsers: User[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchFollowers: (userId: string) => Promise<void>;
  fetchFollowing: (userId: string) => Promise<void>;
  fetchSuggestions: () => Promise<void>;
  fetchBlockedUsers: () => Promise<void>;
  follow: (userId: string) => Promise<void>;
  unfollow: (userId: string) => Promise<void>;
  block: (userId: string) => Promise<void>;
  unblock: (userId: string) => Promise<void>;
}

export const useFollowStore = create<FollowState>((set, get) => ({
  followers: [],
  following: [],
  suggestions: [],
  blockedUsers: [],
  isLoading: false,
  error: null,
  
  fetchFollowers: async (userId: string) => {
    set({ isLoading: true, error: null });
    try {
      const followers = await getFollowers(userId);
      set({ followers, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch followers' 
      });
    }
  },
  
  fetchFollowing: async (userId: string) => {
    set({ isLoading: true, error: null });
    try {
      const following = await getFollowing(userId);
      set({ following, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch following' 
      });
    }
  },
  
  fetchSuggestions: async () => {
    set({ isLoading: true, error: null });
    try {
      const suggestions = await getFollowSuggestions();
      set({ suggestions, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch suggestions' 
      });
    }
  },
  
  fetchBlockedUsers: async () => {
    set({ isLoading: true, error: null });
    try {
      const blockedUsers = await getBlockedUsers();
      set({ blockedUsers, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch blocked users' 
      });
    }
  },
  
  follow: async (userId: string) => {
    set({ isLoading: true, error: null });
    try {
      await followUser(userId);
      
      // Update following list
      set((state) => ({
        following: state.following.map((user) =>
          user.id === userId ? { ...user, isFollowing: true } : user
        ),
        // Update suggestions list
        suggestions: state.suggestions.map((user) =>
          user.id === userId ? { ...user, isFollowing: true } : user
        ),
        // Update followers list
        followers: state.followers.map((user) =>
          user.id === userId ? { ...user, isFollowing: true } : user
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to follow user' 
      });
    }
  },
  
  unfollow: async (userId: string) => {
    set({ isLoading: true, error: null });
    try {
      await unfollowUser(userId);
      
      // Update following list
      set((state) => ({
        following: state.following.map((user) =>
          user.id === userId ? { ...user, isFollowing: false } : user
        ),
        // Update suggestions list
        suggestions: state.suggestions.map((user) =>
          user.id === userId ? { ...user, isFollowing: false } : user
        ),
        // Update followers list
        followers: state.followers.map((user) =>
          user.id === userId ? { ...user, isFollowing: false } : user
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to unfollow user' 
      });
    }
  },
  
  block: async (userId: string) => {
    set({ isLoading: true, error: null });
    try {
      await blockUser(userId);
      
      // Get the user from any of the lists
      const userToBlock = 
        get().followers.find((user) => user.id === userId) ||
        get().following.find((user) => user.id === userId) ||
        get().suggestions.find((user) => user.id === userId);
      
      if (userToBlock) {
        // Add to blocked users
        set((state) => ({
          blockedUsers: [...state.blockedUsers, userToBlock],
          // Remove from other lists
          followers: state.followers.filter((user) => user.id !== userId),
          following: state.following.filter((user) => user.id !== userId),
          suggestions: state.suggestions.filter((user) => user.id !== userId),
          isLoading: false,
        }));
      } else {
        set({ isLoading: false });
      }
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to block user' 
      });
    }
  },
  
  unblock: async (userId: string) => {
    set({ isLoading: true, error: null });
    try {
      await unblockUser(userId);
      
      // Remove from blocked users
      set((state) => ({
        blockedUsers: state.blockedUsers.filter((user) => user.id !== userId),
        isLoading: false,
      }));
      
      // Refresh suggestions to potentially include the unblocked user
      get().fetchSuggestions();
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to unblock user' 
      });
    }
  },
}));
