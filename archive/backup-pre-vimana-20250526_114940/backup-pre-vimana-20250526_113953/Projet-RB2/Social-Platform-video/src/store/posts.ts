import { create } from 'zustand';
import type { Post } from '@/types';
import { fetchPosts, likePost, unlikePost } from '@/lib/api';

interface PostsStore {
  posts: Post[];
  isLoading: boolean;
  error: string | null;
  fetchPosts: () => Promise<void>;
  toggleLike: (postId: string) => Promise<void>
}

export const usePostsStore = create<PostsStore>((set, get) => ({
  posts: [],
  isLoading: false,
  error: null,
  fetchPosts: async () => {
    set({ isLoading: true, error: null });
    try {
      const posts = await fetchPosts();
      set({ posts, isLoading: false });
    } catch(error) {
      set({ error: 'Failed to fetch posts', isLoading: false });
    }
  },
  toggleLike: async (postId: string) => {
    const posts = get().posts;
    const post = posts.find(p => p.id === postId);
    if(!post) { return; }

    // Optimistic update;
    set({
      posts: posts.map(p =>
        p.id === postId
          ? { ...p, hasLiked: !p.hasLiked, likes: p.likes + (p.hasLiked ? -1 : 1) }
          : p
      )
});

    try {
      if (post.hasLiked) {
        await unlikePost(postId)
      } else {
        await likePost(postId)
      }
    } catch(error) {
      // Revert on error;
      set({
        posts: posts.map(p =>
          p.id === postId
            ? { ...p, hasLiked: !p.hasLiked, likes: p.likes + (p.hasLiked ? 1 : -1) }
            : p
        )
});
    }
  }
}));