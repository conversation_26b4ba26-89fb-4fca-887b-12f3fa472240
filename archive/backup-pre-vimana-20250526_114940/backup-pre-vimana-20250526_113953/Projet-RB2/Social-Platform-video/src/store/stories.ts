import { create } from 'zustand';
import {
  Story,
  StoryItem,
  StoryCreateData,
  StoryHighlight,
  StoryHighlightCreateData,
  getStoriesFeed,
  getUserStories,
  createStory,
  deleteStory,
  markStorySeen,
  getStoryViewers,
  getStoryStats,
  addStoryReaction,
  getStoryReactions,
  getArchivedStories,
  createStoryHighlight,
  getStoryHighlights,
  updateStoryHighlight,
  deleteStoryHighlight,
} from '../api/storiesApi';

interface StoriesState {
  // Stories data
  storiesFeed: Story[];
  currentStory: Story | null;
  currentStoryIndex: number;
  currentItemIndex: number;
  archivedStories: Story[];
  highlights: StoryHighlight[];

  // Story viewers and stats
  viewers: Array<{
    id: string;
    name: string;
    username: string;
    avatar: string;
    seenAt: string;
  }>;
  stats: {
    views: number;
    uniqueViewers: number;
    completionRate: number;
    averageViewDuration: number;
  } | null;
  reactions: Array<{
    reaction: string;
    count: number;
    users: Array<{
      id: string;
      name: string;
      username: string;
      avatar: string;
    }>;
  }>;

  // UI state
  isCreatingStory: boolean;
  isViewerOpen: boolean;
  isPaused: boolean;

  // Status
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchStoriesFeed: () => Promise<void>;
  fetchUserStories: (userId: string) => Promise<void>;
  createNewStory: (data: StoryCreateData) => Promise<void>;
  removeStory: (storyItemId: string) => Promise<void>;
  markAsSeen: (storyItemId: string) => Promise<void>;
  fetchViewers: (storyItemId: string) => Promise<void>;
  fetchStats: (storyItemId: string) => Promise<void>;
  addReaction: (storyItemId: string, reaction: string) => Promise<void>;
  fetchReactions: (storyItemId: string) => Promise<void>;

  // Archived stories
  getArchivedStories: () => Promise<Story[]>;

  // Highlights
  fetchHighlights: (userId: string) => Promise<StoryHighlight[]>;
  createHighlight: (data: StoryHighlightCreateData) => Promise<StoryHighlight>;
  updateHighlight: (highlightId: string, data: Partial<StoryHighlightCreateData>) => Promise<StoryHighlight>;
  deleteHighlight: (highlightId: string) => Promise<void>;

  // Navigation
  openStory: (storyId: string, itemIndex?: number) => void;
  closeStory: () => void;
  nextItem: () => void;
  prevItem: () => void;
  nextStory: () => void;
  prevStory: () => void;
  pauseStory: () => void;
  resumeStory: () => void;

  // Creation
  startCreatingStory: () => void;
  cancelCreatingStory: () => void;
}

export const useStoriesStore = create<StoriesState>((set, get) => ({
  // Stories data
  storiesFeed: [],
  currentStory: null,
  currentStoryIndex: 0,
  currentItemIndex: 0,
  archivedStories: [],
  highlights: [],

  // Story viewers and stats
  viewers: [],
  stats: null,
  reactions: [],

  // UI state
  isCreatingStory: false,
  isViewerOpen: false,
  isPaused: false,

  // Status
  isLoading: false,
  error: null,

  // Actions
  fetchStoriesFeed: async () => {
    set({ isLoading: true, error: null });

    try {
      const stories = await getStoriesFeed();
      set({ storiesFeed: stories, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch stories feed',
      });
    }
  },

  fetchUserStories: async (userId: string) => {
    set({ isLoading: true, error: null });

    try {
      const story = await getUserStories(userId);

      if (story) {
        // Update the story in the feed if it exists
        set((state) => {
          const updatedFeed = state.storiesFeed.map((s) =>
            s.userId === userId ? story : s
          );

          // If the story is not in the feed, add it
          if (!updatedFeed.some((s) => s.userId === userId)) {
            updatedFeed.push(story);
          }

          return {
            storiesFeed: updatedFeed,
            isLoading: false,
          };
        });
      } else {
        // Remove the story from the feed if it doesn't exist
        set((state) => ({
          storiesFeed: state.storiesFeed.filter((s) => s.userId !== userId),
          isLoading: false,
        }));
      }
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch user stories',
      });
    }
  },

  createNewStory: async (data: StoryCreateData) => {
    set({ isLoading: true, error: null });

    try {
      const newStoryItem = await createStory(data);

      // Refresh the user's stories
      await get().fetchStoriesFeed();

      set({
        isLoading: false,
        isCreatingStory: false,
      });

      return newStoryItem;
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to create story',
      });
    }
  },

  removeStory: async (storyItemId: string) => {
    set({ isLoading: true, error: null });

    try {
      await deleteStory(storyItemId);

      // Update the stories feed
      set((state) => {
        const updatedFeed = state.storiesFeed.map((story) => ({
          ...story,
          items: story.items.filter((item) => item.id !== storyItemId),
        }));

        // Remove stories with no items
        const filteredFeed = updatedFeed.filter((story) => story.items.length > 0);

        return {
          storiesFeed: filteredFeed,
          isLoading: false,
        };
      });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to remove story',
      });
    }
  },

  markAsSeen: async (storyItemId: string) => {
    try {
      await markStorySeen(storyItemId);

      // Update the stories feed
      set((state) => {
        const updatedFeed = state.storiesFeed.map((story) => ({
          ...story,
          items: story.items.map((item) =>
            item.id === storyItemId ? { ...item, seen: true } : item
          ),
          hasUnseen: story.items.some((item) => item.id !== storyItemId && !item.seen),
        }));

        return { storiesFeed: updatedFeed };
      });
    } catch (error) {
      console.error('Error marking story as seen:', error);
    }
  },

  fetchViewers: async (storyItemId: string) => {
    set({ isLoading: true, error: null });

    try {
      const viewers = await getStoryViewers(storyItemId);
      set({ viewers, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch story viewers',
      });
    }
  },

  fetchStats: async (storyItemId: string) => {
    set({ isLoading: true, error: null });

    try {
      const stats = await getStoryStats(storyItemId);
      set({ stats, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch story stats',
      });
    }
  },

  addReaction: async (storyItemId: string, reaction: string) => {
    try {
      await addStoryReaction(storyItemId, reaction);
      await get().fetchReactions(storyItemId);
    } catch (error) {
      console.error('Error adding story reaction:', error);
    }
  },

  fetchReactions: async (storyItemId: string) => {
    set({ isLoading: true, error: null });

    try {
      const reactions = await getStoryReactions(storyItemId);
      set({ reactions, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch story reactions',
      });
    }
  },

  // Navigation
  openStory: (storyId: string, itemIndex = 0) => {
    const { storiesFeed } = get();
    const storyIndex = storiesFeed.findIndex((story) => story.id === storyId);

    if (storyIndex !== -1) {
      const story = storiesFeed[storyIndex];
      const validItemIndex = Math.max(0, Math.min(itemIndex, story.items.length - 1));

      set({
        currentStory: story,
        currentStoryIndex: storyIndex,
        currentItemIndex: validItemIndex,
        isViewerOpen: true,
        isPaused: false,
      });

      // Mark the current item as seen
      const currentItem = story.items[validItemIndex];
      if (currentItem && !currentItem.seen) {
        get().markAsSeen(currentItem.id);
      }
    }
  },

  closeStory: () => {
    set({
      isViewerOpen: false,
      currentStory: null,
      viewers: [],
      stats: null,
      reactions: [],
    });
  },

  nextItem: () => {
    const { currentStory, currentItemIndex } = get();

    if (currentStory && currentItemIndex < currentStory.items.length - 1) {
      const nextIndex = currentItemIndex + 1;

      set({ currentItemIndex: nextIndex });

      // Mark the next item as seen
      const nextItem = currentStory.items[nextIndex];
      if (!nextItem.seen) {
        get().markAsSeen(nextItem.id);
      }
    } else {
      // Move to the next story
      get().nextStory();
    }
  },

  prevItem: () => {
    const { currentStory, currentItemIndex } = get();

    if (currentStory && currentItemIndex > 0) {
      set({ currentItemIndex: currentItemIndex - 1 });
    } else {
      // Move to the previous story
      get().prevStory();
    }
  },

  nextStory: () => {
    const { storiesFeed, currentStoryIndex } = get();

    if (currentStoryIndex < storiesFeed.length - 1) {
      const nextIndex = currentStoryIndex + 1;
      const nextStory = storiesFeed[nextIndex];

      set({
        currentStory: nextStory,
        currentStoryIndex: nextIndex,
        currentItemIndex: 0,
      });

      // Mark the first item of the next story as seen
      const firstItem = nextStory.items[0];
      if (!firstItem.seen) {
        get().markAsSeen(firstItem.id);
      }
    } else {
      // Close the viewer if there are no more stories
      get().closeStory();
    }
  },

  prevStory: () => {
    const { storiesFeed, currentStoryIndex } = get();

    if (currentStoryIndex > 0) {
      const prevIndex = currentStoryIndex - 1;
      const prevStory = storiesFeed[prevIndex];

      set({
        currentStory: prevStory,
        currentStoryIndex: prevIndex,
        currentItemIndex: prevStory.items.length - 1, // Go to the last item of the previous story
      });
    }
  },

  pauseStory: () => {
    set({ isPaused: true });
  },

  resumeStory: () => {
    set({ isPaused: false });
  },

  // Creation
  startCreatingStory: () => {
    set({ isCreatingStory: true });
  },

  cancelCreatingStory: () => {
    set({ isCreatingStory: false });
  },

  // Archived stories
  getArchivedStories: async () => {
    set({ isLoading: true, error: null });

    try {
      const archived = await getArchivedStories();
      set({ archivedStories: archived, isLoading: false });
      return archived;
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch archived stories',
      });
      return [];
    }
  },

  // Highlights
  fetchHighlights: async (userId: string) => {
    set({ isLoading: true, error: null });

    try {
      const highlights = await getStoryHighlights(userId);
      set({ highlights, isLoading: false });
      return highlights;
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch highlights',
      });
      return [];
    }
  },

  createHighlight: async (data: StoryHighlightCreateData) => {
    set({ isLoading: true, error: null });

    try {
      const newHighlight = await createStoryHighlight(data);

      set((state) => ({
        highlights: [...state.highlights, newHighlight],
        isLoading: false,
      }));

      return newHighlight;
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to create highlight',
      });
      throw error;
    }
  },

  updateHighlight: async (highlightId: string, data: Partial<StoryHighlightCreateData>) => {
    set({ isLoading: true, error: null });

    try {
      const updatedHighlight = await updateStoryHighlight(highlightId, data);

      set((state) => ({
        highlights: state.highlights.map((h) =>
          h.id === highlightId ? updatedHighlight : h
        ),
        isLoading: false,
      }));

      return updatedHighlight;
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to update highlight',
      });
      throw error;
    }
  },

  deleteHighlight: async (highlightId: string) => {
    set({ isLoading: true, error: null });

    try {
      await deleteStoryHighlight(highlightId);

      set((state) => ({
        highlights: state.highlights.filter((h) => h.id !== highlightId),
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to delete highlight',
      });
      throw error;
    }
  },
}));
