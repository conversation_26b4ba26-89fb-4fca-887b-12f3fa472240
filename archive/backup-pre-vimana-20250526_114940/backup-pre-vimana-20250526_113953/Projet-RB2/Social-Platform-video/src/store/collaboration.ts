import { create } from 'zustand';
import {
  getCollaborativeContent,
  getUserCollaborativeContent,
  createCollaborativeContent,
  updateCollaborativeContent,
  deleteCollaborativeContent,
  getCollaborators,
  addCollaborator,
  updateCollaboratorRole,
  removeCollaborator,
  getCollaborationRequests,
  sendCollaborationRequest,
  respondToCollaborationRequest,
  getCollaborationActivity,
  getCollaborationComments,
  addCollaborationComment,
  updateCollaborationComment,
  deleteCollaborationComment,
  toggleCommentResolution,
  searchUsers,
  CollaborativeContent,
  Collaborator,
  CollaborationRequest,
  CollaborationActivity,
  Comment,
} from '../api/collaborationApi';

interface CollaborationState {
  // Content
  currentContent: CollaborativeContent | null;
  userContent: CollaborativeContent[];
  
  // Collaborators
  collaborators: Collaborator[];
  
  // Requests
  collaborationRequests: CollaborationRequest[];
  
  // Activity
  collaborationActivity: CollaborationActivity[];
  
  // Comments
  collaborationComments: Comment[];
  
  // Search
  searchResults: Array<{
    id: string;
    username: string;
    name: string;
    avatar: string;
  }>;
  
  // UI state
  isCreatingContent: boolean;
  isAddingCollaborator: boolean;
  isViewingRequests: boolean;
  selectedRequestId: string | null;
  
  // Status
  isLoading: boolean;
  error: string | null;
  
  // Actions - Content
  fetchContent: (contentId: string, contentType: 'video' | 'post' | 'livestream') => Promise<void>;
  fetchUserContent: (status?: 'draft' | 'published' | 'archived', type?: 'video' | 'post' | 'livestream', role?: 'owner' | 'editor' | 'viewer') => Promise<void>;
  createContent: (type: 'video' | 'post' | 'livestream', title: string, description: string, thumbnailUrl?: string, visibility?: 'public' | 'private' | 'unlisted') => Promise<void>;
  updateContent: (contentId: string, contentType: 'video' | 'post' | 'livestream', updates: Partial<{ title: string; description: string; thumbnailUrl: string; status: 'draft' | 'published' | 'archived'; visibility: 'public' | 'private' | 'unlisted'; }>) => Promise<void>;
  deleteContent: (contentId: string, contentType: 'video' | 'post' | 'livestream') => Promise<void>;
  
  // Actions - Collaborators
  fetchCollaborators: (contentId: string, contentType: 'video' | 'post' | 'livestream') => Promise<void>;
  addContentCollaborator: (contentId: string, contentType: 'video' | 'post' | 'livestream', userId: string, role: 'editor' | 'viewer') => Promise<void>;
  updateCollaboratorRole: (contentId: string, contentType: 'video' | 'post' | 'livestream', userId: string, role: 'editor' | 'viewer') => Promise<void>;
  removeContentCollaborator: (contentId: string, contentType: 'video' | 'post' | 'livestream', userId: string) => Promise<void>;
  
  // Actions - Requests
  fetchCollaborationRequests: (status?: 'pending' | 'accepted' | 'declined' | 'all', type?: 'sent' | 'received' | 'all') => Promise<void>;
  sendRequest: (contentId: string, contentType: 'video' | 'post' | 'livestream', toUserId: string, role: 'editor' | 'viewer', message: string) => Promise<void>;
  respondToRequest: (requestId: string, accept: boolean) => Promise<void>;
  
  // Actions - Activity
  fetchCollaborationActivity: (contentId: string, contentType: 'video' | 'post' | 'livestream') => Promise<void>;
  
  // Actions - Comments
  fetchCollaborationComments: (contentId: string, contentType: 'video' | 'post' | 'livestream') => Promise<void>;
  addComment: (contentId: string, contentType: 'video' | 'post' | 'livestream', text: string, timestamp?: number) => Promise<void>;
  updateComment: (contentId: string, contentType: 'video' | 'post' | 'livestream', commentId: string, text: string) => Promise<void>;
  deleteComment: (contentId: string, contentType: 'video' | 'post' | 'livestream', commentId: string) => Promise<void>;
  toggleCommentResolution: (contentId: string, contentType: 'video' | 'post' | 'livestream', commentId: string, isResolved: boolean) => Promise<void>;
  
  // Actions - Search
  searchCollaborators: (query: string) => Promise<void>;
  
  // UI Actions
  setCreatingContent: (isCreating: boolean) => void;
  setAddingCollaborator: (isAdding: boolean) => void;
  setViewingRequests: (isViewing: boolean) => void;
  setSelectedRequest: (requestId: string | null) => void;
  resetCollaborationState: () => void;
}

export const useCollaborationStore = create<CollaborationState>((set, get) => ({
  // Content
  currentContent: null,
  userContent: [],
  
  // Collaborators
  collaborators: [],
  
  // Requests
  collaborationRequests: [],
  
  // Activity
  collaborationActivity: [],
  
  // Comments
  collaborationComments: [],
  
  // Search
  searchResults: [],
  
  // UI state
  isCreatingContent: false,
  isAddingCollaborator: false,
  isViewingRequests: false,
  selectedRequestId: null,
  
  // Status
  isLoading: false,
  error: null,
  
  // Actions - Content
  fetchContent: async (contentId, contentType) => {
    set({ isLoading: true, error: null });
    
    try {
      const content = await getCollaborativeContent(contentId, contentType);
      set({ currentContent: content, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch content',
      });
    }
  },
  
  fetchUserContent: async (status, type, role) => {
    set({ isLoading: true, error: null });
    
    try {
      const content = await getUserCollaborativeContent(status, type, role);
      set({ userContent: content, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch user content',
      });
    }
  },
  
  createContent: async (type, title, description, thumbnailUrl, visibility) => {
    set({ isLoading: true, error: null });
    
    try {
      const content = await createCollaborativeContent(type, title, description, thumbnailUrl, visibility);
      set((state) => ({
        userContent: [content, ...state.userContent],
        currentContent: content,
        isCreatingContent: false,
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to create content',
      });
    }
  },
  
  updateContent: async (contentId, contentType, updates) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedContent = await updateCollaborativeContent(contentId, contentType, updates);
      
      set((state) => ({
        currentContent: state.currentContent?.id === contentId ? updatedContent : state.currentContent,
        userContent: state.userContent.map((content) =>
          content.id === contentId ? updatedContent : content
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to update content',
      });
    }
  },
  
  deleteContent: async (contentId, contentType) => {
    set({ isLoading: true, error: null });
    
    try {
      await deleteCollaborativeContent(contentId, contentType);
      
      set((state) => ({
        currentContent: state.currentContent?.id === contentId ? null : state.currentContent,
        userContent: state.userContent.filter((content) => content.id !== contentId),
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to delete content',
      });
    }
  },
  
  // Actions - Collaborators
  fetchCollaborators: async (contentId, contentType) => {
    set({ isLoading: true, error: null });
    
    try {
      const collaborators = await getCollaborators(contentId, contentType);
      set({ collaborators, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch collaborators',
      });
    }
  },
  
  addContentCollaborator: async (contentId, contentType, userId, role) => {
    set({ isLoading: true, error: null });
    
    try {
      const collaborator = await addCollaborator(contentId, contentType, userId, role);
      
      set((state) => ({
        collaborators: [...state.collaborators, collaborator],
        isAddingCollaborator: false,
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to add collaborator',
      });
    }
  },
  
  updateCollaboratorRole: async (contentId, contentType, userId, role) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedCollaborator = await updateCollaboratorRole(contentId, contentType, userId, role);
      
      set((state) => ({
        collaborators: state.collaborators.map((collaborator) =>
          collaborator.userId === userId ? updatedCollaborator : collaborator
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to update collaborator role',
      });
    }
  },
  
  removeContentCollaborator: async (contentId, contentType, userId) => {
    set({ isLoading: true, error: null });
    
    try {
      await removeCollaborator(contentId, contentType, userId);
      
      set((state) => ({
        collaborators: state.collaborators.filter((collaborator) => collaborator.userId !== userId),
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to remove collaborator',
      });
    }
  },
  
  // Actions - Requests
  fetchCollaborationRequests: async (status = 'all', type = 'all') => {
    set({ isLoading: true, error: null });
    
    try {
      const requests = await getCollaborationRequests(status, type);
      set({ collaborationRequests: requests, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch collaboration requests',
      });
    }
  },
  
  sendRequest: async (contentId, contentType, toUserId, role, message) => {
    set({ isLoading: true, error: null });
    
    try {
      const request = await sendCollaborationRequest(contentId, contentType, toUserId, role, message);
      
      set((state) => ({
        collaborationRequests: [...state.collaborationRequests, request],
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to send collaboration request',
      });
    }
  },
  
  respondToRequest: async (requestId, accept) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedRequest = await respondToCollaborationRequest(requestId, accept);
      
      set((state) => ({
        collaborationRequests: state.collaborationRequests.map((request) =>
          request.id === requestId ? updatedRequest : request
        ),
        selectedRequestId: null,
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to respond to collaboration request',
      });
    }
  },
  
  // Actions - Activity
  fetchCollaborationActivity: async (contentId, contentType) => {
    set({ isLoading: true, error: null });
    
    try {
      const activity = await getCollaborationActivity(contentId, contentType);
      set({ collaborationActivity: activity, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch collaboration activity',
      });
    }
  },
  
  // Actions - Comments
  fetchCollaborationComments: async (contentId, contentType) => {
    set({ isLoading: true, error: null });
    
    try {
      const comments = await getCollaborationComments(contentId, contentType);
      set({ collaborationComments: comments, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch collaboration comments',
      });
    }
  },
  
  addComment: async (contentId, contentType, text, timestamp) => {
    set({ isLoading: true, error: null });
    
    try {
      const comment = await addCollaborationComment(contentId, contentType, text, timestamp);
      
      set((state) => ({
        collaborationComments: [...state.collaborationComments, comment],
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to add comment',
      });
    }
  },
  
  updateComment: async (contentId, contentType, commentId, text) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedComment = await updateCollaborationComment(contentId, contentType, commentId, text);
      
      set((state) => ({
        collaborationComments: state.collaborationComments.map((comment) =>
          comment.id === commentId ? updatedComment : comment
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to update comment',
      });
    }
  },
  
  deleteComment: async (contentId, contentType, commentId) => {
    set({ isLoading: true, error: null });
    
    try {
      await deleteCollaborationComment(contentId, contentType, commentId);
      
      set((state) => ({
        collaborationComments: state.collaborationComments.filter((comment) => comment.id !== commentId),
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to delete comment',
      });
    }
  },
  
  toggleCommentResolution: async (contentId, contentType, commentId, isResolved) => {
    set({ isLoading: true, error: null });
    
    try {
      const updatedComment = await toggleCommentResolution(contentId, contentType, commentId, isResolved);
      
      set((state) => ({
        collaborationComments: state.collaborationComments.map((comment) =>
          comment.id === commentId ? updatedComment : comment
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to toggle comment resolution',
      });
    }
  },
  
  // Actions - Search
  searchCollaborators: async (query) => {
    set({ isLoading: true, error: null });
    
    try {
      const results = await searchUsers(query);
      set({ searchResults: results, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to search users',
      });
    }
  },
  
  // UI Actions
  setCreatingContent: (isCreating) => {
    set({ isCreatingContent: isCreating });
  },
  
  setAddingCollaborator: (isAdding) => {
    set({ isAddingCollaborator: isAdding });
  },
  
  setViewingRequests: (isViewing) => {
    set({ isViewingRequests: isViewing });
  },
  
  setSelectedRequest: (requestId) => {
    set({ selectedRequestId: requestId });
  },
  
  resetCollaborationState: () => {
    set({
      currentContent: null,
      collaborators: [],
      collaborationActivity: [],
      collaborationComments: [],
      searchResults: [],
      isCreatingContent: false,
      isAddingCollaborator: false,
      isViewingRequests: false,
      selectedRequestId: null,
      isLoading: false,
      error: null,
    });
  },
}));
