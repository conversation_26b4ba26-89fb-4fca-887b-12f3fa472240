# Retreat And Be - Social Platform Video

A wellness-focused social media platform for sharing and discovering mindfulness content, with advanced video and social features.

## 🏗️ Architecture Overview

The Social-Platform-Video microservice is a key component of the Retreat And Be platform, providing video streaming, livestreaming, blog, and social analytics capabilities. It follows a microservice architecture pattern and integrates with other services in the ecosystem.

### System Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Frontend       │◄───►│  Backend-NestJS │◄───►│  Social-Platform│
│  (React)        │     │  (Gateway)      │     │  -Video         │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │                 │
                                               │  Messaging      │
                                               │  Service        │
                                               │                 │
                                               └─────────────────┘
```

### Data Flow

1. **Video Content Flow**:
   - Users upload videos through the frontend
   - Videos are processed, compressed, and stored
   - Videos are served to users through optimized delivery

2. **Livestream Flow**:
   - Host creates a livestream through the frontend
   - WebRTC connections are established between participants
   - Chat messages and reactions are processed in real-time
   - Stream recordings are saved for later viewing

3. **Analytics Flow**:
   - User interactions are tracked as events
   - Events are processed and aggregated
   - Analytics dashboards display insights

## 🌟 Implemented Features

### Core Features
- ✅ Video Feed
  - Vertical scrolling video feed
  - Auto-play/pause on scroll
  - Double-tap to like
  - Video progress bar
  - Mute/unmute controls
  - Loading states and error handling

### Navigation
- ✅ Bottom Navigation Bar
  - Home (Video Feed)
  - Explore
  - Notifications
  - Profile

### User Interface
- ✅ Profile Page
  - User information display
  - Stats (posts, followers, following)
  - Tabs for posts, saved, and liked content
  - Settings button

- ✅ Explore Page
  - Search functionality
  - Popular categories
  - Trending creators
  - Follow/unfollow buttons

- ✅ Notifications Page
  - Different notification types (likes, comments, follows)
  - Read/unread states
  - Relative timestamps
  - Visual indicators for different actions

### Interactions
- ✅ Post Interactions
  - Like/unlike posts
  - View counts
  - Share functionality
  - Comments count display

## 🚀 Features To Implement

### Authentication
- [✅] User registration
- [✅] Login/logout
- [ ] Social media authentication
- [✅] Password recovery
- [IN PROGRESS - UI BLOCKED] Email verification (Backend/service logic complete; UI components pending full setup)

### Content Creation
- [✅] Video upload (Complete with full UI integration)
  - [✅] Multi-format support (MP4, MOV, WebM) (Client-side accept types updated)
  - [✅] Progress indicator (Actual progress reporting implemented)
  - [✅] Background uploading
  - [✅] Automatic thumbnail generation
- [✅] Video editing tools
  - [✅] Trimming and cutting
  - [✅] Filters and effects
  - [✅] Text overlays
  - [✅] Music/audio tracks
- [✅] Caption and hashtag editor
  - [✅] Rich text formatting
  - [✅] Hashtag suggestions
  - [✅] Mention functionality
- [✅] Location tagging
  - [✅] Map integration
  - [✅] Popular location suggestions
  - [✅] Custom location creation
- [✅] Draft saving
  - [✅] Auto-save functionality
  - [✅] Draft management interface
  - [✅] Scheduled publishing

### Social Features
- [✅] Comments system
  - [✅] Comment creation
    - [✅] Rich text editor
    - [✅] Emoji support
    - [✅] Image attachments
  - [✅] Reply threading
    - [✅] Nested replies
    - [✅] Collapsible threads
    - [✅] Notification on replies
  - [✅] Comment likes
    - [✅] Like/unlike toggle
    - [✅] Like count display
    - [ ] Liked comments list
  - [✅] Comment moderation
    - [✅] Edit/delete own comments
    - [✅] Report inappropriate comments
    - [✅] Admin comment removal

- [✅] Follow system
  - [✅] Follow/unfollow users
    - [✅] One-click follow button
    - [✅] Follow suggestions
    - [✅] Follow notifications
  - [✅] Followers/following lists
    - [✅] Sortable lists
    - [✅] Search functionality
    - [✅] Batch actions
  - [✅] Block users
    - [✅] Content hiding
    - [✅] Interaction prevention
    - [✅] Unblock option
  - [✅] Report content/users
    - [✅] Multiple report categories
    - [✅] Evidence submission
    - [✅] Report status tracking

### Content Discovery
- [✅] Advanced search
  - [✅] Filter by category
    - [✅] Predefined categories
    - [✅] Custom category creation
    - [✅] Multi-category selection
  - [✅] Sort by popularity/date
    - [✅] Trending content
    - [✅] Recent uploads
    - [✅] Most liked/commented
  - [✅] Location-based search
    - [✅] Radius filtering
    - [✅] Map view integration
    - [✅] Location suggestions

- [✅] Recommendations
  - [✅] Personalized feed
    - [✅] AI-powered content matching
    - [✅] Interest-based suggestions
    - [✅] Viewing history analysis
  - [✅] Similar content suggestions
    - [✅] Content similarity algorithm
    - [✅] "More like this" feature
    - [✅] Category-based recommendations
  - [✅] User recommendations
    - [✅] Similar user interests
    - [✅] Mutual connections
    - [✅] Activity-based suggestions

### Profile Enhancements
- [ ] Profile editing
  - [ ] Bio editor
    - [✅] Rich text formatting
    - [ ] Character counter
    - [ ] Template suggestions
  - [ ] Profile picture upload
    - [ ] Image cropping tool
    - [ ] Filter options
    - [ ] Default avatars
  - [ ] Cover photo
    - [ ] Responsive sizing
    - [ ] Position adjustment
    - [ ] Gallery selection
  - [ ] Social links
    - [ ] Multiple platform support
    - [ ] Link verification
    - [ ] Custom link labels

- [ ] Content Management
  - [ ] Archive posts
    - [ ] Batch archiving
    - [ ] Archive browsing
    - [ ] Restore functionality
  - [ ] Delete posts
    - [ ] Confirmation dialog
    - [ ] Recycle bin (temporary storage)
    - [ ] Permanent deletion option
  - [ ] Edit post details
    - [ ] Caption editing
    - [ ] Tag modification
    - [ ] Privacy settings adjustment

### Engagement Features
- [✅] Stories
  - [✅] Create/view stories
    - [✅] 24-hour expiration
    - [✅] Privacy controls
    - [✅] View counter
  - [✅] Story interactions
    - [✅] Quick reactions
    - [✅] Direct message replies
    - [✅] Polls and questions
  - [✅] Story highlights
    - [✅] Custom cover images
    - [✅] Categorized highlights
    - [✅] Editing capabilities

- [✅] Collections
  - [✅] Save posts to collections
    - [✅] One-click saving
    - [✅] Collection selection
    - [✅] Save notifications
  - [✅] Organize saved content
    - [✅] Custom collection creation
    - [✅] Drag-and-drop organization
    - [✅] Collection thumbnails
  - [✅] Share collections
    - [✅] Public/private options
    - [✅] Shareable links
    - [✅] Collaborative collections

### Technical Enhancements
- [✅] Video Optimization
  - [✅] Adaptive streaming
    - [✅] Multi-bitrate encoding
    - [✅] Automatic quality switching
    - [✅] Bandwidth detection
  - [✅] Video compression
    - [✅] Efficient codecs (H.265/HEVC, AV1)
    - [✅] Size-quality balance
    - [✅] Background processing
  - [✅] Thumbnail generation
    - [✅] Automatic frame selection
    - [✅] Custom thumbnail upload
    - [✅] Thumbnail preview grid

- [✅] Advanced Animations (FLIP Technique)
  - [✅] Animation Utilities
    - [✅] Core FLIP functions (first, last, invertAndPlay)
    - [✅] Enter/exit animations (flipEnter, flipExit)
    - [✅] Group animations (flipGroup, flipList)
  - [✅] React Animation Hooks
    - [✅] useFlipAnimation for individual elements
    - [✅] useFlipGroup for coordinated transitions
    - [✅] useFlipList for list changes (add/remove/reorder)
  - [✅] Reusable Animated Components
    - [✅] AnimatedList for animated list changes
    - [✅] AnimatedGrid for layout transitions
    - [✅] AnimatedTransition for element appearance/disappearance
  - [✅] Animation Demo Page
    - [✅] Interactive demonstration of FLIP animations
    - [✅] Examples of all animation types
  - [✅] Performance Optimizations
    - [✅] GPU-accelerated animations (transform/opacity)
    - [✅] Pre-calculation to avoid reflows
    - [✅] Memoization to prevent unnecessary renders

- [IN PROGRESS] Performance
  - [IN PROGRESS] Infinite scroll optimization
    - [IN PROGRESS] Virtualized list rendering
    - [ ] Prefetching content
    - [ ] Memory management
  - [IN PROGRESS] Image/video lazy loading
    - [✅] Progressive loading
    - [ ] Low-quality image placeholders
    - [IN PROGRESS] Viewport detection
  - [✅] Cache management
    - [✅] Intelligent caching strategy
    - [✅] Offline content access
    - [✅] Cache invalidation

### Analytics
- [IN PROGRESS] User Analytics
  - [IN PROGRESS] View insights
    - [✅] Daily/weekly/monthly views
    - [ ] Traffic sources
    - [ ] Viewer retention
  - [ ] Engagement metrics
    - [ ] Likes, comments, shares tracking
    - [ ] Engagement rate calculation
    - [ ] Comparative analysis
  - [ ] Follower growth
    - [ ] Growth charts
    - [ ] Follower acquisition sources
    - [ ] Follower retention rates

- [ ] Content Analytics
  - [ ] Post performance
    - [ ] Performance comparison
    - [ ] Content type analysis
    - [ ] Engagement breakdown
  - [ ] Best posting times
    - [ ] Time-based engagement heatmaps
    - [ ] Audience activity patterns
    - [ ] Scheduling recommendations
  - [ ] Audience demographics
    - [ ] Age and location data
    - [ ] Interest categories
    - [ ] Device and platform usage

### Monetization
- [ ] Creator Program
  - [ ] Monetization eligibility
    - [ ] Follower threshold requirements
    - [ ] Content quality assessment
    - [ ] Application process
  - [ ] Revenue sharing
    - [ ] View-based compensation
    - [ ] Premium content options
    - [ ] Subscription tiers
  - [ ] Sponsored content
    - [ ] Brand partnership marketplace
    - [ ] Sponsored content disclosure
    - [ ] Performance tracking
  - [ ] Virtual gifts and donations
    - [ ] Custom gift animations
    - [ ] Donation leaderboards
    - [ ] Creator payout system
  - [ ] Merchandise integration
    - [ ] Product showcase
    - [ ] E-commerce platform connection
    - [ ] Analytics for product performance

### Administrative
- [IN PROGRESS] Content Moderation
  - [✅] Report handling
    - [✅] Report categorization
    - [✅] Priority queue system
    - [✅] Resolution tracking
  - [IN PROGRESS] Content review system
    - [IN PROGRESS] AI-assisted content screening
    - [ ] Human review workflow
    - [ ] Content policy enforcement
  - [ ] User warnings/bans
    - [ ] Graduated warning system
    - [ ] Temporary/permanent ban options
    - [ ] Appeal process

- [ ] Analytics Dashboard
  - [ ] User growth metrics
    - [ ] New user acquisition
    - [ ] User retention rates
    - [ ] Active user tracking
  - [ ] Content trends
    - [ ] Popular content categories
    - [ ] Trending hashtags
    - [ ] Emerging creators
  - [ ] Platform health
    - [ ] System performance monitoring
    - [ ] Error rate tracking
    - [ ] User satisfaction metrics
  - [ ] Moderation metrics
    - [ ] Report resolution times
    - [ ] Moderation action tracking
    - [ ] Content policy effectiveness

## 🛠 Technical Stack

- **Frontend**: React, TypeScript, Vite
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **Data Fetching**: TanStack Query
- **Routing**: React Router
- **Icons**: Lucide React
- **Form Handling**: React Hook Form
- **Validation**: Zod
- **HTTP Client**: Axios

## 📱 Mobile Optimization

The application is designed with a mobile-first approach, ensuring optimal performance and user experience on mobile devices.

## 🔒 Security Considerations

- [✅] Input validation
  - [✅] Client-side validation
  - [✅] Server-side validation
  - [✅] Sanitization of user inputs
- [✅] XSS protection
  - [✅] Content Security Policy (CSP)
  - [✅] Output encoding
  - [✅] HTML sanitization
- [✅] CSRF protection
  - [✅] Anti-CSRF tokens
  - [✅] Same-site cookies
  - [✅] Referrer policy
- [✅] Rate limiting
  - [✅] API request throttling
  - [✅] Account action limits
  - [✅] IP-based restrictions
- [✅] Content encryption
  - [✅] End-to-end encryption for messages
  - [✅] Secure storage of sensitive data
  - [✅] TLS for all communications
- [✅] Secure file upload
  - [✅] File type validation
  - [✅] Virus/malware scanning
  - [✅] Size and quota limitations
- [✅] Authentication security
  - [✅] Multi-factor authentication
  - [✅] Password policies
  - [✅] Account recovery process
- [✅] Privacy controls
  - [✅] User data access controls
  - [✅] Content visibility settings
  - [✅] Data retention policies

## � Intégration avec le microservice Security

Le microservice Social-Platform-Video s'intègre avec le microservice Security centralisé pour offrir des fonctionnalités de sécurité robustes et cohérentes à travers toute la plateforme. Cette intégration suit les principes suivants :

### Services de sécurité utilisés

- **Validation et sanitization** : Utilisation de l'API `/security/validation` pour la validation des entrées complexes
- **Protection XSS** : Intégration avec le service CSP pour des politiques de sécurité adaptées au contenu vidéo
- **Chiffrement E2E** : Utilisation du protocole Double Ratchet via l'API `/security/encryption`
- **Scan antivirus** : Analyse des fichiers téléchargés via l'API `/security/antivirus/scan`
- **Authentification 2FA** : Intégration avec le service 2FA pour l'authentification multi-facteur
- **Contrôle d'accès** : Utilisation du service RBAC pour la gestion fine des permissions
- **Politiques de rétention** : Application des politiques de rétention des données conformes au RGPD

### Architecture de sécurité

L'architecture de sécurité suit un modèle en couches :

1. **Couche API Gateway** : Filtrage initial des requêtes, authentification et autorisation de base
2. **Couche microservice Security** : Services de sécurité centralisés et spécialisés
3. **Couche Social-Platform-Video** : Contrôles de sécurité spécifiques au domaine vidéo et social

Cette approche garantit une sécurité en profondeur tout en maintenant la séparation des préoccupations entre les microservices.

## �🚀 Getting Started

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🚀 Cache Management

Le microservice intègre un système de cache avancé pour optimiser les performances:

- **Architecture multi-niveaux**: Cache en mémoire avec différents TTL selon le type de données
- **Invalidation par tags**: Gestion précise du cycle de vie des données en cache
- **Middleware API**: Mise en cache automatique des réponses API
- **Statistiques de performance**: Monitoring du taux de succès et des performances

Pour plus de détails, consultez la [documentation du cache](./docs/CACHE.md).

## 📝 Integration with Other Microservices

### Backend-NestJS Integration
- API Gateway routing for Social-Platform-Video endpoints
- Authentication and authorization middleware
- Data transformation and validation
- Service communication via HTTP and WebSockets
- Event-driven architecture for real-time updates

### Frontend Integration
- React components for video playback and social features
- State management for social interactions
- Real-time updates with WebSockets
- Responsive UI components for mobile and desktop
- Accessibility features for video content

### Messaging Service Integration
- Real-time notifications for social events
- Chat functionality for livestreams
- Direct messaging related to content
- Message persistence and history
- Typing indicators and read receipts

### Analytics Integration
- Event tracking for social interactions
- Performance metrics collection
- User engagement analysis
- Content popularity tracking
- Conversion and retention metrics

## 🔌 API Documentation

### Video Feed API
```
GET /api/videos - Get video feed
POST /api/videos - Upload new video
GET /api/videos/:id - Get video details
PUT /api/videos/:id - Update video
DELETE /api/videos/:id - Delete video
POST /api/videos/:id/like - Like a video
```

### Livestream API
```
GET /api/livestreams - Get all livestreams
POST /api/livestreams - Create new livestream
GET /api/livestreams/:id - Get livestream details
PUT /api/livestreams/:id - Update livestream
DELETE /api/livestreams/:id - End/delete livestream
POST /api/livestreams/:id/join - Join a livestream
POST /api/livestreams/:id/messages - Send message to livestream
```

### Blog API
```
GET /api/blog - Get all blog posts
POST /api/blog - Create new blog post
GET /api/blog/:id - Get blog post details
PUT /api/blog/:id - Update blog post
DELETE /api/blog/:id - Delete blog post
POST /api/blog/:id/comments - Comment on blog post
```

### Analytics API
```
GET /api/analytics - Get general analytics
GET /api/analytics/livestreams/:id - Get livestream analytics
GET /api/analytics/blog/:id - Get blog post analytics
POST /api/analytics/events - Track analytics event
```

## ⚙️ Configuration

### Environment Variables
```
# Server Configuration
PORT=3002
NODE_ENV=development

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/social_platform_video

# Authentication
JWT_SECRET=your_jwt_secret
API_KEY=your_api_key

# External Services
MESSAGING_SERVICE_URL=http://messaging-service:3003
BACKEND_NESTJS_URL=http://backend-nestjs:3000

# Media Storage
MEDIA_STORAGE_TYPE=s3
S3_BUCKET_NAME=your_bucket_name
S3_REGION=your_region
S3_ACCESS_KEY=your_access_key
S3_SECRET_KEY=your_secret_key

# WebRTC Configuration
ICE_SERVERS=stun:stun.l.google.com:19302,turn:your-turn-server.com
```

## 🚢 Deployment

### Docker
```bash
# Build the Docker image
docker build -t social-platform-video .

# Run the container
docker run -p 3002:3002 --env-file .env social-platform-video
```

### Kubernetes
```bash
# Apply Kubernetes configuration
kubectl apply -f k8s/social-platform-video.yaml
```

### Integration with CI/CD
The microservice is configured for continuous integration and deployment using GitHub Actions. The workflow includes:
- Automated testing
- Docker image building
- Deployment to staging/production environments

## 🔄 WebSocket Events

### Client Events
- `join_room`: Join a livestream room
- `leave_room`: Leave a livestream room
- `send_message`: Send a message to the room
- `reaction`: Send a reaction to the room

### Server Events
- `room_joined`: Confirmation of joining a room
- `room_left`: Confirmation of leaving a room
- `new_message`: New message in the room
- `user_joined`: Notification when a user joins
- `user_left`: Notification when a user leaves
- `stream_ended`: Notification when a stream ends

## 💻 Development Guidelines

### Code Structure
```
social-platform-video/
├── src/
│   ├── config/           # Configuration files
│   ├── controllers/      # API controllers
│   ├── services/         # Business logic
│   ├── models/           # Data models
│   ├── middleware/       # Custom middleware
│   ├── utils/            # Utility functions
│   ├── websockets/       # WebSocket handlers
│   └── app.js            # Application entry point
├── tests/                # Test files
├── docs/                 # Documentation
└── scripts/              # Utility scripts
```

### Coding Standards
- Follow the Airbnb JavaScript Style Guide
- Use TypeScript for type safety
- Write unit tests for all new features
- Document all public APIs using JSDoc
- Use async/await for asynchronous operations

### Pull Request Process
1. Ensure all tests pass locally
2. Update documentation if necessary
3. Add appropriate test coverage
4. Request review from at least one team member
5. Squash commits before merging

## 🧪 Testing

### Running Tests
```bash
# Run all tests
npm test

# Run specific test suite
npm test -- --grep "Livestream API"

# Run tests with coverage
npm run test:coverage
```

### Test Structure
- Unit tests for individual components
- Integration tests for API endpoints
- End-to-end tests for critical user flows
- Performance tests for video streaming

## 📚 Documentation

### Project Documentation
- [README.md](./README.md): Overview of the project and features
- [CONTRIBUTING.md](./CONTRIBUTING.md): Guidelines for contributing to the project
- [CACHE.md](./docs/CACHE.md): Detailed information about the caching system
- [ANIMATIONS.md](./docs/ANIMATIONS.md): Documentation of FLIP animation techniques and components

## 🔮 Future Roadmap

### Short-term (1-3 months)
- [IN PROGRESS] Enhance video compression algorithms
- [✅] Implement advanced content recommendation system
- [ ] Add support for 360° videos
- [IN PROGRESS] Improve livestream chat moderation tools
- [IN PROGRESS] Implement content moderation tools with AI
- [IN PROGRESS] Extend animation system with:
  - [ ] Physics-based animations (spring, momentum)
  - [ ] Coordinated animations between components
  - [ ] Adaptive animations based on device capabilities

### Medium-term (3-6 months)
- [ ] Develop collaborative video editing features
- [IN PROGRESS] Implement AI-powered content tagging
- [ ] Add support for virtual events with multiple hosts
- [ ] Enhance analytics with predictive insights
- [ ] Implement shared element transitions between routes

### Long-term (6-12 months)
- [ ] Develop AR/VR integration for immersive experiences
- [ ] Implement federated learning for personalized recommendations
- [ ] Add support for multi-language subtitles and translations
- [ ] Develop marketplace for premium content creators

## 🤝 Contributing

We welcome contributions to the Social-Platform-Video microservice! Please read our [CONTRIBUTING.md](./CONTRIBUTING.md) file for details on our code of conduct and the process for submitting pull requests.

## 📝 License

MIT