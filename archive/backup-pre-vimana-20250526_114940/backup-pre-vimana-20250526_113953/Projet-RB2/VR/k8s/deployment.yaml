apiVersion: apps/v1
kind: Deployment
metadata:
  name: vr-service
  labels:
    app: vr
spec:
  replicas: 2
  selector:
    matchLabels:
      app: vr
  template:
    metadata:
      labels:
        app: vr
    spec:
      containers:
      - name: vr
        image: vr-service:latest
        ports:
        - containerPort: 8080
        resources:
          limits:
            cpu: "1"
            memory: 512Mi
          requests:
            cpu: "0.5"
            memory: 256Mi
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
