import React from 'react';
import { RetreatCard } from './RetreatCard.tsx';
import type { Retreat } from '../types.ts';

interface RetreatListProps {
  retreats: Retreat[];
  onJoinRetreat: (retreatId: string) => void
}

export const RetreatList: React.FC<RetreatListProps> = ({ retreats, onJoinRetreat }) => {
  return (;
    <div className = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {retreats.map((retreat) => (
        <RetreatCard
          key={retreat.id
}
          retreat = {retreat
}
          onJoin = {onJoinRetreat
}
        />
      ))}
    </div>
  );
}