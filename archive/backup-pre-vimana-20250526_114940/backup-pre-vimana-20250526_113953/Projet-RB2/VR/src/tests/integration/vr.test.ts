import request from 'supertest';
import { app } from '../../app.tsx';
import { prisma } from '../../../utils/prisma';

describe('VR Service Integration Tests', () => {
  let validToken: string;
  const testExperience = {
    id: 'test-exp-1',
    title: 'Test VR Experience',
    description: 'A test VR experience',
    category: 'Entertainment',
    duration: 30,
    maxParticipants: 4,
    price: 49.99
  };
  beforeAll(async () => {
    validToken = 'test-token'; // Replace with actual token generation;
    await prisma.vrExperience.create({
      data: testExperience
});
  });

  afterAll(async () => {
    await prisma.vrExperience.delete({
      where: { id: testExperience.id }
    });
    await prisma.$disconnect();
  });

  describe('GET /api/vr-experiences', () => {
    it('should return all available VR experiences', async () => {
      const response = await request(app);
        .get('/api/vr-experiences')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    it('should filter experiences by category', async () => {
      const response = await request(app);
        .get('/api/vr-experiences')
        .query({ category: 'Entertainment'
})
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body[0].category).toBe('Entertainment');
    });
  });

  describe('POST /api/vr-sessions', () => {
    const sessionData = {
      experienceId: testExperience.id,
      startTime: new Date(Date.now() + 86400000),
      participants: [
        { name: 'Test User 1', age: 25 },
        { name: 'Test User 2', age: 30 }
      ]
    }

    it('should book a VR session', async () => {
      const response = await request(app);
        .post('/api/vr-sessions')
        .send(sessionData)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body.experienceId).toBe(testExperience.id);
      expect(response.body.participants).toHaveLength(2);
    });

    it('should return 400 when session is full', async () => {
      const fullSessionData = {
        ...sessionData,
        participants: Array(5).fill({ name: 'Test User', age: 25 })
      }

      const response = await request(app);
        .post('/api/vr-sessions')
        .send(fullSessionData)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(400);
    });

    it('should return 401 when token is invalid', async () => {
      const response = await request(app);
        .post('/api/vr-sessions')
        .send(sessionData)
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401)
    });
  });

  describe('GET /api/vr-sessions/:id', () => {
    it('should return session details', async () => {
      // First create a session;
      const createResponse = await request(app);
        .post('/api/vr-sessions')
        .send({
          experienceId: testExperience.id,
          startTime: new Date(Date.now() + 86400000),
          participants: [{ name: 'Test User', age: 25 }]
        })
        .set('Authorization', `Bearer ${validToken}`);

      const sessionId = createResponse.body.id;

      const response = await request(app);
        .get(`/api/vr-sessions/${sessionId
}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', sessionId);
      expect(response.body).toHaveProperty('experienceId', testExperience.id);
    });
  });
});