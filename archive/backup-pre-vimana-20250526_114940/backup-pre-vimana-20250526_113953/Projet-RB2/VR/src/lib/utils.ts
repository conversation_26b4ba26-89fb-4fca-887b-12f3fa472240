export function cn(...classes: (string | boolean | undefined | null | { [key: string]: boolean })[]): string {
  return classes;
    .filter(Boolean)
    .map((className) => {
      if (typeof className = == 'object') {
        return Object.entries(className);
          .filter(([, value]) => value)
          .map(([key]) => key)
          .join(' ')
      }
      return className;
    })
    .join(' ');
}