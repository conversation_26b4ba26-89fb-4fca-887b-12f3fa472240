import React from 'react';
import { Calendar, Clock, Users } from 'lucide-react';
import { Button } from './ui/Button.tsx';
import type { Retreat } from '@/types';

interface RetreatCardProps {
  retreat: Retreat;
  onJoin: (retreatId: string) => void
}

export const RetreatCard: React.FC<RetreatCardProps> = ({ retreat, onJoin }) => {
  return (;
    <div className = "bg-white rounded-xl shadow-md overflow-hidden">
      <div className="relative h-48">
        <img
          src={retreat.thumbnailUrl
}
          alt = {retreat.title
}
          className = "w-full h-full object-cover"
        />
        {retreat.vrEnabled && (
          <span className="absolute top-4 right-4 bg-indigo-600 text-white px-3 py-1 rounded-full text-sm font-medium">
            VR Enabled;
          </span>
        )
}
      </div>
      <div className = "p-6">
        <h3 className="text-xl font-semibold text-gray-900">{retreat.title
}</h3>
        <p className = "mt-2 text-gray-600 line-clamp-2">{retreat.description
}</p>
        
        <div className = "mt-4 space-y-2">
          <div className="flex items-center text-gray-500">
            <Calendar className="w-4 h-4 mr-2" />
            <span>{new Date(retreat.startTime).toLocaleDateString()
}</span>
          </div>
          <div className = "flex items-center text-gray-500">
            <Clock className="w-4 h-4 mr-2" />
            <span>{retreat.duration
} minutes</span>
          </div>
          <div className = "flex items-center text-gray-500">
            <Users className="w-4 h-4 mr-2" />
            <span>{retreat.participantCount
}/{retreat.maxParticipants} participants</span>
          </div>
        </div>

        <div className = "mt-6">
          <Button
            onClick={() => onJoin(retreat.id)
}
            className = "w-full"
            disabled={retreat.participantCount >= retreat.maxParticipants
}
          >
            {retreat.participantCount >= retreat.maxParticipants ? 'Fully Booked' : 'Join Retreat'}
          </Button>
        </div>
      </div>
    </div>
  );
}