#!/bin/bash

# Set text colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Correction des problèmes TypeScript courants...${NC}"

# 1. Correction des importations incorrectes dans tous les fichiers
find src -name "*.ts" -o -name "*.tsx" -exec sed -i '' 's/}; from/} from/g' {} \;
find src -name "*.ts" -o -name "*.tsx" -exec sed -i '' 's/const { { /import { /g' {} \;

# 2. Correction des hooks avec appel direct
find src -name "*.ts" -o -name "*.tsx" -exec sed -i '' 's/= useAuth;()/= useAuth()/g' {} \;
find src -name "*.ts" -o -name "*.tsx" -exec sed -i '' 's/= useNotification;()/= useNotification()/g' {} \;
find src -name "*.ts" -o -name "*.tsx" -exec sed -i '' 's/= useTheme;()/= useTheme()/g' {} \;
find src -name "*.ts" -o -name "*.tsx" -exec sed -i '' 's/= useSelector;(/= useSelector(/g' {} \;
find src -name "*.ts" -o -name "*.tsx" -exec sed -i '' 's/= useDispatch;(/= useDispatch(/g' {} \;

# 3. Correction des points-virgules en trop à la fin des objets
find src -name "*.ts" -o -name "*.tsx" -exec sed -i '' 's/};;/};/g' {} \;
find src -name "*.ts" -o -name "*.tsx" -exec sed -i '' 's/return result;;/return result;/g' {} \;

# 4. Ajout des virgules manquantes dans les objets
find src -name "*.ts" -o -name "*.tsx" -exec sed -i '' 's/}\s*\n\s*[a-zA-Z]/},\n    [a-zA-Z]/g' {} \;

echo -e "${GREEN}Correction des problèmes TypeScript terminée${NC}"
echo -e "${BLUE}Vérifiez les résultats avec 'npx tsc --noEmit'${NC}" 