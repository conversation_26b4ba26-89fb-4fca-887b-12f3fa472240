#!/usr/bin/env python3
import os
import re
import glob
import sys
import time
import shutil
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed

def fix_typescript_file(file_path, verbose=False):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            original_content = content

        # Correction des imports destructurés
        content = re.sub(r'const\s+{([^}]+)}\s+=\s+([a-zA-Z0-9_]+)\(\)', r'const { \1 } = \2()', content)

        # Fix triple equals (===) -> normal equals (===)
        content = re.sub(r'([\s(=!])===', r'\1===', content)
        content = re.sub(r'===\s*(["\'])', r'=== \1', content)
        content = re.sub(r'(\w+)\s*===\s*(["\'])', r'\1 === \2', content)
        
        # Fix incorrect JSX attribute syntax
        content = re.sub(r'(\w+)\s*===\s*("|\')(.*?)("|\')(\s*>|\s*/)', r'\1=\2\3\4\5', content)
        content = re.sub(r'(\w+)\s*===\s*({.*?})(\s*>|\s*/)', r'\1=\2\3', content)
        
        # Fix incorrect React component props
        content = re.sub(r'(\w+):\s*React\.FC<\w+>\s*===\s*\({', r'\1: React.FC<\1Props> = ({', content)
        
        # Fix JSX closing tags
        content = re.sub(r'</\w+></(\w+)>', r'</\1>', content)
        
        # Fix common JSX errors
        content = re.sub(r'<([A-Z]\w*)\s+([^>]*)\s+==', r'<\1 \2 =', content)
        
        # Fix unclosed JSX elements
        content = re.sub(r'(<[A-Z]\w+[^<>]*>)([^<]*?)(<\/\w+>)', r'\1\2\3', content)
        
        # Fix template literals
        content = re.sub(r'`([^`]*?)\$\{([^}]*?)\}([^`]*?)`', r'`\1${\2}\3`', content)
        
        # Fix missing commas in object literals
        content = re.sub(r'(\w+):\s*(["\'0-9][^\n,]*?)(\s*^[}\)])', r'\1: \2,\3', content, flags=re.MULTILINE)
        
        # Fix useState hook syntax
        content = re.sub(r'const\s+\[(\w+),\s*set(\w+)\]\s*===\s*useState', r'const [\1, set\2] = useState', content)
        
        # Fix incorrect equals in switch cases
        content = re.sub(r'case\s+["\'](.*?)["\']\s*:(\s+)return\s+value\s*=\s*==\s*', r'case "\1":\2return value === ', content)
        
        # Fix semicolons after return statement
        content = re.sub(r'return ([^;]*?);?$', r'return \1;', content, flags=re.MULTILINE)
        
        # Fix incorrect value comparisons in conditional expressions
        content = re.sub(r'(if\s*\(\s*\w+\s*)>=\s*===(\s*\d+\s*\))', r'\1 >= \2', content)
        
        # Fix incorrect function return types
        content = re.sub(r'(function\s+\w+\s*\([^)]*\))\s*===\s*([^{]*){', r'\1: \2 {', content)
        
        # Fix invalid JSX expression syntax
        content = re.sub(r'return\s*\(;', r'return (', content)
        
        # Fix JSX self-closing tags
        content = re.sub(r'<([A-Z]\w+)([^<>]*?)>([^<>]*?)</\1>', r'<\1\2>\3</\1>', content)
        
        # Fix extra equals in JSX attributes
        content = re.sub(r'(\w+)\s*=\s*={2,}\s*("[^"]*?")', r'\1=\2', content)
        content = re.sub(r'(\w+)\s*=\s*={2,}\s*({[^}]*?})', r'\1=\2', content)

        # Vérifier si une modification a été apportée
        if content != original_content:
            # Créer une sauvegarde du fichier original
            backup_path = file_path + '.bak'
            shutil.copy2(file_path, backup_path)
            
            # Écrire le contenu corrigé
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            
            if verbose:
                print(f"Correction appliquée à: {file_path}")
            return True
        else:
            if verbose:
                print(f"Aucune correction nécessaire pour: {file_path}")
            return False
    except Exception as e:
        print(f"Erreur lors de la correction de {file_path}: {str(e)}")
        return False

def find_typescript_files(target_paths, exclude_node_modules=True):
    typescript_files = []
    
    for target_path in target_paths:
        if os.path.isfile(target_path) and (target_path.endswith('.ts') or target_path.endswith('.tsx')):
            typescript_files.append(target_path)
        elif os.path.isdir(target_path):
            # Recherche récursive des fichiers .ts et .tsx dans le répertoire
            pattern = os.path.join(target_path, '**', '*.ts*')
            files = glob.glob(pattern, recursive=True)
            
            # Filtrer les fichiers .d.ts (déclarations de types) et éventuellement node_modules
            for file_path in files:
                if not file_path.endswith('.d.ts'):
                    if exclude_node_modules and 'node_modules' in file_path:
                        continue
                    typescript_files.append(file_path)
    
    return typescript_files

def cleanup_backups(typescript_files):
    count = 0
    errors = 0
    
    for file_path in typescript_files:
        backup_path = file_path + '.bak'
        if os.path.exists(backup_path):
            try:
                os.remove(backup_path)
                count += 1
            except Exception as e:
                print(f"Erreur lors de la suppression de {backup_path}: {str(e)}")
                errors += 1
    
    return count, errors

def main():
    parser = argparse.ArgumentParser(description='Correct TypeScript syntax in files')
    parser.add_argument('paths', nargs='+', help='Path to TypeScript files or directories')
    parser.add_argument('--verbose', action='store_true', help='Display detailed information')
    parser.add_argument('--exclude-node-modules', action='store_true', help='Exclude node_modules directory')
    parser.add_argument('--cleanup', action='store_true', help='Clean up backup files after processing')
    parser.add_argument('--max-workers', type=int, default=8, help='Maximum number of worker threads')
    
    args = parser.parse_args()
    
    start_time = time.time()
    
    # Trouver tous les fichiers TypeScript
    typescript_files = find_typescript_files(args.paths, args.exclude_node_modules)
    
    if args.verbose:
        print(f"Found {len(typescript_files)} TypeScript files")
    
    # Paralléliser la correction des fichiers
    corrected_count = 0
    with ThreadPoolExecutor(max_workers=args.max_workers) as executor:
        futures = [executor.submit(fix_typescript_file, file_path, args.verbose) for file_path in typescript_files]
        
        for future in as_completed(futures):
            if future.result():
                corrected_count += 1
    
    # Nettoyage des fichiers de sauvegarde si demandé
    if args.cleanup:
        if args.verbose:
            print("Cleaning up backup files...")
        deleted_count, error_count = cleanup_backups(typescript_files)
        if args.verbose:
            print(f"Deleted {deleted_count} backup files with {error_count} errors")
    
    end_time = time.time()
    duration = end_time - start_time
    
    print("\nSummary:")
    print(f"Processed {len(typescript_files)} TypeScript files")
    print(f"Corrected {corrected_count} files")
    print(f"Execution time: {duration:.2f} seconds")

if __name__ == "__main__":
    main() 