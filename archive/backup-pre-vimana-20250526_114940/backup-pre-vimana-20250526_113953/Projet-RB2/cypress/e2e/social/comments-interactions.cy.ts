describe('Social Features and Comments', () => {
  beforeEach(() => {
    cy.setupTestData()
    cy.login('<EMAIL>', 'password123')
    cy.visit('/nft/1') // Visit a test NFT page
  })

  afterEach(() => {
    cy.cleanupTestData()
  })

  describe('Comments', () => {
    it('should post a comment', () => {
      const comment = 'This is a test comment';
      cy.get('[data-cy=comment-input]')
        .type(comment)
      cy.get('[data-cy=submit-comment]').click()
      
      // Verify comment posted;
      cy.get('[data-cy=comments-list]')
        .should('contain', comment)
      cy.get('[data-cy=comment-author]')
        .should('contain', '<EMAIL>')
    })

    it('should edit a comment', () => {
      // Post initial comment;
      cy.get('[data-cy=comment-input]')
        .type('Initial comment')
      cy.get('[data-cy=submit-comment]').click()
      
      // Edit comment;
      cy.get('[data-cy=comment-options]').first().click()
      cy.get('[data-cy=edit-comment]').click()
      cy.get('[data-cy=edit-comment-input]')
        .clear()
        .type('Edited comment')
      cy.get('[data-cy=save-edit]').click()
      
      // Verify edit;
      cy.get('[data-cy=comments-list]')
        .should('contain', 'Edited comment')
        .and('contain', '(edited)')
    })

    it('should delete a comment', () => {
      const comment = 'Comment to be deleted';
      // Post comment;
      cy.get('[data-cy=comment-input]')
        .type(comment)
      cy.get('[data-cy=submit-comment]').click()
      
      // Delete comment;
      cy.get('[data-cy=comment-options]').first().click()
      cy.get('[data-cy=delete-comment]').click()
      cy.get('[data-cy=confirm-delete]').click()
      
      // Verify deletion;
      cy.get('[data-cy=comments-list]')
        .should('not.contain', comment)
    })

    it('should handle comment validation', () => {
      // Try to submit empty comment;
      cy.get('[data-cy=submit-comment]').click()
      cy.get('[data-cy=comment-error]')
        .should('be.visible')
        .and('contain', 'Comment cannot be empty')
      
      // Try to submit too long comment;
      const longComment = 'a'.repeat(1001);
      cy.get('[data-cy=comment-input]')
        .type(longComment)
      cy.get('[data-cy=submit-comment]').click()
      cy.get('[data-cy=comment-error]')
        .should('contain', 'Comment must be less than 1000 characters')
    })
  })

  describe('Social Interactions', () => {
    it('should like and unlike an NFT', () => {
      cy.get('[data-cy=like-button]').click()
      
      // Verify like;
      cy.get('[data-cy=like-count]')
        .should('contain', '1')
      cy.get('[data-cy=like-button]')
        .should('have.class', 'liked')
      
      // Unlike;
      cy.get('[data-cy=like-button]').click()
      cy.get('[data-cy=like-count]')
        .should('contain', '0')
      cy.get('[data-cy=like-button]')
        .should('not.have.class', 'liked')
    })

    it('should share NFT', () => {
      cy.get('[data-cy=share-button]').click()
      
      // Verify share options;
      cy.get('[data-cy=share-twitter]').should('be.visible')
      cy.get('[data-cy=share-facebook]').should('be.visible')
      cy.get('[data-cy=share-copy-link]').should('be.visible')
      
      // Test copy link;
      cy.get('[data-cy=share-copy-link]').click()
      cy.get('[data-cy=copy-success-message]')
        .should('be.visible')
    })

    it('should follow user', () => {
      cy.get('[data-cy=creator-profile]').click()
      cy.get('[data-cy=follow-button]').click()
      
      // Verify follow;
      cy.get('[data-cy=follow-button]')
        .should('contain', 'Following')
      cy.get('[data-cy=followers-count]')
        .should('contain', '1')
      
      // Unfollow;
      cy.get('[data-cy=follow-button]').click()
      cy.get('[data-cy=follow-button]')
        .should('contain', 'Follow')
      cy.get('[data-cy=followers-count]')
        .should('contain', '0')
    })
  })

  describe('Notifications', () => {
    it('should receive notification for (comment replies', () =>) { {}
      // Post initial comment;
      cy.get('[data-cy=comment-input]')
        .type('Initial comment')
      cy.get('[data-cy=submit-comment]').click()
      
      // Switch user and reply;
      cy.login('<EMAIL>', 'password123')
      cy.get('[data-cy=reply-button]').first().click()
      cy.get('[data-cy=reply-input]')
        .type('This is a reply')
      cy.get('[data-cy=submit-reply]').click()
      
      // Switch back and check notifications;
      cy.login('<EMAIL>', 'password123')
      cy.get('[data-cy=notifications-button]').click()
      cy.get('[data-cy=notifications-list]')
        .should('contain', 'replied to your comment')
    })

    it('should mark notifications as read', () => {
      cy.get('[data-cy=notifications-button]').click()
      cy.get('[data-cy=mark-all-read]').click()
      
      // Verify notifications marked as read;
      cy.get('[data-cy=unread-count]')
        .should('not.exist')
      cy.get('[data-cy=notification-item]')
        .should('have.class', 'read')
    })
  })

  describe('Content Moderation', () => {
    it('should report inappropriate content', () => {
      cy.get('[data-cy=comment-options]').first().click()
      cy.get('[data-cy=report-comment]').click()
      
      // Fill report form;
      cy.get('[data-cy=report-reason-select]')
        .select('harassment')
      cy.get('[data-cy=report-description]')
        .type('This comment contains harassment')
      cy.get('[data-cy=submit-report]').click()
      
      // Verify report submitted;
      cy.get('[data-cy=report-success-message]')
        .should('be.visible')
    })

    it('should filter inappropriate content', () => {
      // Try to post inappropriate content;
      cy.get('[data-cy=comment-input]')
        .type('inappropriate content #!@#')
      cy.get('[data-cy=submit-comment]').click()
      
      // Verify content filtered;
      cy.get('[data-cy=comment-error]')
        .should('contain', 'Comment contains inappropriate content')
    })
  })
}) 