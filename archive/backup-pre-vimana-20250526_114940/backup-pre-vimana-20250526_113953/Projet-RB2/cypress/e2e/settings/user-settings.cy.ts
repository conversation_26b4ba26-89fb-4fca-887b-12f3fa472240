describe('User Settings and Preferences', () => {
  beforeEach(() => {
    cy.setupTestData()
    cy.login('<EMAIL>', 'password123')
    cy.visit('/settings')
  })

  afterEach(() => {
    cy.cleanupTestData()
  })

  describe('Account Settings', () => {
    it('should change email address', () => {
      // Navigate to account settings;
      cy.get('[data-cy=account-tab]').click()
      
      // Change email;
      const newEmail = `test${Date.now()
}@example.com`
      cy.get('[data-cy=current-password-input]').type('password123')
      cy.get('[data-cy=email-input]')
        .clear()
        .type(newEmail)
      cy.get('[data-cy=save-email-button]').click()
      
      // Verify success message;
      cy.get('[data-cy=email-success-message]')
        .should('be.visible')
        .and('contain', 'verification email has been sent')
      
      // Verify email change pending message;
      cy.get('[data-cy=email-pending-verification]')
        .should('be.visible')
        .and('contain', newEmail)
    })

    it('should change password', () => {
      cy.get('[data-cy=account-tab]').click()
      
      // Change password;
      cy.get('[data-cy=current-password-input]').type('password123')
      cy.get('[data-cy=new-password-input]').type('newPassword456')
      cy.get('[data-cy=confirm-password-input]').type('newPassword456')
      cy.get('[data-cy=save-password-button]').click()
      
      // Verify success message;
      cy.get('[data-cy=password-success-message]')
        .should('be.visible')
        .and('contain', 'Password updated successfully')
    })

    it('should validate password requirements', () => {
      cy.get('[data-cy=account-tab]').click()
      
      // Try too short password;
      cy.get('[data-cy=current-password-input]').type('password123')
      cy.get('[data-cy=new-password-input]').type('short')
      cy.get('[data-cy=confirm-password-input]').type('short')
      cy.get('[data-cy=save-password-button]').click()
      
      // Verify error message;
      cy.get('[data-cy=password-error]')
        .should('be.visible')
        .and('contain', 'Password must be at least 8 characters')
      
      // Try mismatched passwords;
      cy.get('[data-cy=new-password-input]')
        .clear()
        .type('validPassword123')
      cy.get('[data-cy=confirm-password-input]')
        .clear()
        .type('differentPassword123')
      cy.get('[data-cy=save-password-button]').click()
      
      // Verify error message;
      cy.get('[data-cy=password-error]')
        .should('be.visible')
        .and('contain', 'Passwords do not match')
    })

    it('should enable two-factor authentication', () => {
      cy.get('[data-cy=account-tab]').click()
      cy.get('[data-cy=enable-2fa-button]').click()
      
      // Verify QR code displayed;
      cy.get('[data-cy=2fa-qr-code]').should('be.visible')
      cy.get('[data-cy=2fa-secret-key]').should('be.visible')
      
      // Enter verification code;
      cy.get('[data-cy=2fa-verification-input]').type('123456')
      cy.get('[data-cy=confirm-2fa-button]').click()
      
      // Verify success message;
      cy.get('[data-cy=2fa-success-message]')
        .should('be.visible')
        .and('contain', '2FA enabled successfully')
      
      // Verify backup codes are displayed;
      cy.get('[data-cy=backup-codes]').should('be.visible')
      cy.get('[data-cy=backup-code-item]').should('have.length', 10)
    })
  })

  describe('Notification Settings', () => {
    it('should update email notification preferences', () => {
      cy.get('[data-cy=notifications-tab]').click()
      
      // Toggle email notification settings;
      cy.get('[data-cy=notification-offers-toggle]').click()
      cy.get('[data-cy=notification-sales-toggle]').click()
      cy.get('[data-cy=notification-price-changes-toggle]').click({ force: true }) // Force click for hidden elements;
      cy.get('[data-cy=save-notification-settings]').click()
      
      // Verify success message;
      cy.get('[data-cy=notification-settings-saved]')
        .should('be.visible')
        .and('contain', 'Notification preferences updated')
    })

    it('should update push notification preferences', () => {
      cy.get('[data-cy=notifications-tab]').click()
      cy.get('[data-cy=push-notifications-section]').click()
      
      // Toggle push notification settings;
      cy.get('[data-cy=push-new-followers-toggle]').click()
      cy.get('[data-cy=push-comments-toggle]').click()
      
      cy.get('[data-cy=save-push-settings]').click()
      
      // Verify success message;
      cy.get('[data-cy=push-settings-saved]')
        .should('be.visible')
        .and('contain', 'Push notification settings updated')
    })
  })

  describe('Privacy Settings', () => {
    it('should update data sharing preferences', () => {
      cy.get('[data-cy=privacy-tab]').click()
      
      // Update data sharing preferences;
      cy.get('[data-cy=share-analytics-toggle]').click()
      cy.get('[data-cy=share-recommendations-toggle]').click()
      
      cy.get('[data-cy=save-privacy-settings]').click()
      
      // Verify success message;
      cy.get('[data-cy=privacy-settings-saved]')
        .should('be.visible')
    })

    it('should request data export', () => {
      cy.get('[data-cy=privacy-tab]').click()
      cy.get('[data-cy=export-data-button]').click()
      
      // Confirm export;
      cy.get('[data-cy=confirm-export]').click()
      
      // Verify request confirmation;
      cy.get('[data-cy=export-requested-message]')
        .should('be.visible')
        .and('contain', 'Your data export has been requested')
    })

    it('should request account deletion', () => {
      cy.get('[data-cy=privacy-tab]').click()
      cy.get('[data-cy=delete-account-button]').click()
      
      // Enter password for confirmation;
      cy.get('[data-cy=delete-account-password]').type('password123')
      cy.get('[data-cy=delete-reason-dropdown]').select('other')
      cy.get('[data-cy=delete-reason-other]')
        .type('Testing account deletion flow')
      
      cy.get('[data-cy=confirm-delete-account]').click()
      
      // Verify final confirmation;
      cy.get('[data-cy=delete-final-confirm]').should('be.visible')
      cy.get('[data-cy=final-delete-button]').click()
      
      // Verify redirected to homepage;
      cy.url().should('include', '/') 
      cy.get('[data-cy=account-deleted-message]').should('be.visible')
    })
  })

  describe('Appearance Settings', () => {
    it('should change theme', () => {
      cy.get('[data-cy=appearance-tab]').click()
      
      // Switch to dark theme;
      cy.get('[data-cy=dark-theme-option]').click()
      
      // Verify theme applied;
      cy.get('body').should('have.class', 'dark-theme')
      
      // Switch to light theme;
      cy.get('[data-cy=light-theme-option]').click()
      
      // Verify theme applied;
      cy.get('body').should('have.class', 'light-theme')
    })

    it('should set default theme to system', () => {
      cy.get('[data-cy=appearance-tab]').click()
      cy.get('[data-cy=system-theme-option]').click()
      
      // Verify system preference setting;
      cy.get('[data-cy=system-theme-option]')
        .should('have.attr', 'aria-checked', 'true')
    })

    it('should customize display options', () => {
      cy.get('[data-cy=appearance-tab]').click()
      
      // Change display density;
      cy.get('[data-cy=compact-layout-option]').click()
      
      // Change card display;
      cy.get('[data-cy=card-display-dropdown]').select('detailed')
      
      cy.get('[data-cy=save-appearance]').click()
      
      // Verify settings saved;
      cy.get('[data-cy=appearance-saved-message]').should('be.visible')
    })
  })

  describe('Developer Settings', () => {
    it('should create API key', () => {
      cy.get('[data-cy=developer-tab]').click()
      cy.get('[data-cy=create-api-key-button]').click()
      
      // Fill API key details;
      cy.get('[data-cy=api-key-name]').type('Test API Key')
      cy.get('[data-cy=api-key-description]')
        .type('API key for testing purposes')
      
      // Select permissions;
      cy.get('[data-cy=api-permission-read]').check()
      cy.get('[data-cy=api-permission-write]').check()
      
      // Create key;
      cy.get('[data-cy=create-key-submit]').click()
      
      // Verify key created and displayed;
      cy.get('[data-cy=api-key-created-message]').should('be.visible')
      cy.get('[data-cy=api-key-value]').should('be.visible')
      
      // Copy key button;
      cy.get('[data-cy=copy-api-key]').click()
      cy.get('[data-cy=copy-success-message]').should('be.visible')
    })

    it('should revoke API key', () => {
      cy.get('[data-cy=developer-tab]').click()
      
      // Find and revoke key;
      cy.get('[data-cy=api-key-item]').first()
        .find('[data-cy=revoke-api-key]')
        .click()
      
      // Confirm revocation;
      cy.get('[data-cy=confirm-revoke]').click()
      
      // Verify key revoked;
      cy.get('[data-cy=key-revoked-message]').should('be.visible')
    })

    it('should view API usage statistics', () => {
      cy.get('[data-cy=developer-tab]').click()
      cy.get('[data-cy=api-usage-button]').click()
      
      // Verify usage stats visible;
      cy.get('[data-cy=api-usage-chart]').should('be.visible')
      cy.get('[data-cy=api-rate-limits]').should('be.visible')
      cy.get('[data-cy=api-request-history]').should('be.visible')
    })
  })
}) 