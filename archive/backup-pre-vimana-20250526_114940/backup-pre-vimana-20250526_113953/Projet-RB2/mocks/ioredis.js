// Mock pour ioredis
class RedisMock {
  constructor(options) {
    this.storage = new Map();
    this.options = options || {};
    this.keyPrefix = this.options.keyPrefix || '';
  }

  async get(key) {
    return this.storage.get(this.keyPrefix + key);
  }

  async set(key, value, ttl) {
    this.storage.set(this.keyPrefix + key, value);
    return 'OK';
  }

  async del(key) {
    if (Array.isArray(key)) {
      let count = 0;
      for (const k of key) {
        if (this.storage.delete(this.keyPrefix + k)) {
          count++;
        }
      }
      return count;
    }
    return this.storage.delete(this.keyPrefix + key) ? 1 : 0;
  }

  async flushall() {
    this.storage.clear();
    return 'OK';
  }

  async ping() {
    return 'PONG';
  }

  async hset(key, field, value) {
    const hash = this.storage.get(this.keyPrefix + key) || {};
    hash[field] = value;
    this.storage.set(this.keyPrefix + key, hash);
    return 'OK';
  }

  async hget(key, field) {
    const hash = this.storage.get(this.keyPrefix + key) || {};
    return hash[field];
  }

  async hgetall(key) {
    return this.storage.get(this.keyPrefix + key) || {};
  }

  async hincrby(key, field, increment) {
    const hash = this.storage.get(this.keyPrefix + key) || {};
    const current = parseInt(hash[field] || '0', 10);
    hash[field] = (current + increment).toString();
    this.storage.set(this.keyPrefix + key, hash);
    return parseInt(hash[field], 10);
  }

  async lpush(key, value) {
    let list = this.storage.get(this.keyPrefix + key) || [];
    if (!Array.isArray(list)) {
      list = [];
    }
    list.unshift(value);
    this.storage.set(this.keyPrefix + key, list);
    return list.length;
  }

  async lrange(key, start, stop) {
    const list = this.storage.get(this.keyPrefix + key) || [];
    return list.slice(start, stop === -1 ? undefined : stop + 1);
  }

  async quit() {
    return 'OK';
  }

  async disconnect() {
    return 'OK';
  }
}

module.exports = {
  Redis: RedisMock
};

module.exports.default = module.exports; 