// Mock moderne et complet pour winston
const formatMethods = {
  colorize: jest.fn().mockReturnValue({}),
  combine: jest.fn().mockImplementation((...args) => ({ formats: args })),
  timestamp: jest.fn().mockReturnValue({}),
  printf: jest.fn().mockImplementation(fn => ({ transform: fn })),
  errors: jest.fn().mockReturnValue({}),
  json: jest.fn().mockReturnValue({}),
  prettyPrint: jest.fn().mockReturnValue({}),
  splat: jest.fn().mockReturnValue({}),
  simple: jest.fn().mockReturnValue({}),
  label: jest.fn().mockReturnValue({}),
  metadata: jest.fn().mockReturnValue({})
};

const loggerInstance = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
  log: jest.fn(),
  child: jest.fn().mockReturnThis(),
  on: jest.fn(),
  once: jest.fn(),
  // Méthodes supplémentaires
  close: jest.fn().mockResolvedValue(undefined),
  clear: jest.fn(),
  profile: jest.fn(),
  startTimer: jest.fn().mockReturnValue({
    done: jest.fn()
  }),
  silence: jest.fn(),
  configure: jest.fn(),
  add: jest.fn().mockReturnThis(),
  remove: jest.fn().mockReturnThis(),
  exceptions: {
    handle: jest.fn(),
    unhandle: jest.fn()
  },
  rejections: {
    handle: jest.fn(),
    unhandle: jest.fn()
  },
  format: formatMethods,
  setLevels: jest.fn(),
  stream: jest.fn().mockReturnValue({
    on: jest.fn()
  }),
  query: jest.fn().mockResolvedValue([]),
  isLevelEnabled: jest.fn().mockReturnValue(true)
};

const ConsoleTransport = jest.fn().mockImplementation(() => ({
  on: jest.fn(),
  once: jest.fn(),
  format: formatMethods,
  level: 'info',
  silent: false,
  handleExceptions: false,
  handleRejections: false,
  log: jest.fn(),
  logv: jest.fn(),
  close: jest.fn().mockResolvedValue(undefined),
  setMaxListeners: jest.fn()
}));

const FileTransport = jest.fn().mockImplementation(() => ({
  on: jest.fn(),
  once: jest.fn(),
  format: formatMethods,
  level: 'info',
  silent: false,
  handleExceptions: false,
  handleRejections: false,
  log: jest.fn(),
  logv: jest.fn(),
  close: jest.fn().mockResolvedValue(undefined),
  setMaxListeners: jest.fn(),
  filename: 'mock.log',
  dirname: '/mock/logs',
  options: {}
}));

const winston = {
  format: formatMethods,
  createLogger: jest.fn().mockReturnValue(loggerInstance),
  addColors: jest.fn(),
  config: {
    npm: {
      levels: {
        error: 0,
        warn: 1,
        info: 2,
        http: 3,
        verbose: 4,
        debug: 5,
        silly: 6
      },
      colors: {
        error: 'red',
        warn: 'yellow',
        info: 'green',
        http: 'green',
        verbose: 'cyan',
        debug: 'blue',
        silly: 'magenta'
      }
    }
  },
  transports: {
    Console: ConsoleTransport,
    File: FileTransport,
    Stream: jest.fn().mockImplementation(() => ({})),
    Http: jest.fn().mockImplementation(() => ({}))
  },
  level: 'info',
  add: jest.fn(),
  remove: jest.fn(),
  clear: jest.fn(),
  cli: jest.fn(),
  profile: jest.fn(),
  startTimer: jest.fn(),
  query: jest.fn(),
  stream: jest.fn(),
  handleExceptions: jest.fn(),
  unhandleExceptions: jest.fn(),
  handleRejections: jest.fn(),
  unhandleRejections: jest.fn(),
  rejectionLevels: {},
  exceptionLevels: {},
  padLevels: true,
  exitOnError: true,
  default: {},
  exceptions: {
    handle: jest.fn(),
    unhandle: jest.fn()
  },
  rejections: {
    handle: jest.fn(),
    unhandle: jest.fn()
  },
  // Implémentation des méthodes de journalisation au niveau supérieur
  log: jest.fn(),
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
  silly: jest.fn(),
  http: jest.fn()
};

winston.default = winston;

module.exports = winston; 