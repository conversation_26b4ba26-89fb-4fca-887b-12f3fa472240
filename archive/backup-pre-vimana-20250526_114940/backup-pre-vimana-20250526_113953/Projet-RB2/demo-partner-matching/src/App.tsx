import React from 'react';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { AppBar, Toolbar, Typography, Box, IconButton } from '@mui/material';
import PartnerMatchingExample from './pages/PartnerMatchingExample';
import NotificationCenter from './components/NotificationCenter';

const App: React.FC = () => {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ flexGrow: 1 }}>
        <AppBar position="static">
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              Retreat And Be - Partner Portal
            </Typography>
            <NotificationCenter />
          </Toolbar>
        </AppBar>
        <PartnerMatchingExample />
      </Box>
    </LocalizationProvider>
  );
};

export default App;
