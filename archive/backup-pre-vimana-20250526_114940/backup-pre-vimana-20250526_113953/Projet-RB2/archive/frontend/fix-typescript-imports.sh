#!/bin/bash

# Script pour corriger les erreurs d'importation dans les fichiers TypeScript
# Ce script recherche les motifs problématiques d'importation et les remplace par des importations valides

# Fonction pour corriger les importations dans un fichier
fix_imports() {
  local file=$1
  
  # Vérifie si le fichier existe
  if [ ! -f "$file" ]; then
    echo "Le fichier $file n'existe pas, ignoré."
    return
  fi
  
  echo "Correction des importations dans $file"
  
  # Remplace les importations problématiques "import * from ''react';" par "import React from 'react';"
  sed -i '' "s/import \* from ''react';/import React from 'react';/g" "$file"
  
  # Remplace les autres importations problématiques "import * from ''package';" par "import * as package from 'package';"
  sed -i '' "s/import \* from ''@testing-library\/react';/import * as TestingLibrary from '@testing-library\/react';/g" "$file"
  sed -i '' "s/import \* from ''jest-axe';/import * as axe from 'jest-axe';/g" "$file"
  sed -i '' "s/import \* from ''react-redux';/import * as ReactRedux from 'react-redux';/g" "$file"
  sed -i '' "s/import \* from ''react-router-dom';/import * as ReactRouter from 'react-router-dom';/g" "$file"
  sed -i '' "s/import \* from ''@sentry\/react';/import * as Sentry from '@sentry\/react';/g" "$file"
  
  # Autres remplacements génériques pour les imports qui suivent ce pattern
  sed -i '' -E "s/import \* from ''([^']+)';/import * as \1 from '\1';/g" "$file"
}

# Parcours tous les fichiers TypeScript et TypeScript React dans le projet
echo "Recherche des fichiers TypeScript à corriger..."
find . -type f \( -name "*.ts" -o -name "*.tsx" \) | while read -r file; do
  # Vérifie si le fichier contient des importations problématiques
  if grep -q "import \* from ''" "$file"; then
    fix_imports "$file"
  fi
done

echo "Correction terminée. Exécutez 'npx tsc --noEmit' pour vérifier les erreurs restantes."
