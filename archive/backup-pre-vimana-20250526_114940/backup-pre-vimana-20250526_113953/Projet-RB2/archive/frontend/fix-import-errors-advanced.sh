#!/bin/bash

# Script pour corriger automatiquement les erreurs d'importation dans les fichiers TypeScript

# Couleurs pour les messages
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
NC="\033[0m" # No Color

echo "${YELLOW}Début de la correction des erreurs d'importation...${NC}"

# 1. Correction des imports incorrects avec "./" dans l'alias "as"
echo "${GREEN}Correction des imports avec './' dans l'alias 'as'...${NC}"
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) | xargs -I{} sed -i '' 's/import \* as \.\//import { api } from '\''.\//g' {}

# 2. Correction des imports incorrects avec "../" dans l'alias "as"
echo "${GREEN}Correction des imports avec '../' dans l'alias 'as'...${NC}"
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) | xargs -I{} sed -i '' 's/import \* as \.\.\//import { api } from '\''..\/\//g' {}

# 3. Correction des guillemets non fermés dans les exports
echo "${GREEN}Correction des guillemets non fermés dans les exports...${NC}"
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) | xargs -I{} sed -i '' 's/export \* from "\(.*\)'\''/export * from '\''\\1'\''/' {}

# 4. Correction des imports de styles CSS
echo "${GREEN}Correction des imports de styles CSS...${NC}"
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) | xargs -I{} sed -i '' 's/import \* as \(.*\)\.module\.css/import styles from '\''\\1.module.css'\''/' {}

# 5. Correction des imports de types
echo "${GREEN}Correction des imports de types...${NC}"
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) | xargs -I{} sed -i '' 's/import \* as \(.*\)\.types/import { ButtonProps, ButtonVariant } from '\''\\1.types'\''/' {}

echo "${YELLOW}Correction des erreurs d'importation terminée.${NC}"
