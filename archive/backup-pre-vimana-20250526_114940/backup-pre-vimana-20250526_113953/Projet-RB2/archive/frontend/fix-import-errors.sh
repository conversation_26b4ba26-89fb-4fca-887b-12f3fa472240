#!/bin/bash

# Script pour corriger automatiquement les erreurs d'importation dans les fichiers TypeScript

# Correction des imports incorrects avec "./" dans l'alias "as"
find ./src -type f -name "*.ts" -o -name "*.tsx" | xargs -I{} sed -i '' 's/import \* as \.\//import { api } from '"'"'.\//' {}

# Correction des imports incorrects avec "../" dans l'alias "as"
find ./src -type f -name "*.ts" -o -name "*.tsx" | xargs -I{} sed -i '' 's/import \* as \.\.\//import { api } from '"'"'..\//' {}

# Correction des guillemets non fermés dans les exports
find ./src -type f -name "*.ts" -o -name "*.tsx" | xargs -I{} sed -i '' 's/export \* from "\(.*\)'\''/export * from '"'"'\1'"'"'/' {}

echo "Correction des erreurs d'importation terminée."
