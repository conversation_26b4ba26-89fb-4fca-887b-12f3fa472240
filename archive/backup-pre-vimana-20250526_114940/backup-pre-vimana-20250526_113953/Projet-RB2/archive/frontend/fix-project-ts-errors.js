#!/usr/bin/env node

/**
 * Script pour corriger les erreurs TypeScript dans les fichiers du projet
 * sans se préoccuper des erreurs dans les node_modules
 */
import fs from 'fs';
import path from 'path';
import { glob } from 'glob';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

// Configuration
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT_DIR = process.cwd();
const SRC_DIR = path.join(ROOT_DIR, 'src');

// Motifs de correction courants
const FIXES = [
  // Correction des importations React
  {
    pattern: /^import\s+React\s+from\s+['"]react['"];/gm,
    replacement: "import * as React from 'react';"
  },
  {
    pattern: /^import\s+React,\s*{\s*([^}]+)\s*}\s+from\s+['"]react['"];/gm,
    replacement: "import * as React from 'react';\nconst { $1 } = React;"
  },
  {
    pattern: /^import\s+{\s*([^}]+)\s*}\s+from\s+['"]react['"];/gm,
    replacement: "import * as React from 'react';\nconst { $1 } = React;"
  },
  
  // Correction des variables non utilisées
  {
    pattern: /^(\s*)const\s+([a-zA-Z0-9_]+)\s*=.*?;\s*\/\/\s*.*?$/gm,
    test: (content, match) => {
      // Vérifier si la variable est utilisée ailleurs dans le fichier
      const varName = match[2];
      const varRegex = new RegExp(`[^a-zA-Z0-9_]${varName}[^a-zA-Z0-9_]`);
      return !varRegex.test(content.replace(match[0], ''));
    },
    replacement: (match) => `${match[1]}// Unused: ${match[0].trim()}`
  },
  
  // Correction des importations incorrectes
  {
    pattern: /import\s+\{\s*\.\//g,
    replacement: 'import { '
  },
  
  // Correction des types manquants
  {
    pattern: /(const|let|var)\s+([a-zA-Z0-9_]+)\s*=\s*([^:;]+);/g,
    test: (content, match) => {
      // Ajouter des types uniquement aux variables sans types explicites
      return !content.includes(`${match[2]}: `);
    },
    replacement: (match) => {
      const value = match[3].trim();
      let type = 'any';
      
      // Essayer d'inférer le type
      if (value === '[]') type = 'any[]';
      else if (value === '{}') type = 'Record<string, any>';
      else if (value.startsWith('"') || value.startsWith("'")) type = 'string';
      else if (/^-?\d+(\.\d+)?$/.test(value)) type = 'number';
      else if (value === 'true' || value === 'false') type = 'boolean';
      else if (value.includes('new ')) {
        const className = value.match(/new\s+([a-zA-Z0-9_]+)/);
        if (className) type = className[1];
      }
      
      return `${match[1]} ${match[2]}: ${type} = ${match[3]};`;
    }
  }
];

async function main() {
  try {
    // Trouver tous les fichiers TypeScript
    const tsFiles = await glob(path.join(SRC_DIR, '**/*.{ts,tsx}'), {
      ignore: ['**/node_modules/**', '**/*.d.ts']
    });

    console.log(`Trouvé ${tsFiles.length} fichiers TypeScript à traiter`);

    // Traiter chaque fichier
    let fixedFiles = 0;
    let fixedErrors = 0;

    for (const file of tsFiles) {
      let content = fs.readFileSync(file, 'utf8');
      let originalContent = content;
      let fileFixedErrors = 0;
      
      for (const fix of FIXES) {
        let matches = [...content.matchAll(fix.pattern)];
        
        for (const match of matches) {
          if (!fix.test || fix.test(content, match)) {
            const replacement = typeof fix.replacement === 'function' 
              ? fix.replacement(match) 
              : fix.replacement;
            
            content = content.replace(match[0], replacement);
            fileFixedErrors++;
          }
        }
      }
      
      if (content !== originalContent) {
        fs.writeFileSync(file, content, 'utf8');
        fixedFiles++;
        fixedErrors += fileFixedErrors;
        console.log(`Corrigé ${fileFixedErrors} erreurs dans ${file}`);
      }
    }

    console.log(`\nRésumé :`);
    console.log(`- Corrigé ${fixedErrors} erreurs dans ${fixedFiles} fichiers`);
    console.log(`- ${tsFiles.length - fixedFiles} fichiers étaient déjà corrects`);

    // Vérifier les erreurs restantes dans un fichier spécifique
    console.log('\nVérification des erreurs dans un fichier spécifique...');
    try {
      execSync('npx tsc --noEmit --skipLibCheck src/hooks/useAdaptiveBandwidth.ts', { stdio: 'inherit' });
      console.log('Vérification TypeScript terminée avec succès !');
    } catch (error) {
      console.error('La vérification TypeScript a trouvé des erreurs restantes.');
      console.log('Exécutez le script à nouveau ou corrigez les erreurs restantes manuellement.');
    }
  } catch (error) {
    console.error('Erreur lors de l\'exécution du script :', error);
    process.exit(1);
  }
}

// Exécuter la fonction principale
main().catch(error => {
  console.error('Erreur lors de l\'exécution du script :', error);
  process.exit(1);
});
