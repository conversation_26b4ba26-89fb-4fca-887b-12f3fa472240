# Guide de Tests de Sécurité

## Introduction

Ce document détaille notre stratégie de tests de sécurité frontend, expliquant comment les différents types de tests sont implémentés et comment les exécuter.

La sécurité est une préoccupation essentielle dans notre application. Nos tests de sécurité sont conçus pour identifier les vulnérabilités potentielles et valider que nos contrôles de sécurité fonctionnent correctement.

## Types de Tests de Sécurité

Notre stratégie de test de sécurité comporte plusieurs volets :

1. **Tests unitaires de sécurité** : Validation des contrôles de sécurité individuels
2. **Tests d'intégration de sécurité** : Vérification des interactions entre composants
3. **Tests de bout en bout (E2E)** : Simulation de scénarios d'attaque complets
4. **Analyse statique (SAST)** : Vérification du code à la recherche de vulnérabilités
5. **Analyse de dépendances** : Détection des vulnérabilités dans les bibliothèques
6. **Tests dynamiques (DAST)** : Analyse de l'application en cours d'exécution

## Structure des Tests

Nos tests de sécurité sont organisés comme suit :

```
src/
  tests/
    security/
      examples/            # Exemples spécifiques de tests de sécurité
        LoginFormSecurity.test.tsx
        JwtSecurity.test.tsx
        CsrfSecurity.test.tsx
        XssSecurity.test.tsx
        OpenRedirectSecurity.test.ts
        AuthorizationSecurity.test.tsx
        UserEnumerationTests.test.ts
        ...
      helpers/             # Utilitaires partagés pour les tests de sécurité
        SecurityTestHelpers.ts
        MockVulnerabilities.ts
      SecurityAnalyzer.ts  # Classe centrale pour l'analyse de sécurité
```

## Exécution des Tests

### Tests Unitaires et d'Intégration de Sécurité

Pour exécuter les tests de sécurité unitaires et d'intégration :

```bash
# Exécuter tous les tests de sécurité
npm run test:security

# Exécuter un fichier de test spécifique
npx jest src/tests/security/examples/XssSecurity.test.tsx
```

### Analyse de Dépendances

Pour vérifier les vulnérabilités dans les dépendances :

```bash
npm audit

# Pour générer un rapport détaillé
npm audit --json > security-reports/dependencies/npm-audit-results.json
```

### Tests Dynamiques (DAST)

Notre suite de tests inclut désormais des tests dynamiques (DAST) utilisant OWASP ZAP via Docker.

#### Prérequis

Pour exécuter les tests DAST localement, vous devez avoir :

1. Docker installé et en cours d'exécution
2. Node.js et npm installés
3. L'application en cours d'exécution localement (ou une URL cible accessible)

#### Types de Scans DAST

Nous prenons en charge trois types de scans DAST :

1. **Scan de base (baseline)** : Un scan rapide qui vérifie uniquement les vulnérabilités passives (sans attaques actives).
2. **Scan complet (full)** : Un scan approfondi qui exécute à la fois des tests passifs et actifs.
3. **Scan d'API (api)** : Un scan spécialisé pour les API REST et GraphQL.

#### Exécution des Scans DAST

##### Via NPM Scripts

```bash
# Rendre le script exécutable (une seule fois)
chmod +x scripts/run-dast-scan.ts

# Scan de base rapide
npm run security:dast:baseline

# Scan complet (plus long)
npm run security:dast:full

# Scan d'API
npm run security:dast:api

# Scan personnalisé avec des options spécifiques
DAST_TARGET_URL=https://staging.example.com DAST_HIGH_THRESHOLD=1 npm run security:dast
```

##### Via GitHub Actions

Les scans DAST sont automatiquement exécutés dans notre pipeline CI/CD :

- Scan de base sur chaque pull request
- Scan complet hebdomadaire sur l'environnement de staging
- Les résultats sont archivés sous forme d'artifacts GitHub

#### Configuration des Scans DAST

Vous pouvez configurer les scans DAST via des variables d'environnement :

| Variable              | Description                                      | Valeur par défaut     |
|-----------------------|--------------------------------------------------|----------------------|
| DAST_TARGET_URL       | URL de l'application à scanner                   | http://localhost:3000 |
| DAST_SCAN_TYPE        | Type de scan (baseline, full, api)               | baseline              |
| DAST_HIGH_THRESHOLD   | Seuil pour les vulnérabilités de criticité haute | 0                    |
| DAST_MEDIUM_THRESHOLD | Seuil pour les vulnérabilités de criticité moyenne | 5                  |
| DAST_LOW_THRESHOLD    | Seuil pour les vulnérabilités de criticité basse | 10                   |

#### Comprendre les Résultats DAST

Les rapports DAST sont générés dans le répertoire `security-reports/dast/` :

- Rapports HTML pour une visualisation conviviale
- Rapports JSON pour le traitement automatisé

Les vulnérabilités sont classées par niveau de risque :
- **Haute** : Vulnérabilités critiques nécessitant une attention immédiate
- **Moyenne** : Problèmes importants à résoudre rapidement
- **Basse** : Problèmes de moindre importance
- **Informationnel** : Informations qui ne constituent pas nécessairement des vulnérabilités

#### Bonnes Pratiques pour les Tests DAST

1. **Commencez par des scans de base** pour identifier rapidement les problèmes évidents
2. **Exécutez des scans complets périodiquement** pour une analyse approfondie
3. **Mettez à jour régulièrement votre scanner DAST** pour bénéficier des dernières règles
4. **Analysez les faux positifs** et ajustez les configurations en conséquence
5. **Intégrez les résultats DAST dans votre processus de développement**

#### Résolution des Problèmes Courants

Si vous rencontrez des problèmes lors de l'exécution des scans DAST :

1. **Vérifiez que Docker est en cours d'exécution**
2. **Assurez-vous que l'URL cible est accessible** depuis le conteneur Docker
3. **Augmentez temporairement les seuils** si vous travaillez sur une application en cours de développement
4. **Examinez les journaux Docker** pour plus d'informations sur les erreurs

## Meilleures Pratiques de Test de Sécurité

1. **Tests dès le début** : Intégrez les tests de sécurité dès le début du cycle de développement
2. **Couverture complète** : Assurez-vous de tester tous les aspects de sécurité
3. **Automatisation** : Intégrez les tests de sécurité dans votre pipeline CI/CD
4. **Variété des approches** : Combinez différentes techniques de test
5. **Mise à jour régulière** : Maintenez vos tests à jour avec les nouvelles menaces

## Ressources Additionnelles

- [Guide OWASP pour les Tests de Sécurité](https://owasp.org/www-project-web-security-testing-guide/)
- [Documentation officielle d'OWASP ZAP](https://www.zaproxy.org/docs/)
- [Bonnes pratiques pour les tests de sécurité frontend](https://cheatsheetseries.owasp.org/cheatsheets/Frontend_Security_Cheat_Sheet.html)

---

_Dernière mise à jour : 28 juin 2024_  
_Prochain examen : 28 juillet 2024_ 