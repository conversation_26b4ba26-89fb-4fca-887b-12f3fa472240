{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist", "noEmit": true, "jsx": "react-jsx", "lib": ["DOM", "DOM.Iterable", "ES2015", "ESNext"], "module": "ESNext", "moduleResolution": "node", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noUnusedLocals": false, "noUnusedParameters": false}, "include": ["src/**/*"], "exclude": ["node_modules", "cypress", "**/*.cy.ts", "**/*.cy.tsx", "**/*.spec.ts", "**/*.spec.tsx", "dist"]}