# Stratégie de test pour Redux

## Introduction

Ce document décrit notre approche pour tester l'architecture Redux dans notre application React. Notre stratégie se concentre sur le test des différentes parties de Redux de manière isolée pour s'assurer que chaque composant fonctionne comme prévu, puis sur des tests d'intégration pour vérifier leur interaction.

## Composants testés

### 1. Slices Redux

Nous testons les slices Redux individuellement pour vérifier que les reducers fonctionnent correctement. Les tests de slices vérifient que chaque action modifie l'état comme prévu et que l'état initial est correctement défini.

Exemple de fichier de test : `src/store/slices/__tests__/calendarSlice.test.ts`

```typescript
// Tests pour les reducers du slice calendar
it('should handle setEvents', () => {
  const events = [mockEvent];
  const nextState = reducer(initialState, setEvents(events));
  expect(nextState.events).toEqual(events);
});
```

### 2. Thunks asynchrones

Nous testons les thunks asynchrones pour vérifier qu'ils appellent les services corrects et dispatche les actions appropriées. Ces tests utilisent des mocks pour simuler les appels API et les fonctions dispatch.

Exemple de fichier de test : `src/store/thunks/__tests__/calendarThunks.test.ts`

```typescript
it('returns a thunk function that can be executed', () => {
  const thunkAction = fetchEventById('event-1');
  expect(typeof thunkAction).toBe('function');
  
  // Pour TypeScript, traiter comme une fonction qui retourne une Promise
  const thunkFn = thunkAction as unknown as (
    dispatch: (action: AnyAction) => void,
    getState: () => unknown
  ) => Promise<any>;
  
  return thunkFn(dispatch, () => ({})).then((result) => {
    expect(result).toEqual(expect.objectContaining({ id: 'event-1' }));
  });
});
```

### 3. Sélecteurs

Nous testons les sélecteurs pour vérifier qu'ils extraient correctement les données du state. Ces tests s'assurent que les sélecteurs mémorisés fonctionnent correctement.

Exemple de fichier de test : `src/store/selectors/__tests__/userSelectors.test.ts`

```typescript
it('should select user role', () => {
  const result = selectUserRole(mockState);
  expect(result).toBe('admin');
});
```

### 4. Tests d'intégration React-Redux

Nous testons l'intégration entre React et Redux pour vérifier que les composants React interagissent correctement avec le store Redux. Ces tests vérifient:

- Le rendu des composants en fonction de l'état du store
- La mise à jour du UI lorsque l'état du store change
- La dispatch d'actions lors d'événements utilisateur
- L'exécution de thunks asynchrones et la gestion des états de chargement
- La performance des sélecteurs mémorisés pour éviter les re-rendus inutiles

Exemples de fichiers de test:
- `src/tests/integration/CalendarComponent.test.tsx`: Teste l'intégration des composants avec le store
- `src/tests/integration/CalendarThunkComponent.test.tsx`: Teste l'intégration avec les thunks asynchrones
- `src/tests/integration/CalendarSelectors.test.tsx`: Teste l'intégration avec les sélecteurs mémorisés

```typescript
// Exemple de test d'intégration
it('should change view when clicking view buttons', () => {
  const store = createTestStore();
  const onViewChange = jest.fn();
  
  render(
    <Provider store={store}>
      <CalendarComponent onViewChange={onViewChange} onEventSelect={() => {}} />
    </Provider>
  );
  
  // Cliquer sur le bouton de vue semaine
  fireEvent.click(screen.getByTestId('week-view-btn'));
  
  // Vérifier que la vue a changé dans le composant
  expect(screen.getByTestId('calendar-view')).toHaveTextContent('week');
  
  // Vérifier que le callback a été appelé
  expect(onViewChange).toHaveBeenCalledWith('week');
  
  // Vérifier l'état du store
  expect(store.getState().calendar.view).toBe('week');
});
```

## Défis et solutions

### Mockage de Redux Toolkit

Bien que nous avions initialement prévu de tester l'intégration complète des différents slices avec un store réel, nous avons rencontré des difficultés avec le mockage de `@reduxjs/toolkit` dans Jest. Notre approche a évolué vers:

1. Des tests isolés pour les réducteurs et les actions
2. Des composants de test spécifiques qui interagissent avec un store Redux créé pour le test
3. Des mocks spécifiques pour les thunks dans les tests d'intégration

### Persistance et effets secondaires

Pour tester les fonctionnalités impliquant la persistance (redux-persist) ou d'autres effets secondaires, nous utilisons des mocks pour ces dépendances. Par exemple, nous avons créé des mocks pour `localforage` et `axios`.

### Tests asynchrones

Pour les tests impliquant des thunks asynchrones, nous utilisons les fonctionnalités de test asynchrone de Jest comme `async/await` et `waitFor` de `@testing-library/react` pour attendre que les promesses soient résolues.

## Bonnes pratiques

1. **Isolation** : Tester chaque composant Redux isolément pour identifier plus facilement les bugs.
2. **Mocking** : Utiliser des mocks pour les dépendances externes et les services.
3. **État initial** : Toujours vérifier l'état initial pour chaque reducer.
4. **Couverture complète** : Tester toutes les actions et tous les cas d'usage.
5. **Tests d'intégration React-Redux** : Pour tester l'intégration entre React et Redux, utiliser `@testing-library/react` avec un store réel ou mocké.
6. **Memoization**: Vérifier que les sélecteurs mémorisés ne recalculent pas leurs valeurs lorsque les entrées ne changent pas.

## Conclusion

Notre stratégie de test pour Redux combine des tests unitaires isolés pour chaque partie de l'architecture Redux avec des tests d'intégration qui vérifient comment ces parties fonctionnent ensemble dans un contexte React. Cette approche nous permet de maintenir une haute qualité de code et de détecter rapidement les régressions potentielles. 