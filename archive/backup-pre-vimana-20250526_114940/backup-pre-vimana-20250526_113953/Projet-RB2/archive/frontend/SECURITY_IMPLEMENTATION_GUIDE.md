# Guide d'implémentation de la sécurité

Ce guide explique comment implémenter des contrôles de sécurité efficaces dans notre application frontend React en s'appuyant sur les tests de sécurité que nous avons développés. Il fournit des exemples concrets et des modèles que vous pouvez adapter à vos composants.

## Table des matières

1. [Protection contre les attaques par injection](#protection-contre-les-attaques-par-injection)
2. [Sécurisation de la gestion des fichiers](#sécurisation-de-la-gestion-des-fichiers)
3. [Gestion sécurisée de l'état et des données sensibles](#gestion-sécurisée-de-létat-et-des-données-sensibles)
4. [Contrôles d'autorisation et de contrôle d'accès](#contrôles-dautorisation-et-de-contrôle-daccès)
5. [Protection contre les redirections ouvertes](#protection-contre-les-redirections-ouvertes)
6. [Principes généraux de développement sécurisé](#principes-généraux-de-développement-sécurisé)

## Protection contre les attaques par injection

### Comment implémenter la protection contre les injections XSS

1. **Utiliser React correctement pour l'échappement automatique**

   React échappe automatiquement les expressions dans le JSX, mais il existe des exceptions :

   ```jsx
   // ✅ React échappe automatiquement cette expression
   function SafeComponent({ userInput }) {
     return <div>{userInput}</div>;
   }
   
   // ❌ Éviter dangerouslySetInnerHTML sans sanitization
   function UnsafeComponent({ userInput }) {
     return <div dangerouslySetInnerHTML={{ __html: userInput }} />;
   }
   
   // ✅ Si vous devez utiliser dangerouslySetInnerHTML, utilisez DOMPurify
   import DOMPurify from 'dompurify';
   
   function SafeHtmlComponent({ userInput }) {
     const sanitizedHtml = DOMPurify.sanitize(userInput, {
       ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'],
       ALLOWED_ATTR: ['href', 'target', 'class']
     });
     
     return <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;
   }
   ```

2. **Valider toutes les entrées utilisateur**

   Utilisez des schémas de validation comme Yup ou Zod :

   ```jsx
   import * as Yup from 'yup';
   
   // Schéma de validation
   const commentSchema = Yup.object().shape({
     username: Yup.string()
       .required('Un nom d\'utilisateur est requis')
       .matches(/^[a-zA-Z0-9_-]+$/, 'Caractères non autorisés'),
     
     comment: Yup.string()
       .required('Le commentaire ne peut pas être vide')
       .max(500, 'Le commentaire est trop long')
       .matches(/^[a-zA-Z0-9\s.,!?'"()-]+$/, 'Caractères non autorisés')
   });
   
   function CommentForm() {
     const [values, setValues] = useState({ username: '', comment: '' });
     const [errors, setErrors] = useState({});
     
     const handleSubmit = async (e) => {
       e.preventDefault();
       
       try {
         // Valider les entrées
         await commentSchema.validate(values, { abortEarly: false });
         
         // Envoyer le commentaire (nettoyé et validé)
         await submitComment(values);
       } catch (validationError) {
         // Gérer les erreurs de validation
         const newErrors = {};
         validationError.inner.forEach((err) => {
           newErrors[err.path] = err.message;
         });
         setErrors(newErrors);
       }
     };
     
     // Reste du composant...
   }
   ```

### Comment implémenter la protection contre les injections SQL/NoSQL

Bien que les injections SQL/NoSQL soient principalement un problème backend, le frontend peut aider à atténuer les risques :

```jsx
// Service API sécurisé
class ApiService {
  // Pour les paramètres d'URL
  getUsers(params) {
    // Utiliser URLSearchParams pour encoder correctement les paramètres
    const searchParams = new URLSearchParams();
    
    // Valider et nettoyer les paramètres
    if (params.search) {
      // Valider que la recherche ne contient pas de caractères d'injection SQL
      const sanitizedSearch = params.search.replace(/['";\\]/g, '');
      searchParams.append('search', sanitizedSearch);
    }
    
    if (params.page && Number.isInteger(parseInt(params.page, 10))) {
      searchParams.append('page', params.page.toString());
    }
    
    // Construire l'URL correctement
    return axios.get(`/api/users?${searchParams.toString()}`);
  }
  
  // Pour les requêtes POST/PUT avec JSON
  searchProducts(query) {
    // Valider que query est un objet simple sans opérateurs MongoDB
    const sanitizedQuery = this.sanitizeMongoQuery(query);
    
    return axios.post('/api/products/search', sanitizedQuery);
  }
  
  // Fonction utilitaire pour nettoyer les objets de requête
  sanitizeMongoQuery(obj) {
    // Copier l'objet pour éviter de modifier l'original
    const sanitized = { ...obj };
    
    // Supprimer les clés commençant par $ (opérateurs MongoDB)
    Object.keys(sanitized).forEach(key => {
      if (key.startsWith('$')) {
        delete sanitized[key];
      } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        // Nettoyer récursivement les objets imbriqués
        sanitized[key] = this.sanitizeMongoQuery(sanitized[key]);
      }
    });
    
    return sanitized;
  }
}
```

## Sécurisation de la gestion des fichiers

### Comment implémenter un composant de téléchargement de fichiers sécurisé

```jsx
import React, { useState, useRef } from 'react';
import DOMPurify from 'dompurify';

// Constantes pour la validation des fichiers
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const ALLOWED_EXTENSIONS = {
  'image/jpeg': ['jpg', 'jpeg'],
  'image/png': ['png'],
  'application/pdf': ['pdf']
};

// Fonction d'assainissement des noms de fichiers
const sanitizeFileName = (fileName) => {
  return fileName
    .replace(/[\\/:*?"<>|]/g, '') // Supprime les caractères spéciaux
    .replace(/\.\./g, '')         // Supprime les tentatives de directory traversal
    .replace(/\s+/g, '_')         // Remplace les espaces par des underscores
    .slice(0, 100);               // Limite la longueur du nom
};

function SecureFileUpload({ onUploadComplete }) {
  const [file, setFile] = useState(null);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [preview, setPreview] = useState('');
  const fileInputRef = useRef(null);

  // Validation du type de fichier
  const validateFileType = (file) => {
    if (!ALLOWED_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: `Type de fichier non autorisé. Types autorisés: ${ALLOWED_TYPES.map(t => t.split('/')[1]).join(', ')}`
      };
    }
    
    // Vérifier la cohérence entre l'extension et le type MIME
    const extension = file.name.split('.').pop().toLowerCase();
    const allowedExtensions = ALLOWED_EXTENSIONS[file.type] || [];
    
    if (!allowedExtensions.includes(extension)) {
      return {
        valid: false,
        error: `Extension de fichier incompatible avec le type détecté`
      };
    }
    
    return { valid: true };
  };

  // Validation de la taille du fichier
  const validateFileSize = (file) => {
    if (file.size > MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `Fichier trop volumineux. Maximum: ${(MAX_FILE_SIZE / (1024 * 1024)).toFixed(1)}MB`
      };
    }
    return { valid: true };
  };

  // Gestionnaire de changement de fichier
  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    setError('');
    setPreview('');
    
    if (!selectedFile) return;
    
    // Valider le type de fichier
    const typeValidation = validateFileType(selectedFile);
    if (!typeValidation.valid) {
      setError(typeValidation.error);
      return;
    }
    
    // Valider la taille du fichier
    const sizeValidation = validateFileSize(selectedFile);
    if (!sizeValidation.valid) {
      setError(sizeValidation.error);
      return;
    }
    
    // Sanitizer le nom du fichier
    const sanitizedName = sanitizeFileName(selectedFile.name);
    
    // Créer un nouveau fichier avec le nom sanitisé
    const sanitizedFile = new File([selectedFile], sanitizedName, {
      type: selectedFile.type
    });
    
    setFile(sanitizedFile);
    
    // Générer une prévisualisation sécurisée
    if (selectedFile.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreview(e.target.result);
      };
      reader.readAsDataURL(selectedFile);
    }
  };

  // Gestionnaire de soumission
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file) {
      setError('Veuillez sélectionner un fichier');
      return;
    }
    
    setLoading(true);
    
    try {
      // Simuler la vérification de malware
      const isSafe = await checkFileForMalware(file);
      if (!isSafe) {
        setError('Le fichier semble contenir du contenu malveillant');
        setLoading(false);
        return;
      }
      
      // Créer un FormData pour l'envoi
      const formData = new FormData();
      formData.append('file', file);
      
      // Envoi du fichier
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        headers: {
          // Pas de Content-Type car il est défini automatiquement avec le bon boundary
          'X-CSRF-Token': getCsrfToken() // Fonction pour récupérer le token CSRF
        }
      });
      
      if (!response.ok) {
        throw new Error('Erreur lors du téléchargement');
      }
      
      const result = await response.json();
      
      // Vérifier l'intégrité du fichier
      const isIntegrityValid = await verifyFileIntegrity(file, result.fileHash);
      if (!isIntegrityValid) {
        setError('L\'intégrité du fichier n\'a pas pu être vérifiée');
        setLoading(false);
        return;
      }
      
      onUploadComplete(result);
      
      // Réinitialiser le formulaire
      setFile(null);
      setPreview('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      setError(error.message || 'Une erreur est survenue');
    } finally {
      setLoading(false);
    }
  };

  // Fonction fictive pour simuler une vérification de malware
  const checkFileForMalware = async (file) => {
    // Dans une vraie implémentation, vous enverriez le fichier à un service
    // de scan antivirus ou utiliseriez une API tierce
    return true; // Simuler un scan réussi
  };

  // Fonction fictive pour vérifier l'intégrité du fichier
  const verifyFileIntegrity = async (file, serverHash) => {
    // Dans une vraie implémentation, vous calculeriez un hash local
    // et le compareriez avec celui renvoyé par le serveur
    return true; // Simuler une vérification réussie
  };

  // Fonction fictive pour obtenir le token CSRF
  const getCsrfToken = () => {
    // Dans une vraie implémentation, récupérez le token d'un cookie ou du localStorage
    return 'csrf-token-example';
  };

  return (
    <div className="secure-file-upload">
      <form onSubmit={handleSubmit}>
        <div className="file-input-container">
          <input
            type="file"
            onChange={handleFileChange}
            accept={ALLOWED_TYPES.join(',')}
            ref={fileInputRef}
            aria-label="Choisir un fichier"
          />
        </div>
        
        {error && <div className="error-message">{error}</div>}
        
        {preview && (
          <div className="file-preview">
            <img 
              src={preview} 
              alt="Prévisualisation" 
              style={{ maxWidth: '200px', maxHeight: '200px' }} 
              data-testid="file-preview"
            />
          </div>
        )}
        
        {file && !preview && (
          <div className="file-info" data-testid="file-preview">
            <strong>Fichier sélectionné:</strong> {file.name}
            <br />
            <strong>Taille:</strong> {(file.size / 1024).toFixed(1)} KB
          </div>
        )}
        
        <button 
          type="submit" 
          disabled={!file || loading}
        >
          {loading ? 'Téléchargement en cours...' : 'Télécharger'}
        </button>
      </form>
    </div>
  );
}

export default SecureFileUpload;
```

## Gestion sécurisée de l'état et des données sensibles

### Comment implémenter une gestion d'état sécurisée avec Redux Toolkit

```jsx
// authSlice.js - Version sécurisée
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { tokenService } from '../services/TokenService';

// Action pour la connexion
export const login = createAsyncThunk(
  'auth/login',
  async (credentials, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
      });
      
      if (!response.ok) {
        throw new Error('Authentication failed');
      }
      
      const data = await response.json();
      
      // ✅ Stocker le token de manière sécurisée via un service dédié
      // au lieu de le stocker directement dans l'état Redux
      if (data.token) {
        tokenService.setToken(data.token);
      }
      
      // ✅ Ne retourner que les données utilisateur, pas le token
      return { user: data.user };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// Slice d'authentification sécurisé
const authSlice = createSlice({
  name: 'auth',
  initialState: {
    user: null,
    loading: false,
    error: null,
    isAuthenticated: false
    // ❌ PAS de stockage de credentials ou de tokens dans l'état
  },
  reducers: {
    logout: (state) => {
      // Nettoyer complètement l'état
      state.user = null;
      state.loading = false;
      state.error = null;
      state.isAuthenticated = false;
      
      // Nettoyer le token dans le service
      tokenService.clearToken();
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.isAuthenticated = true;
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.isAuthenticated = false;
      });
  }
});

// Service de gestion des tokens sécurisé
// tokenService.js
export const tokenService = {
  setToken(token) {
    // Vérifier que le token est valide
    if (!token || typeof token !== 'string') {
      throw new Error('Invalid token');
    }
    
    // Option 1: Cookie HttpOnly (nécessite une API backend)
    // Normalement géré par le backend, pas directement ici
    
    // Option 2: Stockage chiffré dans localStorage
    // Dans une vraie implémentation, le token serait chiffré côté client
    // avant d'être stocké dans localStorage
    localStorage.setItem('auth_token', token);
    
    // Stocker l'expiration
    const expiration = this.getTokenExpiration(token);
    if (expiration) {
      localStorage.setItem('token_expiration', expiration.toString());
    }
  },
  
  getToken() {
    const token = localStorage.getItem('auth_token');
    
    // Vérifier l'expiration
    if (token && this.isTokenExpired()) {
      this.clearToken();
      return null;
    }
    
    return token;
  },
  
  clearToken() {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('token_expiration');
  },
  
  isTokenExpired() {
    const expiration = localStorage.getItem('token_expiration');
    return expiration && parseInt(expiration, 10) < Date.now() / 1000;
  },
  
  getTokenExpiration(token) {
    try {
      // Simple JWT decode (dans une vraie app, utilisez jwt-decode)
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = JSON.parse(
        decodeURIComponent(
          atob(base64)
            .split('')
            .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
            .join('')
        )
      );
      
      return jsonPayload.exp;
    } catch (e) {
      console.error('Error decoding token', e);
      return null;
    }
  }
};
```

### Middleware de protection des données sensibles pour Redux

```jsx
// securityMiddleware.js
export const securityMiddleware = store => next => action => {
  // Laisser passer l'action
  const result = next(action);
  
  // Analyser l'état après l'action
  const state = store.getState();
  
  // Vérifier l'état pour des données sensibles
  checkForSensitiveData(state);
  
  return result;
};

// Fonction pour vérifier la présence de données sensibles dans l'état
const checkForSensitiveData = (state) => {
  // Liste de mots-clés sensibles
  const sensitiveKeywords = [
    'password', 'token', 'secret', 'key', 'credit', 'card', 
    'cvv', 'ssn', 'social', 'security'
  ];
  
  // Vérifier récursivement la présence de mots-clés sensibles
  const checkObject = (obj, path = '') => {
    if (!obj || typeof obj !== 'object') return;
    
    Object.entries(obj).forEach(([key, value]) => {
      const currentPath = path ? `${path}.${key}` : key;
      
      // Vérifier si la clé contient un mot-clé sensible
      const hasSensitiveKeyword = sensitiveKeywords.some(keyword => 
        key.toLowerCase().includes(keyword)
      );
      
      if (hasSensitiveKeyword && value) {
        console.error(`⚠️ SECURITY WARNING: Potentially sensitive data found in Redux state at '${currentPath}'`);
        // Dans un environnement de développement, on pourrait logger un avertissement
        // En production, on pourrait nettoyer automatiquement les données sensibles
      }
      
      // Vérifier récursivement les objets imbriqués
      if (value && typeof value === 'object') {
        checkObject(value, currentPath);
      }
    });
  };
  
  // Vérifier chaque slice de l'état
  Object.entries(state).forEach(([sliceName, sliceState]) => {
    checkObject(sliceState, sliceName);
  });
};
```

## Contrôles d'autorisation et de contrôle d'accès

### Comment implémenter un système d'autorisation basé sur les rôles (RBAC)

1. **Défnition du service d'autorisation**

```jsx
// authorizationService.js
export class AuthorizationService {
  // Matrice de permissions par rôle
  rolePermissions = {
    admin: ['read:*', 'write:*', 'delete:*'],
    manager: ['read:*', 'write:products', 'delete:products'],
    user: ['read:products', 'read:profile', 'write:profile'],
    guest: ['read:products']
  };

  // Vérifier si un utilisateur a une permission
  hasPermission(user, permission) {
    if (!user || !user.role) return false;
    
    const userPermissions = this.rolePermissions[user.role] || [];
    
    return userPermissions.some(p => {
      // Correspondance exacte
      if (p === permission) return true;
      
      // Permission générique avec wildcard
      if (p.endsWith(':*')) {
        const resourceType = p.split(':')[0];
        const requiredResourceType = permission.split(':')[0];
        
        return resourceType === requiredResourceType;
      }
      
      // Permission complète avec wildcard
      return p === '*';
    });
  }

  // Vérifier si un utilisateur a un rôle spécifique
  hasRole(user, requiredRole) {
    return user && user.role === requiredRole;
  }

  // Vérifier si un utilisateur a l'un des rôles fournis
  hasAnyRole(user, roles) {
    return user && user.role && roles.includes(user.role);
  }
}

export const authService = new AuthorizationService();
```

2. **Composants de protection de route**

```jsx
// ProtectedRoutes.js
import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { authService } from '../services/authorizationService';

// Protection par authentification
export const RequireAuth = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  
  if (!isAuthenticated) {
    // Rediriger vers la page de connexion avec le chemin d'origine pour redirection après connexion
    return <Navigate to={`/login?redirect=${encodeURIComponent(window.location.pathname)}`} replace />;
  }
  
  return children ? children : <Outlet />;
};

// Protection par rôle
export const RequireRole = ({ roles, children }) => {
  const { user } = useAuth();
  
  if (!user || !authService.hasAnyRole(user, roles)) {
    return <Navigate to="/forbidden" replace />;
  }
  
  return children ? children : <Outlet />;
};

// Protection par permission
export const RequirePermission = ({ permission, children }) => {
  const { user } = useAuth();
  
  if (!user || !authService.hasPermission(user, permission)) {
    return <Navigate to="/forbidden" replace />;
  }
  
  return children ? children : <Outlet />;
};
```

3. **Configuration des routes protégées**

```jsx
// App.js
import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { RequireAuth, RequireRole, RequirePermission } from "./components/ProtectedRoutes';

// Pages
import HomePage from "./pages/HomePage';
import LoginPage from "./pages/LoginPage';
import DashboardPage from "./pages/DashboardPage';
import ProfilePage from "./pages/ProfilePage';
import AdminPage from "./pages/AdminPage';
import ForbiddenPage from "./pages/ForbiddenPage';
import ProductsPage from "./pages/ProductsPage';
import CreateProductPage from "./pages/CreateProductPage';

function App() {
  return (
    <Routes>
      {/* Routes publiques */}
      <Route path="/" element={<HomePage />} />
      <Route path="/login" element={<LoginPage />} />
      <Route path="/products" element={<ProductsPage />} />
      <Route path="/forbidden" element={<ForbiddenPage />} />
      
      {/* Routes protégées par authentification */}
      <Route element={<RequireAuth />}>
        <Route path="/dashboard" element={<DashboardPage />} />
        
        {/* Routes protégées par permission */}
        <Route element={<RequirePermission permission="read:profile" />}>
          <Route path="/profile" element={<ProfilePage />} />
        </Route>
        
        <Route element={<RequirePermission permission="write:products" />}>
          <Route path="/products/create" element={<CreateProductPage />} />
        </Route>
        
        {/* Routes protégées par rôle */}
        <Route element={<RequireRole roles={['admin']} />}>
          <Route path="/admin" element={<AdminPage />} />
        </Route>
      </Route>
    </Routes>
  );
}

export default App;
```

4. **Composant pour le masquage conditionnel basé sur les permissions**

```jsx
// PermissionGate.js
import React from 'react';
import { useAuth } from '../hooks/useAuth';
import { authService } from '../services/authorizationService';

// Composant pour masquer/afficher conditionnellement des éléments basés sur les permissions
export const PermissionGate = ({ 
  permission, 
  role, 
  children, 
  fallback = null 
}) => {
  const { user } = useAuth();
  
  let hasAccess = false;
  
  if (role && permission) {
    // Vérifier le rôle ET la permission
    hasAccess = authService.hasRole(user, role) && 
               authService.hasPermission(user, permission);
  } else if (role) {
    // Vérifier seulement le rôle
    hasAccess = authService.hasRole(user, role);
  } else if (permission) {
    // Vérifier seulement la permission
    hasAccess = authService.hasPermission(user, permission);
  }
  
  return hasAccess ? <>{children}</> : <>{fallback}</>;
};

// Utilisation dans un composant
function ProductActions({ productId }) {
  return (
    <div className="product-actions">
      {/* Visible par tous */}
      <button>Voir détails</button>
      
      {/* Visible seulement par les utilisateurs avec la permission d'édition */}
      <PermissionGate permission="write:products">
        <button>Éditer</button>
      </PermissionGate>
      
      {/* Visible seulement par les admins */}
      <PermissionGate role="admin">
        <button className="danger">Supprimer</button>
      </PermissionGate>
      
      {/* Avec fallback */}
      <PermissionGate 
        permission="write:products" 
        fallback={<span className="disabled-tip">Vous n'avez pas les droits pour éditer</span>}
      >
        <button>Éditer</button>
      </PermissionGate>
    </div>
  );
}
```

## Protection contre les redirections ouvertes

### Comment implémenter un service de validation de redirections

```jsx
// redirectService.js
export class RedirectService {
  // Domaines externes autorisés pour les redirections
  allowedDomains = [
    'example.com',
    'trusted-partner.com',
    'api.myservice.com'
  ];
  
  // Chemins internes autorisés
  internalPaths = [
    '/dashboard',
    '/profile',
    '/account',
    '/login',
    '/logout',
    '/products'
  ];
  
  /**
   * Valide une URL de redirection pour éviter les redirections malveillantes
   */
  validateRedirectUrl(url) {
    // URLs relatives (chemins internes)
    if (url.startsWith('/')) {
      return this.internalPaths.some(path => 
        url === path || url.startsWith(`${path}/`)
      );
    }
    
    try {
      // URLs absolues (externes)
      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname;
      
      return this.allowedDomains.includes(domain);
    } catch (error) {
      // URL mal formée
      return false;
    }
  }
  
  /**
   * Effectue une redirection sécurisée
   */
  redirectTo(url) {
    if (this.validateRedirectUrl(url)) {
      return {
        success: true,
        redirectUrl: url
      };
    }
    
    // Redirection par défaut en cas d'URL non autorisée
    return {
      success: false,
      error: 'Redirection non autorisée',
      redirectUrl: '/dashboard'
    };
  }
}

export const redirectService = new RedirectService();
```

### Comment utiliser le service de redirection

```jsx
// LoginPage.js
import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { redirectService } from '../services/redirectService';

function LoginPage() {
  const [credentials, setCredentials] = useState({ email: '', password: '' });
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  // Extraire le paramètre de redirection de l'URL
  const getRedirectUrl = () => {
    const params = new URLSearchParams(location.search);
    const redirectParam = params.get('redirect');
    
    if (redirectParam) {
      // Valider l'URL de redirection
      const { success, redirectUrl } = redirectService.redirectTo(redirectParam);
      return success ? redirectUrl : '/dashboard';
    }
    
    return '/dashboard'; // Redirection par défaut
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      await login(credentials);
      
      // Obtenir l'URL de redirection sécurisée
      const redirectUrl = getRedirectUrl();
      
      navigate(redirectUrl);
    } catch (error) {
      // Gérer l'erreur d'authentification
    }
  };
  
  // Reste du composant...
}
```

## Principes généraux de développement sécurisé

### Validation et sanitisation

1. **Toujours valider les entrées utilisateur** avant de les utiliser ou de les envoyer au serveur.
2. **Valider côté client ET côté serveur** - la validation côté client est pour l'expérience utilisateur, la validation côté serveur est pour la sécurité.
3. **Utiliser des bibliothèques reconnues** comme Yup, Zod ou Joi pour la validation.
4. **Sanitiser les sorties** avant de les afficher.

### Gestion des tokens et de l'authentification

1. **Ne jamais stocker de tokens JWT dans localStorage** sans mesures supplémentaires.
2. **Préférer les cookies HttpOnly** configurés par le serveur pour le stockage des tokens.
3. **Toujours implémenter la vérification d'expiration** des tokens.
4. **Utiliser un mécanisme de rafraîchissement** pour les tokens.
5. **Nettoyer toutes les données d'authentification** lors de la déconnexion.

### Contrôle d'accès

1. **Implémenter une stratégie de contrôle d'accès** basée sur les rôles et/ou les permissions.
2. **Vérifier l'autorisation à plusieurs niveaux** : routes, composants, API calls.
3. **Ne pas exposer d'informations sensibles** dans l'interface selon le rôle de l'utilisateur.
4. **Appliquer le principe du moindre privilège** - n'accorder que les permissions nécessaires.

### Sécurité des API

1. **Toujours valider et sanitiser les paramètres** des requêtes API.
2. **Utiliser les tokens CSRF** pour les requêtes modifiant l'état.
3. **Valider les réponses API** avant de les utiliser.
4. **Gérer correctement les erreurs** sans exposer d'informations sensibles.

---

Cette documentation vous fournit les bases pour implémenter des contrôles de sécurité efficaces dans votre application frontend. Référez-vous aux tests de sécurité correspondants pour vérifier que vos implémentations sont correctes.

Pour toute question ou préoccupation de sécurité, contactez l'équipe de sécurité.

_Dernière mise à jour : 26 juin 2024_ 