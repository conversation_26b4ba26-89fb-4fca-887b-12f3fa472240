// Ce fichier garantit que les tests e2e ont tous les types dont ils ont besoin

// Définition correcte des fonctions globales de test pour éviter les erreurs TypeScript
// avec les chaînes de caractères passées aux fonctions describe et it

declare global {
  // Redéfinition des fonctions Mocha/Cypress pour éviter les conflits de type
  interface Describe {
    (description: string, spec: () => void): void;
    only(description: string, spec: () => void): void;
    skip(description: string, spec: () => void): void;
  }

  interface It {
    (description: string, test: () => void): void;
    only(description: string, test: () => void): void;
    skip(description: string, test: () => void): void;
  }

  interface BeforeEach {
    (action: () => void): void;
  }

  var describe: Describe;
  var it: It;
  var beforeEach: BeforeEach;
}

export {}; 