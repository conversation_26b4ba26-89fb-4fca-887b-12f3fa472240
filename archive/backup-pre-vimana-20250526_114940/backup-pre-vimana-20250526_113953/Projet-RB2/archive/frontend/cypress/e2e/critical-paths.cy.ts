describe('Critical User Journeys', () => {
  beforeEach(() => {
    cy.visit('/')
  });

  describe('Authentication Flow', () => {
    it('should successfully complete the login process', () => {
      cy.get('[data-testid="login-button"]').click();
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="submit-button"]').click();
      cy.url().should('include', '/dashboard')
    });

    it('should display error message for invalid credentials', () => {
      cy.get('[data-testid="login-button"]').click();
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('wrongpassword');
      cy.get('[data-testid="submit-button"]').click();
      cy.get('[data-testid="error-message"]').should('be.visible');
    });
  });

  describe('Navigation Flow', () => {
    it('should navigate through main sections', () => {
      cy.get('[data-testid="nav-home"]').click();
      cy.url().should('include', '/');
      
      cy.get('[data-testid="nav-profile"]').click();
      cy.url().should('include', '/profile');
      
      cy.get('[data-testid="nav-settings"]').click();
      cy.url().should('include', '/settings')
    });
  });

  describe('User Profile Flow', () => {
    beforeEach(() => {
      // Assuming we have a helper for login
      cy.login('<EMAIL>', 'password123')
    });

    it('should update user profile successfully', () => {
      cy.visit('/profile');
      cy.get('[data-testid="edit-profile"]').click();
      cy.get('[data-testid="name-input"]').clear().type('New Name');
      cy.get('[data-testid="save-profile"]').click();
      cy.get('[data-testid="success-message"]').should('be.visible')
    });
  });

  describe('Accessibility Checks', () => {
    const pages = ['/', '/profile', '/settings', '/dashboard'];

    pages.forEach(page => {
      it(`should pass accessibility tests for ${page}`, () => {
        cy.visit(page);
        cy.injectAxe();
        cy.checkA11y();
      });
    });
  });
});