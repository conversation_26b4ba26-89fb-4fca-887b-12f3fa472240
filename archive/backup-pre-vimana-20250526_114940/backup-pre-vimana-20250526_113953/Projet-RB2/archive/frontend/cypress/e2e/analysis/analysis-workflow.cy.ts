import { AnalysisStatus, AnalysisPriority } from '../../support/types';

describe('Analysis Workflow', () => {
  beforeEach(() => {
    // Se connecter avant chaque test
    cy.intercept('POST', '**/api/auth/login', {
      statusCode: 200,
      body: {
        token: 'fake-jwt-token',
        user: {
          id: '123',
          email: '<EMAIL>',
          name: 'Test User',
          role: 'ADMIN'
        }
      }
    }).as('loginRequest');

    cy.visit('/login');
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('password123');
    cy.get('[data-testid="login-button"]').click();
    cy.wait('@loginRequest');

    // Rediriger vers la page d'analyse
    cy.visit('/analysis');
  });

  it('should create a new analysis request', () => {
    // Intercepter la requête de création d'analyse
    cy.intercept('POST', '**/api/analysis', {
      statusCode: 201,
      body: {
        id: 'analysis-123',
        status: AnalysisStatus.PENDING,
        priority: AnalysisPriority.MEDIUM,
        createdAt: new Date().toISOString(),
        metadata: {
          name: 'Test Analysis',
          description: 'Test analysis description',
          type: 'CODE_QUALITY'
        }
      }
    }).as('createAnalysisRequest');

    // Cliquer sur le bouton pour créer une nouvelle analyse
    cy.get('[data-testid="create-analysis-button"]').click();
    
    // Remplir le formulaire
    cy.get('[data-testid="analysis-name-input"]').type('Test Analysis');
    cy.get('[data-testid="analysis-description-input"]').type('Test analysis description');
    cy.get('[data-testid="analysis-type-select"]').select('CODE_QUALITY');
    cy.get('[data-testid="analysis-priority-select"]').select('MEDIUM');
    
    // Ajouter un fichier pour l'analyse (simulation)
    cy.get('[data-testid="file-upload-input"]').attachFile('test-file.js');
    
    // Soumettre le formulaire
    cy.get('[data-testid="submit-analysis-button"]').click();
    
    // Vérifier que la requête a été envoyée correctement
    cy.wait('@createAnalysisRequest').its('request.body').should('include', {
      name: 'Test Analysis',
      description: 'Test analysis description',
      type: 'CODE_QUALITY',
      priority: 'MEDIUM'
    });
    
    // Vérifier la redirection vers la page de détail de l'analyse
    cy.url().should('include', '/analysis/analysis-123');
    
    // Vérifier l'affichage des informations de l'analyse
    cy.get('[data-testid="analysis-status"]').should('contain', 'PENDING');
    cy.get('[data-testid="analysis-name"]').should('contain', 'Test Analysis');
  });

  it('should view analysis list and filter results', () => {
    // Intercepter la requête de liste d'analyses
    cy.intercept('GET', '**/api/analysis*', {
      statusCode: 200,
      body: {
        items: [
          {
            id: 'analysis-123',
            status: AnalysisStatus.COMPLETED,
            priority: AnalysisPriority.HIGH,
            createdAt: '2023-05-01T10:30:00Z',
            completedAt: '2023-05-01T10:35:00Z',
            metadata: {
              name: 'Security Scan',
              description: 'Security vulnerabilities scan',
              type: 'SECURITY'
            },
            results: {
              issuesFound: 3,
              criticalIssues: 1
            }
          },
          {
            id: 'analysis-124',
            status: AnalysisStatus.IN_PROGRESS,
            priority: AnalysisPriority.MEDIUM,
            createdAt: '2023-05-02T14:20:00Z',
            metadata: {
              name: 'Performance Analysis',
              description: 'Backend API performance analysis',
              type: 'PERFORMANCE'
            }
          },
          {
            id: 'analysis-125',
            status: AnalysisStatus.PENDING,
            priority: AnalysisPriority.LOW,
            createdAt: '2023-05-03T09:15:00Z',
            metadata: {
              name: 'Code Quality',
              description: 'Code quality assessment',
              type: 'CODE_QUALITY'
            }
          }
        ],
        total: 3,
        page: 1,
        pageSize: 10
      }
    }).as('getAnalysisList');

    // Charger la page de liste des analyses
    cy.visit('/analysis');
    cy.wait('@getAnalysisList');
    
    // Vérifier que les analyses sont affichées
    cy.get('[data-testid="analysis-list-item"]').should('have.length', 3);
    
    // Tester le filtre par statut
    cy.intercept('GET', '**/api/analysis?status=COMPLETED*', {
      statusCode: 200,
      body: {
        items: [
          {
            id: 'analysis-123',
            status: AnalysisStatus.COMPLETED,
            priority: AnalysisPriority.HIGH,
            createdAt: '2023-05-01T10:30:00Z',
            completedAt: '2023-05-01T10:35:00Z',
            metadata: {
              name: 'Security Scan',
              description: 'Security vulnerabilities scan',
              type: 'SECURITY'
            },
            results: {
              issuesFound: 3,
              criticalIssues: 1
            }
          }
        ],
        total: 1,
        page: 1,
        pageSize: 10
      }
    }).as('getCompletedAnalyses');
    
    cy.get('[data-testid="status-filter"]').select('COMPLETED');
    cy.wait('@getCompletedAnalyses');
    
    // Vérifier que seulement les analyses complétées sont affichées
    cy.get('[data-testid="analysis-list-item"]').should('have.length', 1);
    cy.get('[data-testid="analysis-list-item"]').should('contain', 'Security Scan');
  });

  it('should view analysis details and results', () => {
    // Intercepter la requête de détail d'analyse
    cy.intercept('GET', '**/api/analysis/analysis-123', {
      statusCode: 200,
      body: {
        id: 'analysis-123',
        status: AnalysisStatus.COMPLETED,
        priority: AnalysisPriority.HIGH,
        createdAt: '2023-05-01T10:30:00Z',
        completedAt: '2023-05-01T10:35:00Z',
        metadata: {
          name: 'Security Scan',
          description: 'Security vulnerabilities scan',
          type: 'SECURITY'
        },
        results: {
          issuesFound: 3,
          criticalIssues: 1,
          issues: [
            {
              id: 'ISSUE-1',
              severity: 'CRITICAL',
              title: 'SQL Injection Vulnerability',
              description: 'Unsanitized input used in SQL query',
              location: 'src/api/users.js:42',
              recommendation: 'Use parameterized queries'
            },
            {
              id: 'ISSUE-2',
              severity: 'MEDIUM',
              title: 'Outdated Dependency',
              description: 'Using an outdated package with known vulnerabilities',
              location: 'package.json',
              recommendation: 'Update to the latest version'
            },
            {
              id: 'ISSUE-3',
              severity: 'LOW',
              title: 'Missing Error Handling',
              description: 'Promise rejection not handled properly',
              location: 'src/services/data.js:78',
              recommendation: 'Add proper error handling'
            }
          ]
        }
      }
    }).as('getAnalysisDetails');

    // Naviguer vers la page de détail de l'analyse
    cy.visit('/analysis/analysis-123');
    cy.wait('@getAnalysisDetails');
    
    // Vérifier les informations générales
    cy.get('[data-testid="analysis-name"]').should('contain', 'Security Scan');
    cy.get('[data-testid="analysis-status"]').should('contain', 'COMPLETED');
    cy.get('[data-testid="analysis-priority"]').should('contain', 'HIGH');
    cy.get('[data-testid="analysis-type"]').should('contain', 'SECURITY');
    
    // Vérifier le résumé des résultats
    cy.get('[data-testid="issues-summary"]').should('contain', '3 issues found');
    cy.get('[data-testid="critical-issues"]').should('contain', '1 critical');
    
    // Vérifier la liste des problèmes
    cy.get('[data-testid="issue-item"]').should('have.length', 3);
    
    // Vérifier le détail du premier problème
    cy.get('[data-testid="issue-item"]').first().click();
    cy.get('[data-testid="issue-detail-title"]').should('contain', 'SQL Injection Vulnerability');
    cy.get('[data-testid="issue-detail-severity"]').should('contain', 'CRITICAL');
    cy.get('[data-testid="issue-detail-location"]').should('contain', 'src/api/users.js:42');
    cy.get('[data-testid="issue-detail-description"]').should('contain', 'Unsanitized input');
    
    // Tester le téléchargement du rapport
    cy.intercept('GET', '**/api/analysis/analysis-123/report', {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="analysis-123-report.pdf"'
      },
      body: new Uint8Array([1, 2, 3, 4])  // Simuler un PDF
    }).as('downloadReport');
    
    cy.get('[data-testid="download-report-button"]').click();
    cy.wait('@downloadReport');
  });

  it('should cancel a pending analysis', () => {
    // Intercepter la requête de détail d'analyse
    cy.intercept('GET', '**/api/analysis/analysis-125', {
      statusCode: 200,
      body: {
        id: 'analysis-125',
        status: AnalysisStatus.PENDING,
        priority: AnalysisPriority.LOW,
        createdAt: '2023-05-03T09:15:00Z',
        metadata: {
          name: 'Code Quality',
          description: 'Code quality assessment',
          type: 'CODE_QUALITY'
        }
      }
    }).as('getAnalysisDetails');

    // Intercepter la requête d'annulation
    cy.intercept('POST', '**/api/analysis/analysis-125/cancel', {
      statusCode: 200,
      body: {
        id: 'analysis-125',
        status: AnalysisStatus.FAILED,
        statusReason: 'Cancelled by user',
        priority: AnalysisPriority.LOW,
        createdAt: '2023-05-03T09:15:00Z',
        metadata: {
          name: 'Code Quality',
          description: 'Code quality assessment',
          type: 'CODE_QUALITY'
        }
      }
    }).as('cancelAnalysisRequest');

    // Naviguer vers la page de détail de l'analyse
    cy.visit('/analysis/analysis-125');
    cy.wait('@getAnalysisDetails');
    
    // Annuler l'analyse
    cy.get('[data-testid="cancel-analysis-button"]').click();
    
    // Confirmer l'annulation dans la modal
    cy.get('[data-testid="confirm-cancel-button"]').click();
    
    // Vérifier que la requête d'annulation a été envoyée
    cy.wait('@cancelAnalysisRequest');
    
    // Vérifier que le statut a été mis à jour
    cy.get('[data-testid="analysis-status"]').should('contain', 'FAILED');
    cy.get('[data-testid="analysis-status-reason"]').should('contain', 'Cancelled by user');
  });

  it('should rerun a completed analysis', () => {
    // Intercepter la requête de détail d'analyse
    cy.intercept('GET', '**/api/analysis/analysis-123', {
      statusCode: 200,
      body: {
        id: 'analysis-123',
        status: AnalysisStatus.COMPLETED,
        priority: AnalysisPriority.HIGH,
        createdAt: '2023-05-01T10:30:00Z',
        completedAt: '2023-05-01T10:35:00Z',
        metadata: {
          name: 'Security Scan',
          description: 'Security vulnerabilities scan',
          type: 'SECURITY'
        },
        results: {
          issuesFound: 3,
          criticalIssues: 1
        }
      }
    }).as('getAnalysisDetails');

    // Intercepter la requête de relance
    cy.intercept('POST', '**/api/analysis/rerun', {
      statusCode: 201,
      body: {
        id: 'analysis-126',
        status: AnalysisStatus.PENDING,
        priority: AnalysisPriority.HIGH,
        createdAt: new Date().toISOString(),
        metadata: {
          name: 'Security Scan (Rerun)',
          description: 'Security vulnerabilities scan',
          type: 'SECURITY',
          originalAnalysisId: 'analysis-123'
        }
      }
    }).as('rerunAnalysisRequest');

    // Naviguer vers la page de détail de l'analyse
    cy.visit('/analysis/analysis-123');
    cy.wait('@getAnalysisDetails');
    
    // Relancer l'analyse
    cy.get('[data-testid="rerun-analysis-button"]').click();
    
    // Confirmer la relance
    cy.get('[data-testid="confirm-rerun-button"]').click();
    
    // Vérifier que la requête de relance a été envoyée
    cy.wait('@rerunAnalysisRequest');
    
    // Vérifier la redirection vers la nouvelle analyse
    cy.url().should('include', '/analysis/analysis-126');
    
    // Vérifier les informations de la nouvelle analyse
    cy.get('[data-testid="analysis-name"]').should('contain', 'Security Scan (Rerun)');
    cy.get('[data-testid="analysis-status"]').should('contain', 'PENDING');
    cy.get('[data-testid="original-analysis-link"]').should('exist');
  });

  it('should update analysis status and add comments', () => {
    // Intercepter la requête de détail d'analyse
    cy.intercept('GET', '**/api/analysis/analysis-124', {
      statusCode: 200,
      body: {
        id: 'analysis-124',
        status: AnalysisStatus.IN_PROGRESS,
        priority: AnalysisPriority.MEDIUM,
        createdAt: '2023-05-02T14:20:00Z',
        metadata: {
          name: 'Performance Analysis',
          description: 'Backend API performance analysis',
          type: 'PERFORMANCE'
        },
        comments: [
          {
            id: 'comment-1',
            author: 'Tech Lead',
            content: 'Please focus on the authentication endpoints',
            createdAt: '2023-05-02T15:30:00Z'
          }
        ]
      }
    }).as('getAnalysisDetails');

    // Intercepter la requête de mise à jour de statut
    cy.intercept('PATCH', '**/api/analysis/analysis-124/status', {
      statusCode: 200,
      body: {
        id: 'analysis-124',
        status: AnalysisStatus.COMPLETED,
        updatedAt: new Date().toISOString()
      }
    }).as('updateStatus');

    // Intercepter la requête d'ajout de commentaire
    cy.intercept('POST', '**/api/analysis/analysis-124/comments', {
      statusCode: 201,
      body: {
        id: 'comment-2',
        author: 'Test User',
        content: 'Analysis completed successfully',
        createdAt: new Date().toISOString()
      }
    }).as('addComment');

    // Naviguer vers la page de détail de l'analyse
    cy.visit('/analysis/analysis-124');
    cy.wait('@getAnalysisDetails');
    
    // Vérifier le statut actuel
    cy.get('[data-testid="analysis-status"]').should('contain', 'IN_PROGRESS');
    
    // Mettre à jour le statut
    cy.get('[data-testid="status-select"]').select('COMPLETED');
    cy.get('[data-testid="update-status-button"]').click();
    cy.wait('@updateStatus');
    
    // Vérifier que le statut a été mis à jour
    cy.get('[data-testid="analysis-status"]').should('contain', 'COMPLETED');
    
    // Vérifier le commentaire existant
    cy.get('[data-testid="comment-list-item"]').should('have.length', 1);
    cy.get('[data-testid="comment-list-item"]').should('contain', 'Please focus on');
    
    // Ajouter un nouveau commentaire
    cy.get('[data-testid="comment-input"]').type('Analysis completed successfully');
    cy.get('[data-testid="add-comment-button"]').click();
    cy.wait('@addComment');
    
    // Vérifier que le commentaire a été ajouté
    cy.get('[data-testid="comment-list-item"]').should('have.length', 2);
    cy.get('[data-testid="comment-list-item"]').last().should('contain', 'Analysis completed successfully');
    
    // Ajouter un handler d'erreur approprié pour les requêtes réseau
    cy.on('fail', (err) => {
      // Loguer l'erreur et continuer si c'est une erreur réseau attendue
      if (err.message.includes('Network Error')) {
        console.error('Network error occurred:', err);
        return false;
      }
      // Laisser d'autres erreurs se propager
      throw err;
    });
  });
}); 