describe('NavigationCarousel Component', () => {
  beforeEach(() => {
    // On charge une page qui contient le composant NavigationCarousel
    // Ceci est un exemple, adaptez le chemin selon votre structure
    cy.visit('/components/navigation-carousel');
    
    // Alternative: nous pouvons monter le composant directement avec cypress/support/component
    // si votre configuration le permet
  });

  it('should render the carousel with navigation links', () => {
    // Vérifie que le carrousel est rendu
    cy.get('.navigation-slider').should('exist');
    
    // Vérifie la présence des liens de navigation
    cy.get('.navigation-slider a').should('have.length.at.least', 3);
  });

  it('should navigate when clicking next and previous arrows', () => {
    // Vérifie que les flèches sont présentes
    cy.get('[aria-label="Next"]').should('be.visible');
    cy.get('[aria-label="Previous"]').should('be.visible');
    
    // Capture l'état initial
    cy.get('.navigation-slider .slick-active').first().as('firstVisible');
    
    // Clique sur la flèche suivante
    cy.get('[aria-label="Next"]').click();
    
    // Vérifie que le carousel a changé
    cy.get('.navigation-slider .slick-active').first().should('not.have.same.text', '@firstVisible');
    
    // Clique sur la flèche précédente
    cy.get('[aria-label="Previous"]').click();
    
    // On devrait revenir à l'état initial
    cy.get('.navigation-slider .slick-active').first().should('have.same.text', '@firstVisible');
  });

  it('should be navigable via keyboard', () => {
    // Focus sur le premier lien
    cy.get('.navigation-slider a').first().focus();
    
    // Vérifie que le focus est visible
    cy.focused().should('have.attr', 'href');
    
    // Navigue avec Tab
    cy.focused().tab();
    
    // Vérifie que le focus a avancé
    cy.focused().should('have.attr', 'href');
  });

  it('should be responsive', () => {
    // Test sur un écran large
    cy.viewport(1920, 1080);
    cy.get('.navigation-slider .slick-slide').should('have.length.at.least', 6);
    
    // Test sur un écran moyen
    cy.viewport(1024, 768);
    cy.get('.navigation-slider .slick-slide').should('have.length.at.least', 3);
    
    // Test sur un petit écran (mobile)
    cy.viewport(375, 667);
    cy.get('.navigation-slider .slick-slide').should('exist');
  });
}); 