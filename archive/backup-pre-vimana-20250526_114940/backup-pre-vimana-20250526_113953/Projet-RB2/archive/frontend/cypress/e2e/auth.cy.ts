describe('Authentication', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('should allow users to login', () => {
    cy.get('[data-cy="login-button"]').click();
    cy.get('[data-cy="email-input"]').type('<EMAIL>');
    cy.get('[data-cy="password-input"]').type('password123');
    cy.get('[data-cy="submit-button"]').click();
    cy.url().should('include', '/dashboard');
  });

  it('should show error message for invalid credentials', () => {
    cy.get('[data-cy="login-button"]').click();
    cy.get('[data-cy="email-input"]').type('<EMAIL>');
    cy.get('[data-cy="password-input"]').type('wrongpassword');
    cy.get('[data-cy="submit-button"]').click();
    cy.get('[data-cy="error-message"]').should('be.visible');
  });

  it('should allow users to register', () => {
    cy.get('[data-cy="register-button"]').click();
    cy.get('[data-cy="name-input"]').type('Test User');
    cy.get('[data-cy="email-input"]').type('<EMAIL>');
    cy.get('[data-cy="password-input"]').type('password123');
    cy.get('[data-cy="confirm-password-input"]').type('password123');
    cy.get('[data-cy="submit-button"]').click();
    cy.url().should('include', '/onboarding');
  });
});