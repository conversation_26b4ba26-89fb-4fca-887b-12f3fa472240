describe('User Journey', () => {
  beforeEach(() => {
    cy.intercept('GET', '/api/alerts', { fixture: 'alerts.json' }).as('getAlerts')
    cy.intercept('POST', '/api/alerts/*/acknowledge', { status: 200 }).as('ackAlert')
  })

  it('should complete critical user flows', () => {
    // Authentication
    cy.login()
    
    // Dashboard navigation
    cy.visit('/dashboard')
    cy.wait('@getAlerts')
    
    // Alert interaction
    cy.get('[data-testid="alert-item"]').first().click()
    cy.get('[data-testid="acknowledge-btn"]').click()
    cy.wait('@ackAlert')
    
    // Verify alert state
    cy.get('[data-testid="alert-status"]').should('contain', 'Acknowledged')
  })
})