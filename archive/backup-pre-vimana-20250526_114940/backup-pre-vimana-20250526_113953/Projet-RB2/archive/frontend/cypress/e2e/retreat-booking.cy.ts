describe('Retreat Booking Flow', () => {
  beforeEach(() => {
    cy.login(Cypress.env('TEST_USER_EMAIL'), Cypress.env('TEST_USER_PASSWORD'))
  });

  it('should display available retreats', () => {
    cy.visit('/retreats');
    cy.get('[data-testid="retreat-card"]').should('have.length.at.least', 1);
    cy.get('[data-testid="retreat-search"]').should('be.visible');
    cy.get('[data-testid="retreat-filter"]').should('be.visible')
  });

  it('should filter retreats by category', () => {
    cy.visit('/retreats');
    cy.get('[data-testid="category-filter"]').click();
    cy.get('[data-testid="category-yoga"]').click();
    cy.get('[data-testid="retreat-card"]').should('have.length.at.least', 1);
    cy.get('[data-testid="retreat-category"]').each(($el) => {
      expect($el.text()).to.include('Yoga')
    });
  });

  it('should search retreats by location', () => {
    cy.visit('/retreats');
    cy.get('[data-testid="location-search"]').type('Paris');
    cy.get('[data-testid="search-button"]').click();
    cy.get('[data-testid="retreat-location"]').each(($el) => {
      expect($el.text()).to.include('Paris')
    });
  });

  it('should complete booking process', () => {
    cy.visit('/retreats');
    cy.get('[data-testid="retreat-card"]').first().click();
    cy.get('[data-testid="book-now-button"]').click();
    
    // Step 1: Select dates
    cy.get('[data-testid="date-picker"]').click();
    cy.get('.available-date').first().click();
    cy.get('[data-testid="next-step-button"]').click();
    
    // Step 2: Select participants
    cy.get('[data-testid="participants-input"]').clear().type('2');
    cy.get('[data-testid="next-step-button"]').click();
    
    // Step 3: Additional options
    cy.get('[data-testid="accommodation-option"]').first().click();
    cy.get('[data-testid="meal-preference"]').select('vegetarian');
    cy.get('[data-testid="next-step-button"]').click();
    
    // Step 4: Review booking
    cy.get('[data-testid="booking-summary"]').should('be.visible');
    cy.get('[data-testid="total-price"]').should('be.visible');
    cy.get('[data-testid="confirm-booking-button"]').click();
    
    // Step 5: Payment
    cy.get('[data-testid="payment-form"]').should('be.visible');
    cy.get('[data-testid="card-number"]').type('****************');
    cy.get('[data-testid="card-expiry"]').type('1225');
    cy.get('[data-testid="card-cvc"]').type('123');
    cy.get('[data-testid="submit-payment"]').click();
    
    // Confirmation
    cy.get('[data-testid="booking-confirmation"]').should('be.visible');
    cy.get('[data-testid="booking-reference"]').should('be.visible')
  });

  it('should handle booking cancellation', () => {
    cy.visit('/my-bookings');
    cy.get('[data-testid="booking-card"]').first().within(() => {
      cy.get('[data-testid="cancel-booking-button"]').click()
    });
    cy.get('[data-testid="confirm-cancellation"]').click();
    cy.get('[data-testid="cancellation-success"]').should('be.visible');
  });
});