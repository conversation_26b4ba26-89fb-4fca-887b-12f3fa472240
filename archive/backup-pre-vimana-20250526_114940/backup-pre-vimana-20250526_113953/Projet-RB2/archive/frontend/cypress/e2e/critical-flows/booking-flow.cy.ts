import { BookingData } from '../../support/types';

describe('Booking Flow', () => {
  beforeEach(() => {
    cy.intercept('GET', '/api/hotels*').as('getHotels');
    cy.intercept('POST', '/api/bookings').as('createBooking');
    cy.visit('/')
  });

  const testBookingData: BookingData = {
    checkIn: '2024-03-01',
    checkOut: '2024-03-05',
    guests: 2,
    roomType: 'standard'
  }

  it('should complete a successful booking flow', () => {
    // Search for hotels
    cy.get('[data-testid="search-form"]').within(() => {
      cy.get('[data-testid="check-in"]').type(testBookingData.checkIn);
      cy.get('[data-testid="check-out"]').type(testBookingData.checkOut);
      cy.get('[data-testid="guests"]').type(testBookingData.guests.toString());
      cy.get('[data-testid="search-button"]').click()
    });

    // Wait for search results
    cy.wait('@getHotels');
    cy.get('[data-testid="hotel-list"]').should('be.visible');

    // Select first hotel
    cy.get('[data-testid="hotel-card"]').first().click();

    // Fill booking details
    cy.get('[data-testid="booking-form"]').within(() => {
      cy.get('[data-testid="room-type"]').select(testBookingData.roomType);
      cy.get('[data-testid="submit-booking"]').click()
    });

    // Verify booking confirmation
    cy.wait('@createBooking');
    cy.get('[data-testid="booking-confirmation"]').should('be.visible');
    cy.get('[data-testid="booking-reference"]').should('not.be.empty');
  });

  it('should handle validation errors', () => {
    cy.get('[data-testid="search-form"]').within(() => {
      // Submit without required fields
      cy.get('[data-testid="search-button"]').click();
      
      // Verify validation messages
      cy.get('[data-testid="check-in-error"]').should('be.visible');
      cy.get('[data-testid="check-out-error"]').should('be.visible');
      cy.get('[data-testid="guests-error"]').should('be.visible')
    });
  });

  it('should handle network errors gracefully', () => {
    // Simulate network error
    cy.intercept('GET', '/api/hotels*', {
      statusCode: 500,
      body: { error: 'Internal Server Error' }
    }).as('getHotelsError');

    // Perform search
    cy.get('[data-testid="search-form"]').within(() => {
      cy.get('[data-testid="check-in"]').type(testBookingData.checkIn);
      cy.get('[data-testid="check-out"]').type(testBookingData.checkOut);
      cy.get('[data-testid="guests"]').type(testBookingData.guests.toString());
      cy.get('[data-testid="search-button"]').click()
    });

    // Verify error handling
    cy.wait('@getHotelsError');
    cy.get('[data-testid="error-message"]').should('be.visible');
    cy.get('[data-testid="retry-button"]').should('be.visible');
  });
});