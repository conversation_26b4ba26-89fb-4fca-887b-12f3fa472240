describe('NFT Flow', () => {
  beforeEach(() => {
    // Mock the NFT fixture data
    cy.fixture('nfts.json').as('nftData')
  });

  it('Loads NFTs after wallet connection', () => {
    // Intercept NFT API calls
    cy.intercept('GET', '/api/nft*', { fixture: 'nfts.json' }).as('getNFTs');
    
    // Connect wallet and visit NFT page
    cy.connectWallet('metamask');
    cy.visit('/nft');
    
    // Wait for API call and verify response
    cy.wait('@getNFTs');
    cy.get('[data-testid="nft-grid"]').children().should('have.length', 3);
  });

  it('Displays empty state when no NFTs', () => {
    cy.intercept('GET', '/api/nft*', { body: [] }).as('emptyNFTs');
    cy.connectWallet('metamask');
    cy.visit('/nft');
    cy.wait('@emptyNFTs');
    cy.get('[data-testid="no-nfts"]').should('be.visible');
  });

  it('Shows error state on API failure', () => {
    cy.intercept('GET', '/api/nft*', { statusCode: 500 }).as('failedNFTs');
    cy.connectWallet('metamask');
    cy.visit('/nft');
    cy.wait('@failedNFTs');
    cy.get('[data-testid="error-message"]').should('be.visible');
  });
});