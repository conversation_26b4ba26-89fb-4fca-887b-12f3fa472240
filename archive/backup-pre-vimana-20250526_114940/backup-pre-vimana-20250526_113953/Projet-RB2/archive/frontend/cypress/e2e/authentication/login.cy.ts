import { UserRole } from '../../support/types';

describe('Authentication Flow', () => {
  beforeEach(() => {
    cy.visit('/login');
    // Réinitialiser l'état avant chaque test
    cy.window().then((win) => {
      win.localStorage.clear();
      win.sessionStorage.clear()
    });
  });

  it('should successfully login with valid credentials', () => {
    // Intercepter les appels API d'authentification
    cy.intercept('POST', '**/api/auth/login', {
      statusCode: 200,
      body: {
        token: 'fake-jwt-token',
        user: {
          id: '123',
          email: '<EMAIL>',
          name: 'Test User',
          role: UserRole.ADMIN
        }
      }
    }).as('loginRequest');

    // Remplir et soumettre le formulaire
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('password123');
    cy.get('[data-testid="login-button"]').click();

    // Vérifier l'appel API
    cy.wait('@loginRequest').its('request.body').should('deep.include', {
      email: '<EMAIL>',
      password: 'password123'
    });

    // Vérifier la redirection
    cy.url().should('include', '/dashboard');
    
    // Vérifier l'affichage du dashboard
    cy.get('[data-testid="user-welcome"]').should('be.visible');
    cy.get('[data-testid="user-welcome"]').should('contain', 'Test User');
    
    // Vérifier que le token est stocké
    cy.window().then((win) => {
      expect(win.localStorage.getItem('auth_token')).to.equal('fake-jwt-token')
    });
  });

  it('should display error with invalid credentials', () => {
    // Intercepter les appels API avec erreur
    cy.intercept('POST', '**/api/auth/login', {
      statusCode: 401,
      body: {
        message: 'Invalid credentials'
      }
    }).as('failedLoginRequest');

    // Remplir et soumettre le formulaire avec des identifiants incorrects
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('wrongpassword');
    cy.get('[data-testid="login-button"]').click();

    // Vérifier l'appel API
    cy.wait('@failedLoginRequest');

    // Vérifier l'affichage du message d'erreur
    cy.get('[data-testid="error-message"]').should('be.visible');
    cy.get('[data-testid="error-message"]').should('contain', 'Invalid credentials');
    
    // Vérifier qu'on reste sur la page de login
    cy.url().should('include', '/login');
    
    // Vérifier qu'aucun token n'est stocké
    cy.window().then((win) => {
      expect(win.localStorage.getItem('auth_token')).to.be.null
    });
  });

  it('should handle password reset request', () => {
    // Intercepter l'appel API de réinitialisation de mot de passe
    cy.intercept('POST', '**/api/auth/reset-password', {
      statusCode: 200,
      body: {
        message: 'Password reset email sent'
      }
    }).as('resetPasswordRequest');

    // Naviguer vers la page de récupération de mot de passe
    cy.get('[data-testid="forgot-password"]').click();
    cy.url().should('include', '/reset-password');
    
    // Remplir et soumettre le formulaire
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="submit-button"]').click();
    
    // Vérifier l'appel API
    cy.wait('@resetPasswordRequest').its('request.body').should('deep.include', {
      email: '<EMAIL>'
    });
    
    // Vérifier l'affichage du message de succès
    cy.get('[data-testid="success-message"]').should('be.visible');
    cy.get('[data-testid="success-message"]').should('contain', 'Password reset email sent');
  });

  it('should handle session timeout and redirect to login', () => {
    // Simuler un token expiré dans localStorage
    cy.window().then((win) => {
      win.localStorage.setItem('auth_token', 'expired-token')
    });

    // Intercepter les appels API avec erreur d'expiration
    cy.intercept('GET', '**/api/user/profile', {
      statusCode: 401,
      body: {
        message: 'Token expired'
      }
    }).as('expiredTokenRequest');

    // Naviguer vers une page protégée
    cy.visit('/dashboard');

    // Vérifier l'appel API
    cy.wait('@expiredTokenRequest');
    
    // Vérifier la redirection vers la page de login
    cy.url().should('include', '/login');
    
    // Vérifier l'affichage du message d'expiration
    cy.get('[data-testid="session-expired"]').should('be.visible');
    cy.get('[data-testid="session-expired"]').should('contain', 'Your session has expired');
  });

  it('should handle logout correctly', () => {
    // Simuler un utilisateur connecté
    cy.window().then((win) => {
      win.localStorage.setItem('auth_token', 'valid-token')
    });

    // Intercepter les appels API de profil utilisateur
    cy.intercept('GET', '**/api/user/profile', {
      statusCode: 200,
      body: {
        id: '123',
        email: '<EMAIL>',
        name: 'Test User',
        role: UserRole.ADMIN
      }
    }).as('profileRequest');

    // Naviguer vers le dashboard
    cy.visit('/dashboard');
    cy.wait('@profileRequest');

    // Intercepter les appels API de déconnexion
    cy.intercept('POST', '**/api/auth/logout', {
      statusCode: 200,
      body: {
        message: 'Logged out successfully'
      }
    }).as('logoutRequest');

    // Cliquer sur le bouton de déconnexion
    cy.get('[data-testid="logout-button"]').click();
    
    // Vérifier l'appel API
    cy.wait('@logoutRequest');
    
    // Vérifier la redirection vers la page de login
    cy.url().should('include', '/login');
    
    // Vérifier que le token est supprimé
    cy.window().then((win) => {
      expect(win.localStorage.getItem('auth_token')).to.be.null
    });
  });
}); 