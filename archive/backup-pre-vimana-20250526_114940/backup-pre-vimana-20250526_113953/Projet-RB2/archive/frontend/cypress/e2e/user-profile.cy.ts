describe('User Profile Management', () => {
  beforeEach(() => {
    cy.login(Cypress.env('TEST_USER_EMAIL'), Cypress.env('TEST_USER_PASSWORD'));
    cy.visit('/profile')
  });

  it('should display user profile information', () => {
    cy.get('[data-testid="profile-section"]').should('be.visible');
    cy.get('[data-testid="user-name"]').should('be.visible');
    cy.get('[data-testid="user-email"]').should('be.visible');
    cy.get('[data-testid="edit-profile-button"]').should('be.visible')
  });

  it('should update user profile information', () => {
    cy.get('[data-testid="edit-profile-button"]').click();
    
    // Update profile information
    cy.get('[data-testid="name-input"]').clear().type('Updated Name');
    cy.get('[data-testid="phone-input"]').clear().type('+33123456789');
    cy.get('[data-testid="bio-input"]').clear().type('Updated bio information');
    
    cy.get('[data-testid="save-profile-button"]').click();
    
    // Verify updates
    cy.get('[data-testid="success-message"]').should('be.visible');
    cy.get('[data-testid="user-name"]').should('contain', 'Updated Name')
  });

  it('should update notification preferences', () => {
    cy.get('[data-testid="notification-settings-tab"]').click();
    
    cy.get('[data-testid="email-notifications"]').click();
    cy.get('[data-testid="push-notifications"]').click();
    cy.get('[data-testid="save-notifications"]').click();
    
    cy.get('[data-testid="settings-saved-message"]').should('be.visible')
  });

  it('should update privacy settings', () => {
    cy.get('[data-testid="privacy-settings-tab"]').click();
    
    cy.get('[data-testid="profile-visibility"]').select('private');
    cy.get('[data-testid="save-privacy"]').click();
    
    cy.get('[data-testid="settings-saved-message"]').should('be.visible')
  });

  it('should handle profile picture upload', () => {
    cy.get('[data-testid="upload-photo-button"]').click();
    cy.get('[data-testid="photo-input"]').attachFile('test-photo.jpg');
    
    cy.get('[data-testid="crop-photo-button"]').should('be.visible').click();
    cy.get('[data-testid="save-photo-button"]').click();
    
    cy.get('[data-testid="profile-photo"]').should('have.attr', 'src').and('include', 'updated-photo')
  });

  it('should display booking history', () => {
    cy.get('[data-testid="booking-history-tab"]').click();
    
    cy.get('[data-testid="booking-list"]').should('be.visible');
    cy.get('[data-testid="booking-item"]').should('have.length.at.least', 1);
    
    // Verify booking details
    cy.get('[data-testid="booking-item"]').first().within(() => {
      cy.get('[data-testid="booking-date"]').should('be.visible');
      cy.get('[data-testid="booking-status"]').should('be.visible');
      cy.get('[data-testid="booking-details"]').should('be.visible')
    });
  });
});