describe('Professional Dashboard', () => {
  beforeEach(() => {
    // Mock the analytics API response
    cy.intercept('GET', '/api/professional/analytics', {
      fixture: 'analytics.json'
    }).as('getAnalytics');

    // Visit the dashboard page
    cy.visit('/professional/dashboard');
  });

  it('should display loading state initially', () => {
    cy.get('.animate-spin').should('be.visible')
  });

  it('should display analytics charts after loading', () => {
    // Wait for API response
    cy.wait('@getAnalytics');

    // Verify dashboard title
    cy.get('h1').contains('Professional Dashboard');

    // Verify all three charts are present
    cy.get('.recharts-wrapper').should('have.length', 3);

    // Verify chart titles
    cy.contains('Bookings Overview');
    cy.contains('Revenue Analytics');
    cy.contains('Visitor Statistics')
  });

  it('should handle API errors gracefully', () => {
    // Mock API error
    cy.intercept('GET', '/api/professional/analytics', {
      statusCode: 500,
      body: { message: 'Server error' }
    }).as('getAnalyticsError');

    cy.visit('/professional/dashboard');
    cy.contains('Failed to fetch analytics data');
  });

  it('should display correct data in charts', () => {
    cy.fixture('analytics.json').then((analytics) => {
      cy.wait('@getAnalytics');

      // Verify data points in charts
      analytics.forEach((data) => {
        cy.contains(data.date);
        cy.contains(data.bookings);
        cy.contains(data.revenue);
        cy.contains(data.visitors)
      });
    });
  });
});