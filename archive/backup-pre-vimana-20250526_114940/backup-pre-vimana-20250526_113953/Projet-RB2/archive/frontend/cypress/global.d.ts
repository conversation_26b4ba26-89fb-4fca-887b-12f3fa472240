/// <reference types="cypress" />
/// <reference types="mocha" />

// Déclaration correcte des fonctions globales de test
declare namespace Mocha {
  interface SuiteFunction {
    (title: string, fn: (this: Suite) => void): Suite;
    only(title: string, fn: (this: Suite) => void): Suite;
    skip(title: string, fn: (this: Suite) => void): Suite;
  }

  interface TestFunction {
    (title: string, fn: (this: Context) => void): Test;
    only(title: string, fn: (this: Context) => void): Test;
    skip(title: string, fn: (this: Context) => void): Test;
  }

  interface HookFunction {
    (fn: () => void): void;
  }
}

// Redéfinir les variables pour éviter les conflits
declare const describe: Mocha.SuiteFunction;
declare const context: Mocha.SuiteFunction;
declare const it: Mocha.TestFunction;
declare const test: Mocha.TestFunction;
declare const specify: Mocha.TestFunction;
declare const before: Mocha.HookFunction;
declare const after: Mocha.HookFunction;
declare const beforeEach: Mocha.HookFunction;
declare const afterEach: Mocha.HookFunction;

// Prise en charge des fonctions spécifiques à Cypress
declare namespace Cypress {
  interface Chainable {
    // Ajoutez vos propres commandes ici
  }
} 