// Accessibility testing commands for Cy<PERSON>
import 'cypress-axe'

// Add accessibility testing commands
Cypress.Commands.add('checkPageA11y', (path: string) => {
  cy.visit(path)
  cy.injectAxe()
  cy.checkA11y(
    null,
    {
      includedImpacts: ['critical', 'serious', 'moderate']
    },
    null,
    'A11y violations found on page: ' + path
  )
})

// Check specific element for accessibility
Cypress.Commands.add('checkElementA11y', (selector: string) => {
  cy.injectAxe()
  cy.checkA11y(
    selector,
    {
      includedImpacts: ['critical', 'serious', 'moderate']
    },
    null,
    'A11y violations found on element: ' + selector
  )
})

// Check accessibility with custom rules
Cypress.Commands.add('checkA11yWithRules', (rules: string[]) => {
  cy.injectAxe()
  cy.checkA11y({
    runOnly: {
      type: 'rule',
      values: rules
    }
  })
})

// Check accessibility excluding specific elements
Cypress.Commands.add('checkA11yExclude', (excludeSelector: string) => {
  cy.injectAxe()
  cy.checkA11y({
    exclude: [excludeSelector]
  })
})

// Check color contrast accessibility
Cypress.Commands.add('checkColorContrast', (selector: string) => {
  cy.injectAxe()
  cy.checkA11y(
    selector,
    {
      rules: {
        'color-contrast': {
          enabled: true
        }
      }
    }
  )
})

// Check keyboard navigation accessibility
Cypress.Commands.add('checkKeyboardNavigation', () => {
  cy.get('body').focus()
  cy.tab().should('have.focus')
  cy.get('a, button, input, select, textarea, [tabindex]:not([tabindex==="-1"])')
    .each(($el) => {
      cy.wrap($el).focus().should('have.focus')
    })
})

// Check focus management
Cypress.Commands.add('checkFocusManagement', (selector: string) => {
  cy.get(selector).focus()
  cy.focused().should('have.attr', 'aria-label').and('not.be.empty')
  cy.focused().should('be.visible')
})

// Check form validation accessibility
Cypress.Commands.add('checkFormA11y', (formSelector: string) => {
  cy.get(formSelector).within(() => {
    cy.get('input, select, textarea').each(($input) => {
      cy.wrap($input).should('have.attr', 'aria-label').or('have.attr', 'aria-labelledby')
      if ($input.prop('required')) {
        cy.wrap($input).should('have.attr', 'aria-required', 'true')
      }
    })
  })
})