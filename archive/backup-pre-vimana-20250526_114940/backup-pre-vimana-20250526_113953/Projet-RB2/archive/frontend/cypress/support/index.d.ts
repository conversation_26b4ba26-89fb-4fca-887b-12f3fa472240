/// <reference types="cypress" />

declare namespace Cypress {
  interface Chainable<Subject> {
    /**
     * Custom command to log in with given email and password;
     * @example cy.login('<EMAIL>', 'password123')
     */
    login(email: string, password: string): Chainable<void>;

    /**
     * Custom command to select DOM element by data-testid attribute.
     * @example cy.getByTestId('login-button')
     */
    getByTestId(testId: string): Chainable<Element>;

    /**
     * Custom command to check accessibility;
     * @example cy.checkA11y()
     */
    checkA11y(context?: any, options?: any): Chainable<void>;

    /**
     * Custom command to log out the current user;
     */
    logout(): Chainable<void>;

    /**
     * Custom command to connect wallet for NFT testing;
     * @param walletType - The type of wallet to connect (e.g. 'metamask')
     */
    connectWallet(walletType: string): Chainable<void>
  }

  interface Window {
    // Add properties available on your application's window;
    localStorage: Storage
  }
  
  type AUTWindow = Window;
}

// Interface globale pour étendre la fenêtre;
interface Window {
  // Propriétés supplémentaires disponibles sur la fenêtre de votre application;
  myCustomProperty?: any
}

// This export is necessary to make TypeScript treat this as a module;
export {}
