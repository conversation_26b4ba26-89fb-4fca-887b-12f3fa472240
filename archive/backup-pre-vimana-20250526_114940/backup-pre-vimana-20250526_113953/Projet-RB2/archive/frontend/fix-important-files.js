#!/usr/bin/env node

/**
 * Script pour corriger les erreurs TypeScript dans les fichiers les plus importants
 */
import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// Liste des fichiers importants à corriger
const IMPORTANT_FILES = [
  'src/App.tsx',
  'src/index.tsx',
  'src/hooks/useAdaptiveBandwidth.ts',
  'src/services/AdaptiveBandwidthManager.ts',
  'src/components/atoms/Button/Button.tsx',
  'src/components/atoms/Button/Button.types.ts',
  'src/components/atoms/Input/Input.tsx',
  'src/components/atoms/Input/Input.types.ts',
  'src/components/atoms/Badge/Badge.tsx',
  'src/components/atoms/Badge/Badge.types.ts',
  'src/components/atoms/Checkbox/Checkbox.tsx',
  'src/components/atoms/Checkbox/Checkbox.types.ts',
  'src/components/atoms/Label/Label.tsx',
  'src/components/atoms/Label/Label.types.ts',
  'src/components/atoms/Link/Link.tsx',
  'src/components/atoms/Link/Link.types.ts',
  'src/components/atoms/Icon/Icon.tsx',
  'src/components/atoms/Icon/Icon.types.ts',
  'src/components/atoms/Image/Image.tsx',
  'src/components/atoms/Image/Image.types.ts'
];

// Corrections à appliquer
const FIXES = [
  // Correction des importations React
  {
    pattern: /^import\s+React\s+from\s+['"]react['"];/gm,
    replacement: "import * as React from 'react';"
  },
  {
    pattern: /^import\s+React,\s*{\s*([^}]+)\s*}\s+from\s+['"]react['"];/gm,
    replacement: "import * as React from 'react';\nconst { $1 } = React;"
  },
  {
    pattern: /^import\s+{\s*([^}]+)\s*}\s+from\s+['"]react['"];/gm,
    replacement: "import * as React from 'react';\nconst { $1 } = React;"
  },
  
  // Correction des variables non utilisées
  {
    pattern: /^(\s*)const\s+([a-zA-Z0-9_]+)\s*=.*?;\s*\/\/\s*.*?$/gm,
    test: (content, match) => {
      // Vérifier si la variable est utilisée ailleurs dans le fichier
      const varName = match[2];
      const varRegex = new RegExp(`[^a-zA-Z0-9_]${varName}[^a-zA-Z0-9_]`);
      return !varRegex.test(content.replace(match[0], ''));
    },
    replacement: (match) => `${match[1]}// Unused: ${match[0].trim()}`
  },
  
  // Correction des importations incorrectes
  {
    pattern: /import\s+\{\s*\.\//g,
    replacement: 'import { '
  },
  
  // Correction des types manquants
  {
    pattern: /(const|let|var)\s+([a-zA-Z0-9_]+)\s*=\s*([^:;]+);/g,
    test: (content, match) => {
      // Ajouter des types uniquement aux variables sans types explicites
      return !content.includes(`${match[2]}: `);
    },
    replacement: (match) => {
      const value = match[3].trim();
      let type = 'any';
      
      // Essayer d'inférer le type
      if (value === '[]') type = 'any[]';
      else if (value === '{}') type = 'Record<string, any>';
      else if (value.startsWith('"') || value.startsWith("'")) type = 'string';
      else if (/^-?\d+(\.\d+)?$/.test(value)) type = 'number';
      else if (value === 'true' || value === 'false') type = 'boolean';
      else if (value.includes('new ')) {
        const className = value.match(/new\s+([a-zA-Z0-9_]+)/);
        if (className) type = className[1];
      }
      
      return `${match[1]} ${match[2]}: ${type} = ${match[3]};`;
    }
  },
  
  // Correction des erreurs de syntaxe dans les interfaces
  {
    pattern: /(\s*)([a-zA-Z0-9_]+)(\s*=\s*[^,;]+)([^\n]*?)$/gm,
    test: (content, match, index) => {
      // Vérifier si nous sommes dans une interface ou un type
      const beforeMatch = content.substring(0, index);
      const lastInterfaceOrType = beforeMatch.lastIndexOf('interface');
      const lastClosingBrace = beforeMatch.lastIndexOf('}');
      return lastInterfaceOrType > lastClosingBrace;
    },
    replacement: (match) => `${match[1]}${match[2]}: ${match[3].replace('=', '')},`
  }
];

// Fonction principale
async function main() {
  try {
    console.log('Correction des erreurs TypeScript dans les fichiers importants...');
    
    let fixedFiles = 0;
    let fixedErrors = 0;
    
    for (const filePath of IMPORTANT_FILES) {
      try {
        const fullPath = path.resolve(process.cwd(), filePath);
        
        if (!fs.existsSync(fullPath)) {
          console.log(`Le fichier ${filePath} n'existe pas, on le saute.`);
          continue;
        }
        
        let content = fs.readFileSync(fullPath, 'utf8');
        let originalContent = content;
        let fileFixedErrors = 0;
        
        for (const fix of FIXES) {
          let matches = [...content.matchAll(fix.pattern)];
          
          for (const match of matches) {
            if (!fix.test || fix.test(content, match, match.index)) {
              const replacement = typeof fix.replacement === 'function' 
                ? fix.replacement(match) 
                : fix.replacement;
              
              content = content.replace(match[0], replacement);
              fileFixedErrors++;
            }
          }
        }
        
        if (content !== originalContent) {
          fs.writeFileSync(fullPath, content, 'utf8');
          fixedFiles++;
          fixedErrors += fileFixedErrors;
          console.log(`Corrigé ${fileFixedErrors} erreurs dans ${filePath}`);
        } else {
          console.log(`Aucune erreur à corriger dans ${filePath}`);
        }
      } catch (error) {
        console.error(`Erreur lors du traitement du fichier ${filePath}:`, error.message);
      }
    }
    
    console.log(`\nRésumé :`);
    console.log(`- Corrigé ${fixedErrors} erreurs dans ${fixedFiles} fichiers`);
    console.log(`- ${IMPORTANT_FILES.length - fixedFiles} fichiers étaient déjà corrects ou n'existent pas`);
    
    // Vérifier les erreurs restantes dans un fichier spécifique
    console.log('\nVérification des erreurs dans un fichier spécifique...');
    try {
      execSync('npx tsc --noEmit --skipLibCheck --jsx react src/hooks/useAdaptiveBandwidth.ts', { stdio: 'inherit' });
      console.log('Vérification TypeScript terminée avec succès !');
    } catch (error) {
      console.error('La vérification TypeScript a trouvé des erreurs restantes.');
      console.log('Exécutez le script à nouveau ou corrigez les erreurs restantes manuellement.');
    }
  } catch (error) {
    console.error('Erreur lors de l\'exécution du script :', error);
    process.exit(1);
  }
}

// Exécuter la fonction principale
main().catch(error => {
  console.error('Erreur lors de l\'exécution du script :', error);
  process.exit(1);
});
