# Guide d'Implémentation du Chart Helm Frontend

## Introduction

Ce document détaille le plan d'implémentation d'un chart Helm dédié pour le frontend. L'objectif est d'optimiser le déploiement, la scalabilité et la gestion de l'application frontend dans un environnement Kubernetes.

## Structure Cible

```
frontend/helm/
  ├── Chart.yaml           # Métadonnées du chart
  ├── values.yaml          # Valeurs par défaut
  ├── values-prod.yaml     # Valeurs de production
  └── templates/
      ├── deployment.yaml  # Configuration du déploiement
      ├── service.yaml     # Configuration du service
      ├── ingress.yaml     # Configuration de l'ingress
      ├── configmap.yaml   # Variables d'environnement
      ├── hpa.yaml         # Autoscaling horizontal
      └── pdb.yaml         # Budget de perturbation des pods
```

## Plan d'Implémentation

### Phase 1: Préparation (Semaine 1)

1. **Analyse des besoins**
   - Identifier les ressources nécessaires pour le frontend
   - Déterminer les variables d'environnement requises
   - Définir les exigences de scalabilité

2. **Configuration initiale**
   - Créer la structure de dossiers
   - Initialiser le fichier Chart.yaml
   - Définir les dépendances éventuelles

3. **Documentation des standards**
   - Établir les conventions de nommage
   - Définir les labels et annotations standards
   - Documenter les bonnes pratiques

### Phase 2: Développement des Templates de Base (Semaine 2)

1. **Templates à créer en priorité**
   - deployment.yaml
   - service.yaml
   - configmap.yaml

2. **Processus de développement**
   - Créer les templates avec les valeurs paramétrables
   - Implémenter les sondes de santé
   - Configurer les ressources (CPU/mémoire)
   - Tester le déploiement en environnement de développement

### Phase 3: Développement des Templates Avancés (Semaine 3)

1. **Templates à créer**
   - ingress.yaml
   - hpa.yaml (Horizontal Pod Autoscaler)
   - pdb.yaml (Pod Disruption Budget)

2. **Processus de développement**
   - Configurer l'ingress avec TLS
   - Implémenter l'autoscaling basé sur CPU/mémoire
   - Définir les règles de perturbation des pods
   - Tester les fonctionnalités avancées

### Phase 4: Configuration Multi-environnements (Semaine 4)

1. **Fichiers de valeurs à créer**
   - values-dev.yaml
   - values-staging.yaml
   - values-prod.yaml

2. **Processus de configuration**
   - Définir les valeurs spécifiques à chaque environnement
   - Configurer les ressources appropriées
   - Ajuster les paramètres de scalabilité
   - Tester le déploiement dans chaque environnement

## Standards d'Implémentation

### Chart.yaml

```yaml
apiVersion: v2
name: frontend
description: Chart Helm pour l'application frontend
type: application
version: 0.1.0
appVersion: "1.0.0"
maintainers:
  - name: Équipe DevOps
    email: <EMAIL>
```

### Deployment Template

```yaml
# templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "frontend.fullname" . }}
  labels:
    {{- include "frontend.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "frontend.selectorLabels" . | nindent 6 }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: {{ .Values.deployment.maxSurge }}
      maxUnavailable: {{ .Values.deployment.maxUnavailable }}
  template:
    metadata:
      labels:
        {{- include "frontend.selectorLabels" . | nindent 8 }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          readinessProbe:
            httpGet:
              path: /ready
              port: http
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            {{- range $key, $value := .Values.env }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          volumeMounts:
            - name: config
              mountPath: /usr/share/nginx/html/config
              readOnly: true
      volumes:
        - name: config
          configMap:
            name: {{ include "frontend.fullname" . }}-config
```

### HPA Template

```yaml
# templates/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "frontend.fullname" . }}
  labels:
    {{- include "frontend.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "frontend.fullname" . }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
```

### Values.yaml

```yaml
# values.yaml
replicaCount: 2

image:
  repository: registry.example.com/frontend
  pullPolicy: IfNotPresent
  tag: "latest"

deployment:
  maxSurge: 1
  maxUnavailable: 0

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: frontend.example.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: frontend-tls
      hosts:
        - frontend.example.com

resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 250m
    memory: 512Mi

livenessProbe:
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3

readinessProbe:
  initialDelaySeconds: 10
  periodSeconds: 10
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

podDisruptionBudget:
  enabled: true
  minAvailable: 1

env:
  NODE_ENV: production
  API_URL: https://api.example.com
```

### Values-prod.yaml

```yaml
# values-prod.yaml
replicaCount: 4

image:
  tag: "stable"

resources:
  limits:
    cpu: 2000m
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 1Gi

autoscaling:
  minReplicas: 4
  maxReplicas: 20
  targetCPUUtilizationPercentage: 70

podDisruptionBudget:
  minAvailable: 2
```

## Bonnes Pratiques

1. **Gestion des Ressources**
   - Toujours définir des limites et des requêtes de ressources
   - Ajuster les ressources en fonction de l'environnement
   - Utiliser des métriques réelles pour déterminer les valeurs optimales

2. **Haute Disponibilité**
   - Configurer plusieurs réplicas
   - Implémenter des Pod Disruption Budgets
   - Utiliser des déploiements progressifs (RollingUpdate)

3. **Monitoring et Santé**
   - Implémenter des sondes de vivacité et de préparation
   - Configurer des métriques personnalisées pour l'autoscaling
   - Ajouter des annotations pour Prometheus

4. **Sécurité**
   - Utiliser des images non-root
   - Configurer des contextes de sécurité
   - Implémenter des Network Policies

## Mesures de Succès

1. **Performance**
   - Temps de déploiement réduit
   - Utilisation optimale des ressources
   - Scalabilité automatique efficace

2. **Fiabilité**
   - Zéro temps d'arrêt lors des déploiements
   - Récupération automatique en cas de défaillance
   - Résistance aux perturbations du cluster

3. **Maintenabilité**
   - Configuration cohérente entre les environnements
   - Documentation claire et complète
   - Facilité de mise à jour

## Conclusion

L'implémentation d'un chart Helm dédié pour le frontend permettra d'améliorer significativement la gestion du déploiement, la scalabilité et la fiabilité de l'application. Cette approche standardisée facilitera également la gestion multi-environnements et l'intégration dans les pipelines CI/CD.