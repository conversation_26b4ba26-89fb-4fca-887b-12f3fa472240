# Configuration Helm pour le Frontend

## Introduction

Ce document décrit la configuration Helm recommandée pour le déploiement du frontend dans un environnement Kubernetes. Le chart Helm est conçu pour être flexible, configurable et optimisé pour les différents environnements (développement, staging, production).

## Structure du Chart

```
frontend/helm/
  ├── Chart.yaml           # Métadonnées du chart
  ├── values.yaml          # Valeurs par défaut
  ├── values-dev.yaml      # Valeurs pour l'environnement de développement
  ├── values-staging.yaml  # Valeurs pour l'environnement de staging
  ├── values-prod.yaml     # Valeurs pour l'environnement de production
  └── templates/
      ├── deployment.yaml  # Configuration du déploiement
      ├── service.yaml     # Configuration du service
      ├── ingress.yaml     # Configuration de l'ingress
      ├── configmap.yaml   # Variables d'environnement
      ├── hpa.yaml         # Autoscaling horizontal
      └── pdb.yaml         # Budget de perturbation des pods
```

## Configuration de Base

### Chart.yaml

```yaml
apiVersion: v2
name: frontend
description: Frontend application chart
type: application
version: 0.1.0
appVersion: "1.0.0"
```

### values.yaml

```yaml
# Configuration par défaut pour le frontend

replicaCount: 2

image:
  repository: your-registry/frontend
  tag: latest
  pullPolicy: IfNotPresent

imagePullSecrets: []

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: nginx
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  hosts:
    - host: frontend.example.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: frontend-tls
      hosts:
        - frontend.example.com

resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 250m
    memory: 512Mi

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 75
  targetMemoryUtilizationPercentage: 80

podDisruptionBudget:
  enabled: true
  minAvailable: 1

podSecurityContext:
  runAsNonRoot: true
  runAsUser: 101
  fsGroup: 101

nginx:
  config:
    client_max_body_size: 50m
    proxy_connect_timeout: 300
    enable_gzip: true
    enable_brotli: true

# Configuration des sondes de santé
livenessProbe:
  httpGet:
    path: /health
    port: http
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /health
    port: http
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 2

# Variables d'environnement
env:
  NODE_ENV: production
  API_URL: https://api.example.com
  ENABLE_ANALYTICS: "true"
```

## Templates

### deployment.yaml

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "frontend.fullname" . }}
  labels:
    {{- include "frontend.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "frontend.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "frontend.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          env:
            {{- range $key, $value := .Values.env }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
```

### hpa.yaml

```yaml
{{- if .Values.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "frontend.fullname" . }}
  labels:
    {{- include "frontend.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "frontend.fullname" . }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
```

### pdb.yaml

```yaml
{{- if .Values.podDisruptionBudget.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "frontend.fullname" . }}
  labels:
    {{- include "frontend.labels" . | nindent 4 }}
spec:
  {{- if .Values.podDisruptionBudget.minAvailable }}
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
  {{- end }}
  {{- if .Values.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ .Values.podDisruptionBudget.maxUnavailable }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "frontend.selectorLabels" . | nindent 6 }}
{{- end }}
```

## Configurations Spécifiques par Environnement

### values-prod.yaml

```yaml
# Configuration spécifique à la production

replicaCount: 4

image:
  tag: stable
  pullPolicy: Always

resources:
  limits:
    cpu: 2000m
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 1Gi

autoscaling:
  minReplicas: 4
  maxReplicas: 20
  targetCPUUtilizationPercentage: 70

podDisruptionBudget:
  minAvailable: 2

ingress:
  hosts:
    - host: app.example.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: app-tls
      hosts:
        - app.example.com
```

## Bonnes Pratiques

1. **Ressources Appropriées**
   - Définir des limites et requêtes de ressources réalistes
   - Ajuster les ressources en fonction de l'environnement

2. **Haute Disponibilité**
   - Configurer un nombre minimum de réplicas (2+)
   - Utiliser un PodDisruptionBudget pour garantir la disponibilité pendant les mises à jour
   - Configurer l'autoscaling horizontal pour gérer les pics de charge

3. **Sécurité**
   - Utiliser des contextes de sécurité pour les pods
   - Configurer des politiques de sécurité des pods
   - Utiliser des secrets pour les informations sensibles

4. **Monitoring**
   - Configurer des sondes de vivacité et de préparation
   - Définir des métriques personnalisées pour l'autoscaling
   - Intégrer avec Prometheus pour la surveillance

5. **Déploiement**
   - Utiliser des stratégies de déploiement appropriées (RollingUpdate)
   - Configurer des hooks de cycle de vie pour les migrations ou autres tâches
   - Implémenter des tests de smoke après le déploiement

## Commandes Utiles

### Installation

```bash
# Installation en développement
helm install frontend ./helm --values ./helm/values-dev.yaml

# Installation en production
helm install frontend ./helm --values ./helm/values-prod.yaml
```

### Mise à jour

```bash
# Mise à jour en production
helm upgrade frontend ./helm --values ./helm/values-prod.yaml
```

### Vérification

```bash
# Vérifier la syntaxe des templates
helm lint ./helm

# Afficher les templates générés sans installer
helm template frontend ./helm --values ./helm/values-prod.yaml
```

## Ressources

- [Helm Documentation](https://helm.sh/docs/)
- [Kubernetes Best Practices](https://kubernetes.io/docs/concepts/configuration/overview/)
- [Helm Chart Development Tips and Tricks](https://helm.sh/docs/howto/charts_tips_and_tricks/)