#!/bin/bash

# Script pour corriger les erreurs d'importation avec des chemins relatifs
# Ce script recherche les importations incorrectes comme "import * as ../path" et les remplace par des importations correctes

# Fonction pour corriger les imports relatifs dans un fichier
fix_relative_imports() {
  local file=$1
  
  if [ ! -f "$file" ]; then
    echo "Le fichier $file n'existe pas, ignoré."
    return
  fi
  
  echo "Correction des importations relatives dans $file"
  
  # Extraire le nom du module à partir du chemin relatif
  # Par exemple, '../config' devient 'config'
  # Pour ensuite créer "import * as config from '../config'"
  
  # Remplacer les imports avec @ dans le nom (comme @mui/material)
  sed -i '' -E "s/import \* as @([^[:space:]]+) from '([^']+)';/import * as MUI from '\2';/g" "$file"
  
  # Remplacer les imports avec chemins relatifs ../
  sed -i '' -E "s/import \* as \.\.\/([^[:space:]\/]+)(\/[^[:space:]]*|) from '([^']+)';/import * as \1 from '\3';/g" "$file"
  
  # Remplacer les imports avec chemins relatifs ./
  sed -i '' -E "s/import \* as \.\/([^[:space:]\/]+)(\/[^[:space:]]*|) from '([^']+)';/import * as \1 from '\3';/g" "$file"
}

# Parcourir tous les fichiers TypeScript et TypeScript React dans le projet
echo "Recherche des fichiers TypeScript à corriger pour les imports relatifs..."
find . -type f \( -name "*.ts" -o -name "*.tsx" \) | while read -r file; do
  # Vérifier si le fichier contient des importations problématiques
  if grep -q "import \* as \.\." "$file" || grep -q "import \* as \." "$file" || grep -q "import \* as @" "$file"; then
    fix_relative_imports "$file"
  fi
done

echo "Correction terminée. Exécutez 'npx tsc --noEmit' pour vérifier les erreurs restantes."
