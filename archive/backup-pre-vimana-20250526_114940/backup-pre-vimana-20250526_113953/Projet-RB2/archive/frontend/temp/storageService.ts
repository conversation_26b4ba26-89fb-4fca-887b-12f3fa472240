import { BehaviorSubject } from "rxjs";

export interface StorageOptions {
  prefix?: string;
  encryption?: boolean;
  compression?: boolean;
  expiry?: number; // in milliseconds
}

interface StorageItem {
  value: any;
  timestamp: number;
  expiry?: number;
}

class StorageService {
  private static instance: StorageService;
  private prefix: string;
  private changes: BehaviorSubject<{ key: string; value: any }>;
  private encryption: boolean;
  private compression: boolean;

  private constructor(options: StorageOptions = {}) {
    this.prefix = options.prefix || 'app_';
    this.encryption = options.encryption || false;
    this.compression = options.compression || false;
    this.changes = new BehaviorSubject<{ key: string; value: any }>({
      key: '',
      value: null
    });

    this.setupStorageListener();
  }

  public static getInstance(options?: StorageOptions): StorageService {
    if(!StorageService.instance) {
      StorageService.instance = new StorageService(options);
    }
    return StorageService.instance;
  }

  private setupStorageListener(): void {
    window.addEventListener('storage', event => {
      if (event.key && event.key.startsWith(this.prefix)) {
        const key = event.key.slice(this.prefix.length);
        const value = event.newValue ? JSON.parse(event.newValue) : null;
        this.changes.next({ key, value });
      }
    });
  }

  public async set(key: string, value: any, options: StorageOptions = {}): Promise<void> {
    const prefixedKey = this.prefix + key;
    const item: StorageItem = {
      value: value,
      timestamp: Date.now(),
      expiry: options.expiry
    };

    let serializedValue = JSON.stringify(item);

    if(this.compression || options.compression) {
      serializedValue = await this.compress(serializedValue);
    }

    if(this.encryption || options.encryption) {
      serializedValue = await this.encrypt(serializedValue);
    }

    try {
      localStorage.setItem(prefixedKey, serializedValue);
      this.changes.next({ key, value });
    } catch(error: unknown) {
      if(error instanceof Error && error.name === 'QuotaExceededError') {
        this.handleQuotaExceeded();
      }
      throw error;
    }
  }

  public async get<T>(key: string): Promise<T | null> {
    const prefixedKey = this.prefix + key;
    const serializedValue = localStorage.getItem(prefixedKey);

    if (!serializedValue) return null;

    let value = serializedValue;

    if(this.encryption) {
      value = await this.decrypt(value);
    }

    if(this.compression) {
      value = await this.decompress(value);
    }

    try {
      const item: StorageItem = JSON.parse(value);

      if (item.expiry && Date.now() > item.timestamp + item.expiry) {
        this.remove(key);
        return null;
      }

      return item.value as T;
    } catch {
      return null;
    }
  }

  public remove(key: string): void {
    const prefixedKey = this.prefix + key;
    localStorage.removeItem(prefixedKey);
    this.changes.next({ key, value: null });
  }

  public clear(): void {
    const keys = this.keys();
    keys.forEach(key => this.remove(key));
  }

  public keys(): string[] {
    return Object.keys(localStorage)
      .filter(key => key.startsWith(this.prefix))
      .map(key => key.slice(this.prefix.length));
  }

  public size(): number {
    return this.keys().length;
  }

  public watch(key: string): BehaviorSubject<any> {
    const subject = new BehaviorSubject<any>(null);

    this.get(key).then(value => subject.next(value));

    const subscription = this.changes.subscribe(change => {
      if(change.key === key) {
        subject.next(change.value);
      }
    });

    // Cleanup logic
    const originalComplete = subject.complete.bind(subject);
    subject.complete = () => {
      subscription.unsubscribe();
      originalComplete();
    };

    return subject;
  }

  public async getMultiple<T>(keys: string[]): Promise<Record<string, T | null>> {
    const result: Record<string, T | null> = {};
    await Promise.all(
      keys.map(async (key: string) => {
        result[key] = await this.get<T>(key);
      })
    );
    return result;
  }

  public setMultiple(items: Record<string, any>): Promise<void[]> {
    return Promise.all(
      Object.entries(items).map(([key, value]) => this.set(key, value))
    );
  }

  public removeMultiple(keys: string[]): void {
    keys.forEach(key => this.remove(key));
  }

  private async handleQuotaExceeded(): Promise<void> {
    const keys = this.keys();
    if (keys.length === 0) return;

    // Remove oldest items until we free up 20% of the items
    const itemsToRemove = Math.ceil(keys.length * 0.2);
    const itemsWithTimestamp = await Promise.all(
      keys.map(async key => {
        const value = await this.get<StorageItem>(key);
        return {
          key,
          timestamp: value?.timestamp || 0
        };
      })
    );

    // Définir l'interface pour les éléments avec timestamp
    interface TimestampItem {
      key: string;
      timestamp: number;
    }
    
    // Assurer que les éléments sont du type TimestampItem
    const typedItems = itemsWithTimestamp as TimestampItem[];
    
    // Trier les éléments par timestamp (du plus ancien au plus récent)
    const itemsToDelete = typedItems
      .sort((a: TimestampItem, b: TimestampItem) => a.timestamp - b.timestamp)
      .slice(0, itemsToRemove);

    // Supprimer les éléments les plus anciens
    itemsToDelete.forEach((item: TimestampItem) => this.remove(item.key));
  }

  private async compress(value: string): Promise<string> {
    // Implementation would depend on your chosen compression library
    // This is a placeholder that returns the original value
    return value;
  }

  private async decompress(value: string): Promise<string> {
    // Implementation would depend on your chosen compression library
    // This is a placeholder that returns the original value
    return value;
  }

  private async encrypt(value: string): Promise<string> {
    // Implementation would depend on your chosen encryption library
    // This is a placeholder that returns the original value
    return value;
  }

  private async decrypt(value: string): Promise<string> {
    // Implementation would depend on your chosen encryption library
    // This is a placeholder that returns the original value
    return value;
  }

  public onChange(): BehaviorSubject<{ key: string; value: any }> {
    return this.changes;
  }

  public hasKey(key: string): boolean {
    return localStorage.hasOwnProperty(this.prefix + key);
  }

  public getTotalSize(): number {
    return this.keys().reduce((size, key) => {
      const value = localStorage.getItem(this.prefix + key) || '';
      return size + value.length * 2; // Multiply by 2 for UTF-16 encoding
    }, 0);
  }
}

export default StorageService;