groups:
  - name: notification_alerts
    rules:
      - alert: HighNotificationLatency
        expr: histogram_quantile(0.95, rate(notification_delivery_latency_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High notification delivery latency
          description: 95th percentile of notification delivery latency is above 2 seconds

      - alert: FrequentWebSocketReconnects
        expr: rate(notification_ws_reconnects_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: Frequent WebSocket reconnections
          description: WebSocket is reconnecting frequently, possible connection issues