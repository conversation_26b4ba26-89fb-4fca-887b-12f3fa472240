#!/usr/bin/env node

/**
 * Script pour compiler tous les fichiers TypeScript avec l'option --jsx react
 */
import { execSync } from 'child_process';

try {
  console.log('Compilation des fichiers TypeScript...');
  execSync('npx tsc --noEmit --skipLibCheck --jsx react', { stdio: 'inherit' });
  console.log('Compilation terminée avec succès !');
} catch (error) {
  console.error('Erreur lors de la compilation :', error.message);
  process.exit(1);
}
