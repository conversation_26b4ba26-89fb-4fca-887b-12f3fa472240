# Checklist de Revue de Code pour la Sécurité

Cette checklist est conçue pour vous aider à identifier les problèmes de sécurité potentiels lors des revues de code. Utilisez-la pour tous les composants et services frontend avant d'approuver une pull request.

## Instructions

1. Parcourez chaque section de cette checklist lors de la revue de code
2. Pour chaque élément, marquez-le comme :
   - ✅ Conforme
   - ❌ Non conforme (nécessite correction)
   - N/A Non applicable
3. Ajoutez des commentaires détaillés pour tout élément non conforme
4. Ne fusionnez pas le code jusqu'à ce que tous les éléments applicables soient conformes

## Validation des entrées

- [ ] Toutes les entrées utilisateur sont validées côté client
- [ ] Des validations de type et de format sont appliquées (ex: Yup, Zod)
- [ ] Des limites de longueur appropriées sont définies pour les champs texte
- [ ] Les validations sont effectuées au niveau du composant ET lors de la soumission
- [ ] Les messages d'erreur sont informatifs sans révéler d'informations sensibles
- [ ] Les validations sont cohérentes avec celles du backend (si applicable)

## Protection XSS (Cross-Site Scripting)

- [ ] Aucune utilisation de `dangerouslySetInnerHTML` sans sanitization
- [ ] DOMPurify est utilisé pour toute manipulation de HTML
- [ ] Les contenus générés par l'utilisateur sont toujours traités comme non sûrs
- [ ] React est utilisé pour échapper automatiquement les entrées (pas de manipulation DOM directe)
- [ ] Les URL dynamiques sont validées (particulièrement pour les attributs `href` et `src`)
- [ ] Les expressions JSX n'incluent pas de valeurs non échappées

## Gestion d'état et données sensibles

- [ ] Aucune donnée sensible n'est stockée dans l'état Redux ou Context API
- [ ] Les mots de passe, tokens et autres données sensibles ne sont pas persistés en clair
- [ ] Pas de fuite d'informations sensibles dans les logs ou les commentaires
- [ ] Les erreurs d'API ne révèlent pas d'informations sensibles
- [ ] Les tokens d'authentification sont stockés de manière sécurisée (pas dans localStorage sans protection)
- [ ] L'état de l'application est nettoyé à la déconnexion

## Contrôle d'accès

- [ ] Les contrôles d'accès sont vérifiés à chaque niveau pertinent
- [ ] Les composants sensibles vérifient les permissions avant le rendu
- [ ] Les routes protégées redirigent correctement les utilisateurs non autorisés
- [ ] Les fonctionnalités administratives sont correctement protégées
- [ ] Le principe du moindre privilège est appliqué
- [ ] Les données affichées correspondent aux permissions de l'utilisateur

## Gestion des API et requêtes

- [ ] Les tokens CSRF sont inclus dans les requêtes appropriées
- [ ] Les en-têtes de sécurité sont correctement configurés pour les requêtes
- [ ] Les erreurs d'API sont gérées correctement
- [ ] Les mécanismes de rafraîchissement de token sont implémentés si nécessaire
- [ ] Les paramètres d'URL sont validés et échappés
- [ ] Les réponses API sont validées avant utilisation

## Gestion des fichiers

- [ ] Les téléchargements de fichiers sont validés (taille, type, extension)
- [ ] Les noms de fichiers sont sanitisés
- [ ] La vérification de type MIME est appliquée
- [ ] Des limites de taille appropriées sont appliquées
- [ ] La génération de prévisualisations est sécurisée

## Redirections et navigation

- [ ] Les redirections utilisent une liste blanche de destinations autorisées
- [ ] Les paramètres de redirection sont validés
- [ ] Protection contre les redirections ouvertes
- [ ] Les URL externes sont validées avant la navigation

## Stockage local

- [ ] Aucune donnée sensible dans localStorage ou sessionStorage
- [ ] Les cookies sensibles ont les bons attributs (HttpOnly, Secure, SameSite)
- [ ] Les données stockées localement sont validées avant utilisation
- [ ] Le stockage local est nettoyé à la déconnexion

## Sécurité des communications

- [ ] Les URLs absolues utilisent HTTPS
- [ ] Les WebSockets utilisent WSS (si applicable)
- [ ] Les API tierces sont appelées de manière sécurisée
- [ ] Les références à des ressources externes utilisent HTTPS

## Dépendances

- [ ] Les dépendances npm n'ont pas de vulnérabilités connues (vérifiées avec `npm audit`)
- [ ] Aucune dépendance inutilisée ou obsolète
- [ ] Les dépendances sont verrouillées à des versions spécifiques
- [ ] Les CDN externes ont une intégrité vérifiée (attributs SRI)

## Intégration des frameworks

- [ ] Les fonctionnalités de sécurité des frameworks sont correctement utilisées
- [ ] Les middlewares de sécurité sont correctement configurés (si applicable)
- [ ] Les configurations des outils de build n'exposent pas d'informations sensibles
- [ ] Les variables d'environnement sont utilisées de manière sécurisée

## Tests de sécurité

- [ ] Des tests existent pour les fonctionnalités de sécurité implémentées
- [ ] Les tests couvrent les cas limites et les scénarios d'attaque
- [ ] Les tests de validation vérifient les entrées valides ET invalides
- [ ] Les tests sont cohérents avec la couverture de test exigée

## Spécifique à notre application

- [ ] Le système de permissions RBAC est correctement utilisé
- [ ] La validation des formulaires utilise nos schémas de validation standards
- [ ] Le service de redirection est utilisé pour toutes les redirections
- [ ] Les services API suivent notre pattern standard de validation des paramètres

## Notes supplémentaires

[Ajoutez ici des notes spécifiques à cette revue]

---

**Réviseur** : _________________________  
**Date de révision** : _________________  
**Pull Request #** : ___________________  

---

## Ressources

- [Guide d'implémentation de la sécurité](./SECURITY_IMPLEMENTATION_GUIDE.md)
- [Résumé des Tests de Sécurité](./SECURITY_TESTING_SUMMARY.md)
- [OWASP Top 10](https://owasp.org/Top10/)
- [OWASP Cheatsheet Series](https://cheatsheetseries.owasp.org/) 