#!/usr/bin/env node

/**
 * <PERSON>ript to automatically fix common TypeScript errors
 */
import fs from 'fs';
import path from 'path';
import { glob } from 'glob';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

// Main async function
async function main() {

// Configuration
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ROOT_DIR = process.cwd();
const SRC_DIR = path.join(ROOT_DIR, 'src');

// Common TypeScript errors and their fixes
const FIXES = [
  // Fix unused variables
  {
    pattern: /^(\s*)const\s+([a-zA-Z0-9_]+)\s*=.*?;\s*\/\/\s*.*?$/gm,
    test: (content, match) => {
      // Check if the variable is used elsewhere in the file
      const varName = match[2];
      const varRegex = new RegExp(`[^a-zA-Z0-9_]${varName}[^a-zA-Z0-9_]`);
      return !varRegex.test(content.replace(match[0], ''));
    },
    replacement: (match) => `${match[1]}// Unused: ${match[0].trim()}`
  },

  // Fix missing type declarations
  {
    pattern: /(const|let|var)\s+([a-zA-Z0-9_]+)\s*=\s*([^:;]+);/g,
    test: (content, match) => {
      // Only add types to variables without explicit types
      return !content.includes(`${match[2]}: `);
    },
    replacement: (match) => {
      const value = match[3].trim();
      let type = 'any';

      // Try to infer the type
      if (value === '[]') type = 'any[]';
      else if (value === '{}') type = 'Record<string, any>';
      else if (value.startsWith('"') || value.startsWith("'")) type = 'string';
      else if (/^-?\d+(\.\d+)?$/.test(value)) type = 'number';
      else if (value === 'true' || value === 'false') type = 'boolean';
      else if (value.includes('new ')) {
        const className = value.match(/new\s+([a-zA-Z0-9_]+)/);
        if (className) type = className[1];
      }

      return `${match[1]} ${match[2]}: ${type} = ${match[3]};`;
    }
  },

  // Fix incorrect imports
  {
    pattern: /import\s+\{\s*\.\//g,
    replacement: () => 'import { '
  },

  // Fix missing React imports
  {
    pattern: /^(import\s+(?!.*?React).+from\s+['"]react['"];)$/gm,
    test: (content) => !content.includes('import React'),
    replacement: (match) => `import React from 'react';\n${match[0]}`
  }
];

// Find all TypeScript files
const tsFiles = await glob(path.join(SRC_DIR, '**/*.{ts,tsx}'), {
  ignore: ['**/node_modules/**', '**/*.d.ts']
});

console.log(`Found ${tsFiles.length} TypeScript files to process`);

// Process each file
let fixedFiles = 0;
let fixedErrors = 0;

tsFiles.forEach(file => {
  let content = fs.readFileSync(file, 'utf8');
  let originalContent = content;
  let fileFixedErrors = 0;

  FIXES.forEach(fix => {
    let matches = [...content.matchAll(fix.pattern)];

    matches.forEach(match => {
      if (!fix.test || fix.test(content, match)) {
        const replacement = typeof fix.replacement === 'function'
          ? fix.replacement(match)
          : fix.replacement;

        content = content.replace(match[0], replacement);
        fileFixedErrors++;
      }
    });
  });

  if (content !== originalContent) {
    fs.writeFileSync(file, content, 'utf8');
    fixedFiles++;
    fixedErrors += fileFixedErrors;
    console.log(`Fixed ${fileFixedErrors} errors in ${file}`);
  }
});

console.log(`\nSummary:`);
console.log(`- Fixed ${fixedErrors} errors in ${fixedFiles} files`);
console.log(`- ${tsFiles.length - fixedFiles} files were already correct`);

// Run TypeScript compiler to check remaining errors
try {
  console.log('\nRunning TypeScript compiler to check remaining errors...');
  execSync('npx tsc --noEmit', { stdio: 'inherit' });
  console.log('TypeScript check completed successfully!');
} catch (error) {
  console.error('TypeScript check found remaining errors.');
  console.log('Run the script again or fix the remaining errors manually.');
}

} // End of main function

// Run the main function
main().catch(error => {
  console.error('Error running the script:', error);
  process.exit(1);
});
