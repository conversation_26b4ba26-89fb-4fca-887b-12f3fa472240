// Serveur Express simple pour servir l'application avec prise en charge SPA
const express = require('express');
const path = require('path');

// Créer l'application Express
const app = express();

// Servir les fichiers statiques
app.use(express.static(path.join(__dirname, 'dist')));

// Route de secours pour SPA - toutes les routes non reconnues sont envoyées vers index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// Démarrer le serveur
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`
╔════════════════════════════════════════════════════════╗
║                                                        ║
║   🚀 Serveur Express démarré sur port ${PORT}                ║
║   🌐 http://localhost:${PORT}                               ║
║                                                        ║
║   📝 Toutes les routes sont redirigées vers index.html  ║
║   🔍 Testez les routes:                                 ║
║      http://localhost:${PORT}/test                         ║
║      http://localhost:${PORT}/login                        ║
║      http://localhost:${PORT}/dashboard                    ║
║                                                        ║
╚════════════════════════════════════════════════════════╝
`);
}); 