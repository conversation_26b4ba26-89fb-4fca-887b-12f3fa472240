Directory structure:
└── pages/
    ├── InvoicePage.tsx
    ├── index.ts
    ├── Booking/
    │   ├── BookingPage.tsx
    │   └── components/
    │       ├── BookingCalendar.tsx
    │       ├── BookingForm.tsx
    │       └── BookingSummary.tsx
    ├── Dashboard/
    │   ├── AdminDashboard.tsx
    │   ├── Analytics.tsx
    │   ├── Dashboard.tsx
    │   ├── GroupDashboard.tsx
    │   ├── Home.tsx
    │   ├── MainDashboard.tsx
    │   ├── PartnerDashboard.tsx
    │   ├── UserDashboard.tsx
    │   └── components/
    │       ├── ActivityFeed.tsx
    │       ├── BookingCard.tsx
    │       ├── RecentBookings.tsx
    │       ├── Stats.tsx
    │       └── UpcomingEvents.tsx
    ├── Files/
    │   ├── index.tsx
    │   └── styles.css
    ├── Login/
    │   ├── LoginPage.tsx
    │   └── index.tsx
    ├── MarketplacePage/
    │   └── index.tsx
    ├── NftGallery/
    │   ├── index.tsx
    │   ├── .DS_Store
    │   └── nft/
    │       ├── NFTGalleryPage.tsx
    │       ├── TokenPage.tsx
    │       └── index.ts
    ├── Private/
    │   ├── .DS_Store
    │   └── user/
    │       ├── Profile.tsx
    │       ├── ProfilePage.tsx
    │       ├── RewardsPage.tsx
    │       ├── SettingsPage.tsx
    │       ├── UserDashboardPage.tsx
    │       └── .DS_Store
    ├── Profile/
    │   ├── Profile.tsx
    │   ├── .DS_Store
    │   └── components/
    │       └── ChangePasswordDialog.tsx
    ├── Public/
    │   ├── BecomePartnerPage.tsx
    │   ├── BlogPage.tsx
    │   ├── CGUPage.tsx
    │   ├── CareersPage.tsx
    │   ├── CaterersPage.tsx
    │   ├── CommunityMemberPage.tsx
    │   ├── CommunityPage.tsx
    │   ├── ContactPage.tsx
    │   ├── EventsPage.tsx
    │   ├── ExploreRetreatsPage.tsx
    │   ├── FAQPage.tsx
    │   ├── FoundationPage.tsx
    │   ├── GDPRPage.tsx
    │   ├── GalleryPage.tsx
    │   ├── HomePage.test.tsx
    │   ├── HomePage.tsx
    │   ├── HostsPage.tsx
    │   ├── InsurancePage.tsx
    │   ├── InvoicePage.tsx
    │   ├── LandingPage.tsx
    │   ├── LocationPage.tsx
    │   ├── LoginPage.tsx
    │   ├── LoyaltyPage.tsx
    │   ├── MainPage.tsx
    │   ├── NFTGalleryPage.tsx
    │   ├── NewsPage.tsx
    │   ├── OrganizersPage.tsx
    │   ├── PartnersPage.tsx
    │   ├── PrivacyPolicyPage.tsx
    │   ├── ProgramsPage.tsx
    │   ├── RegisterPage.tsx
    │   ├── RetreatPage.tsx
    │   ├── RetreatsPage.tsx
    │   ├── SecurityPage.tsx
    │   ├── SettingsPage.tsx
    │   ├── SupportPage.tsx
    │   ├── TermsPage.tsx
    │   ├── TestimonialsPage.tsx
    │   ├── TokenPage.tsx
    │   ├── TravelAgenciesPage.tsx
    │   ├── UnauthorizedPage.tsx
    │   ├── WellnessPage.tsx
    │   ├── index.ts
    │   └── Retreats/
    │       └── DetailsPage.tsx
    ├── Register/
    │   └── index.tsx
    ├── Settings/
    │   ├── Profile.tsx
    │   ├── Security.tsx
    │   ├── Settings.tsx
    │   ├── SettingsPage.tsx
    │   ├── .DS_Store
    │   ├── SettingsPage/
    │   │   ├── SettingsPage.tsx
    │   │   ├── components/
    │   │   │   ├── NotificationsSection.tsx
    │   │   │   ├── PreferencesSection.tsx
    │   │   │   ├── PrivacySection.tsx
    │   │   │   └── SecuritySection.tsx
    │   │   └── hooks/
    │   │       └── useSettings.ts
    │   └── components/
    │       ├── AppearanceSettings.tsx
    │       ├── NotificationsSettings.tsx
    │       └── SecuritySettings.tsx
    ├── admin/
    │   ├── AdminDashboard.tsx
    │   ├── AdminPage.tsx
    │   ├── AdminPanel.tsx
    │   ├── Analytics.tsx
    │   ├── Content.tsx
    │   ├── Dashboard.tsx
    │   ├── MonitoringDashboard.tsx
    │   ├── ReportsPage.tsx
    │   ├── ResourcesPage.tsx
    │   ├── Roles.tsx
    │   ├── Settings.tsx
    │   └── Users.tsx
    ├── auth/
    │   ├── ForgotPasswordPage.tsx
    │   ├── LoginPage.tsx
    │   ├── RegisterPage.tsx
    │   ├── ResetPasswordPage.tsx
    │   └── index.ts
    ├── dashboards/
    │   ├── DashboardPage.tsx
    │   ├── NotificationHistory.tsx
    │   ├── ProfilePage.tsx
    │   └── SettingsPage.tsx
    ├── error/
    │   ├── Error404Page.tsx
    │   ├── Error500Page.tsx
    │   ├── ErrorPage.tsx
    │   ├── NotFoundPage.tsx
    │   ├── UnauthorizedPage.tsx
    │   ├── UnderConstructionPage.tsx
    │   ├── UpgradePage.tsx
    │   ├── index.ts
    │   └── __tests__/
    │       └── Error404Page.test.tsx
    ├── financial/
    │   ├── FinancialReports.tsx
    │   ├── InsurancePage.tsx
    │   ├── InvoiceList.tsx
    │   ├── InvoicePage.tsx
    │   └── PaymentManager.tsx
    ├── microservices/
    │   ├── Analytics.tsx
    │   ├── AnalyzerDashboard.tsx
    │   ├── AnalyzerLayout.tsx
    │   ├── AnalyzerReports.tsx
    │   ├── BrowseFiles.tsx
    │   ├── CarDetailsPage.tsx
    │   ├── CarRentalPage.tsx
    │   ├── CompareInsurance.tsx
    │   ├── CompareInsuranceLayout.tsx
    │   ├── Domains.tsx
    │   ├── Editor.tsx
    │   ├── EducationPage.tsx
    │   ├── FinancialManagement.tsx
    │   ├── FinancialManagementLayout.tsx
    │   ├── Messaging.tsx
    │   ├── NFT.tsx
    │   ├── RetreatMatcher.tsx
    │   ├── RetreatMatcherLayout.tsx
    │   ├── RetreatStream.tsx
    │   ├── SecurityService.tsx
    │   ├── Social.tsx
    │   ├── SocialVideo.tsx
    │   ├── StorageLayout.tsx
    │   ├── TransportBooking.tsx
    │   ├── Upload.tsx
    │   ├── VR.tsx
    │   ├── VRLayout.tsx
    │   └── WebsiteCreatorLayout.tsx
    ├── nft/
    │   ├── NFTGalleryPage.tsx
    │   ├── TokenPage.tsx
    │   └── index.ts
    ├── partner/
    │   ├── AdminDashboardPage.tsx
    │   ├── AffiliatePortal.tsx
    │   ├── CreateRetreatAIPage.tsx
    │   ├── GetStartedPage.tsx
    │   └── OnboardingPage.tsx
    ├── professional/
    │   ├── OrganizersPage.tsx
    │   ├── PartnersPage.tsx
    │   ├── ProDashboard.tsx
    │   └── index.tsx
    ├── retreats/
    │   ├── ExploreRetreatsPage.tsx
    │   ├── LivestreamRetreatPage.tsx
    │   ├── RetreatPage.tsx
    │   ├── types.ts
    │   └── components/
    │       ├── RetreatCard.tsx
    │       └── RetreatFilters.tsx
    ├── services/
    │   ├── AffiliatePortal.tsx
    │   ├── CarDetailsPage.tsx
    │   ├── CarRentalPage.tsx
    │   ├── LocationPage.tsx
    │   ├── LoyaltyPage.tsx
    │   ├── MessagesPage.tsx
    │   ├── NFTGalleryPage.tsx
    │   ├── NotificationHistory.tsx
    │   └── index.ts
    └── social/
        ├── CommunityMemberPage.tsx
        ├── MessagesPage.tsx
        ├── NewsPage.tsx
        ├── NotificationHistory.tsx
        ├── index.ts
        ├── .DS_Store
        └── SocialFeed/
            ├── SocialFeedPage.tsx
            └── index.tsx
