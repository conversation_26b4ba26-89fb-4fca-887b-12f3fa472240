10016	IGNORE	Regular expression denial of service	
10020	IGNORE	X-Frame-Options Header	
10021	IGNORE	X-Content-Type-Options Header	
10023	IGNORE	Information Disclosure - Debug Error Messages	
10024	IGNORE	Information Disclosure - Sensitive Information in URL	
10025	IGNORE	Information Disclosure - Sensitive Information in HTTP Referrer Header	
10026	IGNORE	HTTP Parameter Override	
10027	IGNORE	Information Disclosure - Suspicious Comments	
10028	IGNORE	Open Redirect	
10029	IGNORE	Cookie Poisoning	
10030	IGNORE	User Controllable Charset	
10031	IGNORE	User Controllable HTML Element Attribute (Potential XSS)	
10032	IGNORE	Viewstate Scanner	
10033	IGNORE	Directory Browsing	
10034	IGNORE	Heartbleed OpenSSL Vulnerability	
10035	IGNORE	Strict-Transport-Security Header	
10036	IGNORE	Server Leaks Information via "X-Powered-By" HTTP Response Header Field(s)	
10037	IGNORE	Server Leaks Version Information via "Server" HTTP Response Header Field	
10038	IGNORE	Content Security Policy (CSP) Header Not Set	
10039	IGNORE	X-Backend-Server Header Information Leak	
10040	IGNORE	Secure Pages Include Mixed Content	
10041	IGNORE	HTTP to HTTPS Insecure Transition in Form Post	
10042	IGNORE	HTTPS to HTTP Insecure Transition in Form Post	
10043	IGNORE	User Controllable JavaScript Event (XSS)	
10044	IGNORE	Big Redirect Detected (Potential Sensitive Information Leak)	
10045	IGNORE	Source Code Disclosure - /WEB-INF folder	
10046	IGNORE	Insecure Component	
10047	IGNORE	HTTPS Content Available via HTTP	
10048	IGNORE	Remote Code Execution - Shell Shock	
10049	IGNORE	Weak SSL Cipher Suites	
10050	IGNORE	Retrieved from Cache	
10051	IGNORE	Relative Path Confusion	
10052	IGNORE	X-ChromeLogger-Data Header Information Leak	
10053	IGNORE	Apache Range Header DoS (CVE-2011-3192)	
10054	IGNORE	Cookie without SameSite Attribute	
10055	IGNORE	CSP Scanner	
10056	IGNORE	X-Debug-Token Information Leak	
10057	IGNORE	Username Hash Found	
10058	IGNORE	GET for POST	
10059	IGNORE	X-AspNet-Version Response Header	
10060	IGNORE	X-AspNetMvc-Version Response Header	
10061	IGNORE	X-WebKit-CSP Header	
10062	IGNORE	PII Scanner	
10063	IGNORE	Feature Policy Header	
10064	IGNORE	Permissions Policy Header	
