name: Dynamic Application Security Testing (DAST)

on:
  workflow_dispatch:  # Déclenchement manuel
  schedule:
    - cron: '0 2 * * 1'  # Tous les lundis à 2h du matin
  push:
    branches:
      - main
      - staging
    paths:
      - 'frontend/**'
      - '.github/workflows/dast.yml'

jobs:
  dast-scan:
    name: DAST Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch' || github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: |
          cd frontend
          npm ci
      
      - name: Start frontend in background
        run: |
          cd frontend
          npm run build
          nohup npm run serve &
          echo "Waiting for app to start..."
          sleep 10
        env:
          NODE_ENV: test
      
      - name: Run health check
        run: |
          ATTEMPT=0
          MAX_ATTEMPTS=10
          ENDPOINT=http://localhost:3000
          
          until $(curl --output /dev/null --silent --head --fail $ENDPOINT) || [ $ATTEMPT -eq $MAX_ATTEMPTS ]; do
            ATTEMPT=$((ATTEMPT+1))
            echo "Attempt $ATTEMPT: waiting for $ENDPOINT..."
            sleep 5
          done
          
          if [ $ATTEMPT -eq $MAX_ATTEMPTS ]; then
            echo "Health check failed after $MAX_ATTEMPTS attempts!"
            exit 1
          fi
          
          echo "Health check succeeded!"
      
      - name: Setup ZAP
        uses: zaproxy/action-setup-zap@v1
        with:
          version: '2.12.0'
      
      - name: Install required packages for DAST script
        run: npm install -g ts-node typescript @types/node
      
      - name: Run DAST scan (baseline)
        run: |
          cd frontend
          ts-node scripts/run-dast-scan.ts
        env:
          DAST_TARGET_URL: 'http://localhost:3000'
          DAST_SCAN_TYPE: 'baseline'
          DAST_INCLUDE_PASSIVE: 'true'
          DAST_INCLUDE_ACTIVE: 'false'
          DAST_REPORT_FORMATS: 'html,json,xml'
          DAST_THRESHOLD_HIGH: '0'
          DAST_THRESHOLD_MEDIUM: '0'
          DAST_THRESHOLD_LOW: '5'
          DAST_THRESHOLD_INFO: '20'
          DAST_EXCLUDE_URLS: '/api/docs,/docs/api,/assets/images'
          CI: 'true'
      
      - name: Archive DAST results
        uses: actions/upload-artifact@v3
        with:
          name: dast-reports
          path: frontend/reports/dast/
          retention-days: 30
      
      - name: Generate summary
        run: |
          echo "## DAST Scan Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ -f frontend/reports/dast/baseline-report-*.json ]; then
            LATEST_REPORT=$(ls -t frontend/reports/dast/baseline-report-*.json | head -1)
            
            # Extract statistics using jq if available
            if command -v jq &> /dev/null; then
              HIGH=$(jq '.site[0].alerts | map(select(.riskcode == "3")) | length' $LATEST_REPORT)
              MEDIUM=$(jq '.site[0].alerts | map(select(.riskcode == "2")) | length' $LATEST_REPORT)
              LOW=$(jq '.site[0].alerts | map(select(.riskcode == "1")) | length' $LATEST_REPORT)
              INFO=$(jq '.site[0].alerts | map(select(.riskcode == "0")) | length' $LATEST_REPORT)
              
              echo "| Severity | Count |" >> $GITHUB_STEP_SUMMARY
              echo "|----------|-------|" >> $GITHUB_STEP_SUMMARY
              echo "| High     | $HIGH    |" >> $GITHUB_STEP_SUMMARY
              echo "| Medium   | $MEDIUM    |" >> $GITHUB_STEP_SUMMARY
              echo "| Low      | $LOW    |" >> $GITHUB_STEP_SUMMARY
              echo "| Info     | $INFO    |" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
            else
              echo "JQ not available, skipping detailed statistics." >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "No JSON report found, skipping statistics." >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "For detailed results, download the DAST reports artifact." >> $GITHUB_STEP_SUMMARY

  dast-scan-staging:
    name: DAST Scan (Staging Environment)
    runs-on: ubuntu-latest
    needs: dast-scan
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch' || github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install required packages for DAST script
        run: npm install -g ts-node typescript @types/node
      
      - name: Setup ZAP
        uses: zaproxy/action-setup-zap@v1
        with:
          version: '2.12.0'
      
      - name: Run DAST scan on staging environment
        run: |
          cd frontend
          ts-node scripts/run-dast-scan.ts
        env:
          DAST_TARGET_URL: 'https://staging.example.com'  # Remplacer par l'URL réelle de l'environnement de staging
          DAST_SCAN_TYPE: 'full'
          DAST_INCLUDE_PASSIVE: 'true'
          DAST_INCLUDE_ACTIVE: 'true'
          DAST_REPORT_FORMATS: 'html,json,xml'
          DAST_THRESHOLD_HIGH: '0'
          DAST_THRESHOLD_MEDIUM: '3'
          DAST_THRESHOLD_LOW: '10'
          DAST_THRESHOLD_INFO: '50'
          DAST_SPIDER_TIMEOUT: '5'
          DAST_SCAN_TIMEOUT: '10'
          DAST_EXCLUDE_URLS: '/api/docs,/docs/api,/assets/images,/api/metrics'
          CI: 'true'
      
      - name: Archive DAST results (Staging)
        uses: actions/upload-artifact@v3
        with:
          name: dast-reports-staging
          path: frontend/reports/dast/
          retention-days: 30
      
      - name: Generate summary (Staging)
        run: |
          echo "## DAST Scan Results (Staging)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ -f frontend/reports/dast/full-scan-report-*.json ]; then
            LATEST_REPORT=$(ls -t frontend/reports/dast/full-scan-report-*.json | head -1)
            
            # Extract statistics using jq if available
            if command -v jq &> /dev/null; then
              HIGH=$(jq '.site[0].alerts | map(select(.riskcode == "3")) | length' $LATEST_REPORT)
              MEDIUM=$(jq '.site[0].alerts | map(select(.riskcode == "2")) | length' $LATEST_REPORT)
              LOW=$(jq '.site[0].alerts | map(select(.riskcode == "1")) | length' $LATEST_REPORT)
              INFO=$(jq '.site[0].alerts | map(select(.riskcode == "0")) | length' $LATEST_REPORT)
              
              echo "| Severity | Count |" >> $GITHUB_STEP_SUMMARY
              echo "|----------|-------|" >> $GITHUB_STEP_SUMMARY
              echo "| High     | $HIGH    |" >> $GITHUB_STEP_SUMMARY
              echo "| Medium   | $MEDIUM    |" >> $GITHUB_STEP_SUMMARY
              echo "| Low      | $LOW    |" >> $GITHUB_STEP_SUMMARY
              echo "| Info     | $INFO    |" >> $GITHUB_STEP_SUMMARY
              echo "" >> $GITHUB_STEP_SUMMARY
            else
              echo "JQ not available, skipping detailed statistics." >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "No JSON report found, skipping statistics." >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "For detailed results, download the DAST reports (Staging) artifact." >> $GITHUB_STEP_SUMMARY 