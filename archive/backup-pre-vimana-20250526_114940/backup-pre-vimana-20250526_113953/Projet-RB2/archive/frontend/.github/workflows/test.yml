name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Type checking
        run: npm run lint:ts
      - name: Run unit tests
        run: npm test
      - name: Upload unit test coverage
        uses: actions/upload-artifact@v3
        with:
          name: unit-test-coverage
          path: coverage/
          retention-days: 5

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run API tests with MSW
        run: npm run test:api
      - name: Upload integration test coverage
        uses: actions/upload-artifact@v3
        with:
          name: integration-test-coverage
          path: coverage/
          retention-days: 5

  accessibility-tests:
    name: Accessibility Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run accessibility tests
        run: npm run test:a11y
      - name: Upload a11y violations report
        uses: actions/upload-artifact@v3
        with:
          name: a11y-report
          path: reports/a11y/
          retention-days: 5

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run performance tests
        run: npm run test:perf
      - name: Upload performance report
        uses: actions/upload-artifact@v3
        with:
          name: performance-report
          path: reports/performance/
          retention-days: 5

  edge-cases-tests:
    name: Edge Cases Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run edge cases tests
        run: npm run test:edge
      - name: Upload edge cases report
        uses: actions/upload-artifact@v3
        with:
          name: edge-cases-report
          path: coverage/
          retention-days: 5

  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run security tests
        run: npm run test:security
      - name: Upload security report
        uses: actions/upload-artifact@v3
        with:
          name: security-report
          path: coverage/
          retention-days: 5
      - name: Run dependency vulnerability scan
        run: npm audit --production
        continue-on-error: true # Allow the job to continue even if vulnerabilities are found

  security-analyzer:
    name: Automated Security Analysis
    runs-on: ubuntu-latest
    needs: [unit-tests, security-tests]
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Start app in background
        run: npm run dev & sleep 10
      - name: Run security analyzer
        run: npm run security:scan:ci
      - name: Upload security analyzer report
        uses: actions/upload-artifact@v3
        with:
          name: security-analyzer-report
          path: reports/security/
          retention-days: 10
      - name: Run OWASP ZAP baseline scan
        uses: zaproxy/action-baseline@v0.9.0
        with:
          target: 'http://localhost:3000'
          rules_file_name: '.github/zap-rules.tsv'
          cmd_options: '-a'
        continue-on-error: true

  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Install Cypress
        run: npm install cypress
      - name: Run Cypress tests
        uses: cypress-io/github-action@v5
        with:
          start: npm run dev
          wait-on: 'http://localhost:3000'
          browser: chrome
          headed: false
      - name: Upload Cypress screenshots
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots/
          retention-days: 5
      - name: Upload Cypress videos
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: cypress-videos
          path: cypress/videos/
          retention-days: 5

  merge-reports:
    name: Merge Test Reports
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, accessibility-tests, performance-tests, edge-cases-tests, security-tests, security-analyzer, e2e-tests]
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Download all test reports
        uses: actions/download-artifact@v3
      - name: Generate combined report
        run: npx nyc merge . .nyc_output/out.json && npx nyc report --reporter=html --reporter=text
      - name: Upload combined report
        uses: actions/upload-artifact@v3
        with:
          name: combined-test-report
          path: coverage/
          retention-days: 10 