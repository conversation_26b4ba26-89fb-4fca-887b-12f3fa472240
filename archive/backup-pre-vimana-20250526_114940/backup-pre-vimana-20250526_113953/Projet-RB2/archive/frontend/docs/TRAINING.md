# Guide de formation pour l'intégration de Front-Audrey-V1-Main-main

Ce document est destiné à l'équipe de développement pour comprendre l'intégration de Front-Audrey-V1-Main-main dans le projet principal.

## Introduction

Front-Audrey-V1-Main-main est une version du frontend qui a été intégrée au projet principal. Cette intégration permet de bénéficier des fonctionnalités et des composants développés dans Front-Audrey-V1-Main-main tout en maintenant la cohérence avec le projet principal.

## Architecture

L'architecture de l'intégration suit le modèle suivant :

```
frontend/
├── src/
│   ├── components/
│   │   └── randbefrontend/       # Composants migrés d'Audrey-V1
│   │       ├── atoms/            # Composants atomiques
│   │       ├── molecules/        # Composants moléculaires
│   │       ├── organisms/        # Composants organismes
│   │       ├── templates/        # Templates
│   │       └── ui/               # Composants UI génériques
│   ├── pages/
│   │   └── randbefrontend/       # Pages migrées d'Audrey-V1
│   ├── styles/
│   │   ├── audrey-styles.css     # Styles spécifiques à Audrey-V1
│   │   └── audrey-integration.css # Adaptations pour l'intégration
│   ├── config/
│   │   └── audrey-integration.ts # Configuration pour l'intégration
│   └── routes/
│       ├── audreyRoutes.tsx      # Routes d'Audrey-V1
│       └── combinedRoutes.tsx    # Combinaison des routes
└── scripts/
    └── audrey-migration.js       # Script de migration
```

## Composants

### Composants atomiques

Les composants atomiques sont les briques de base de l'interface utilisateur. Ils sont simples et réutilisables.

Exemple d'utilisation d'un composant atomique :

```tsx
import Button from '../components/randbefrontend/atoms/Button/Button';

function MyComponent() {
  return (
    <Button onClick={() => console.log('Clicked!')} variant="primary">
      Click me
    </Button>
  );
}
```

### Composants moléculaires

Les composants moléculaires sont composés de plusieurs composants atomiques. Ils représentent des éléments d'interface plus complexes.

Exemple d'utilisation d'un composant moléculaire :

```tsx
import RetreatCard from '../components/randbefrontend/molecules/RetreatCard/RetreatCard';

function MyComponent() {
  const retreat = {
    id: '1',
    title: 'Retreat Title',
    location: 'Retreat Location',
    price: 100,
    rating: 4.5,
    image: 'retreat-image.jpg',
  };

  return <RetreatCard retreat={retreat} />;
}
```

### Composants organismes

Les composants organismes sont composés de plusieurs composants moléculaires. Ils représentent des sections complètes de l'interface utilisateur.

Exemple d'utilisation d'un composant organisme :

```tsx
import NavBarClient from '../components/randbefrontend/organisms/NavBarClient/NavBarClient';

function MyComponent() {
  return <NavBarClient />;
}
```

### Templates

Les templates sont des composants qui définissent la structure globale d'une page.

Exemple d'utilisation d'un template :

```tsx
import MainLayout from '../components/randbefrontend/templates/MainLayout/MainLayout';

function MyComponent() {
  return (
    <MainLayout>
      <h1>Page Content</h1>
      <p>This is the content of the page.</p>
    </MainLayout>
  );
}
```

## Pages

Les pages sont des composants qui représentent des vues complètes de l'application. Elles sont généralement associées à une route.

Exemple d'utilisation d'une page :

```tsx
import HomePage from '../pages/randbefrontend/HomePage';

function App() {
  return (
    <Routes>
      <Route path="/" element={<HomePage />} />
      {/* Autres routes */}
    </Routes>
  );
}
```

## Styles

Les styles sont organisés en deux fichiers principaux :

- `audrey-styles.css` : Styles spécifiques à Audrey-V1
- `audrey-integration.css` : Adaptations pour l'intégration

Les classes CSS sont préfixées avec `audrey-v1-` pour éviter les collisions avec les styles existants.

Exemple d'utilisation des styles :

```tsx
import '../styles/audrey-styles.css';

function MyComponent() {
  return (
    <div className="audrey-v1-container">
      <h1 className="audrey-v1-title">Title</h1>
      <p className="audrey-v1-text">Text</p>
    </div>
  );
}
```

## Routes

Les routes sont définies dans deux fichiers :

- `audreyRoutes.tsx` : Routes spécifiques à Audrey-V1
- `combinedRoutes.tsx` : Combinaison des routes d'Audrey-V1 et des routes existantes

Exemple d'utilisation des routes :

```tsx
import { BrowserRouter } from 'react-router-dom';
import CombinedRoutes from './routes/combinedRoutes';

function App() {
  return (
    <BrowserRouter>
      <CombinedRoutes />
    </BrowserRouter>
  );
}
```

## Intégration avec le Backend-NestJS

L'intégration avec le Backend-NestJS se fait via des services API. Ces services sont configurés dans le fichier `config/audrey-integration.ts`.

Exemple d'utilisation d'un service API :

```tsx
import { API_CONFIG } from '../config/audrey-integration';

async function fetchRetreats() {
  const response = await fetch(API_CONFIG.endpoints.retreats);
  const data = await response.json();
  return data;
}
```

## Intégration avec les microservices

L'intégration avec les microservices se fait via le `MicroserviceConnector`. Les endpoints sont configurés dans le fichier `config/audrey-integration.ts`.

Exemple d'utilisation du `MicroserviceConnector` :

```tsx
import { MICROSERVICE_CONFIG } from '../config/audrey-integration';
import { MicroserviceConnector } from '../services/MicroserviceConnector';

async function authenticateUser(email, password) {
  const response = await MicroserviceConnector.post(
    MICROSERVICE_CONFIG.security.authEndpoint,
    { email, password }
  );
  return response.data;
}
```

## Tests

Les tests sont organisés en trois catégories :

- Tests unitaires : Tests des composants individuels
- Tests d'intégration : Tests des interactions entre les composants
- Tests end-to-end : Tests des parcours utilisateur complets

Exemple d'exécution des tests :

```bash
# Exécuter tous les tests
npm run test

# Exécuter les tests d'Audrey-V1
npm run test:audrey
```

## Optimisation des performances

Des fonctions d'optimisation des performances sont disponibles dans le fichier `utils/performance-optimizations.ts`.

Exemple d'utilisation des fonctions d'optimisation :

```tsx
import { memoize, debounce } from '../utils/performance-optimizations';

// Memoization
const expensiveCalculation = memoize((a, b) => {
  console.log('Calculating...');
  return a + b;
});

// Debounce
const handleSearch = debounce((query) => {
  console.log('Searching for:', query);
  // Effectuer la recherche
}, 300);
```

## Déploiement

Le déploiement est documenté dans le fichier `docs/DEPLOYMENT.md`.

## Ressources supplémentaires

- [Documentation technique complète](./AUDREY-INTEGRATION.md)
- [Guide de déploiement](./DEPLOYMENT.md)
- [Guide de contribution](./CONTRIBUTING.md)
