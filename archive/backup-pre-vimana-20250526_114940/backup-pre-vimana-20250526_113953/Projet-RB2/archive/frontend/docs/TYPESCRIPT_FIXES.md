# Guide de résolution des erreurs TypeScript

Ce document décrit les problèmes TypeScript rencontrés dans le projet et les solutions appliquées.

## Problèmes rencontrés et solutions

### 1. Erreurs d'importation

**Problème** : Importations multiples avec le même nom et chemins incorrects.

**Solution** :
- Utiliser des imports nommés spécifiques au lieu d'importer le même nom plusieurs fois
- Corriger les chemins relatifs (utiliser `../../` au lieu de `../` quand nécessaire)
- Supprimer l'extension `.ts` ou `.tsx` dans les imports

### 2. Erreurs de références manquantes

**Problème** : Références à des objets ou classes qui n'existent pas ou ne sont pas accessibles.

**Solution** :
- Implémenter des versions temporaires des fonctionnalités manquantes
- Corriger les types des paramètres dans les appels de fonction (par exemple, `logError` attend une chaîne, pas un objet Error)

### 3. Erreurs d'itération

**Problème** : L'itération sur `formData.entries()` avec `for...of` cause des erreurs de compilation.

**Solution** :
- Utiliser `Array.from(formData.entries()).forEach()` au lieu de `for...of`
- Ajouter `downlevelIteration: true` dans le tsconfig.json

### 4. Erreurs de bibliothèques externes (Zod)

**Problème** : Les identifiants privés dans Zod ne sont pas compatibles avec la cible ECMAScript.

**Solution** :
- Utiliser l'option `--skipLibCheck` pour ignorer les vérifications de type dans les bibliothèques externes
- Mettre à jour la cible à ES2022 dans tsconfig.json

## Configuration TypeScript recommandée

```json
{
  "compilerOptions": {
    "target": "ES2022",
    "downlevelIteration": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "jsx": "react-jsx",
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true
  }
}
```

## Commande de compilation pour les fichiers sécurisés

Pour vérifier uniquement les fichiers liés à la sécurité, utilisez la commande suivante :

```bash
npm run type-check:secure
```

ou manuellement :

```bash
npx tsc --noEmit --skipLibCheck --jsx react --esModuleInterop src/components/booking/SecureBookingForm.tsx src/pages/partner/OnboardingPage.tsx src/tests/accessibility/axe-setup.ts
```

## Problèmes restants

Il reste des erreurs TypeScript dans d'autres fichiers du projet qui nécessiteront des corrections similaires. Ces erreurs incluent principalement :

1. Problèmes de syntaxe JSX
2. Erreurs d'importation
3. Problèmes de destructuration
4. Erreurs de fermeture de balises

Pour résoudre ces problèmes, appliquez les mêmes techniques que celles décrites dans ce document.
