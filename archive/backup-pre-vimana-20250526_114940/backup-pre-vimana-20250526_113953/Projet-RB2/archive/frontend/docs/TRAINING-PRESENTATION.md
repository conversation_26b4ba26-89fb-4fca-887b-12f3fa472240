# Présentation de l'intégration de Front-Audrey-V1-Main-main

## Introduction

### Objectif de la présentation

- Présenter l'intégration de Front-Audrey-V1-Main-main dans le projet principal
- Expliquer l'architecture et les choix techniques
- <PERSON><PERSON> comment utiliser les composants et les fonctionnalités
- Répondre aux questions de l'équipe de développement

### Plan de la présentation

1. Contexte et objectifs
2. Architecture de l'intégration
3. Composants et pages
4. Styles et thèmes
5. Intégration avec le Backend-NestJS
6. Intégration avec les microservices
7. Tests et qualité
8. Déploiement
9. Maintenance et évolution
10. Démonstration
11. Questions et réponses

## 1. Contexte et objectifs

### Contexte

- Front-Audrey-V1-Main-main est une version du frontend développée par Audrey
- Cette version contient des fonctionnalités et des composants qui doivent être intégrés au projet principal
- L'intégration doit être réalisée de manière modulaire et maintenable

### Objectifs

- Intégrer toutes les fonctionnalités et tous les composants de Front-Audrey-V1-Main-main
- Préserver le comportement et l'apparence d'origine
- Assurer la compatibilité avec le projet principal
- Faciliter la maintenance et l'évolution future

## 2. Architecture de l'intégration

### Structure des dossiers

```
frontend/
├── src/
│   ├── components/
│   │   └── randbefrontend/       # Composants migrés d'Audrey-V1
│   │       ├── atoms/            # Composants atomiques
│   │       ├── molecules/        # Composants moléculaires
│   │       ├── organisms/        # Composants organismes
│   │       ├── templates/        # Templates
│   │       └── ui/               # Composants UI génériques
│   ├── pages/
│   │   └── randbefrontend/       # Pages migrées d'Audrey-V1
│   ├── styles/
│   │   ├── audrey-styles.css     # Styles spécifiques à Audrey-V1
│   │   └── audrey-integration.css # Adaptations pour l'intégration
│   ├── config/
│   │   └── audrey-integration.ts # Configuration pour l'intégration
│   └── routes/
│       ├── audreyRoutes.tsx      # Routes d'Audrey-V1
│       └── combinedRoutes.tsx    # Combinaison des routes
└── scripts/
    └── audrey-migration.js       # Script de migration
```

### Stratégie d'intégration

- **Isolation** : Les composants sont isolés dans un sous-répertoire dédié
- **Préfixage** : Les classes CSS sont préfixées pour éviter les collisions
- **Adaptation** : Les imports sont adaptés pour fonctionner dans la nouvelle structure
- **Lazy loading** : Les pages sont chargées en lazy loading pour optimiser les performances

## 3. Composants et pages

### Composants atomiques

- Composants de base : Button, Input, Icon, etc.
- Exemple d'utilisation :

```tsx
import Button from '../components/randbefrontend/atoms/Button/Button';

function MyComponent() {
  return (
    <Button onClick={() => console.log('Clicked!')} variant="primary">
      Click me
    </Button>
  );
}
```

### Composants moléculaires

- Combinaisons d'atomes : SearchBar, Card, FormField, etc.
- Exemple d'utilisation :

```tsx
import RetreatCard from '../components/randbefrontend/molecules/RetreatCard/RetreatCard';

function MyComponent() {
  const retreat = {
    id: '1',
    title: 'Retreat Title',
    location: 'Retreat Location',
    price: 100,
    rating: 4.5,
    image: 'retreat-image.jpg',
  };

  return <RetreatCard retreat={retreat} />;
}
```

### Composants organismes

- Combinaisons de molécules : NavBar, Footer, FilterBar, etc.
- Exemple d'utilisation :

```tsx
import NavBarClient from '../components/randbefrontend/organisms/NavBarClient/NavBarClient';

function MyComponent() {
  return <NavBarClient />;
}
```

### Templates

- Structures de page : MainLayout, AuthLayout, etc.
- Exemple d'utilisation :

```tsx
import MainLayout from '../components/randbefrontend/templates/MainLayout/MainLayout';

function MyComponent() {
  return (
    <MainLayout>
      <h1>Page Content</h1>
      <p>This is the content of the page.</p>
    </MainLayout>
  );
}
```

### Pages

- Vues complètes : HomePage, ClientHomePage, etc.
- Exemple d'utilisation :

```tsx
import { Routes, Route } from 'react-router-dom';
import HomePage from '../pages/randbefrontend/HomePage';

function App() {
  return (
    <Routes>
      <Route path="/" element={<HomePage />} />
      {/* Autres routes */}
    </Routes>
  );
}
```

## 4. Styles et thèmes

### Système de préfixage

- Les classes CSS sont préfixées avec `audrey-v1-` pour éviter les collisions
- Exemple :

```css
/* Avant */
.container {
  max-width: 1200px;
}

/* Après */
.audrey-v1-container {
  max-width: 1200px;
}
```

### Intégration avec MUI

- Les styles sont adaptés pour fonctionner avec Material-UI
- Exemple :

```css
/* Adaptation pour les boutons MUI */
.MuiButton-root.audrey-v1-btn {
  text-transform: none;
}

.MuiButton-root.audrey-v1-btn-primary {
  background-color: var(--audrey-primary);
  color: white;
}
```

## 5. Intégration avec le Backend-NestJS

### Services API

- Les services API sont configurés dans `config/audrey-integration.ts`
- Exemple d'utilisation :

```tsx
import { API_CONFIG } from '../config/audrey-integration';

async function fetchRetreats() {
  const response = await fetch(API_CONFIG.endpoints.retreats);
  const data = await response.json();
  return data;
}
```

### Authentification

- L'authentification est intégrée avec le système d'authentification du projet principal
- Exemple d'utilisation :

```tsx
import { useAuth } from '../hooks/useAuth';

function MyComponent() {
  const { isAuthenticated, user, login, logout } = useAuth();

  return (
    <div>
      {isAuthenticated ? (
        <button onClick={logout}>Logout</button>
      ) : (
        <button onClick={login}>Login</button>
      )}
    </div>
  );
}
```

## 6. Intégration avec les microservices

### MicroserviceConnector

- Le `MicroserviceConnector` est configuré dans `config/audrey-integration.ts`
- Exemple d'utilisation :

```tsx
import { MICROSERVICE_CONFIG } from '../config/audrey-integration';
import { MicroserviceConnector } from '../services/MicroserviceConnector';

async function authenticateUser(email, password) {
  const response = await MicroserviceConnector.post(
    MICROSERVICE_CONFIG.security.authEndpoint,
    { email, password }
  );
  return response.data;
}
```

### Microservices intégrés

- Security
- Financial-Management
- Social-Platform-Video
- Education
- Agent IA

## 7. Tests et qualité

### Tests unitaires

- Tests des composants individuels
- Exemple :

```tsx
import { render, screen } from '@testing-library/react';
import Button from '../components/randbefrontend/atoms/Button/Button';

test('Button renders correctly', () => {
  render(<Button>Click me</Button>);
  expect(screen.getByText('Click me')).toBeInTheDocument();
});
```

### Tests d'intégration

- Tests des interactions entre les composants
- Exemple :

```tsx
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import NavBarClient from '../components/randbefrontend/organisms/NavBarClient/NavBarClient';

test('NavBarClient renders correctly', () => {
  render(
    <BrowserRouter>
      <NavBarClient />
    </BrowserRouter>
  );
  expect(screen.getByRole('navigation')).toBeInTheDocument();
});
```

### Tests end-to-end

- Tests des parcours utilisateur complets
- Exemple :

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import App from '../App';

test('User can navigate to the home page', () => {
  render(
    <BrowserRouter>
      <App />
    </BrowserRouter>
  );
  fireEvent.click(screen.getByText('Home'));
  expect(screen.getByText('Welcome to Retreat And Be')).toBeInTheDocument();
});
```

## 8. Déploiement

### Environnements

- Développement
- Staging
- Production

### Processus de déploiement

1. Build de l'application
2. Déploiement sur AWS S3
3. Invalidation du cache CloudFront
4. Vérification du déploiement

### Scripts de déploiement

- `npm run build:dev` : Build pour le développement
- `npm run build:staging` : Build pour le staging
- `npm run build:prod` : Build pour la production
- `npm run deploy:staging` : Déploiement sur l'environnement de staging
- `npm run deploy:prod` : Déploiement sur l'environnement de production

## 9. Maintenance et évolution

### Processus de maintenance

- Mise à jour régulière des dépendances
- Correction des bugs
- Amélioration des performances

### Processus d'évolution

- Ajout de nouvelles fonctionnalités
- Amélioration des fonctionnalités existantes
- Refactoring du code

### Documentation

- [Documentation technique](./AUDREY-INTEGRATION.md)
- [Guide de déploiement](./DEPLOYMENT.md)
- [Guide de formation](./TRAINING.md)
- [Guide de contribution](./CONTRIBUTING.md)

## 10. Démonstration

### Démonstration des fonctionnalités

- Page d'accueil
- Recherche de retraites
- Authentification
- Réservation
- Gestion du compte

### Démonstration des composants

- Composants atomiques
- Composants moléculaires
- Composants organismes
- Templates
- Pages

## 11. Questions et réponses

### Questions fréquentes

- Comment ajouter un nouveau composant ?
- Comment modifier un composant existant ?
- Comment ajouter une nouvelle page ?
- Comment intégrer avec un nouveau microservice ?
- Comment déployer l'application ?

### Ressources supplémentaires

- [Documentation technique](./AUDREY-INTEGRATION.md)
- [Guide de déploiement](./DEPLOYMENT.md)
- [Guide de formation](./TRAINING.md)
- [Guide de contribution](./CONTRIBUTING.md)
- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro/)
