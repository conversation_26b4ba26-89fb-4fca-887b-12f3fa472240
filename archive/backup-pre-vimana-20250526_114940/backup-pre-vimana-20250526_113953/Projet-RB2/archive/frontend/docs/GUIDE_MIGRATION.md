# Guide de Migration vers les Nouveaux Composants Sécurisés

Ce document fournit des instructions pour migrer les composants existants vers les nouvelles versions sécurisées.

## 1. Migration des Formulaires

### Avant

```tsx
import React, { useState } from 'react';

function OldForm() {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  
  const validateForm = () => {
    const newErrors = {};
    if (!formData.email) newErrors.email = 'Email requis';
    if (!formData.password) newErrors.password = 'Mot de passe requis';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      // Soumettre le formulaire
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label>Email</label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
        />
        {errors.email && <div className="error">{errors.email}</div>}
      </div>
      
      <div>
        <label>Mot de passe</label>
        <input
          type="password"
          value={formData.password}
          onChange={(e) => setFormData({ ...formData, password: e.target.value })}
        />
        {errors.password && <div className="error">{errors.password}</div>}
      </div>
      
      <button type="submit">Se connecter</button>
    </form>
  );
}
```

### Après

```tsx
import { z } from 'zod';
import { ValidatedForm } from '../components/form/ValidatedForm';

// Définir un schéma de validation
const loginSchema = z.object({
  email: z.string().email({ message: 'Email invalide' }),
  password: z.string().min(8, { message: 'Mot de passe trop court' })
});

function NewForm() {
  const handleSubmit = async (data) => {
    // Les données sont déjà validées et sanitizées
    // Soumettre le formulaire
  };
  
  return (
    <ValidatedForm
      schema={loginSchema}
      onSubmit={handleSubmit}
      loggingContext="LoginForm"
    >
      {({ values, setValue, getFieldError, isTouched, isSubmitting }) => (
        <>
          <div className="form-group">
            <label>Email</label>
            <input
              type="email"
              value={values.email || ''}
              onChange={(e) => setValue('email', e.target.value)}
            />
            {getFieldError('email') && isTouched('email') && (
              <div className="error">{getFieldError('email')}</div>
            )}
          </div>
          
          <div className="form-group">
            <label>Mot de passe</label>
            <input
              type="password"
              value={values.password || ''}
              onChange={(e) => setValue('password', e.target.value)}
            />
            {getFieldError('password') && isTouched('password') && (
              <div className="error">{getFieldError('password')}</div>
            )}
          </div>
          
          <button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Connexion...' : 'Se connecter'}
          </button>
        </>
      )}
    </ValidatedForm>
  );
}
```

### Avantages

- Validation avec Zod (plus puissante et typée)
- Sanitization automatique des entrées
- Journalisation des erreurs de validation
- Réduction du code boilerplate
- Typage fort avec TypeScript

## 2. Migration vers useOffline Amélioré

### Avant

```tsx
import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { OfflineManager } from '../services/offline/OfflineManager';

function OldOfflineComponent({ userId }) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const isOffline = useSelector(state => state.network.isOffline);
  
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const offlineManager = OfflineManager.getInstance();
        const cachedData = await offlineManager.getCachedData(`user_${userId}`);
        setData(cachedData);
        setError(null);
      } catch (err) {
        console.error('Erreur lors du chargement des données:', err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [userId]);
  
  const saveData = async (newData) => {
    try {
      const offlineManager = OfflineManager.getInstance();
      await offlineManager.cacheData(`user_${userId}`, newData);
      setData(newData);
      return true;
    } catch (err) {
      console.error('Erreur lors de la sauvegarde des données:', err);
      setError(err);
      return false;
    }
  };
  
  return {
    data,
    loading,
    error,
    saveData
  };
}
```

### Après

```tsx
import { userProfileSchema } from '../schemas/offlineDataSchema';
import { useOffline } from '../hooks/useOffline';

function NewOfflineComponent({ userId }) {
  const { 
    data,
    loading,
    error,
    saveData,
    syncData,
    clearData,
    refresh
  } = useOffline(`user_${userId}`, {
    syncUrl: '/api/sync/user',
    syncOnLoad: true,
    autoSync: true,
    syncInterval: 5 * 60 * 1000, // 5 minutes
    customValidation: userProfileSchema,
    loggingContext: `UserProfileComponent:${userId}`
  });
  
  // Toutes les fonctionnalités sont déjà intégrées
  
  return {
    data,
    loading,
    error,
    saveData,
    syncData,
    clearData,
    refresh
  };
}
```

### Avantages

- Validation et sanitization intégrées
- Synchronisation automatique
- Journalisation sécurisée
- Gestion des erreurs améliorée
- API plus riche (syncData, clearData, refresh)
- Typage fort avec les schémas Zod

## 3. Migration vers le Système de Logging

### Avant

```tsx
function oldFunction() {
  try {
    // Opération risquée
    console.log('Opération réussie');
  } catch (err) {
    console.error('Erreur:', err);
  }
}

function oldUserOperation(userData) {
  console.log('Opération utilisateur:', userData);
  // Les données sensibles sont exposées dans les logs
}
```

### Après

```tsx
import { logger } from '../services/logger/logger';

function newFunction() {
  try {
    // Opération risquée
    logger.info('Opération réussie');
  } catch (err) {
    logger.error('Erreur lors de l\'opération', err);
  }
}

function newUserOperation(userData) {
  logger.info('Opération utilisateur', {
    userId: userData.id,
    operation: 'update',
    // Les données sensibles sont automatiquement masquées
    // password, email, etc. seront redactés
  });
}
```

### Avantages

- Masquage automatique des données sensibles
- Niveaux de logging configurables
- Format standardisé
- Intégration avec Sentry en production
- Contexte utilisateur pour meilleure traçabilité

## 4. Création et Utilisation des Schémas de Validation

### Définir un nouveau schéma

```tsx
// src/schemas/paymentSchema.ts
import { z } from 'zod';

export const paymentMethodSchema = z.object({
  type: z.enum(['credit_card', 'paypal', 'bank_transfer']),
  
  // Champs conditionnels selon le type
  cardNumber: z.string()
    .regex(/^\d{16}$/, { message: 'Numéro de carte invalide' })
    .optional()
    .refine(val => val === undefined || /^\d{16}$/.test(val), {
      message: 'Le numéro de carte doit contenir 16 chiffres'
    }),
  
  expiryDate: z.string()
    .regex(/^\d{2}\/\d{2}$/, { message: 'Format invalide (MM/YY)' })
    .optional(),
  
  cvv: z.string()
    .regex(/^\d{3,4}$/, { message: 'CVV invalide' })
    .optional(),
  
  email: z.string()
    .email({ message: 'Email PayPal invalide' })
    .optional(),
  
  accountNumber: z.string()
    .optional(),
});

export const paymentSchema = z.object({
  amount: z.number().positive({ message: 'Le montant doit être positif' }),
  currency: z.enum(['EUR', 'USD', 'GBP']),
  description: z.string().max(200, { message: 'Description trop longue' }).optional(),
  paymentMethod: paymentMethodSchema,
});

// Type dérivé du schéma
export type Payment = z.infer<typeof paymentSchema>;
```

### Utiliser le schéma

```tsx
import { paymentSchema, type Payment } from '../schemas/paymentSchema';

// Validation directe
const validatePayment = (data: unknown): { valid: boolean; data?: Payment; errors?: string[] } => {
  const result = paymentSchema.safeParse(data);
  
  if (result.success) {
    return { valid: true, data: result.data };
  } else {
    return { 
      valid: false, 
      errors: result.error.errors.map(e => e.message) 
    };
  }
};

// Utilisation avec ValidatedForm
function PaymentForm() {
  return (
    <ValidatedForm
      schema={paymentSchema}
      onSubmit={processPayment}
    >
      {/* Formulaire */}
    </ValidatedForm>
  );
}

// Utilisation comme type TypeScript
function processPayment(payment: Payment) {
  // TypeScript connaît la structure exacte de payment
}
```

## 5. Liste de Contrôle pour la Migration

Pour chaque composant à migrer :

### Formulaires

- [ ] Créer un schéma Zod pour la validation des données
- [ ] Remplacer le formulaire par `ValidatedForm`
- [ ] Remplacer la gestion d'état par les propriétés fournies
- [ ] Remplacer la validation manuelle par le schéma Zod
- [ ] Ajouter le contexte de journalisation

### Stockage Hors Ligne

- [ ] Créer ou réutiliser un schéma pour les données
- [ ] Remplacer l'implémentation par `useOffline`
- [ ] Configurer les options (syncUrl, autoSync, etc.)
- [ ] Ajouter le contexte de journalisation

### Journalisation

- [ ] Remplacer tous les `console.log` par `logger.info`
- [ ] Remplacer tous les `console.error` par `logger.error`
- [ ] Remplacer tous les `console.warn` par `logger.warn`
- [ ] Ajouter un contexte aux messages de log
- [ ] Masquer les données sensibles

## 6. Ressources

- [Documentation Zod](https://github.com/colinhacks/zod)
- [Documentation DOMPurify](https://github.com/cure53/DOMPurify)
- [Guide Sentry](https://docs.sentry.io/platforms/javascript/react/)

Pour plus d'informations sur l'utilisation de ces nouveaux composants, consultez les documents :

- `frontend/docs/VALIDATION_SANITIZATION.md`
- `frontend/docs/SECURITE_AMELIORATIONS.md`
- `frontend/docs/NOUVELLES_FONCTIONNALITES.md` 