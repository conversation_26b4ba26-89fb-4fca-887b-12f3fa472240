# Guide de contribution pour Front-Audrey-V1-Main-main

Ce document décrit les processus et les bonnes pratiques pour contribuer au développement de Front-Audrey-V1-Main-main intégré au projet principal.

## Prérequis

- Node.js 16.x ou supérieur
- npm 8.x ou supérieur
- Connaissance de React, TypeScript et des bonnes pratiques de développement frontend

## Configuration de l'environnement de développement

1. <PERSON><PERSON><PERSON> le dépôt :

```bash
git clone https://github.com/retreat-and-be/projet-rb2.git
cd projet-rb2
```

2. Installer les dépendances :

```bash
cd frontend
npm install
```

3. Lancer l'application en mode développement :

```bash
npm run dev
```

L'application sera accessible à l'adresse [http://localhost:5173](http://localhost:5173).

## Structure du projet

L'intégration de Front-Audrey-V1-Main-main suit la structure suivante :

```
frontend/
├── src/
│   ├── components/
│   │   └── randbefrontend/       # Composants migrés d'Audrey-V1
│   │       ├── atoms/            # Composants atomiques
│   │       ├── molecules/        # Composants moléculaires
│   │       ├── organisms/        # Composants organismes
│   │       ├── templates/        # Templates
│   │       └── ui/               # Composants UI génériques
│   ├── pages/
│   │   └── randbefrontend/       # Pages migrées d'Audrey-V1
│   ├── styles/
│   │   ├── audrey-styles.css     # Styles spécifiques à Audrey-V1
│   │   └── audrey-integration.css # Adaptations pour l'intégration
│   ├── config/
│   │   └── audrey-integration.ts # Configuration pour l'intégration
│   └── routes/
│       ├── audreyRoutes.tsx      # Routes d'Audrey-V1
│       └── combinedRoutes.tsx    # Combinaison des routes
└── scripts/
    └── audrey-migration.js       # Script de migration
```

## Workflow de développement

### 1. Création d'une branche

Pour chaque nouvelle fonctionnalité ou correction de bug, créer une nouvelle branche à partir de la branche `develop` :

```bash
git checkout develop
git pull
git checkout -b feature/nom-de-la-fonctionnalite
```

ou

```bash
git checkout develop
git pull
git checkout -b fix/nom-du-bug
```

### 2. Développement

Développer la fonctionnalité ou corriger le bug en suivant les bonnes pratiques décrites ci-dessous.

### 3. Tests

Écrire des tests pour la fonctionnalité ou la correction de bug :

```bash
npm run test
```

### 4. Commit

Commiter les changements avec un message descriptif :

```bash
git add .
git commit -m "feat: description de la fonctionnalité"
```

ou

```bash
git add .
git commit -m "fix: description de la correction de bug"
```

### 5. Push

Pousser les changements vers le dépôt distant :

```bash
git push origin feature/nom-de-la-fonctionnalite
```

ou

```bash
git push origin fix/nom-du-bug
```

### 6. Pull Request

Créer une Pull Request vers la branche `develop` sur GitHub.

## Bonnes pratiques

### Conventions de nommage

- **Fichiers** : Utiliser le PascalCase pour les composants React (ex: `Button.tsx`) et le kebab-case pour les autres fichiers (ex: `api-service.ts`).
- **Composants** : Utiliser le PascalCase pour les noms de composants (ex: `Button`).
- **Variables et fonctions** : Utiliser le camelCase pour les noms de variables et de fonctions (ex: `handleClick`).
- **Constantes** : Utiliser le SNAKE_CASE pour les constantes (ex: `API_URL`).
- **Classes CSS** : Utiliser le kebab-case pour les classes CSS (ex: `button-primary`). Préfixer les classes avec `audrey-v1-` pour éviter les collisions (ex: `audrey-v1-button-primary`).

### Structure des composants

Utiliser la structure suivante pour les composants React :

```tsx
import React from 'react';
import './ComponentName.css';

interface ComponentNameProps {
  prop1: string;
  prop2: number;
  onEvent: () => void;
}

const ComponentName: React.FC<ComponentNameProps> = ({ prop1, prop2, onEvent }) => {
  // Logique du composant

  return (
    <div className="audrey-v1-component-name">
      {/* JSX du composant */}
    </div>
  );
};

export default ComponentName;
```

### Tests

Écrire des tests pour chaque composant et fonctionnalité. Utiliser Jest et React Testing Library.

```tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ComponentName from './ComponentName';

describe('ComponentName', () => {
  test('renders correctly', () => {
    render(<ComponentName prop1="value1" prop2={2} onEvent={() => {}} />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  test('calls onEvent when clicked', () => {
    const onEvent = jest.fn();
    render(<ComponentName prop1="value1" prop2={2} onEvent={onEvent} />);
    fireEvent.click(screen.getByRole('button'));
    expect(onEvent).toHaveBeenCalled();
  });
});
```

### Documentation

Documenter chaque composant et fonction avec des commentaires JSDoc :

```tsx
/**
 * Composant Button
 * 
 * @param {string} variant - Variante du bouton (primary, secondary, outline)
 * @param {React.ReactNode} children - Contenu du bouton
 * @param {() => void} onClick - Fonction appelée lors du clic sur le bouton
 * @returns {JSX.Element} Composant Button
 */
const Button: React.FC<ButtonProps> = ({ variant, children, onClick }) => {
  // ...
};
```

### Styles

Utiliser CSS Modules ou styled-components pour les styles. Préfixer les classes CSS avec `audrey-v1-` pour éviter les collisions.

```css
/* Button.module.css */
.audrey-v1-button {
  /* Styles communs */
}

.audrey-v1-button-primary {
  /* Styles spécifiques à la variante primary */
}

.audrey-v1-button-secondary {
  /* Styles spécifiques à la variante secondary */
}

.audrey-v1-button-outline {
  /* Styles spécifiques à la variante outline */
}
```

### Performance

Optimiser les performances des composants en utilisant les fonctions d'optimisation disponibles dans `utils/performance-optimizations.ts`.

```tsx
import { memoize, debounce } from '../../utils/performance-optimizations';

// Memoization
const expensiveCalculation = memoize((a, b) => {
  console.log('Calculating...');
  return a + b;
});

// Debounce
const handleSearch = debounce((query) => {
  console.log('Searching for:', query);
  // Effectuer la recherche
}, 300);
```

## Intégration avec le Backend-NestJS

Pour intégrer un composant avec le Backend-NestJS, utiliser les services API configurés dans `config/audrey-integration.ts`.

```tsx
import { API_CONFIG } from '../../config/audrey-integration';

async function fetchRetreats() {
  const response = await fetch(API_CONFIG.endpoints.retreats);
  const data = await response.json();
  return data;
}
```

## Intégration avec les microservices

Pour intégrer un composant avec les microservices, utiliser le `MicroserviceConnector` configuré dans `config/audrey-integration.ts`.

```tsx
import { MICROSERVICE_CONFIG } from '../../config/audrey-integration';
import { MicroserviceConnector } from '../../services/MicroserviceConnector';

async function authenticateUser(email, password) {
  const response = await MicroserviceConnector.post(
    MICROSERVICE_CONFIG.security.authEndpoint,
    { email, password }
  );
  return response.data;
}
```

## Ressources

- [Documentation technique complète](./AUDREY-INTEGRATION.md)
- [Guide de déploiement](./DEPLOYMENT.md)
- [Guide de formation](./TRAINING.md)
- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro/)
