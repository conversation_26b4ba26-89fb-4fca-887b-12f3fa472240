# Guide d'onboarding pour les nouveaux développeurs

Bienvenue dans l'équipe de développement de Retreat And Be ! Ce guide vous aidera à vous familiariser avec le projet et à commencer à contribuer rapidement.

## 1. Présentation du projet

Retreat And Be est une plateforme complète dédiée aux retraites bien-être et au développement personnel. Elle offre une expérience utilisateur moderne et intuitive, intégrant des fonctionnalités avancées comme la réalité virtuelle, les NFTs, et un système de communauté robuste.

Le projet utilise une architecture de microservices, avec un frontend React/TypeScript et un backend NestJS.

## 2. Configuration de l'environnement de développement

### Prérequis

- Node.js 16.x ou supérieur
- npm 8.x ou supérieur
- Git
- Docker et Docker Compose (pour les microservices)
- IDE recommandé : Visual Studio Code avec les extensions suivantes :
  - ESLint
  - Prettier
  - TypeScript Vue Plugin (Volar)
  - Docker
  - Jest

### Installation

1. <PERSON><PERSON><PERSON> le dép<PERSON>t :

```bash
git clone https://github.com/retreat-and-be/projet-rb2.git
cd projet-rb2
```

2. Installer les dépendances du frontend :

```bash
cd frontend
npm install
```

3. Installer les dépendances du backend :

```bash
cd ../Backend-NestJS
npm install
```

4. Lancer les services avec Docker Compose :

```bash
cd ..
docker-compose up -d
```

5. Lancer le frontend en mode développement :

```bash
cd frontend
npm run dev
```

6. Lancer le backend en mode développement :

```bash
cd ../Backend-NestJS
npm run start:dev
```

## 3. Architecture du projet

### Frontend

Le frontend est développé avec React, TypeScript et Vite. Il utilise Material UI pour les composants d'interface utilisateur et React Router pour la navigation.

L'architecture du frontend suit le modèle Atomic Design, avec les composants organisés en atomes, molécules, organismes et templates.

```
frontend/
├── src/
│   ├── components/         # Composants réutilisables
│   │   ├── auth/          # Composants d'authentification
│   │   ├── common/        # Composants communs
│   │   ├── security/      # Composants de sécurité (2FA, etc.)
│   │   ├── layout/        # Layouts et templates
│   │   └── randbefrontend/ # Composants migrés d'Audrey-V1
│   │       ├── atoms/     # Composants atomiques
│   │       ├── molecules/ # Composants moléculaires
│   │       ├── organisms/ # Composants organismes
│   │       ├── templates/ # Templates
│   │       └── ui/        # Composants UI génériques
│   ├── contexts/          # Contextes React
│   ├── services/          # Services
│   ├── hooks/             # Hooks personnalisés
│   ├── utils/             # Utilitaires
│   ├── pages/             # Pages de l'application
│   ├── styles/            # Styles globaux
│   ├── config/            # Configuration
│   ├── routes/            # Routes de l'application
│   └── App.tsx            # Composant racine
```

### Backend

Le backend est développé avec NestJS, un framework Node.js inspiré d'Angular. Il utilise TypeORM pour l'accès à la base de données et Passport pour l'authentification.

```
Backend-NestJS/
├── src/
│   ├── auth/              # Module d'authentification
│   ├── users/             # Module de gestion des utilisateurs
│   ├── retreats/          # Module de gestion des retraites
│   ├── bookings/          # Module de gestion des réservations
│   ├── payments/          # Module de gestion des paiements
│   ├── common/            # Modules communs
│   └── main.ts            # Point d'entrée de l'application
```

### Microservices

Le projet utilise plusieurs microservices pour des fonctionnalités spécifiques :

- **Security** : Authentification et autorisation
- **Financial-Management** : Gestion des paiements et des abonnements
- **Social-Platform-Video** : Fonctionnalités sociales et vidéo
- **Education** : Contenu éducatif et cours
- **Agent IA** : Recommandations et assistance IA

## 4. Workflow de développement

### Branches

- `main` : Branche principale, contient le code en production
- `develop` : Branche de développement, contient le code en cours de développement
- `feature/*` : Branches de fonctionnalités
- `fix/*` : Branches de corrections de bugs
- `release/*` : Branches de préparation des releases

### Processus de développement

1. Créer une branche à partir de `develop` :

```bash
git checkout develop
git pull
git checkout -b feature/nom-de-la-fonctionnalite
```

2. Développer la fonctionnalité ou corriger le bug

3. Exécuter les tests :

```bash
npm run test
```

4. Commiter les changements :

```bash
git add .
git commit -m "feat: description de la fonctionnalité"
```

5. Pousser les changements :

```bash
git push origin feature/nom-de-la-fonctionnalite
```

6. Créer une Pull Request vers `develop`

7. Après validation, la Pull Request est fusionnée dans `develop`

8. Périodiquement, `develop` est fusionné dans `main` pour une release

## 5. Tests

Le projet utilise Jest pour les tests unitaires et d'intégration, et Cypress pour les tests end-to-end.

### Tests unitaires et d'intégration

```bash
# Exécuter tous les tests
npm run test

# Exécuter les tests en mode watch
npm run test:watch

# Exécuter les tests avec couverture
npm run test:coverage

# Exécuter les tests spécifiques à l'intégration d'Audrey-V1
npm run test:audrey
```

### Tests end-to-end

```bash
# Exécuter les tests end-to-end
npm run test:e2e
```

## 6. Déploiement

Le projet utilise Docker et Docker Compose pour le déploiement.

### Déploiement en staging

```bash
npm run deploy:staging
```

### Déploiement en production

```bash
npm run deploy:prod
```

## 7. Documentation

Pour plus d'informations, consultez les documents suivants :

- [Guide de démarrage rapide](./QUICKSTART.md)
- [Documentation technique](./AUDREY-INTEGRATION.md)
- [Guide de déploiement](./DEPLOYMENT.md)
- [Guide de formation](./TRAINING.md)
- [Guide de contribution](./CONTRIBUTING.md)
- [Roadmap des futures fonctionnalités](./ROADMAP-FUTURE.md)

## 8. Ressources

- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [NestJS Documentation](https://docs.nestjs.com/)
- [Material UI Documentation](https://mui.com/material-ui/getting-started/overview/)
- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Cypress Documentation](https://docs.cypress.io/guides/overview/why-cypress)

## 9. Contact

Pour toute question ou suggestion, veuillez contacter :

- **Équipe de développement** : <EMAIL>
- **Lead Developer** : <EMAIL>
- **Product Owner** : <EMAIL>
