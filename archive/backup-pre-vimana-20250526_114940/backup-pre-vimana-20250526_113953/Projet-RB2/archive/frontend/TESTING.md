# Guide de test pour le projet

## Approche progressive

Nous avons identifié plusieurs problèmes avec la configuration de test actuelle, notamment des conflits entre les outils et les dépendances. Pour résoudre ce problème, nous recommandons une approche progressive des tests:

### Niveau 1: Tests de base (fonctionne actuellement)

```bash
npx jest src/simple-tests/basic.test.js
```

Ces tests sont simples et ne nécessitent pas de configuration particulière. Ils permettent de vérifier que Jest fonctionne correctement dans l'environnement.

### Niveau 2: Tests React simples (fonctionne actuellement)

```bash
npx jest src/simple-tests/simple-button.test.js
```

Ces tests utilisent React et Testing Library mais sans JSX, en utilisant `React.createElement()` à la place, ce qui évite les problèmes de transpilation. Ils sont utiles pour tester des composants simples.

### Niveau 3: Tests de services et hooks (fonctionne actuellement)

```bash
npx jest src/simple-tests/auth-hook.test.js
```

Ces tests permettent de tester des fonctions asynchrones et d'utiliser les mocks de Jest, ce qui est essentiel pour tester les services et les hooks. Ils montrent comment simuler les appels API et gérer l'état asynchrone.

### Niveau 4: Tests de composants avec dépendances externes (fonctionne actuellement)

```bash
npx jest src/simple-tests/task-list.test.js
```

Ces tests permettent de tester des composants qui utilisent des bibliothèques externes comme Redux, en mockant ces dépendances. Ils montrent comment tester l'interaction entre les composants et le state global.

### Niveau 5: Tests de services API avec des helpers

```bash
npx jest src/tests/services/userService.test.ts
```

Ces tests utilisent des helpers réutilisables pour mocker Axios et localStorage, ce qui rend les tests plus maintenables et facilite l'écriture de nouveaux tests.

### Niveau 6: Tests de composants Redux avec des helpers

```bash
npx jest src/tests/components/UserProfile.test.tsx
```

Ces tests utilisent des helpers pour mocker Redux et faciliter les tests des composants qui utilisent des hooks Redux comme `useSelector` et `useDispatch`.

### Niveau 7: Tests de composants RTK Query

```bash
npx jest src/tests/components/ProductList.test.tsx
```

Ces tests montrent comment tester des composants qui utilisent RTK Query pour les opérations d'API, en mockant les endpoints et les mutations.

## Exemples de tests fonctionnels

### 1. Test de base (basic.test.js)

```javascript
// Test de base sans TypeScript ou React
describe('Basic test', () => {
  test('adds 1 + 2 to equal 3', () => {
    expect(1 + 2).toBe(3);
  });

  test('true is truthy', () => {
    expect(true).toBeTruthy();
  });

  test('false is falsy', () => {
    expect(false).toBeFalsy();
  });
});
```

### 2. Test de hook avec mocks (auth-hook.test.js)

```javascript
// Mock du service d'authentification
const mockAuthService = {
  login: jest.fn(),
  getUserData: jest.fn(),
  logout: jest.fn()
};

// Mock du module authService
jest.mock('../services/authService', () => mockAuthService);

test('login success', async () => {
  // Préparer le mock
  const mockUser = { id: '1', name: 'Test User' };
  mockAuthService.login.mockResolvedValueOnce({ 
    user: mockUser, 
    token: 'test-token' 
  });

  // Render le hook
  const { result } = renderHook(() => useAuth());
  
  // Exécuter la méthode login avec un objet credentials
  await act(async () => 
    await result.current.login({ email: '<EMAIL>', password: 'password' })
  );
  
  // Vérifications
  expect(mockAuthService.login).toHaveBeenCalledWith({ 
    email: '<EMAIL>', 
    password: 'password' 
  });
  expect(result.current.isAuthenticated).toBe(true);
});
```

### 3. Test de composant avec Redux (task-list.test.js)

```javascript
// Mock de Redux
const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  useDispatch: () => mockDispatch,
  useSelector: jest.fn(selector => {
    // Simuler le state Redux
    const state = {
      tasks: {
        items: [
          { id: 1, text: 'Tâche 1', completed: false },
          { id: 2, text: 'Tâche 2', completed: true }
        ],
        loading: false,
        error: null
      }
    };
    return selector(state);
  })
}));

test('permet de cocher/décocher une tâche', () => {
  render(React.createElement(TaskList));
  
  // Simuler un clic sur la case à cocher
  fireEvent.click(screen.getByTestId('checkbox-1'));
  
  // Vérifier que l'action a été dispatchée
  expect(mockDispatch).toHaveBeenCalledWith({
    type: 'TOGGLE_TASK',
    payload: 1
  });
});
```

### 4. Test de service avec helper (userService.test.ts)

```typescript
import userService from '../../services/userService';
import { mockAxiosCreate, mockLocalStorage, createAxiosError } from '../utils/test-helpers';

describe('userService', () => {
  let mockAxios;
  let localStorageMock;
  
  beforeEach(() => {
    // Configurer les mocks avant chaque test
    mockAxios = mockAxiosCreate();
    localStorageMock = mockLocalStorage();
    jest.clearAllMocks();
  });
  
  it('should fetch user profile successfully', async () => {
    // Arrange
    const mockUser = { id: '1', name: 'Test User' };
    mockAxios.get.mockResolvedValueOnce({ data: mockUser });
    localStorageMock.getItem.mockReturnValueOnce('test-token');
    
    // Act
    const result = await userService.getProfile();
    
    // Assert
    expect(mockAxios.get).toHaveBeenCalledWith('/users/profile');
    expect(mockAxios.defaults.headers.common['Authorization']).toBe('Bearer test-token');
    expect(result).toEqual(mockUser);
  });
});
```

### 5. Test de RTK Query avec helper (ProductList.test.tsx)

```typescript
import { renderWithRtkQuery, mockEndpoint, mockMutation } from '../utils/rtk-query-helpers';

it('deletes a product when delete button is clicked', async () => {
  // Mocker les endpoints RTK Query
  const mockProducts = [
    { id: '1', name: 'Product 1', price: 19.99 },
    { id: '2', name: 'Product 2', price: 29.99 }
  ];
  mockEndpoint(mockApi, 'getProducts', mockProducts);
  
  // Mocker la mutation de suppression
  const { trigger: deleteTrigger } = mockMutation(mockApi, 'deleteProduct', { success: true });
  
  // Rendre le composant
  renderWithRtkQuery(<ProductList />, mockApi);
  
  // Cliquer sur le bouton de suppression
  fireEvent.click(screen.getByTestId('delete-1'));
  
  // Vérifier que la mutation a été appelée avec le bon ID
  expect(deleteTrigger).toHaveBeenCalledWith('1');
  
  // Vérifier que refetch a été appelé
  await waitFor(() => {
    expect(mockApi.endpoints.getProducts.useQuery().refetch).toHaveBeenCalled();
  });
});
```

## Helpers de test

Nous avons créé plusieurs helpers de test réutilisables pour faciliter l'écriture de tests :

### 1. Helpers génériques (test-helpers.ts)

```typescript
// Mocker Axios
export function mockAxiosCreate() {
  const mockInstance = {
    post: jest.fn(),
    get: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    patch: jest.fn(),
    // ...autres propriétés
  };
  
  (axios.create).mockReturnValue(mockInstance);
  
  return mockInstance;
}

// Mocker localStorage
export function mockLocalStorage() {
  let store = {};
  
  const localStorageMock = {
    getItem: jest.fn((key) => store[key] || null),
    setItem: jest.fn((key, value) => {
      store[key] = value;
    }),
    // ...autres méthodes
  };
  
  Object.defineProperty(window, 'localStorage', { value: localStorageMock });
  
  return localStorageMock;
}
```

### 2. Helpers pour RTK Query (rtk-query-helpers.ts)

```typescript
// Créer un wrapper de test avec Redux pour RTK Query
export const createTestWrapper = (api, reducers = {}) => {
  const store = configureStore({
    reducer: {
      [api.reducerPath]: api.reducer,
      ...reducers
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(api.middleware)
  });

  const Wrapper = ({ children }) => (
    <Provider store={store}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </Provider>
  );

  return { store, Wrapper };
};

// Mocker un endpoint RTK Query
export const mockEndpoint = (api, endpoint, response, options = {}) => {
  const { status = 'fulfilled', error = null } = options;
  
  const spy = jest.spyOn(api.endpoints[endpoint], 'useQuery');
  
  spy.mockReturnValue({
    data: status === 'fulfilled' ? response : undefined,
    isLoading: status === 'loading',
    isSuccess: status === 'fulfilled',
    isError: status === 'error',
    // ...autres propriétés
  });
  
  return spy;
};
```

## Recommandations

1. **Démarrer petit**: Commencer par des tests unitaires simples qui ne dépendent pas de bibliothèques externes.

2. **Utiliser des helpers**: Créer et utiliser des helpers de test pour factoriser le code de test et le rendre plus maintenable.

3. **Mocks explicites**: Au lieu d'utiliser des mocks globaux qui peuvent causer des conflits, préférer les mocks explicites dans chaque fichier de test:

```javascript
// Exemple de mock explicite
jest.mock('tailwind-merge', () => ({
  twMerge: (...classes) => classes.filter(Boolean).join(' ')
}));
```

4. **Éviter JSX dans les tests simples**: Utiliser `React.createElement()` au lieu de JSX pour éviter les problèmes de transpilation:

```javascript
// Au lieu de <Button onClick={handleClick}>Click me</Button>
React.createElement(Button, { onClick: handleClick }, 'Click me')
```

5. **Isoler les tests**: Garder les tests aussi indépendants que possible pour éviter les effets de bord.

6. **Tests progressifs**: Suivre l'approche progressive en commençant par les tests les plus simples et en avançant vers des tests plus complexes.

7. **Désactiver TypeScript si nécessaire**: Utiliser `// @ts-nocheck` en haut des fichiers de test problématiques pour éviter les erreurs TypeScript avec des bibliothèques tierces.

## Structure de test recommandée

```
frontend/
├── __tests__/                  # Tests globaux (niveau 5+)
├── src/
│   ├── components/
│   │   ├── __tests__/          # Tests de composants (niveau 4-5)
│   │   │   └── Button.test.tsx
│   ├── hooks/
│   │   ├── __tests__/          # Tests de hooks (niveau 3-4)
│   │   │   └── useAuth.test.ts
│   ├── services/
│   │   ├── __tests__/          # Tests de services (niveau 3)
│   │   │   └── authService.test.ts
│   ├── tests/
│   │   ├── utils/              # Helpers de test réutilisables
│   │   │   ├── test-helpers.ts
│   │   │   └── rtk-query-helpers.ts
│   │   ├── services/           # Tests de services avec helpers
│   │   │   └── userService.test.ts
│   │   ├── components/         # Tests de composants avec helpers
│   │   │   ├── UserProfile.test.tsx
│   │   │   └── ProductList.test.tsx
│   ├── simple-tests/           # Tests simples (niveau 1-2)
│   │   ├── basic.test.js          # Tests JS pure
│   │   ├── simple-button.test.js  # Tests React basiques
│   │   ├── auth-hook.test.js      # Tests hooks/services
│   │   └── task-list.test.js      # Tests avec bibliothèques externes
```

## Résolution des problèmes courants

1. **Erreur "Cannot use import statement outside a module"**
   Solution: Utiliser `require()` au lieu de `import` ou configurer correctement Babel.

2. **Erreur "ReferenceError: module is not defined"**
   Solution: S'assurer que les fichiers de configuration sont en format CommonJS (`.cjs`).

3. **Erreur "Property 'toBeInTheDocument' does not exist"**
   Solution: Ajouter `require('@testing-library/jest-dom')` au début du test.

4. **Erreur "Cannot find module 'tailwind-merge'"**
   Solution: Mocker correctement la bibliothèque ou utiliser des classes simples au lieu de bibliothèques externes.

5. **Erreur "Unexpected token '<'"**
   Solution: Utiliser `React.createElement()` au lieu de JSX, ou configurer Babel pour transpiler JSX.

6. **Erreur "Expected 1 arguments, but got 0" avec Axios**
   Solution: Mocker correctement Axios ou utiliser `@ts-nocheck` pour ignorer les erreurs TypeScript.

7. **Erreur avec les types RTK Query**
   Solution: Utiliser les helpers de test RTK Query ou `@ts-nocheck` pour désactiver la vérification de types. 