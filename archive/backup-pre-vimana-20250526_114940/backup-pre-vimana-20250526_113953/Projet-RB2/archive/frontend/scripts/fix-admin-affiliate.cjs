const fs = require('fs');
const path = require('path');

function processFile(filePath) {
  console.log(`Processing file: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let originalContent = content;
  let corrections = 0;

  // Correction pour les importations avec "from" incorrects
  content = content.replace(/};(\s*)from(\s*)['"](.*?)['"];/g, (match, space1, space2, importPath) => {
    corrections++;
    return `} from${space2}'${importPath}';`;
  });

  // Correction des balises incorrectes (span, button, p, etc.)
  const tagPairs = {
    'UserDialog': 'option',
    'span': 'div',
    'p': 'div',
    'button': 'div',
    'h2': 'h2',
    'h3': 'h3',
    'h4': 'h4',
    'option': 'option',
    'select': 'select'
  };
  
  for (const [wrongTag, correctTag] of Object.entries(tagPairs)) {
    // Remplacer les balises fermantes incorrectes
    content = content.replace(new RegExp(`</${wrongTag}>`, 'g'), `</${correctTag}>`);
    corrections += (content.match(new RegExp(`</${wrongTag}>`, 'g')) || []).length;
  }

  // Correction des balises auto-fermantes incorrectes
  content = content.replace(/<([A-Za-z0-9.]+)([^>]*?)\/>/g, '<$1$2 />');
  corrections += (content.match(/<([A-Za-z0-9.]+)([^>]*?)\/>/g) || []).length;

  // Correction des balises incorrectes et incomplètes - cas spécifiques
  content = content.replace(/<\/UserDialog>/g, '</div>');
  corrections += (content.match(/<\/UserDialog>/g) || []).length;

  // Correction spécifique pour AdminGuard.tsx
  content = content.replace(/useAuth;(\(\))/g, 'useAuth()');
  corrections += (content.match(/useAuth;(\(\))/g) || []).length;

  // Correction des opérateurs de comparaison incorrects
  content = content.replace(/(\w+)\s*>=\s*(\w+)/g, '$1 >= $2');
  corrections += (content.match(/(\w+)\s*>=\s*(\w+)/g) || []).length;
  
  content = content.replace(/(\w+)\s*>\s*(\w+)/g, '$1 > $2');
  corrections += (content.match(/(\w+)\s*>\s*(\w+)/g) || []).length;

  // Correction des erreurs avec les interpolations JSX
  content = content.replace(/{\s*`rank\s*\${([^}]+)}\s*`}/g, '{`rank ${$1}`}');
  corrections += (content.match(/{\s*`rank\s*\${([^}]+)}\s*`}/g) || []).length;

  // Correction du problème rank.toLowerCase()
  content = content.replace(/{\s*`rank\s*\${entry\.rank\.toLowerCase\(\)`}/g, '{`rank ${entry.rank.toLowerCase()}`}');
  corrections += (content.match(/{\s*`rank\s*\${entry\.rank\.toLowerCase\(\)`}/g) || []).length;

  // Correction des balises fermantes manquantes
  const openingTags = ['div', 'span', 'h2', 'h3', 'h4', 'p', 'button', 'select', 'option'];
  
  openingTags.forEach(tag => {
    const regex = new RegExp(`<${tag}([^>]*)>([^<]*?)$`, 'gm');
    const matches = content.match(regex);
    
    if (matches) {
      matches.forEach(match => {
        const newMatch = match + `</${tag}>`;
        content = content.replace(match, newMatch);
        corrections++;
      });
    }
  });

  // Correction des balises génériques mal fermées
  content = content.replace(/<([a-zA-Z0-9]+)([^>]*)>([^<]*?)<\/[a-zA-Z0-9]+>/g, (match, opening, attrs, content) => {
    return `<${opening}${attrs}>${content}</${opening}>`;
  });
  corrections += (content.match(/<([a-zA-Z0-9]+)([^>]*)>([^<]*?)<\/[a-zA-Z0-9]+>/g) || []).length;

  // Correction pour les attributs de className incomplets
  content = content.replace(/<div className=`}/g, '<div className={`');
  corrections += (content.match(/<div className=`}/g) || []).length;
  
  content = content.replace(/className=`([^`]*)`/g, 'className={`$1`}');
  corrections += (content.match(/className=`([^`]*)`/g) || []).length;

  // Correction des erreurs de parenthèses pour les expressions conditionnelles
  content = content.replace(/{([^}]*)\s+&&\s+\(/g, '{$1 && (');
  corrections += (content.match(/{([^}]*)\s+&&\s+\(/g) || []).length;

  // Correction des fichiers incomplerts
  if (!content.endsWith('};') && !content.endsWith('}')) {
    content += '\n};';
    corrections++;
  }

  if (corrections > 0 && content !== originalContent) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed ${corrections} issues in ${filePath}`);
    return corrections;
  } else {
    console.log(`No changes made to ${filePath}`);
    return 0;
  }
}

function walkDir(dir, callback) {
  fs.readdirSync(dir).forEach(f => {
    const dirPath = path.join(dir, f);
    const isDirectory = fs.statSync(dirPath).isDirectory();
    if (isDirectory) {
      walkDir(dirPath, callback);
    } else {
      callback(path.join(dir, f));
    }
  });
}

function fixComponents() {
  const rootDir = process.cwd();
  const adminPath = path.join(rootDir, 'src', 'components', 'Admin');
  const affiliatePath = path.join(rootDir, 'src', 'components', 'affiliate');
  const additionalFiles = [
    path.join(rootDir, 'src', 'components', 'AdminGuard.tsx')
  ];
  
  let totalFixedFiles = 0;
  let totalCorrections = 0;

  // Vérifier et corriger les fichiers du répertoire Admin
  if (fs.existsSync(adminPath)) {
    console.log(`Processing Admin components...`);
    
    fs.readdirSync(adminPath)
      .filter(file => file.endsWith('.tsx') || file.endsWith('.jsx'))
      .forEach(file => {
        const filePath = path.join(adminPath, file);
        const corrections = processFile(filePath);
        if (corrections > 0) {
          totalFixedFiles++;
          totalCorrections += corrections;
        }
      });
  } else {
    console.log(`Directory not found: ${adminPath}`);
  }

  // Vérifier et corriger les fichiers du répertoire affiliate
  if (fs.existsSync(affiliatePath)) {
    console.log(`Processing affiliate components...`);
    
    walkDir(affiliatePath, (filePath) => {
      if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
        const corrections = processFile(filePath);
        if (corrections > 0) {
          totalFixedFiles++;
          totalCorrections += corrections;
        }
      }
    });
  } else {
    console.log(`Directory not found: ${affiliatePath}`);
  }

  // Vérifier et corriger les fichiers additionnels
  console.log(`Processing additional files...`);
  
  additionalFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const corrections = processFile(filePath);
      if (corrections > 0) {
        totalFixedFiles++;
        totalCorrections += corrections;
      }
    } else {
      console.log(`File not found: ${filePath}`);
    }
  });

  console.log(`Fixed ${totalCorrections} issues in ${totalFixedFiles} files`);
}

fixComponents(); 