import * as fs from 'fs';
import * as path from 'path';
import * as ../src/services/seo.service from '../src/services/seo.service';

// Configuration des routes;
const routes = [;
  {
    path: '/',
    lastmod: new Date().toISOString(),
    changefreq: 'daily',
    priority: 1.0
},
  {
    path: '/events',
    lastmod: new Date().toISOString(),
    changefreq: 'daily',
    priority: 0.9
},
  {
    path: '/communities',
    lastmod: new Date().toISOString(),
    changefreq: 'daily',
    priority: 0.9
},
  {
    path: '/profile',
    lastmod: new Date().toISOString(),
    changefreq: 'weekly',
    priority: 0.8
},
  {
    path: '/about',
    lastmod: new Date().toISOString(),
    changefreq: 'monthly',
    priority: 0.7
},
  {
    path: '/contact',
    lastmod: new Date().toISOString(),
    changefreq: 'monthly',
    priority: 0.7
}
];

// Générer le sitemap;
const sitemap = generateSitemap(routes);
const robotsTxt = generateRobotsTxt();

// Chemin vers le dossier public;
const publicDir = path.join(__dirname, '../public');

// Écrire le sitemap.xml;
fs.writeFileSync(path.join(publicDir, 'sitemap.xml'), sitemap);
console.log('✅ sitemap.xml generated successfully');

// Écrire le robots.txt;
fs.writeFileSync(path.join(publicDir, 'robots.txt'), robotsTxt);
console.log('✅ robots.txt generated successfully');
