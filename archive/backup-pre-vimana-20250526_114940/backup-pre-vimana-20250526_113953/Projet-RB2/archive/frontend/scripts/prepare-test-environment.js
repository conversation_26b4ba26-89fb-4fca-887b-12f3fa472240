#!/usr/bin/env node

/**
 * Script pour préparer l'environnement de test
 * Ce script prépare l'environnement de test en :
 * 1. Créant les répertoires nécessaires s'ils n'existent pas
 * 2. Créant des fichiers de test simples pour vérifier que les tests fonctionnent
 * 3. Vérifiant que les dépendances de test sont installées
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const chalk = require('chalk') || { green: (s) => s, red: (s) => s, yellow: (s) => s, blue: (s) => s };

// Répertoires à créer
const directories = [
  'src/simple-tests',
  'src/tests/utils',
  'src/tests/services',
  'src/tests/components',
  'src/tests/accessibility',
  'src/tests/performance',
  'cypress/e2e',
  'cypress/fixtures',
  'cypress/support'
];

// Fichiers à créer
const files = {
  'src/simple-tests/basic.test.js': `
// Test de base sans TypeScript ou React
describe('Basic test', () => {
  test('adds 1 + 2 to equal 3', () => {
    expect(1 + 2).toBe(3);
  });

  test('true is truthy', () => {
    expect(true).toBeTruthy();
  });

  test('false is falsy', () => {
    expect(false).toBeFalsy();
  });
});
`,
  'cypress/support/commands.js': `
// ***********************************************
// Commandes Cypress personnalisées
// ***********************************************

// Commande pour se connecter
Cypress.Commands.add('login', (email, password) => {
  cy.get('[data-testid="login-button"]').click();
  cy.get('[data-testid="email-input"]').type(email);
  cy.get('[data-testid="password-input"]').type(password);
  cy.get('[data-testid="login-submit"]').click();
});

// Commande pour sélectionner par attribut data-testid
Cypress.Commands.add('getByTestId', (testId) => {
  return cy.get(\`[data-testid="\${testId}"]\`);
});
`,
  'cypress/fixtures/users.json': `
[
  {
    "id": "1",
    "name": "Test User",
    "email": "<EMAIL>",
    "password": "password123"
  },
  {
    "id": "2",
    "name": "Admin User",
    "email": "<EMAIL>",
    "password": "admin123",
    "isAdmin": true
  }
]
`,
  'jest.simple.config.js': `
// Configuration Jest simplifiée pour les tests de niveau 1-2
module.exports = {
  testEnvironment: 'node',
  testMatch: [
    '<rootDir>/src/simple-tests/**/*.test.js'
  ],
  transform: {
    '^.+\\.jsx?$': 'babel-jest'
  },
  moduleFileExtensions: ['js', 'json', 'node'],
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/'
  ]
};
`
};

// Vérifier si les dépendances de test sont installées
const requiredDependencies = [
  'jest',
  'jest-environment-jsdom',
  '@testing-library/react',
  '@testing-library/jest-dom',
  'cypress',
  'jest-axe'
];

// Créer les répertoires
console.log(chalk.blue('🔧 Création des répertoires de test...'));
directories.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir);
  if (!fs.existsSync(fullPath)) {
    try {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(chalk.green(`✓ Répertoire créé : ${dir}`));
    } catch (error) {
      console.error(chalk.red(`✗ Erreur lors de la création du répertoire ${dir}: ${error.message}`));
    }
  } else {
    console.log(chalk.yellow(`ℹ Le répertoire existe déjà: ${dir}`));
  }
});

// Créer les fichiers
console.log(chalk.blue('\n🔧 Création des fichiers de test...'));
Object.entries(files).forEach(([file, content]) => {
  const fullPath = path.join(process.cwd(), file);
  if (!fs.existsSync(fullPath)) {
    try {
      // Créer le répertoire parent si nécessaire
      const dir = path.dirname(fullPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      fs.writeFileSync(fullPath, content.trim());
      console.log(chalk.green(`✓ Fichier créé : ${file}`));
    } catch (error) {
      console.error(chalk.red(`✗ Erreur lors de la création du fichier ${file}: ${error.message}`));
    }
  } else {
    console.log(chalk.yellow(`ℹ Le fichier existe déjà: ${file}`));
  }
});

// Vérifier les dépendances
console.log(chalk.blue('\n🔍 Vérification des dépendances de test...'));
const packageJsonPath = path.join(process.cwd(), 'package.json');
try {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const allDependencies = {
    ...(packageJson.dependencies || {}),
    ...(packageJson.devDependencies || {})
  };
  
  const missingDependencies = requiredDependencies.filter(dep => !allDependencies[dep]);
  
  if (missingDependencies.length === 0) {
    console.log(chalk.green('✓ Toutes les dépendances requises sont installées'));
  } else {
    console.log(chalk.yellow(`⚠ Dépendances manquantes: ${missingDependencies.join(', ')}`));
    console.log(chalk.yellow(`ℹ Vous pouvez les installer avec la commande :`));
    console.log(chalk.blue(`npm install --save-dev ${missingDependencies.join(' ')}`));
  }
} catch (error) {
  console.error(chalk.red(`✗ Erreur lors de la lecture de package.json: ${error.message}`));
}

// Exécuter un test simple pour vérifier l'installation
console.log(chalk.blue('\n🧪 Exécution d\'un test simple pour vérifier l\'installation...'));
exec('npx jest --version', (error, stdout, stderr) => {
  if (error) {
    console.error(chalk.red(`✗ Erreur lors de la vérification de Jest: ${error.message}`));
    console.log(chalk.yellow('ℹ Assurez-vous que Jest est correctement installé avec `npm install --save-dev jest`'));
    return;
  }
  
  console.log(chalk.green(`✓ Jest version ${stdout.trim()} est installé`));
  
  // Créer un test temporaire et l'exécuter
  const tempTestPath = path.join(process.cwd(), 'temp-test.js');
  fs.writeFileSync(tempTestPath, `
    test('temporary test', () => {
      expect(1).toBe(1);
    });
  `);
  
  console.log(chalk.blue('🧪 Exécution du test temporaire...'));
  exec('npx jest temp-test.js', (error, stdout, stderr) => {
    // Supprimer le fichier de test temporaire
    fs.unlinkSync(tempTestPath);
    
    if (error) {
      console.error(chalk.red(`✗ Erreur lors de l'exécution du test: ${error.message}`));
      console.log(stderr);
      return;
    }
    
    console.log(chalk.green('✓ Test exécuté avec succès'));
    console.log(chalk.green('\n✅ Environnement de test prêt!'));
    console.log(chalk.blue('ℹ Vous pouvez maintenant exécuter les tests avec les commandes suivantes:'));
    console.log(chalk.blue('   - npm test              # Exécuter tous les tests Jest'));
    console.log(chalk.blue('   - npm run test:simple   # Exécuter les tests de niveau 1-2'));
    console.log(chalk.blue('   - npm run test:a11y     # Exécuter les tests d\'accessibilité'));
    console.log(chalk.blue('   - npm run test:perf     # Exécuter les tests de performance'));
    console.log(chalk.blue('   - npm run test:e2e      # Exécuter les tests E2E avec Cypress'));
    console.log(chalk.blue('   - npm run test:all      # Exécuter tous les types de tests'));
  });
}); 