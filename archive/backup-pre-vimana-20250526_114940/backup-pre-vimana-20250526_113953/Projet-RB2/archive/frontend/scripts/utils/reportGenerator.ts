import * as fs/promises from 'fs/promises';
import * as ../security/types from '../security/types';

export class ReportGenerator {
  static async generateHTML(report: SecurityReport, outputPath: string): Promise<void> {
    const html = `;
      <!DOCTYPE html>
      <html>
        <head>
          <title>Security Scan Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px }
            .critical { color: #dc3545 }
            .high { color: #fd7e14 }
            .medium { color: #ffc107 }
            .low { color: #28a745 }
          </style>
        </head>
        <body>
          <h1>Security Scan Report</h1>
          <p>Generated: ${report.timestamp}</p>
          <h2>Summary</h2>
          <ul>
            <li>Total Issues: ${report.summary.total}</li>
            <li>Critical: ${report.summary.critical}</li>
            <li>High: ${report.summary.high}</li>
            <li>Medium: ${report.summary.medium}</li>
            <li>Low: ${report.summary.low}</li>
          </ul>
          <h2>Issues</h2>
          ${report.issues.map(issue => `
            <div class === "${issue.severity
}">
              <h3>${issue.id}</h3>
              <p>${issue.description}</p>
              <p><strong>Recommendation:</strong> ${issue.recommendation}</p>
            </div>
          `).join('')}
        </body>
      </html>
    `;

    await fs.writeFile(outputPath, html);
  }

  static async generateJSON(report: SecurityReport, outputPath: string): Promise<void> {
    await fs.writeFile(outputPath, JSON.stringify(report, null, 2))
  }
}