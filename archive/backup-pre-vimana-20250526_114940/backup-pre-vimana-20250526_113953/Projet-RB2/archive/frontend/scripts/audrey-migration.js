/**
 * Script de migration pour l'intégration de Front-Audrey-V1-Main-main
 *
 * Ce script facilite la migration des composants, styles et fonctionnalités
 * de Front-Audrey-V1-Main-main vers le projet principal.
 */

import fs from 'fs';
import path from 'path';
import chalk from 'chalk';
import { glob } from 'glob';
import { fileURLToPath } from 'url';

// Obtenir le chemin du répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Chemins des répertoires
const AUDREY_ROOT = path.resolve(__dirname, '../../Front-Audrey-V1-Main-main');
const FRONTEND_ROOT = path.resolve(__dirname, '..');
const COMPONENTS_SRC = path.join(AUDREY_ROOT, 'src/components');
const COMPONENTS_DEST = path.join(FRONTEND_ROOT, 'src/components/randbefrontend');
const PAGES_SRC = path.join(AUDREY_ROOT, 'src/pages');
const PAGES_DEST = path.join(FRONTEND_ROOT, 'src/pages/randbefrontend');
const STYLES_SRC = path.join(AUDREY_ROOT, 'src/styles');
const STYLES_DEST = path.join(FRONTEND_ROOT, 'src/styles');

// Fonction pour créer un répertoire s'il n'existe pas
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(chalk.green(`Répertoire créé: ${dir}`));
  }
}

// Fonction pour copier un fichier
function copyFile(src, dest) {
  try {
    const content = fs.readFileSync(src, 'utf8');
    fs.writeFileSync(dest, content);
    console.log(chalk.green(`Fichier copié: ${dest}`));
    return true;
  } catch (error) {
    console.error(chalk.red(`Erreur lors de la copie du fichier ${src} vers ${dest}:`), error);
    return false;
  }
}

// Fonction pour adapter les imports dans un fichier
function adaptImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');

    // Adapter les imports relatifs
    content = content.replace(
      /from ['"]\.\.\/\.\.\/components\/(.*)['"]/g,
      'from \'@/components/randbefrontend/$1\''
    );

    content = content.replace(
      /from ['"]\.\.\/components\/(.*)['"]/g,
      'from \'@/components/randbefrontend/$1\''
    );

    content = content.replace(
      /from ['"]\.\.\/\.\.\/pages\/(.*)['"]/g,
      'from \'@/pages/randbefrontend/$1\''
    );

    content = content.replace(
      /from ['"]\.\.\/pages\/(.*)['"]/g,
      'from \'@/pages/randbefrontend/$1\''
    );

    // Adapter les imports de styles
    content = content.replace(
      /from ['"]\.\.\/\.\.\/styles\/(.*)['"]/g,
      'from \'@/styles/$1\''
    );

    content = content.replace(
      /from ['"]\.\.\/styles\/(.*)['"]/g,
      'from \'@/styles/$1\''
    );

    // Écrire le contenu modifié
    fs.writeFileSync(filePath, content);
    console.log(chalk.blue(`Imports adaptés dans: ${filePath}`));
    return true;
  } catch (error) {
    console.error(chalk.red(`Erreur lors de l'adaptation des imports dans ${filePath}:`), error);
    return false;
  }
}

// Fonction pour préfixer les classes CSS dans un fichier
function prefixCssClasses(filePath, prefix) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');

    // Préfixer les classes CSS dans les attributs className
    content = content.replace(
      /className="([^"]*)"/g,
      (match, classNames) => {
        const prefixedClassNames = classNames
          .split(' ')
          .map(className => className.trim() ? `${prefix}-${className}` : '')
          .join(' ');
        return `className="${prefixedClassNames}"`;
      }
    );

    // Écrire le contenu modifié
    fs.writeFileSync(filePath, content);
    console.log(chalk.blue(`Classes CSS préfixées dans: ${filePath}`));
    return true;
  } catch (error) {
    console.error(chalk.red(`Erreur lors du préfixage des classes CSS dans ${filePath}:`), error);
    return false;
  }
}

// Fonction pour migrer les composants
async function migrateComponents() {
  console.log(chalk.yellow('Migration des composants...'));

  // Créer les répertoires de destination
  ensureDirectoryExists(COMPONENTS_DEST);
  ensureDirectoryExists(path.join(COMPONENTS_DEST, 'atoms'));
  ensureDirectoryExists(path.join(COMPONENTS_DEST, 'molecules'));
  ensureDirectoryExists(path.join(COMPONENTS_DEST, 'organisms'));
  ensureDirectoryExists(path.join(COMPONENTS_DEST, 'templates'));
  ensureDirectoryExists(path.join(COMPONENTS_DEST, 'ui'));

  // Copier les composants atomiques
  const atomsFiles = await glob(path.join(COMPONENTS_SRC, 'atoms/**/*.tsx'));
  for (const file of atomsFiles) {
    const relativePath = path.relative(path.join(COMPONENTS_SRC, 'atoms'), file);
    const destPath = path.join(COMPONENTS_DEST, 'atoms', relativePath);
    ensureDirectoryExists(path.dirname(destPath));
    copyFile(file, destPath);
    adaptImports(destPath);
  }

  // Copier les composants moléculaires
  const moleculesFiles = await glob(path.join(COMPONENTS_SRC, 'molecules/**/*.tsx'));
  for (const file of moleculesFiles) {
    const relativePath = path.relative(path.join(COMPONENTS_SRC, 'molecules'), file);
    const destPath = path.join(COMPONENTS_DEST, 'molecules', relativePath);
    ensureDirectoryExists(path.dirname(destPath));
    copyFile(file, destPath);
    adaptImports(destPath);
  }

  // Copier les composants organismes
  const organismsFiles = await glob(path.join(COMPONENTS_SRC, 'organisms/**/*.tsx'));
  for (const file of organismsFiles) {
    const relativePath = path.relative(path.join(COMPONENTS_SRC, 'organisms'), file);
    const destPath = path.join(COMPONENTS_DEST, 'organisms', relativePath);
    ensureDirectoryExists(path.dirname(destPath));
    copyFile(file, destPath);
    adaptImports(destPath);
  }

  // Copier les templates
  const templatesFiles = await glob(path.join(COMPONENTS_SRC, 'templates/**/*.tsx'));
  for (const file of templatesFiles) {
    const relativePath = path.relative(path.join(COMPONENTS_SRC, 'templates'), file);
    const destPath = path.join(COMPONENTS_DEST, 'templates', relativePath);
    ensureDirectoryExists(path.dirname(destPath));
    copyFile(file, destPath);
    adaptImports(destPath);
  }

  // Copier les composants UI
  const uiFiles = await glob(path.join(COMPONENTS_SRC, 'ui/**/*.tsx'));
  for (const file of uiFiles) {
    const relativePath = path.relative(path.join(COMPONENTS_SRC, 'ui'), file);
    const destPath = path.join(COMPONENTS_DEST, 'ui', relativePath);
    ensureDirectoryExists(path.dirname(destPath));
    copyFile(file, destPath);
    adaptImports(destPath);
  }

  console.log(chalk.green('Migration des composants terminée.'));
}

// Fonction pour migrer les pages
async function migratePages() {
  console.log(chalk.yellow('Migration des pages...'));

  // Créer le répertoire de destination
  ensureDirectoryExists(PAGES_DEST);

  // Copier les pages
  const pagesFiles = await glob(path.join(PAGES_SRC, '*.tsx'));
  for (const file of pagesFiles) {
    const fileName = path.basename(file);
    const destPath = path.join(PAGES_DEST, fileName);
    copyFile(file, destPath);
    adaptImports(destPath);
  }

  console.log(chalk.green('Migration des pages terminée.'));
}

// Fonction pour migrer les styles
async function migrateStyles() {
  console.log(chalk.yellow('Migration des styles...'));

  // Créer le répertoire de destination
  ensureDirectoryExists(STYLES_DEST);

  // Copier les fichiers CSS
  const cssFiles = await glob(path.join(STYLES_SRC, '*.css'));
  for (const file of cssFiles) {
    const fileName = path.basename(file);
    const destPath = path.join(STYLES_DEST, `audrey-${fileName}`);
    copyFile(file, destPath);
  }

  console.log(chalk.green('Migration des styles terminée.'));
}

// Fonction principale
async function main() {
  console.log(chalk.blue('Début de la migration de Front-Audrey-V1-Main-main...'));

  // Vérifier que les répertoires source existent
  if (!fs.existsSync(AUDREY_ROOT)) {
    console.error(chalk.red(`Le répertoire source ${AUDREY_ROOT} n'existe pas.`));
    process.exit(1);
  }

  // Migrer les composants, pages et styles
  await migrateComponents();
  await migratePages();
  await migrateStyles();

  console.log(chalk.blue('Migration terminée avec succès.'));
}

// Exécuter la fonction principale
main().catch(error => {
  console.error(chalk.red('Erreur lors de la migration:'), error);
  process.exit(1);
});
