#!/bin/bash

echo "Applying simple and effective import fixes..."

# Trouver tous les fichiers TypeScript
TS_FILES=$(find src -type f \( -name "*.ts" -o -name "*.tsx" \))

# Fonction pour appliquer une correction à tous les fichiers
apply_fix() {
    local pattern="$1"
    local replacement="$2"
    
    for file in $TS_FILES; do
        sed -i '' "s/$pattern/$replacement/g" "$file"
    done
}

# Correction des guillemets non fermés correctement
apply_fix 'from "socket\.io-client'"'"';' 'from "socket.io-client";'

# Correction de l'import socket.io-client
apply_fix 'import \* as socket\.io-client from' 'import * as socketIo from'

# Correction des doubles guillemets à la fin
apply_fix 'from "\([^"]*\)""' 'from "\1"'

# Correction pour "import * as .. from ..;"
apply_fix 'import \* as \.\. from' 'import * as parent from'

# Correction des imports avec traits d'union
for file in $TS_FILES; do
    # Remplacer les guillemets déséquilibrés à la fin
    sed -i '' "s/\(\"\)\\{2,\\}/\"/g" "$file"
    
    # Corriger les imports problématiques courants
    sed -i '' 's/import \* as react-ga4 from/import * as ReactGA from/g' "$file"
    sed -i '' 's/import \* as react-i18next from/import { useTranslation } from/g' "$file"
    
    # Corriger les guillemets simples à la fin d'une chaîne
    sed -i '' "s/\"\([^\"]*\)'\";\$/\"\1\";/g" "$file"
done

echo "Processing imports with paths..."
for file in $TS_FILES; do
    # Utiliser un script Python pour des remplacements plus complexes
    python3 -c '
import re, sys

with open(sys.argv[1], "r") as f:
    content = f.read()

# Corriger les imports avec points dans le nom du module
content = re.sub(r"import \* as ([a-zA-Z0-9_-]+)\.([a-zA-Z0-9_-]+) from", 
                r"import * as \1\2 from", content)

# Corriger les imports avec slashes dans le nom du module
content = re.sub(r"import \* as ([a-zA-Z0-9_-]+)/([a-zA-Z0-9_-]+) from", 
                r"import * as \1\2 from", content)

with open(sys.argv[1], "w") as f:
    f.write(content)
' "$file"
done

echo "Import fixes completed!" 