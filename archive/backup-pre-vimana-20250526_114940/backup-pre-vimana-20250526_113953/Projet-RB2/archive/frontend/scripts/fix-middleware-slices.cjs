const fs = require('fs');
const path = require('path');

function processFile(filePath) {
  console.log(`Processing file: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let corrections = 0;

  if (filePath.includes('notificationMiddleware.ts')) {
    // Correction des erreurs spécifiques au fichier notificationMiddleware.ts
    content = content.replace(/\s+\);(\s*)\}(\s*)\};/g, '\n  });\n};\n');
    corrections += (content.match(/\s+\);(\s*)\}(\s*)\};/g) || []).length;
  }

  if (filePath.includes('validationMiddleware.ts')) {
    // Correction des erreurs spécifiques au fichier validationMiddleware.ts
    content = content.replace(/\s+\}\);/g, '\n    });\n  });');
    corrections += (content.match(/\s+\}\);/g) || []).length;
  }

  if (filePath.includes('messageSlice.ts')) {
    // Correction des erreurs spécifiques au fichier messageSlice.ts
    content = content.replace(/\},;/g, '},');
    corrections += (content.match(/\},;/g) || []).length;

    content = content.replace(/\s+\};(\s*)\};(\s*)\}\);/g, '\n  }\n});\n');
    corrections += (content.match(/\s+\};(\s*)\};(\s*)\}\);/g) || []).length;
  }

  if (corrections > 0) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed ${corrections} issues in ${filePath}`);
    return corrections;
  } else {
    console.log(`No issues found in ${filePath}`);
    return 0;
  }
}

function fixMiddlewareAndSlices() {
  const storeDir = path.join(process.cwd(), 'src', 'store');
  const middlewareDir = path.join(storeDir, 'middleware');
  const slicesDir = path.join(storeDir, 'slices');
  
  let totalFixedFiles = 0;
  let totalCorrections = 0;

  // Vérifier et corriger les fichiers middleware
  if (fs.existsSync(middlewareDir)) {
    const middlewareFiles = [
      'notificationMiddleware.ts',
      'validationMiddleware.ts'
    ];
    
    middlewareFiles.forEach(file => {
      const filePath = path.join(middlewareDir, file);
      if (fs.existsSync(filePath)) {
        const corrections = processFile(filePath);
        if (corrections > 0) {
          totalFixedFiles++;
          totalCorrections += corrections;
        }
      } else {
        console.log(`File not found: ${filePath}`);
      }
    });
  } else {
    console.log(`Directory not found: ${middlewareDir}`);
  }

  // Vérifier et corriger les fichiers slices
  if (fs.existsSync(slicesDir)) {
    const slicesFiles = [
      'messageSlice.ts'
    ];
    
    slicesFiles.forEach(file => {
      const filePath = path.join(slicesDir, file);
      if (fs.existsSync(filePath)) {
        const corrections = processFile(filePath);
        if (corrections > 0) {
          totalFixedFiles++;
          totalCorrections += corrections;
        }
      } else {
        console.log(`File not found: ${filePath}`);
      }
    });
  } else {
    console.log(`Directory not found: ${slicesDir}`);
  }

  console.log(`Fixed ${totalCorrections} issues in ${totalFixedFiles} files`);
}

fixMiddlewareAndSlices(); 