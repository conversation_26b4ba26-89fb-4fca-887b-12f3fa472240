#!/bin/bash

# Script to run all tests for the frontend

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting test suite for frontend...${NC}"

# Function to run tests and check results
run_test() {
  local test_name=$1
  local test_command=$2
  
  echo -e "\n${YELLOW}Running $test_name...${NC}"
  
  if eval $test_command; then
    echo -e "${GREEN}✓ $test_name passed${NC}"
    return 0
  else
    echo -e "${RED}✗ $test_name failed${NC}"
    return 1
  fi
}

# Create a temporary directory for test results
mkdir -p ./test-results

# Run unit tests for pages
run_test "Unit tests for pages" "npm test -- --testPathPattern=src/pages"

# Run unit tests for components
run_test "Unit tests for components" "npm test -- --testPathPattern=src/components"

# Run integration tests
run_test "Integration tests" "npm test -- --testPathPattern=src/tests/integration"

# Run accessibility tests
run_test "Accessibility tests" "npm test -- --testPathPattern=accessibility"

# Run SEO audit if the server is running
if curl -s http://localhost:3000 > /dev/null; then
  run_test "SEO audit" "node scripts/seo-audit.js --report=./test-results/seo-audit.json"
else
  echo -e "${YELLOW}Skipping SEO audit - development server not running${NC}"
  echo -e "${YELLOW}Start the server with 'npm start' and run 'node scripts/seo-audit.js' separately${NC}"
fi

# Generate coverage report
echo -e "\n${YELLOW}Generating test coverage report...${NC}"
npm test -- --coverage

# Check if any tests failed
if [ $? -eq 0 ]; then
  echo -e "\n${GREEN}All tests completed successfully!${NC}"
  exit 0
else
  echo -e "\n${RED}Some tests failed. Please check the output above for details.${NC}"
  exit 1
fi
