/**
 * Script pour exécuter les tests d'intégration de Front-Audrey-V1-Main-main
 */

import { spawn } from 'child_process';
import chalk from 'chalk';

// Fonction pour exécuter une commande
function runCommand(command, args) {
  return new Promise((resolve, reject) => {
    console.log(chalk.blue(`Exécution de la commande: ${command} ${args.join(' ')}`));

    const process = spawn(command, args, { stdio: 'inherit' });

    process.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`La commande a échoué avec le code ${code}`));
      }
    });

    process.on('error', (error) => {
      reject(error);
    });
  });
}

// Fonction principale
async function main() {
  try {
    console.log(chalk.green('=== Exécution des tests d\'intégration de Front-Audrey-V1-Main-main ==='));

    // Exécuter les tests de composants
    console.log(chalk.yellow('\n=== Tests des composants ==='));
    await runCommand('npx', ['jest', '--config=jest.config.js', 'src/tests/audrey-integration.test.tsx']);

    // Exécuter les tests d'intégration avec le Backend-NestJS
    console.log(chalk.yellow('\n=== Tests d\'intégration avec le Backend-NestJS ==='));
    await runCommand('npx', ['jest', '--config=jest.config.js', 'src/tests/audrey-backend-integration.test.tsx']);

    // Exécuter les tests d'intégration avec les microservices
    console.log(chalk.yellow('\n=== Tests d\'intégration avec les microservices ==='));
    await runCommand('npx', ['jest', '--config=jest.config.js', 'src/tests/audrey-microservices-integration.test.tsx']);

    console.log(chalk.green('\n=== Tous les tests ont été exécutés avec succès ==='));
  } catch (error) {
    console.error(chalk.red(`Erreur lors de l'exécution des tests: ${error.message}`));
    process.exit(1);
  }
}

// Exécuter la fonction principale
main();
