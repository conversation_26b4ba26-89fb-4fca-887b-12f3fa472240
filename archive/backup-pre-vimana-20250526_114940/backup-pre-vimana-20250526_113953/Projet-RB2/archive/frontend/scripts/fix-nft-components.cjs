const fs = require('fs');
const path = require('path');

function processFile(filePath) {
  console.log(`Processing file: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let corrections = 0;

  // Correction des imports avec from incorrects
  content = content.replace(/};(\s*)from(\s*)['"](.*?)['"];/g, (match, space1, space2, importPath) => {
    corrections++;
    return `} from${space2}'${importPath}';`;
  });

  // Correction des balises de fermeture incorrectes 
  const incorrectTags = ['button', 'span', 'NFTCard', 'Sparkles', 'Grid', 'div'];
  
  incorrectTags.forEach(tag => {
    content = content.replace(new RegExp(`</${tag}>`, 'g'), `</${tag === 'NFTCard' || tag === 'Sparkles' ? 'div' : tag}>`);
    const replacements = content.match(new RegExp(`</${tag}>`, 'g'));
    if (replacements) {
      corrections += replacements.length;
    }
  });

  // Corriger les balises "useX;()"
  content = content.replace(/use([A-Za-z0-9]+);(\(\))/g, 'use$1()');
  corrections += (content.match(/use([A-Za-z0-9]+);(\(\))/g) || []).length;

  // Corriger les chaînes avec backticks malformées
  content = content.replace(/`\}/g, '}');
  corrections += (content.match(/`\}/g) || []).length;

  // Correction des backticks dans les attributs variant
  content = content.replace(/variant=`\}([^"]+)"/g, 'variant="$1"');
  corrections += (content.match(/variant=`\}([^"]+)"/g) || []).length;

  // Correction des guillemets dans les chaînes de template literals
  content = content.replace(/\${([^}]+)"\s+([^`]+)`}/g, '${$1 $2}');
  corrections += (content.match(/\${([^}]+)"\s+([^`]+)`}/g) || []).length;

  // Correction des balises fermantes pour les composants Material UI
  const muiComponents = [
    'Typography', 'Box', 'Container', 'Paper', 'Grid', 'Tab', 'Tabs', 'TabPanel',
    'Dialog', 'DialogTitle', 'DialogContent', 'DialogActions', 'Card', 'CardContent',
    'CardMedia', 'Button', 'InputLabel', 'FormControl', 'MenuItem', 'CircularProgress'
  ];
  
  muiComponents.forEach(component => {
    content = content.replace(new RegExp(`</${component}>`, 'g'), `</${component}>`);
    const replacements = content.match(new RegExp(`</${component}>`, 'g'));
    if (replacements) {
      corrections += replacements.length;
    }
  });

  // Correction des composants spécifiques
  const specificComponents = [
    'Trophy', 'Heart', 'img', 'label', 'Crown', 'Timer', 'section'
  ];
  
  specificComponents.forEach(component => {
    content = content.replace(new RegExp(`</${component}>`, 'g'), `</${component}>`);
    const replacements = content.match(new RegExp(`</${component}>`, 'g'));
    if (replacements) {
      corrections += replacements.length;
    }
  });

  // Correction de <Typography variant="h6">${someVar}"
  content = content.replace(/<Typography variant="([^"]+)">(.*?)<\/Typography>/g, (match, variant, text) => {
    let correctedText = text;
    if (text.includes('"')) {
      correctedText = text.replace(/"/g, '');
      corrections++;
    }
    return `<Typography variant="${variant}">${correctedText}</Typography>`;
  });

  // Correction des balises auto-fermantes
  content = content.replace(/<([A-Za-z0-9]+)([^>]*?)>\s*<\/\1>/g, '<$1$2 />');
  corrections += (content.match(/<([A-Za-z0-9]+)([^>]*?)>\s*<\/\1>/g) || []).length;

  // Correction des balises self-closing
  content = content.replace(/<([A-Za-z0-9]+)([^>]*?)\s*\/>/g, '<$1$2 />');
  corrections += (content.match(/<([A-Za-z0-9]+)([^>]*?)\s*\/>/g) || []).length;

  // Correction du try/catch dans useEffect
  content = content.replace(/\}, \[\]\) catch/g, '}).catch');
  corrections += (content.match(/\}, \[\]\) catch/g) || []).length;

  // Correction des balises React.Fragment et des fragments
  content = content.replace(/<\s*\/\s*>/g, '</>');
  corrections += (content.match(/<\s*\/\s*>/g) || []).length;

  if (corrections > 0) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed ${corrections} issues in ${filePath}`);
    return corrections;
  } else {
    console.log(`No issues found in ${filePath}`);
    return 0;
  }
}

function fixNFTComponents() {
  const nftComponentsPath = path.join(process.cwd(), 'src', 'components', 'nft');
  const nftGalleryPath = path.join(process.cwd(), 'src', 'components', 'NFTGallery.tsx');
  
  let totalFixedFiles = 0;
  let totalCorrections = 0;

  // Vérifier si le répertoire nft existe
  if (fs.existsSync(nftComponentsPath)) {
    const nftFiles = fs.readdirSync(nftComponentsPath)
      .filter(file => file.endsWith('.tsx') || file.endsWith('.jsx'));
    
    console.log(`Found ${nftFiles.length} NFT component files in directory`);
    
    nftFiles.forEach(file => {
      const filePath = path.join(nftComponentsPath, file);
      const corrections = processFile(filePath);
      if (corrections > 0) {
        totalFixedFiles++;
        totalCorrections += corrections;
      }
    });
  } else {
    console.log(`Directory not found: ${nftComponentsPath}`);
  }

  // Vérifier si le fichier NFTGallery.tsx existe à la racine des composants
  if (fs.existsSync(nftGalleryPath)) {
    console.log('Found NFTGallery.tsx at components root');
    const corrections = processFile(nftGalleryPath);
    if (corrections > 0) {
      totalFixedFiles++;
      totalCorrections += corrections;
    }
  } else {
    console.log(`File not found: ${nftGalleryPath}`);
  }

  console.log(`Fixed ${totalCorrections} issues in ${totalFixedFiles} files`);
}

fixNFTComponents(); 