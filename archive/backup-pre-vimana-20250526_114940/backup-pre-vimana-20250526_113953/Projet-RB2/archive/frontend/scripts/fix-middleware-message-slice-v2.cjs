const fs = require('fs');
const path = require('path');

// Version améliorée avec des logs plus explicites
function fixValidationMiddleware() {
  console.log('Starting validation middleware fix...');
  
  const filePath = path.join(process.cwd(), 'src', 'store', 'middleware', 'validationMiddleware.ts');
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ File not found: ${filePath}`);
    return 0;
  }
  
  console.log(`📂 Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  console.log(`📄 Original file size: ${content.length} bytes`);
  
  let corrections = 0;
  
  // Afficher quelques lignes problématiques pour debug
  const lines = content.split('\n');
  console.log(`🔍 File has ${lines.length} lines`);
  console.log('--- Checking for problematic patterns ---');
  
  // Pattern 1: "});};);" => "});"
  console.log('Looking for pattern: "});};);"');
  const pattern1regex = /\}\);\s*\};?\s*\);/g;
  const matches1 = content.match(pattern1regex) || [];
  if (matches1.length > 0) {
    console.log(`Found ${matches1.length} matches for pattern 1`);
    matches1.forEach((match, i) => console.log(`  Match ${i+1}: "${match}"`));
    
    const newContent = content.replace(pattern1regex, '});');
    corrections += matches1.length;
    content = newContent;
    console.log(`✅ Replaced ${matches1.length} occurrences of pattern 1`);
  } else {
    console.log('❌ No matches found for pattern 1');
  }
  
  // Pattern 2: Remove trailing "};""
  console.log('Looking for pattern: trailing "};""');
  const pattern2regex = /\};\s*$/g;
  const matches2 = content.match(pattern2regex) || [];
  if (matches2.length > 0) {
    console.log(`Found ${matches2.length} matches for pattern 2`);
    matches2.forEach((match, i) => console.log(`  Match ${i+1}: "${match}"`));
    
    const newContent = content.replace(pattern2regex, '}');
    corrections += matches2.length;
    content = newContent;
    console.log(`✅ Replaced ${matches2.length} occurrences of pattern 2`);
  } else {
    console.log('❌ No matches found for pattern 2');
  }
  
  // Pattern 3: Fix malformed declarations "} };"
  console.log('Looking for pattern: "} };"');
  const pattern3regex = /\}\s*\}\s*;/g;
  const matches3 = content.match(pattern3regex) || [];
  if (matches3.length > 0) {
    console.log(`Found ${matches3.length} matches for pattern 3`);
    matches3.forEach((match, i) => console.log(`  Match ${i+1}: "${match}"`));
    
    const newContent = content.replace(pattern3regex, '} }');
    corrections += matches3.length;
    content = newContent;
    console.log(`✅ Replaced ${matches3.length} occurrences of pattern 3`);
  } else {
    console.log('❌ No matches found for pattern 3');
  }
  
  // Essayer une méthode plus directe pour résoudre les erreurs specifiques
  console.log('Applying direct fixes based on error lines...');
  
  // Fixer directement ligne 14-15
  if (lines.length >= 15) {
    console.log(`Line 14: "${lines[13]}"`);
    console.log(`Line 15: "${lines[14]}"`);
    
    if (lines[13].includes('});') && lines[14].includes('};);')) {
      lines[14] = '};';
      corrections++;
      console.log('✅ Fixed line 15');
    }
  }
  
  // Fixer directement ligne 30-31
  if (lines.length >= 31) {
    console.log(`Line 30: "${lines[29]}"`);
    console.log(`Line 31: "${lines[30]}"`);
    
    if (lines[29].includes('});') && lines[30].includes('};);')) {
      lines[30] = '};';
      corrections++;
      console.log('✅ Fixed line 31');
    }
  }
  
  // Recomposer le contenu avec les lignes modifiées
  content = lines.join('\n');
  
  if (corrections > 0) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`💾 Fixed ${corrections} issues in ${filePath}`);
    console.log(`📄 New file size: ${content.length} bytes`);
  } else {
    console.log(`⚠️ No issues found in ${filePath}`);
  }
  
  return corrections;
}

function fixMessageSlice() {
  console.log('\nStarting message slice fix...');
  
  const filePath = path.join(process.cwd(), 'src', 'store', 'slices', 'messageSlice.ts');
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ File not found: ${filePath}`);
    return 0;
  }
  
  console.log(`📂 Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  console.log(`📄 Original file size: ${content.length} bytes`);
  
  const lines = content.split('\n');
  console.log(`🔍 File has ${lines.length} lines`);
  
  let corrections = 0;
  
  // Vérifier les lignes mentionnées dans les erreurs
  console.log('--- Checking specific error lines ---');
  
  // Ligne 56 (erreur TS1005: ',' expected)
  if (lines.length >= 56) {
    console.log(`Line 56: "${lines[55]}"`);
    if (lines[55].trim().endsWith('},')) {
      lines[55] = lines[55].replace(/},\s*$/, '}');
      corrections++;
      console.log('✅ Fixed line 56');
    } else {
      console.log('❌ Line 56 does not match expected pattern');
    }
  }
  
  // Ligne 60 (erreur TS1005: ',' expected)
  if (lines.length >= 60) {
    console.log(`Line 60: "${lines[59]}"`);
    if (lines[59].trim().endsWith('};')) {
      lines[59] = lines[59].replace(/};\s*$/, '}');
      corrections++;
      console.log('✅ Fixed line 60');
    } else {
      console.log('❌ Line 60 does not match expected pattern');
    }
  }
  
  // Recomposer le contenu
  content = lines.join('\n');
  
  if (corrections > 0) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`💾 Fixed ${corrections} issues in ${filePath}`);
    console.log(`📄 New file size: ${content.length} bytes`);
  } else {
    console.log(`⚠️ No issues found in ${filePath}`);
  }
  
  return corrections;
}

// Exécuter les corrections
console.log('🔧 Starting fix script...');
let totalCorrections = 0;

totalCorrections += fixValidationMiddleware();
totalCorrections += fixMessageSlice();

console.log(`\n✨ Total issues fixed: ${totalCorrections}`);
console.log('Script completed'); 