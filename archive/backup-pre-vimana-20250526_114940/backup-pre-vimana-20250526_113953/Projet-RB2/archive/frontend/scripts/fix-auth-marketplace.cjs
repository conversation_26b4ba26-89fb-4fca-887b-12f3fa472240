const fs = require('fs');
const path = require('path');

// Fonction pour parcourir récursivement un répertoire
function walkDir(dir, callback) {
  fs.readdirSync(dir).forEach(f => {
    const dirPath = path.join(dir, f);
    const isDirectory = fs.statSync(dirPath).isDirectory();
    if (isDirectory) {
      walkDir(dirPath, callback);
    } else {
      callback(path.join(dir, f));
    }
  });
}

// Fonction pour corriger les erreurs dans un fichier
function processFile(filePath) {
  console.log(`Processing file: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let corrections = 0;

  // Correction 1: Supprimer les points-virgules avant les parenthèses dans les appels de hooks
  content = content.replace(/([A-Za-z]+);(\(\))/g, '$1$2');
  corrections += (content.match(/([A-Za-z]+);(\(\))/g) || []).length;

  // Correction 2: Correction des imports avec "from" incorrects
  content = content.replace(/};(\s*)from(\s*)['"](.*?)['"];/g, (match, space1, space2, importPath) => {
    corrections++;
    return `} from${space2}'${importPath}';`;
  });

  // Correction 3: Correction des balises de fermeture incorrectes
  const tagPairs = {
    'button': 'Button', 
    'div': 'Box', 
    'Link': 'Typography',
    'ShoppingCartIcon': 'Badge',
    'DeleteIcon': 'ListItemSecondaryAction',
    'ListItemSecondaryAction': 'ListItemText',
    'AttachFileIcon': 'Box',
    'MicIcon': 'IconButton',
    'SendIcon': 'IconButton',
    'CircularProgress': 'Box'
  };
  
  for (const [wrongTag, correctTag] of Object.entries(tagPairs)) {
    const regex = new RegExp(`<\/${wrongTag}>`, 'g');
    content = content.replace(regex, `</${correctTag}>`);
    corrections += (content.match(regex) || []).length;
  }

  // Correction 4: Correction des useEffect
  content = content.replace(/}, \[\]\)(\s*)catch/g, '}).catch');
  corrections += (content.match(/}, \[\]\)(\s*)catch/g) || []).length;

  // Correction 5: Correction des useEffect manquant de virgule
  content = content.replace(/}, \[\]\)(\r\n|\n|\r)/g, '}, []);\n');
  corrections += (content.match(/}, \[\]\)(\r\n|\n|\r)/g) || []).length;

  // Correction 6: Correction des fragments vides
  content = content.replace(/<\s*\/\s*>/g, '</>');
  corrections += (content.match(/<\s*\/\s*>/g) || []).length;

  // Correction 7: Correction des expressions lambda dans les filtres et les promesses
  content = content.replace(/=\/>/g, '=> ');
  corrections += (content.match(/=\/>/g) || []).length;

  // Correction 8: Correction des fermetures de fragment React
  content = content.replace(/<\/React\.Fragment\/>/g, '</React.Fragment>');
  corrections += (content.match(/<\/React\.Fragment\/>/g) || []).length;

  // Correction 9: Correction des expressions lambda avec => manquant
  content = content.replace(/product =(\r\n|\n|\r)/g, 'product => ');
  corrections += (content.match(/product =(\r\n|\n|\r)/g) || []).length;

  // Correction 10: Correction des balises auto-fermantes
  content = content.replace(/<([A-Za-z0-9.]+)([^>]*?)\/>/g, '<$1$2 />');
  corrections += (content.match(/<([A-Za-z0-9.]+)([^>]*?)\/>/g) || []).length;

  // Correction 11: Correction des hooks useAuth et useWeb3
  content = content.replace(/useAuth;(\(\))/g, 'useAuth()');
  corrections += (content.match(/useAuth;(\(\))/g) || []).length;
  content = content.replace(/useWeb3;(\(\))/g, 'useWeb3()');
  corrections += (content.match(/useWeb3;(\(\))/g) || []).length;
  content = content.replace(/useMessage;(\(\))/g, 'useMessage()');
  corrections += (content.match(/useMessage;(\(\))/g) || []).length;

  // Correction 12: Fermeture du AuthContext.Provider
  content = content.replace(/<\/AuthContext\.Provider\/>/g, '</AuthContext.Provider>');
  corrections += (content.match(/<\/AuthContext\.Provider\/>/g) || []).length;

  if (corrections > 0) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed ${corrections} issues in ${filePath}`);
    return corrections;
  } else {
    console.log(`No issues found in ${filePath}`);
    return 0;
  }
}

function fixComponents() {
  const rootDir = process.cwd();
  const featuresDir = path.join(rootDir, 'src', 'features');
  
  let totalFixedFiles = 0;
  let totalCorrections = 0;
  
  // Vérifier si le répertoire features existe
  if (fs.existsSync(featuresDir)) {
    const authDir = path.join(featuresDir, 'auth');
    const marketplaceDir = path.join(featuresDir, 'marketplace');
    const messagingDir = path.join(featuresDir, 'messaging');
    const securityDir = path.join(featuresDir, 'security');
    
    const directories = [
      { path: authDir, name: 'auth' },
      { path: marketplaceDir, name: 'marketplace' },
      { path: messagingDir, name: 'messaging' },
      { path: securityDir, name: 'security' }
    ];
    
    directories.forEach(dir => {
      if (fs.existsSync(dir.path)) {
        console.log(`Processing ${dir.name} components...`);
        
        walkDir(dir.path, (filePath) => {
          if (filePath.endsWith('.tsx') || filePath.endsWith('.jsx')) {
            const corrections = processFile(filePath);
            if (corrections > 0) {
              totalFixedFiles++;
              totalCorrections += corrections;
            }
          }
        });
      } else {
        console.log(`Directory not found: ${dir.path}`);
      }
    });
  } else {
    console.log(`Features directory not found: ${featuresDir}`);
  }
  
  console.log(`Fixed ${totalCorrections} issues in ${totalFixedFiles} files`);
}

fixComponents(); 