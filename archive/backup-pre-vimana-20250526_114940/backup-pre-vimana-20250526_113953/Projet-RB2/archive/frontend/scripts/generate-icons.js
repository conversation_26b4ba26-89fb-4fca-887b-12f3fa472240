import sharp from 'sharp';
import { promises as fs } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const ICONS_DIR = join(__dirname, '../public/icons');
const SOURCE_ICON = join(__dirname, '../src/assets/logo.svg');

const SIZES = [
  72, 96, 128, 144, 152, 192, 384, 512
];

async function generateIcons() {
  try {
    // Ensure icons directory exists
    await fs.mkdir(ICONS_DIR, { recursive: true });

    // Generate icons for each size
    for (const size of SIZES) {
      await sharp(SOURCE_ICON)
        .resize(size, size)
        .png()
        .toFile(join(ICONS_DIR, `icon-${size}x${size}.png`));
      
      console.log(`Generated ${size}x${size} icon`);
    }

    console.log('All icons generated successfully!');
  } catch (error) {
    console.error('Error generating icons:', error);
    process.exit(1);
  }
}

generateIcons();
