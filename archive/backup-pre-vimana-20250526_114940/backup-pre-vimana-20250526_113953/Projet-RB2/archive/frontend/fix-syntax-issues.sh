#!/bin/bash

# Script pour corriger les problèmes de syntaxe typiques dans les fichiers TypeScript/TSX

echo "Correction des problèmes de syntaxe typiques..."

# Correction des problèmes avec les imports qui ont des tirets
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as react-iconsfi/import * as ReactIconsFi/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as date-fns\/locale\/en-US/import * as dateFnsLocaleEnUS/g' {} \;

# Correction des problèmes de terminaison
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/alt="Avatar"/alt="Avatar" \/>/g' {} \;

# Correction des exports problématiques
find ./src -type f -name "index.ts" -exec sed -i '' 's/from "\.\/RoadmapKPIDashboard"";/from "\.\/RoadmapKPIDashboard";/g' {} \;

# Correction des problèmes de MenuItem
find ./src -type f -name "SecurityEventsList.tsx" -exec sed -i '' 's/<MenuItem value=""Tous<\/MenuItem>/<MenuItem value="">Tous<\/MenuItem>/g' {} \;

echo "Corrections terminées!"
