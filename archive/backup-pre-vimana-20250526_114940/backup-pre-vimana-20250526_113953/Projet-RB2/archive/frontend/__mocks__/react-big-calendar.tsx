import React from 'react';

// Création d'un mock simple pour React Big Calendar
const Calendar = (props: any) => {
  return <div data-testid="calendar-mock">{props.children}</div>;
};

// Export des différents composants et fonctionnalités du calendrier
const Views = {
  MONTH: 'month',
  WEEK: 'week',
  WORK_WEEK: 'work_week',
  DAY: 'day',
  AGENDA: 'agenda',
};

const Navigate = {
  PREVIOUS: 'PREV',
  NEXT: 'NEXT',
  TODAY: 'TODAY',
  DATE: 'DATE',
};

// Mock des fonctions utilitaires
const momentLocalizer = () => ({});
const dateFnsLocalizer = () => ({});
const globalizeLocalizer = () => ({});

// Assigner les propriétés au composant Calendar
Calendar.Views = Views;
Calendar.Navigate = Navigate;

// Exporter le composant et les helpers
export {
  Calendar,
  momentLocalizer,
  dateFnsLocalizer,
  globalizeLocalizer,
  Views,
  Navigate,
};

// Exporter Calendar comme export par défaut
export default Calendar;