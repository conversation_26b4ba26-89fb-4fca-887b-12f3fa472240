image: node:20-alpine

stages:
  - test
  - build
  - deploy
  - monitor

variables:
  DOCKER_REGISTRY: ${CI_REGISTRY}
  CACHE_KEY: ${CI_COMMIT_REF_SLUG}

# Cache configuration
cache:
  key: ${CACHE_KEY}
  paths:
    - node_modules/
    - .yarn-cache/

# Job Templates
.test_template: &test_definition
  stage: test
  script:
    - yarn install --frozen-lockfile
    - yarn test:coverage
  coverage: '/Lines\s*:\s*([0-9.]+)%/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml

# Jobs
lint:
  stage: test
  script:
    - yarn install --frozen-lockfile
    - yarn lint
    - yarn type-check

unit_tests:
  <<: *test_definition
  script:
    - yarn install --frozen-lockfile
    - yarn test:unit

e2e_tests:
  stage: test
  image: cypress/included:12.8.1
  script:
    - yarn install --frozen-lockfile
    - yarn test:e2e:ci
  artifacts:
    when: always
    paths:
      - cypress/videos/
      - cypress/screenshots/

build:
  stage: build
  script:
    - yarn install --frozen-lockfile
    - yarn build
    - docker build -t ${DOCKER_REGISTRY}/frontend:${CI_COMMIT_SHA} .
    - docker push ${DOCKER_REGISTRY}/frontend:${CI_COMMIT_SHA}
  artifacts:
    paths:
      - dist/

deploy_staging:
  stage: deploy
  script:
    - kubectl set image deployment/frontend frontend=${DOCKER_REGISTRY}/frontend:${CI_COMMIT_SHA} -n staging
  environment:
    name: staging
  only:
    - develop

deploy_production:
  stage: deploy
  script:
    - kubectl set image deployment/frontend frontend=${DOCKER_REGISTRY}/frontend:${CI_COMMIT_SHA} -n production
  environment:
    name: production
  when: manual
  only:
    - main

monitoring:
  stage: monitor
  script:
    - yarn monitor:check
    - yarn lighthouse:ci
  artifacts:
    reports:
      metrics: metrics.json
      lighthouse: lighthouse-report.json
