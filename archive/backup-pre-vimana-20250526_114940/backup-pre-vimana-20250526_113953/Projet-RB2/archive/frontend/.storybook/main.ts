import { StorybookConfig } from '@storybook/react-vite';

const config: StorybookConfig = {
  stories: ['../src/**/*.stories.@(js|jsx|ts|tsx)'],
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-interactions',
    '@storybook/addon-a11y'
],
  framework: {
    name: '@storybook/react-vite',
    options: {}
},
  docs: {
    autodocs: 'tag'
},
  core: {
    builder: '@storybook/builder-vite'
}
}

export default config;
