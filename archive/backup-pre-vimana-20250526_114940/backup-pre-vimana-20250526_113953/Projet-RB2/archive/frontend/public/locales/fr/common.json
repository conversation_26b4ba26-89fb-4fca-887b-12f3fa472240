{"common": {"loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "cancel": "Annuler", "save": "Enregistrer", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmer", "back": "Retour", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "view": "Voir", "details": "Détails", "noData": "<PERSON><PERSON><PERSON> donnée disponible"}, "auth": {"signIn": "Se connecter", "signUp": "S'inscrire", "signOut": "Se déconnecter", "forgotPassword": "Mot de passe oublié ?", "resetPassword": "Réinitialiser le mot de passe", "email": "Email", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe"}, "booking": {"title": "Réserver votre séance", "selectDate": "Sélectionner une date", "selectTime": "Sélectionner une heure", "participants": "Nombre de participants", "specialRequests": "De<PERSON><PERSON> spéciales", "bookNow": "Réserver maintenant", "confirmBooking": "Confirmer la réservation", "bookingSuccess": "Réservation réussie !", "bookingError": "Échec de la réservation"}, "accessibility": {"skipToMain": "Passer au contenu principal", "menuButton": "Basculer le menu", "closeButton": "<PERSON><PERSON><PERSON>", "expandButton": "Développer", "collapseButton": "<PERSON><PERSON><PERSON><PERSON>", "nextPage": "<PERSON> suivante", "previousPage": "<PERSON> p<PERSON>"}, "dates": {"today": "<PERSON><PERSON><PERSON>'hui", "tomorrow": "<PERSON><PERSON><PERSON>", "thisWeek": "<PERSON><PERSON> se<PERSON>", "nextWeek": "Semaine prochaine"}, "errors": {"required": "Ce champ est requis", "invalidEmail": "<PERSON><PERSON><PERSON> email invalide", "passwordMismatch": "Les mots de passe ne correspondent pas", "serverError": "Une erreur serveur est survenue", "networkError": "Une erreur réseau est survenue"}, "analysis": {"title": "Analyses", "newAnalysis": "Nouvelle Analyse", "detail": "Détail de l'analyse", "name": "Nom", "type": "Type", "status": "Statut", "priority": "Priorité", "createdAt": "Date de création", "completedAt": "Date de fin", "result": "Résultat", "score": "Score", "issues": "Problèmes", "logs": "Logs", "actions": "Actions", "rerun": "Relancer", "cancelAnalysis": "Annuler l'analyse", "filters": "Filtres", "searchPlaceholder": "Rechercher une analyse...", "noAnalysisFound": "Aucune analyse trouvée", "loading": "Chargement des analyses...", "createSuccess": "<PERSON><PERSON><PERSON> c<PERSON> avec succès", "cancelSuccess": "Ana<PERSON><PERSON> annu<PERSON> avec succès", "rerunSuccess": "Analyse relancée avec succès", "error": "Une erreur est survenue lors du traitement de l'analyse", "statusPending": "En attente", "statusInProgress": "En cours", "statusCompleted": "<PERSON><PERSON><PERSON><PERSON>", "statusFailed": "<PERSON><PERSON><PERSON>", "priorityLow": "<PERSON><PERSON>", "priorityMedium": "<PERSON><PERSON><PERSON>", "priorityHigh": "Haute", "priorityCritical": "Critique", "typeCodeQuality": "Qualité du code", "typeSecurity": "Sécurité", "typePerformance": "Performance", "typeArchitecture": "Architecture", "typeCustom": "<PERSON><PERSON><PERSON><PERSON>"}}