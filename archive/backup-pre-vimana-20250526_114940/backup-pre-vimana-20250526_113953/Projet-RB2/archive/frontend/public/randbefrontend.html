<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Retreat And Be - Votre plateforme de retraites bien-être</title>
  <meta name="description" content="Découvrez et réservez des retraites bien-être personnalisées. Trouvez votre séjour de relaxation et de développement personnel idéal.">
  <meta name="theme-color" content="#38C283">
  <style>
    :root {
      --retreat-green: #38C283;
      --mint-700: #1a6e47;
    }
    
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
        Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    #root {
      min-height: 100vh;
    }
    
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background: linear-gradient(to bottom, #f0faf5, #ffffff);
    }
    
    .loading-logo {
      width: 200px;
      height: auto;
      margin-bottom: 2rem;
    }
    
    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 5px solid rgba(56, 194, 131, 0.3);
      border-radius: 50%;
      border-top-color: var(--retreat-green);
      animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div id="root">
    <div class="loading-container">
      <img src="/assets/randbefrontend/Logo-Retreat-and-Be.svg" alt="Retreat And Be Logo" class="loading-logo">
      <div class="loading-spinner"></div>
    </div>
  </div>
  <script type="module" src="/src/RandBeFrontEnd.tsx"></script>
</body>
</html>
