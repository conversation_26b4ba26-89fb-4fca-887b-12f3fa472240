// Serveur de développement personnalisé avec prise en charge SPA
import express from 'express';
import { createServer } from 'vite';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function startServer() {
  const app = express();
  
  // Créer un serveur Vite en mode middleware
  const vite = await createServer({
    server: { middlewareMode: true },
    appType: 'spa',
    configFile: path.resolve(__dirname, 'vite.config.ts')
  });
  
  // Utiliser vite en tant que middleware
  app.use(vite.middlewares);
  
  // Configurer les routes API proxy si nécessaire
  app.use('/api', (req, res) => {
    res.status(404).send('API route not found');
  });
  
  // Route de secours pour SPA - toutes les routes non reconnues sont envoyées vers index.html
  app.use('*', async (req, res, next) => {
    const url = req.originalUrl;
    
    try {
      // Si c'est une demande de fichier statique connu, passez au middleware suivant (qui est vite)
      if (url.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|map)$/)) {
        return next();
      }
      
      // Lire le fichier index.html
      let template = fs.readFileSync(
        path.resolve(__dirname, 'index.html'),
        'utf-8'
      );
      
      // Appliquer les transformations Vite à index.html
      template = await vite.transformIndexHtml(url, template);
      
      // Envoyer le contenu transformé
      res.status(200).set({ 'Content-Type': 'text/html' }).end(template);
    } catch (e) {
      console.error(`Erreur lors du traitement de ${url}:`, e);
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
  
  // Démarrer le serveur
  const port = 3000;
  app.listen(port, () => {
    console.log(`
╔═════════════════════════════════════════════════════╗
║                                                     ║
║   🚀 Serveur de développement démarré sur:          ║
║      http://localhost:${port}                          ║
║                                                     ║
║   🌐 Routes SPA activées                             ║
║      Toutes les routes sont dirigées vers index.html ║
║                                                     ║
║   🔍 Pour tester, essayez:                           ║
║      http://localhost:${port}/test                       ║
║      http://localhost:${port}/dashboard                  ║
║      http://localhost:${port}/login                      ║
║                                                     ║
╚═════════════════════════════════════════════════════╝
`);
  });
}

// Démarrer le serveur avec gestion d'erreurs
startServer().catch((e) => {
  console.error('Erreur lors du démarrage du serveur:', e);
  process.exit(1);
}); 