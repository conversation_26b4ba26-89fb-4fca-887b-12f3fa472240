# Production values for frontend chart

replicaCount: 3

image:
  repository: frontend
  tag: latest
  pullPolicy: Always

resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 500m
    memory: 512Mi

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  targetCPUUtilizationPercentage: 75
  targetMemoryUtilizationPercentage: 75

ingress:
  enabled: true
  className: nginx
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/from-to-www-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "Strict-Transport-Security: max-age=31536000";
      more_set_headers "X-Frame-Options: DENY";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-XSS-Protection: 1; mode=block";
  hosts:
    - host: frontend.example.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: frontend-tls
      hosts:
        - frontend.example.com

nginx:
  config:
    client_max_body_size: 50m
    proxy_connect_timeout: 300
    enable_gzip: true
    enable_brotli: true
    worker_processes: auto
    worker_connections: 2048
    keepalive_timeout: 65
    keepalive_requests: 100

cache:
  enabled: true
  storage:
    size: 10Gi
    storageClass: standard
  retention:
    images: 7d
    static: 1d

security:
  podSecurityContext:
    runAsNonRoot: true
    runAsUser: 101
    fsGroup: 101
  containerSecurityContext:
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: true
    capabilities:
      drop:
        - ALL

monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    interval: 30s
  grafana:
    enabled: true
    dashboards: true

affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: app.kubernetes.io/name
                operator: In
                values:
                  - frontend
          topologyKey: kubernetes.io/hostname