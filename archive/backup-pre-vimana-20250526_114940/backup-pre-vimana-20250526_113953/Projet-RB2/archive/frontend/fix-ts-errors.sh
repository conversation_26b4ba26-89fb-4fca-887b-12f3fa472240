#!/bin/bash

# Script pour corriger automatiquement les erreurs TypeScript courantes

# Couleurs pour les messages
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RESET="\033[0m"

echo -e "${YELLOW}Début des corrections TypeScript...${RESET}"

# 1. Correction des importations avec double "from"
echo -e "${GREEN}1. Correction des importations avec double 'from'...${RESET}"
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import { [^}]* } from ..*\(.*\) from ..\(.*\).;/import { \1 } from \2;/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' "s/import { api } from '\([^']*\) from '\([^']*\)';/import { \2 } from '\2';/g" {} \;

# 2. Correction des importations avec tirets dans le nom du module
echo -e "${GREEN}2. Correction des importations avec tirets...${RESET}"
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as \([a-zA-Z]*\)-\([a-zA-Z]*\) from/import \1\2 from/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as \([a-zA-Z]*\)-\([a-zA-Z]*\)-\([a-zA-Z]*\) from/import \1\2\3 from/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as color-contrast-checker/import ColorContrastChecker/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as react-dom/import ReactDOM/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as focus-trap-react/import FocusTrapReact/g' {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/import \* as react-popper/import ReactPopper/g' {} \;

# 3. Correction des importations avec plusieurs préfixes
echo -e "${GREEN}3. Correction des importations avec préfixes incorrects...${RESET}"
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' "s/import { api } from '\([^']*\)';/import { \1 } from '\1';/g" {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' "s/import { api } from '\./import { MyComponent } from '\./g" {} \;
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' "s/import { api } from '\.\./import { MyComponent } from '\.\./g" {} \;

# 4. Correction des guillemets non fermés dans les exports
echo -e "${GREEN}4. Correction des guillemets dans les exports...${RESET}"
find ./src -type f \( -name "*.ts" -o -name "*.tsx" \) -exec sed -i '' 's/export \* from "\(.*\)'\''/export * from '\''\\1'\''/' {} \;

echo -e "${YELLOW}Corrections terminées. Veuillez vérifier le résultat.${RESET}"
