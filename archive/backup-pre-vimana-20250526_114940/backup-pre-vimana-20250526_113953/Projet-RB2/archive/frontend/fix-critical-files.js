#!/usr/bin/env node

/**
 * Script pour corriger les erreurs TypeScript dans les fichiers critiques
 */
import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// Liste des fichiers critiques à corriger
const CRITICAL_FILES = [
  'src/index.tsx',
  'src/App.tsx',
  'src/AppWithChatBot.tsx',
  'src/routes.tsx',
  'src/components/common/LoadingFallback.tsx',
  'src/components/ProtectedRoute.tsx',
  'src/lazyComponents.tsx'
];

// Fonction principale
async function main() {
  try {
    console.log('Vérification des erreurs TypeScript dans les fichiers critiques...');
    
    for (const filePath of CRITICAL_FILES) {
      try {
        const fullPath = path.resolve(process.cwd(), filePath);
        
        if (!fs.existsSync(fullPath)) {
          console.log(`Le fichier ${filePath} n'existe pas, on le saute.`);
          continue;
        }
        
        console.log(`Vérification de ${filePath}...`);
        
        try {
          execSync(`npx tsc --noEmit --skipLibCheck --jsx react ${filePath}`, { stdio: 'pipe' });
          console.log(`✅ ${filePath} est valide.`);
        } catch (error) {
          console.error(`❌ ${filePath} contient des erreurs :`);
          console.error(error.stdout.toString());
        }
      } catch (error) {
        console.error(`Erreur lors du traitement du fichier ${filePath}:`, error.message);
      }
    }
    
    console.log('\nVérification terminée.');
  } catch (error) {
    console.error('Erreur lors de l\'exécution du script :', error);
    process.exit(1);
  }
}

// Exécuter la fonction principale
main().catch(error => {
  console.error('Erreur lors de l\'exécution du script :', error);
  process.exit(1);
});
