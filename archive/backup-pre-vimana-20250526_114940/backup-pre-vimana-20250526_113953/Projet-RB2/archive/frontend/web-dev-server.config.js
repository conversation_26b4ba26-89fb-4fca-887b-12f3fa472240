// web-dev-server.config.js
export default {
  port: 3000,
  middlewares: [
    // Middleware pour le SPA routing
    function rewriteIndex(context, next) {
      // Ne pas intercepter les appels API
      if (context.url.startsWith('/api')) {
        return next();
      }
      
      // Ne pas intercepter les requêtes pour des fichiers statiques
      if (context.url.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|map)$/)) {
        return next();
      }
      
      // Pour toutes les autres routes, redirigez vers index.html
      context.url = '/index.html';
      return next();
    },
  ],
  // Ajoutez des en-têtes supplémentaires pour le développement local
  responseHeaders: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  },
}; 