import * as vite from 'vite';
import * as <PERSON><PERSON> from '@vitejs/plugin-react';
import * as path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
}
},
  // Demo specific configuration;
  build: {
    outDir: 'dist-demo'
},
  // Use demo-app as entry point;
  server: {
    port: 3030
},
  // Custom HTML file for the demo;
  optimizeDeps: {
    entries: [
      'src/demo-app.tsx'
    ]
  },
  build: {
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'demo.html')
      }
    }
  }
});
