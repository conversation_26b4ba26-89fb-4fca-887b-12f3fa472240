# Documentation de la fusion des frontends

Ce document explique la fusion entre le frontend original (RandBeFrontend) et le nouveau frontend (frontend-refonte).

## Structure de la fusion

La fusion a été réalisée en intégrant les composants et pages du frontend original dans la structure du nouveau frontend. Voici comment les différents éléments ont été organisés :

### Structure des dossiers

```
src/
├── components/
│   ├── audrey-frontend/       # Composants du frontend original
│   │   ├── adapters.ts        # Adaptateurs pour les imports
│   │   ├── Blog/              # Composants du blog
│   │   ├── HomePage/          # Composants de la page d'accueil
│   │   ├── hooks/             # Hooks personnalisés
│   │   ├── lib/               # Utilitaires et composants stub
│   │   ├── organisms/         # Composants organisms
│   │   ├── RetreatFinder/     # Composants de recherche de retraites
│   │   └── ui/                # Composants UI
│   └── ...                    # Autres composants du nouveau frontend
├── pages/
│   ├── audrey-frontend/       # Pages du frontend original
│   │   ├── index.ts           # Export de toutes les pages
│   │   ├── HomePage.tsx       # Page d'accueil
│   │   ├── RetreatFinderPage.tsx # Page de recherche de retraites
│   │   └── ...                # Autres pages
│   └── ...                    # Pages du nouveau frontend
└── ...
```

### Système d'adaptateurs

Pour faciliter la transition entre les deux frontends, nous avons mis en place un système d'adaptateurs qui permet d'importer les composants du frontend original dans le nouveau frontend sans avoir à modifier les imports dans les fichiers originaux.

Le fichier `adapters.ts` sert de point central pour tous les imports des composants du frontend original. Il réexporte les composants depuis leurs nouveaux emplacements pour que les imports relatifs dans les fichiers originaux fonctionnent correctement.

### Composants stub

Pour les composants qui n'ont pas encore été migrés, nous avons créé des composants stub qui servent de placeholders. Ces composants affichent un message indiquant qu'ils seront implémentés ultérieurement.

## Comment utiliser le frontend fusionné

### Développement

Pour développer de nouvelles fonctionnalités ou modifier des fonctionnalités existantes, suivez ces étapes :

1. Identifiez si la fonctionnalité appartient au frontend original ou au nouveau frontend
2. Si elle appartient au frontend original, modifiez les fichiers dans le dossier `src/components/audrey-frontend/` ou `src/pages/audrey-frontend/`
3. Si elle appartient au nouveau frontend, modifiez les fichiers dans les autres dossiers

### Ajout de nouveaux composants

Pour ajouter un nouveau composant au frontend original :

1. Créez le composant dans le dossier approprié sous `src/components/audrey-frontend/`
2. Exportez-le depuis le fichier `adapters.ts`
3. Importez-le dans les pages ou composants qui en ont besoin

### Navigation entre les frontends

La navigation entre les deux frontends est gérée par le composant `FrontendSwitcher` qui permet de basculer entre les deux frontends. Ce composant est accessible depuis la barre de navigation.

## Roadmap de la fusion

La fusion des deux frontends est un processus en cours. Voici les prochaines étapes :

1. ✅ Migrer les composants stub vers des implémentations complètes
2. ✅ Harmoniser les styles entre les deux frontends
3. ✅ Unifier les systèmes d'authentification
4. ✅ Unifier les systèmes de navigation
5. ✅ Unifier les systèmes de gestion d'état
6. ✅ Unifier les systèmes de formulaires
7. ✅ Unifier les systèmes de notifications
8. Tester et optimiser les fonctionnalités
9. Intégrer les fonctionnalités spécifiques à chaque frontend dans une expérience unifiée

## Système de Design Unifié

Un système de design unifié a été créé pour harmoniser l'apparence des deux frontends. Ce système définit les couleurs, typographies, espacements, bordures, ombres et animations à utiliser dans toute l'application.

### Comment utiliser le système de design

Le système de design est accessible via le hook `useTheme` :

```tsx
import { useTheme } from '../components/providers/ThemeProvider';

const MyComponent = () => {
  const { theme, isDarkMode, toggleDarkMode } = useTheme();

  return (
    <div style={{ color: theme.colors.brand.primary }}>
      Mon composant avec la couleur principale
    </div>
  );
};
```

### Visualisation du système de design

Une page de démonstration du système de design est disponible à l'adresse `/design-system`. Cette page présente tous les éléments du système de design et permet de basculer entre le mode clair et le mode sombre.

### Variables CSS

Des variables CSS sont également disponibles pour utiliser le système de design dans les fichiers CSS :

```css
.my-component {
  color: var(--color-brand-primary);
  background-color: var(--color-background-primary);
  font-family: var(--font-family-primary);
}
```

## Système d'authentification unifié

Un système d'authentification unifié a été créé pour gérer l'authentification des utilisateurs dans les deux frontends. Ce système fournit une API cohérente pour les opérations d'authentification.

### Comment utiliser le système d'authentification

Le système d'authentification est accessible via le hook `useAuth` :

```tsx
import { useAuth } from '../components/providers/UnifiedAuthProvider';

const MyComponent = () => {
  const { user, isAuthenticated, login, register, logout } = useAuth();

  const handleLogin = async () => {
    try {
      await login({ email: '<EMAIL>', password: 'password' });
      // Redirection après connexion
    } catch (error) {
      // Gestion des erreurs
    }
  };

  return (
    <div>
      {isAuthenticated ? (
        <div>
          <p>Bienvenue, {user?.firstName}!</p>
          <button onClick={logout}>Déconnexion</button>
        </div>
      ) : (
        <button onClick={handleLogin}>Connexion</button>
      )}
    </div>
  );
};
```

## Système de navigation unifié

Un système de navigation unifié a été créé pour gérer la navigation dans les deux frontends. Ce système fournit une API cohérente pour les opérations de navigation.

### Comment utiliser le système de navigation

Le système de navigation est accessible via le hook `useNavigation` :

```tsx
import { useNavigation } from '../components/providers/UnifiedNavigationProvider';

const MyComponent = () => {
  const {
    items,
    currentPath,
    toggleMenu,
    getNavigationItemsByFrontend
  } = useNavigation();

  // Obtenir les éléments de navigation pour le frontend original
  const originalItems = getNavigationItemsByFrontend('original');

  return (
    <div>
      <button onClick={() => toggleMenu()}>Toggle Menu</button>
      <ul>
        {items.map(item => (
          <li key={item.id}>
            <a href={item.path}>{item.label}</a>
          </li>
        ))}
      </ul>
    </div>
  );
};
```

### Composant de navigation unifié

Un composant de navigation unifié a été créé pour afficher les éléments de navigation des deux frontends. Ce composant utilise le système de navigation unifié.

```tsx
import UnifiedNavigation from '../components/navigation/UnifiedNavigation';

const MyComponent = () => {
  return (
    <div>
      <UnifiedNavigation showFrontendFilter={true} />
      {/* Contenu de la page */}
    </div>
  );
};
```

## Système de gestion d'état unifié

Un système de gestion d'état unifié a été créé pour gérer l'état de l'application dans les deux frontends. Ce système utilise Zustand pour créer des stores qui peuvent être utilisés dans toute l'application.

### Comment utiliser le système de gestion d'état

Le système de gestion d'état est accessible via plusieurs hooks :

```tsx
import { useUnifiedStore, usePreferences, useUserData, useAppState } from '../../stores/unifiedStore';

const MyComponent = () => {
  // Accès au store complet
  const store = useUnifiedStore();

  // Accès aux préférences utilisateur
  const { theme, setTheme } = usePreferences();

  // Accès aux données utilisateur
  const { favorites, addFavorite } = useUserData();

  // Accès à l'état de l'application
  const { isLoading, setLoading } = useAppState();

  return (
    <div>
      <button onClick={() => setTheme('dark')}>Passer au mode sombre</button>
      <button onClick={() => addFavorite('123')}>Ajouter aux favoris</button>
      {isLoading && <div>Chargement...</div>}
    </div>
  );
};
```

## Système de formulaires unifié

Un système de formulaires unifié a été créé pour gérer les formulaires dans les deux frontends. Ce système utilise React Hook Form et Zod pour la validation.

### Comment utiliser le système de formulaires

Le système de formulaires est accessible via le hook `useUnifiedForm` et les composants `Form` et `FormField` :

```tsx
import { z } from 'zod';
import { useUnifiedForm } from '../../hooks/useUnifiedForm';
import { Form } from '../../components/forms/Form';
import { FormField } from '../../components/forms/FormField';

// Définir le schéma de validation
const schema = z.object({
  email: z.string().email('Email invalide'),
  password: z.string().min(8, 'Le mot de passe doit contenir au moins 8 caractères'),
});

const LoginForm = () => {
  const form = useUnifiedForm({
    schema,
    defaultValues: {
      email: '',
      password: '',
    },
    onSubmit: async (data) => {
      // Traiter les données du formulaire
      console.log(data);
    },
  });

  return (
    <Form form={form}>
      <FormField
        name="email"
        label="Email"
        type="email"
        placeholder="Entrez votre email"
        required
      />
      <FormField
        name="password"
        label="Mot de passe"
        type="password"
        placeholder="Entrez votre mot de passe"
        required
      />
      <button type="submit" className="form-submit">
        Se connecter
      </button>
    </Form>
  );
};
```

## Système de notifications unifié

Un système de notifications unifié a été créé pour gérer les notifications dans les deux frontends. Ce système fournit une API cohérente pour afficher des notifications.

### Comment utiliser le système de notifications

Le système de notifications est accessible via le hook `useNotifications` :

```tsx
import { useNotifications } from '../../components/providers/UnifiedNotificationProvider';

const MyComponent = () => {
  const { success, error, info, warning } = useNotifications();

  const handleSuccess = () => {
    success('Opération réussie !', 'Succès');
  };

  const handleError = () => {
    error('Une erreur est survenue.', 'Erreur');
  };

  return (
    <div>
      <button onClick={handleSuccess}>Afficher une notification de succès</button>
      <button onClick={handleError}>Afficher une notification d'erreur</button>
    </div>
  );
};
```

## Problèmes connus

- Certains composants du frontend original peuvent nécessiter des ajustements pour utiliser pleinement les systèmes unifiés
- Certaines fonctionnalités spécifiques à chaque frontend peuvent nécessiter des adaptations pour fonctionner correctement dans l'environnement fusionné
- Les tests et l'optimisation des performances sont en cours

## Dépendances

Les dépendances des deux frontends ont été fusionnées dans le fichier `package.json`. Si vous rencontrez des problèmes liés aux dépendances, vérifiez que toutes les dépendances nécessaires sont installées et que leurs versions sont compatibles.

## Conclusion

La fusion des deux frontends est un travail en cours qui vise à unifier l'expérience utilisateur tout en préservant les fonctionnalités spécifiques à chaque frontend. Cette approche permet de migrer progressivement les fonctionnalités du frontend original vers le nouveau frontend tout en maintenant une expérience utilisateur cohérente.
