#!/bin/bash

# Set text colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Correction des importations dans les fichiers du répertoire profile...${NC}"

# Correction des importations incorrectes comme "}; from" en "}; import"
find src/components/profile -name "*.tsx" -exec sed -i '' 's/}; from/} from/g' {} \;

# Correction des doubles accolades dans les importations React
find src/components/profile -name "*.tsx" -exec sed -i '' 's/const { { /import { /g' {} \;

# Correction des importations déstructurées de hooks avec appel direct
find src/components/profile -name "*.tsx" -exec sed -i '' 's/= useAuth;()/= useAuth()/g' {} \;
find src/components/profile -name "*.tsx" -exec sed -i '' 's/= useNotification;()/= useNotification()/g' {} \;
find src/components/profile -name "*.tsx" -exec sed -i '' 's/= useTheme;()/= useTheme()/g' {} \;

echo -e "${GREEN}Correction des importations terminée${NC}"
echo -e "${BLUE}Vérifiez les résultats après correction${NC}" 