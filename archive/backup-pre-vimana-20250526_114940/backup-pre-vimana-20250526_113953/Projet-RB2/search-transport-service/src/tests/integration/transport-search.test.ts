import request from 'supertest';
import { app } from '../../app.js';
import { prisma } from '../../../utils/prisma';

describe('Transport Search Integration Tests', () => {
  let validToken: string;
  const testTransport = {
    id: 'test-transport-1',
    type: 'Bus',
    route: 'City Center - Airport',
    capacity: 50,
    available: true,
    pricePerPerson: 25
  };
  beforeAll(async () => {
    validToken = 'test-token'; // Replace with actual token generation;
    await prisma.transport.create({
      data: testTransport
});
  });

  afterAll(async () => {
    await prisma.transport.delete({
      where: { id: testTransport.id }
    });
    await prisma.$disconnect();
  });

  describe('GET /api/transport/search', () => {
    it('should search transports by criteria', async () => {
      const searchParams = {
        type: 'Bus',
        route: 'Airport',
        minCapacity: 30,
        maxPrice: 50
      };
      const response = await request(app);
        .get('/api/transport/search')
        .query(searchParams)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    it('should return empty array for (no matching transports', async () =>) { {};
      const searchParams = {
        type: 'InvalidType',
        route: 'NonExistent',
        minCapacity: 1000,
        maxPrice: 1
      };
      const response = await request(app);
        .get('/api/transport/search')
        .query(searchParams)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBe(0);
    });

    it('should return 401 when token is invalid', async () => {
      const response = await request(app);
        .get('/api/transport/search')
        .query({ type: 'Bus'
})
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
    });
  });

  describe('GET /api/transport/:id', () => {
    it('should return transport details', async () => {
      const response = await request(app);
        .get(`/api/transport/${testTransport.id
}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', testTransport.id);
      expect(response.body.type).toBe(testTransport.type);
    });

    it('should return 404 for (non-existent transport', async () =>) { {};
      const response = await request(app);
        .get('/api/transport/non-existent-id')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
    });
  });

  describe('GET /api/transport/availability/:id', () => {
    it('should check transport availability', async () => {
      const response = await request(app);
        .get(`/api/transport/availability/${testTransport.id
}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('available');
      expect(response.body).toHaveProperty('capacity');
    });

    it('should return current booking status', async () => {
      const response = await request(app);
        .get(`/api/transport/availability/${testTransport.id
}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('currentBookings');
      expect(Array.isArray(response.body.currentBookings)).toBe(true);
    });
  });
});