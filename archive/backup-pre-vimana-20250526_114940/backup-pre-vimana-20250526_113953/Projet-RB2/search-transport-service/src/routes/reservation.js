const express = require('express');
const Reservation = require('../models/Reservation');
const Transport = require('../models/Transport');
const authMiddleware = require('../middleware/auth');
const router = express.Router();

// Protect all reservation routes
router.use(authMiddleware);

// Create reservation
router.post('/', async (req, res) => {
  try {
    const {
      transportId,
      passengers,
      pickupLocation,
      dropoffLocation,
      pickupDateTime,
      specialRequests,
    } = req.body;

    // Vérifier la disponibilité du transport
    const transport = await Transport.findById(transportId);
    if (!transport || !transport.available) {
      return res.status(400).json({ message: 'Transport non disponible' });
    }

    // Calculer le prix total
    const totalPrice = transport.price * passengers;

    const reservation = new Reservation({
      user: req.userId, // Utiliser l'ID de l'utilisateur du token
      transport: transportId,
      passengers,
      totalPrice,
      pickupLocation,
      dropoffLocation,
      pickupDateTime,
      specialRequests,
    });

    await reservation.save();
    res.status(201).json(reservation);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la création de la réservation', error: error.message });
  }
});

// Get user reservations
router.get('/my-reservations', async (req, res) => {
  try {
    const reservations = await Reservation.find({ user: req.userId })
      .populate('transport')
      .sort({ createdAt: -1 });
    res.json(reservations);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la récupération des réservations', error: error.message });
  }
});

// Update reservation status
router.patch('/:id/status', async (req, res) => {
  try {
    const { status } = req.body;
    const reservation = await Reservation.findOne({
      _id: req.params.id,
      user: req.userId
    });

    if (!reservation) {
      return res.status(404).json({ message: 'Réservation non trouvée' });
    }

    reservation.status = status;
    await reservation.save();
    res.json(reservation);
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de la mise à jour du statut', error: error.message });
  }
});

// Cancel reservation
router.delete('/:id', async (req, res) => {
  try {
    const reservation = await Reservation.findOne({
      _id: req.params.id,
      user: req.userId
    });
    
    if (!reservation) {
      return res.status(404).json({ message: 'Réservation non trouvée' });
    }

    reservation.status = 'cancelled';
    await reservation.save();
    res.json({ message: 'Réservation annulée avec succès' });
  } catch (error) {
    res.status(500).json({ message: 'Erreur lors de l\'annulation de la réservation', error: error.message });
  }
});

module.exports = router;
