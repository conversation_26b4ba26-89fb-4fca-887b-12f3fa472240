apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "search-transport-service.fullname" . }}
  labels:
    {{- include "search-transport-service.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "search-transport-service.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "search-transport-service.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "search-transport-service.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.securityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.containerSecurityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: {{ .Values.probes.livenessProbe.path }}
              port: http
            initialDelaySeconds: {{ .Values.probes.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.probes.livenessProbe.periodSeconds }}
          readinessProbe:
            httpGet:
              path: {{ .Values.probes.readinessProbe.path }}
              port: http
            initialDelaySeconds: {{ .Values.probes.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.probes.readinessProbe.periodSeconds }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: MONGODB_URI
              valueFrom:
                secretKeyRef:
                  name: {{ include "search-transport-service.fullname" . }}-mongodb
                  key: mongodb-uri
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.jwt.secretName }}
                  key: {{ .Values.jwt.secretKey }}
            {{- if .Values.env }}
            {{- toYaml .Values.env | nindent 12 }}
            {{- end }}
          {{- if .Values.envFrom }}
          envFrom:
            {{- toYaml .Values.envFrom | nindent 12 }}
          {{- end }}
      {{- with .Values.nodeAffinity }}
      affinity:
        nodeAffinity:
          {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.podAffinity }}
        podAffinity:
          {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.podAntiAffinity }}
        podAntiAffinity:
          {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}