# Default values for search-transport-service
replicaCount: 2

image:
  repository: search-transport-service
  tag: latest
  pullPolicy: IfNotPresent

nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

# Pod Security Context
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  fsGroup: 2000

# Container Security Context
containerSecurityContext:
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop:
      - ALL

# Service configuration
service:
  type: ClusterIP
  port: 8080
  targetPort: 8080

# Resource limits
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 200m
    memory: 256Mi

# MongoDB configuration
mongodb:
  enabled: true
  auth:
    enabled: true
    rootPassword: ""  # Will be auto-generated if empty
    username: transport
    password: ""  # Will be auto-generated if empty
    database: transport-db
  persistence:
    enabled: true
    size: 8Gi
  metrics:
    enabled: true

# JWT Secret configuration
jwt:
  secretName: search-transport-jwt
  secretKey: "jwt-secret"

# Network Policies
networkPolicy:
  enabled: true
  ingressRules:
    - from:
        - podSelector:
            matchLabels:
              app.kubernetes.io/part-of: frontend
        - podSelector:
            matchLabels:
              app.kubernetes.io/part-of: api-gateway

# Ingress configuration
ingress:
  enabled: true
  className: nginx
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: transport.retreatandbe.example.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: transport-tls
      hosts:
        - transport.retreatandbe.example.com

# Monitoring and logging
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    additionalLabels: {}

# Autoscaling configuration
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

# Probe configuration
probes:
  livenessProbe:
    path: /health
    initialDelaySeconds: 30
    periodSeconds: 10
  readinessProbe:
    path: /health
    initialDelaySeconds: 5
    periodSeconds: 10

# Node affinity settings
nodeAffinity: {}

# Pod affinity settings
podAffinity: {}

# Pod anti-affinity settings
podAntiAffinity:
  preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
            - key: app.kubernetes.io/name
              operator: In
              values:
                - search-transport-service
        topologyKey: "kubernetes.io/hostname"

# Additional environment variables
env: []

# Additional environment variables from secrets
envFrom: []

# Cache configuration
cache:
  enabled: false
  type: "redis"  # Options: redis, memory
  ttl: 3600  # Time to live in seconds