import React, { useState } from 'react';
import { Container, Typo<PERSON>, Box, TextField, Button, Grid, Card, CardContent } from '@mui/material';

const SearchPage = () => {
  const [searchParams, setSearchParams] = useState({
    departure: '',
    destination: '',
    date: ''
  });
  const [results, setResults] = useState([]);

  const handleSearch = async (e) => {
    e.preventDefault();
    try {
      const response = await fetch('http://localhost:3001/api/transports/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(searchParams),
      });
      const data = await response.json();
      setResults(data);
    } catch (error) {
      console.error('Search error:', error);
    }
  };

  return (
    <Container>
      <Box sx={{ mt: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Search Transport
        </Typography>
        <Box component="form" onSubmit={handleSearch} sx={{ mt: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Departure"
                value={searchParams.departure}
                onChange={(e) => setSearchParams({ ...searchParams, departure: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Destination"
                value={searchParams.destination}
                onChange={(e) => setSearchParams({ ...searchParams, destination: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                type="date"
                label="Date"
                InputLabelProps={{ shrink: true }}
                value={searchParams.date}
                onChange={(e) => setSearchParams({ ...searchParams, date: e.target.value })}
              />
            </Grid>
          </Grid>
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Button variant="contained" color="primary" type="submit">
              Search
            </Button>
          </Box>
        </Box>

        <Box sx={{ mt: 4 }}>
          <Grid container spacing={2}>
            {results.map((transport) => (
              <Grid item xs={12} sm={6} md={4} key={transport.id}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" component="h2">
                      {transport.departure} → {transport.destination}
                    </Typography>
                    <Typography color="textSecondary">
                      Date: {new Date(transport.date).toLocaleDateString()}
                    </Typography>
                    <Typography>
                      Price: ${transport.price}
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                      <Button variant="contained" color="primary">
                        Book Now
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Box>
    </Container>
  );
};

export default SearchPage;
