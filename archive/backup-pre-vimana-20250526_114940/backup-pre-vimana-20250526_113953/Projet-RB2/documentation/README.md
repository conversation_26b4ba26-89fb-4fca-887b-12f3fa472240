# Documentation du Système de Recommandation Retreat And Be

Cette documentation couvre les différents aspects du système de recommandation développé pour la plateforme Retreat And Be.

## Structure de la Documentation

### Guides Techniques

- [**Système de Recommandation Hybride**](./hybrid_recommendation_system.md) - Architecture et utilisation du système de recommandation hybride
- [**Intégration de Données Externes**](./external_data_integration.md) - Guide pour l'intégration de sources de données externes
- [**Système de Recommandation en Temps Réel**](./realtime_recommendation_system.md) - Documentation du système de recommandation en temps réel

### Résumés de Sprints

Les résumés de sprints sont disponibles dans le répertoire `../sprint_summaries/` :

- [**Sprint 8 - Recommandations Hybrides et Intégration de Données Externes**](../sprint_summaries/sprint8_summary.md)

## Roadmap

La roadmap complète du système de recommandation est disponible dans le fichier [recommendation_roadmap.md](../recommendation_roadmap.md).

## Architecture Globale

Le système de recommandation est composé de plusieurs modules interconnectés :

```
┌─────────────────────────────────────────────────────────────────────┐
│                  Système de Recommandation Retreat And Be           │
│                                                                     │
│  ┌───────────────┐  ┌───────────────┐  ┌───────────────────────┐   │
│  │ Algorithmes   │  │  Système      │  │ Intégration Données   │   │
│  │    de Base    │  │   Hybride     │  │      Externes         │   │
│  └───────────────┘  └───────────────┘  └───────────────────────┘   │
│                                                                     │
│  ┌───────────────┐  ┌───────────────┐  ┌───────────────────────┐   │
│  │ Recommandation│  │  Évaluation   │  │ Interfaces            │   │
│  │  en Temps Réel│  │   & Tests A/B │  │      Utilisateur      │   │
│  └───────────────┘  └───────────────┘  └───────────────────────┘   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

## Technologies Utilisées

- **Backend**: NestJS (TypeScript)
- **Base de données**: PostgreSQL avec Prisma ORM
- **Services d'IA**: Python avec FastAPI
- **Intégrations externes**: OpenWeatherMap, Eventful, Google Maps, SNCF

## Guides d'Utilisation

### Intégration avec le Frontend

Pour intégrer les recommandations dans le frontend, utilisez les endpoints suivants :

```typescript
// Recommandations standard
GET /api/recommendations?userId=123&type=RETREAT&limit=10

// Recommandations hybrides avec méthode spécifique
GET /api/recommendations/hybrid?userId=123&type=RETREAT&method=WEIGHTED&limit=10

// Recommandations en temps réel
GET /api/recommendations/realtime?userId=123&type=RETREAT&limit=10

// Recommandations avec données externes
GET /api/recommendations?userId=123&type=RETREAT&enrichWithExternalData=true
```

### Configuration du Système

La configuration du système se fait via les variables d'environnement ou le fichier `config/recommendation.config.ts`.

## Contribution

Pour contribuer à la documentation ou au système de recommandation, veuillez suivre les étapes suivantes :

1. Créer une branche à partir de `main`
2. Effectuer les modifications
3. Soumettre une pull request avec une description détaillée des changements

## Contact

Pour toute question concernant le système de recommandation, veuillez contacter l'équipe de développement à [<EMAIL>](mailto:<EMAIL>).
