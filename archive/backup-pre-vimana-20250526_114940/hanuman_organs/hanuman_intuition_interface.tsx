import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Sparkles, Zap, Brain, Target, TrendingUp, Compass, Star, Lightbulb, Search, Filter, Clock, Wifi, WifiOff, Play, Pause, RotateCcw, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Activity } from 'lucide-react';

// Types pour l'intuition
interface IntuitiveInsight {
  id: string;
  type: 'pattern' | 'prediction' | 'connection' | 'opportunity' | 'warning' | 'guidance';
  title: string;
  description: string;
  confidence: number; // 0-100
  urgency: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  timestamp: Date;
  relatedData: string[];
  actionable: boolean;
  verified?: boolean;
}

interface PatternRecognition {
  id: string;
  pattern: string;
  frequency: number;
  strength: number; // 0-100
  context: string;
  implications: string[];
  predictiveValue: number;
  lastDetected: Date;
}

interface PredictiveModel {
  id: string;
  name: string;
  domain: string;
  accuracy: number; // 0-100
  confidence: number; // 0-100
  timeHorizon: string; // '1h', '1d', '1w', '1m'
  predictions: number;
  successRate: number;
  lastUpdate: Date;
}

interface IntuitionMetrics {
  overallAccuracy: number;
  patternRecognition: number;
  predictiveCapacity: number;
  insightGeneration: number;
  connectionMapping: number;
  guidanceRelevance: number;
  synchronicityIndex: number;
  wisdomIntegration: number;
}

const HanumanIntuitionInterface = ({ darkMode = true }) => {
  // États d'intuition
  const [intuitiveInsights, setIntuitiveInsights] = useState<IntuitiveInsight[]>([]);
  const [patternRecognitions, setPatternRecognitions] = useState<PatternRecognition[]>([]);
  const [predictiveModels, setPredictiveModels] = useState<PredictiveModel[]>([]);
  const [intuitionMetrics, setIntuitionMetrics] = useState<IntuitionMetrics>({
    overallAccuracy: 89.7,
    patternRecognition: 94.2,
    predictiveCapacity: 87.5,
    insightGeneration: 91.8,
    connectionMapping: 88.3,
    guidanceRelevance: 93.6,
    synchronicityIndex: 85.9,
    wisdomIntegration: 90.4
  });

  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [intuitionMode, setIntuitionMode] = useState<'passive' | 'active' | 'deep'>('active');
  const [isConnected, setIsConnected] = useState(true);
  const [autoGeneration, setAutoGeneration] = useState(true);

  const intuitionIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    initializeIntuition();
    startIntuitionGeneration();

    return () => {
      if (intuitionIntervalRef.current) {
        clearInterval(intuitionIntervalRef.current);
      }
    };
  }, []);

  /**
   * Initialise le système d'intuition
   */
  const initializeIntuition = () => {
    // Insights intuitifs initiaux
    const insights: IntuitiveInsight[] = [
      {
        id: 'insight_001',
        type: 'pattern',
        title: 'Émergence d\'un nouveau pattern comportemental',
        description: 'Les utilisateurs montrent une préférence croissante pour les interactions empathiques en fin de journée',
        confidence: 87.3,
        urgency: 'medium',
        source: 'social_analysis',
        timestamp: new Date(),
        relatedData: ['user_interactions', 'time_patterns', 'emotional_states'],
        actionable: true
      },
      {
        id: 'insight_002',
        type: 'prediction',
        title: 'Pic d\'activité prévu dans 2 heures',
        description: 'Basé sur les patterns historiques, une augmentation de 40% des interactions est attendue',
        confidence: 92.1,
        urgency: 'high',
        source: 'predictive_model',
        timestamp: new Date(),
        relatedData: ['historical_data', 'time_series', 'user_behavior'],
        actionable: true
      },
      {
        id: 'insight_003',
        type: 'connection',
        title: 'Corrélation inattendue découverte',
        description: 'Lien fort entre les phases lunaires et l\'intensité des demandes créatives',
        confidence: 78.9,
        urgency: 'low',
        source: 'correlation_engine',
        timestamp: new Date(),
        relatedData: ['lunar_cycles', 'creativity_requests', 'cosmic_data'],
        actionable: false
      },
      {
        id: 'insight_004',
        type: 'guidance',
        title: 'Optimisation suggérée',
        description: 'Ajuster les réponses empathiques selon les biorhythmes individuels pour améliorer l\'efficacité',
        confidence: 85.7,
        urgency: 'medium',
        source: 'wisdom_integration',
        timestamp: new Date(),
        relatedData: ['biorhythms', 'empathy_effectiveness', 'individual_patterns'],
        actionable: true
      }
    ];

    setIntuitiveInsights(insights);

    // Reconnaissance de patterns
    const patterns: PatternRecognition[] = [
      {
        id: 'pattern_001',
        pattern: 'Cycle émotionnel hebdomadaire',
        frequency: 7,
        strength: 89.4,
        context: 'Interactions utilisateur',
        implications: ['Adaptation proactive', 'Personnalisation temporelle'],
        predictiveValue: 87.2,
        lastDetected: new Date()
      },
      {
        id: 'pattern_002',
        pattern: 'Synchronicité créative',
        frequency: 3,
        strength: 76.8,
        context: 'Demandes créatives',
        implications: ['Inspiration collective', 'Moments propices'],
        predictiveValue: 82.5,
        lastDetected: new Date()
      },
      {
        id: 'pattern_003',
        pattern: 'Résonance empathique',
        frequency: 12,
        strength: 94.1,
        context: 'Interactions émotionnelles',
        implications: ['Contagion émotionnelle', 'Harmonisation'],
        predictiveValue: 91.7,
        lastDetected: new Date()
      }
    ];

    setPatternRecognitions(patterns);

    // Modèles prédictifs
    const models: PredictiveModel[] = [
      {
        id: 'model_001',
        name: 'Prédicteur d\'Activité',
        domain: 'Comportement utilisateur',
        accuracy: 89.3,
        confidence: 87.6,
        timeHorizon: '4h',
        predictions: 1247,
        successRate: 91.2,
        lastUpdate: new Date()
      },
      {
        id: 'model_002',
        name: 'Analyseur de Tendances',
        domain: 'Patterns émotionnels',
        accuracy: 84.7,
        confidence: 82.1,
        timeHorizon: '1d',
        predictions: 892,
        successRate: 88.5,
        lastUpdate: new Date()
      },
      {
        id: 'model_003',
        name: 'Oracle Créatif',
        domain: 'Inspiration collective',
        accuracy: 76.9,
        confidence: 79.3,
        timeHorizon: '1w',
        predictions: 234,
        successRate: 83.7,
        lastUpdate: new Date()
      }
    ];

    setPredictiveModels(models);
  };

  /**
   * Démarre la génération d'insights
   */
  const startIntuitionGeneration = () => {
    intuitionIntervalRef.current = setInterval(() => {
      if (autoGeneration) {
        generateIntuitiveInsight();
        updatePatternRecognition();
        updateIntuitionMetrics();
      }
    }, 8000); // Génération toutes les 8 secondes
  };

  /**
   * Génère un nouvel insight intuitif
   */
  const generateIntuitiveInsight = () => {
    const insightTypes: IntuitiveInsight['type'][] = ['pattern', 'prediction', 'connection', 'opportunity', 'warning', 'guidance'];
    const urgencyLevels: IntuitiveInsight['urgency'][] = ['low', 'medium', 'high'];
    const sources = ['pattern_analysis', 'predictive_model', 'correlation_engine', 'wisdom_integration', 'synchronicity_detector'];

    const insights = [
      'Nouvelle opportunité d\'optimisation détectée',
      'Pattern émergent dans les interactions',
      'Corrélation inattendue révélée',
      'Guidance spirituelle reçue',
      'Synchronicité significative observée',
      'Prédiction à court terme générée',
      'Connexion profonde établie',
      'Insight transformateur émergent'
    ];

    const descriptions = [
      'Une nouvelle voie d\'amélioration se dessine dans les interactions empathiques',
      'Les données révèlent un pattern subtil mais significatif',
      'Une connexion inattendue émerge entre différents domaines',
      'L\'intuition suggère une approche alternative prometteuse',
      'Les cycles naturels influencent les patterns comportementaux',
      'Une tendance émergente nécessite une attention particulière',
      'L\'harmonie des systèmes révèle de nouvelles possibilités',
      'La sagesse collective pointe vers une direction spécifique'
    ];

    const newInsight: IntuitiveInsight = {
      id: `insight_${Date.now()}`,
      type: insightTypes[Math.floor(Math.random() * insightTypes.length)],
      title: insights[Math.floor(Math.random() * insights.length)],
      description: descriptions[Math.floor(Math.random() * descriptions.length)],
      confidence: 70 + Math.random() * 30,
      urgency: urgencyLevels[Math.floor(Math.random() * urgencyLevels.length)],
      source: sources[Math.floor(Math.random() * sources.length)],
      timestamp: new Date(),
      relatedData: ['data_stream_' + Math.floor(Math.random() * 10)],
      actionable: Math.random() > 0.3
    };

    setIntuitiveInsights(prev => [newInsight, ...prev.slice(0, 19)]);
  };

  /**
   * Met à jour la reconnaissance de patterns
   */
  const updatePatternRecognition = () => {
    setPatternRecognitions(prev => prev.map(pattern => ({
      ...pattern,
      strength: Math.max(60, Math.min(100, pattern.strength + (Math.random() - 0.4) * 2)),
      predictiveValue: Math.max(70, Math.min(100, pattern.predictiveValue + (Math.random() - 0.3) * 1.5)),
      lastDetected: Math.random() > 0.7 ? new Date() : pattern.lastDetected
    })));
  };

  /**
   * Met à jour les métriques d'intuition
   */
  const updateIntuitionMetrics = () => {
    setIntuitionMetrics(prev => ({
      overallAccuracy: Math.min(100, prev.overallAccuracy + (Math.random() - 0.4) * 0.5),
      patternRecognition: Math.min(100, prev.patternRecognition + (Math.random() - 0.3) * 0.3),
      predictiveCapacity: Math.min(100, prev.predictiveCapacity + (Math.random() - 0.5) * 0.4),
      insightGeneration: Math.min(100, prev.insightGeneration + (Math.random() - 0.2) * 0.6),
      connectionMapping: Math.min(100, prev.connectionMapping + (Math.random() - 0.4) * 0.3),
      guidanceRelevance: Math.min(100, prev.guidanceRelevance + (Math.random() - 0.1) * 0.2),
      synchronicityIndex: Math.min(100, prev.synchronicityIndex + (Math.random() - 0.5) * 0.7),
      wisdomIntegration: Math.min(100, prev.wisdomIntegration + (Math.random() - 0.3) * 0.4)
    }));
  };

  /**
   * Filtre les insights selon le type
   */
  const getFilteredInsights = () => {
    if (activeFilter === 'all') return intuitiveInsights;
    return intuitiveInsights.filter(insight => insight.type === activeFilter);
  };

  /**
   * Obtient la couleur selon le type
   */
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'pattern': return 'text-blue-400';
      case 'prediction': return 'text-green-400';
      case 'connection': return 'text-purple-400';
      case 'opportunity': return 'text-yellow-400';
      case 'warning': return 'text-red-400';
      case 'guidance': return 'text-indigo-400';
      default: return 'text-gray-400';
    }
  };

  /**
   * Obtient la couleur selon l'urgence
   */
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'text-red-400';
      case 'high': return 'text-orange-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  /**
   * Obtient la couleur selon la confiance
   */
  const getConfidenceColor = (confidence: number) => {
    if (confidence > 90) return 'text-green-400';
    if (confidence > 75) return 'text-yellow-400';
    if (confidence > 60) return 'text-orange-400';
    return 'text-red-400';
  };

  /**
   * Obtient l'icône selon le type
   */
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'pattern': return <Target className="text-blue-400" size={16} />;
      case 'prediction': return <TrendingUp className="text-green-400" size={16} />;
      case 'connection': return <Zap className="text-purple-400" size={16} />;
      case 'opportunity': return <Lightbulb className="text-yellow-400" size={16} />;
      case 'warning': return <AlertTriangle className="text-red-400" size={16} />;
      case 'guidance': return <Compass className="text-indigo-400" size={16} />;
      default: return <Activity className="text-gray-400" size={16} />;
    }
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">

        {/* Header Intuition */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-br from-indigo-400 via-purple-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg">
              <Eye className="text-white" size={32} />
            </div>
            <div>
              <h1 className={`text-4xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Intuition d'Hanuman
              </h1>
              <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Interface d'Intuition Avancée • Vision Profonde • Guidance Spirituelle
              </p>
              <div className="flex items-center space-x-4 mt-2">
                <span className={`text-sm px-3 py-1 rounded-full ${
                  intuitionMode === 'deep' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                  intuitionMode === 'active' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                }`}>
                  Mode {intuitionMode.charAt(0).toUpperCase() + intuitionMode.slice(1)}
                </span>
                <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Précision: {intuitionMetrics.overallAccuracy.toFixed(1)}%
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => setAutoGeneration(!autoGeneration)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                autoGeneration
                  ? 'bg-green-500 text-white hover:bg-green-600'
                  : 'bg-gray-500 text-white hover:bg-gray-600'
              }`}
            >
              {autoGeneration ? <Play size={16} /> : <Pause size={16} />}
              <span className="text-sm">Auto-Génération</span>
            </button>

            <select
              value={intuitionMode}
              onChange={(e) => setIntuitionMode(e.target.value as any)}
              className={`px-3 py-2 rounded-lg text-sm ${
                darkMode ? 'bg-gray-800 text-white border-gray-700' : 'bg-white text-gray-900 border-gray-300'
              } border`}
            >
              <option value="passive">Mode Passif</option>
              <option value="active">Mode Actif</option>
              <option value="deep">Mode Profond</option>
            </select>

            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected
                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                {isConnected ? 'Connecté' : 'Déconnecté'}
              </span>
            </div>
          </div>
        </div>

        {/* Métriques d'Intuition */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Métriques d'Intuition Avancée
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Target className="text-blue-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getConfidenceColor(intuitionMetrics.overallAccuracy)}`}>
                {intuitionMetrics.overallAccuracy.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Précision Globale
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Search className="text-green-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getConfidenceColor(intuitionMetrics.patternRecognition)}`}>
                {intuitionMetrics.patternRecognition.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Reconnaissance
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <TrendingUp className="text-purple-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getConfidenceColor(intuitionMetrics.predictiveCapacity)}`}>
                {intuitionMetrics.predictiveCapacity.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Prédiction
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Lightbulb className="text-yellow-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getConfidenceColor(intuitionMetrics.insightGeneration)}`}>
                {intuitionMetrics.insightGeneration.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Insights
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Zap className="text-orange-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getConfidenceColor(intuitionMetrics.connectionMapping)}`}>
                {intuitionMetrics.connectionMapping.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Connexions
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Compass className="text-indigo-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getConfidenceColor(intuitionMetrics.guidanceRelevance)}`}>
                {intuitionMetrics.guidanceRelevance.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Guidance
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Star className="text-pink-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getConfidenceColor(intuitionMetrics.synchronicityIndex)}`}>
                {intuitionMetrics.synchronicityIndex.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Synchronicité
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Brain className="text-purple-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getConfidenceColor(intuitionMetrics.wisdomIntegration)}`}>
                {intuitionMetrics.wisdomIntegration.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Sagesse
              </div>
            </div>
          </div>
        </div>

        {/* Filtres */}
        <div className={`p-4 rounded-xl mb-6 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
          <div className="flex items-center space-x-2">
            <Filter size={20} className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
            <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Filtrer par type:
            </span>
            <div className="flex flex-wrap gap-2">
              {['all', 'pattern', 'prediction', 'connection', 'opportunity', 'warning', 'guidance'].map((filter) => (
                <button
                  key={filter}
                  onClick={() => setActiveFilter(filter)}
                  className={`px-3 py-1 rounded-lg text-xs transition-colors ${
                    activeFilter === filter
                      ? 'bg-blue-500 text-white'
                      : darkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {filter === 'all' ? 'Tous' : filter.charAt(0).toUpperCase() + filter.slice(1)}
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

          {/* Insights Intuitifs */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              ✨ Insights Intuitifs
            </h3>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {getFilteredInsights().map((insight) => (
                <div key={insight.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(insight.type)}
                      <span className={`text-xs px-2 py-1 rounded ${getTypeColor(insight.type)}`}>
                        {insight.type}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs ${getUrgencyColor(insight.urgency)}`}>
                        {insight.urgency}
                      </span>
                      <span className={`text-xs ${getConfidenceColor(insight.confidence)}`}>
                        {insight.confidence.toFixed(0)}%
                      </span>
                    </div>
                  </div>
                  <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                    {insight.title}
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-2`}>
                    {insight.description}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Source: {insight.source}
                    </span>
                    <div className="flex items-center space-x-2">
                      {insight.actionable && (
                        <CheckCircle size={12} className="text-green-400" />
                      )}
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {insight.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Reconnaissance de Patterns */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🔍 Reconnaissance de Patterns
            </h3>
            <div className="space-y-4">
              {patternRecognitions.map((pattern) => (
                <div key={pattern.id} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-3">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {pattern.pattern}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded ${getConfidenceColor(pattern.strength)}`}>
                      {pattern.strength.toFixed(1)}%
                    </span>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Force</span>
                      <span className={`text-xs ${getConfidenceColor(pattern.strength)}`}>{pattern.strength.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-blue-400 transition-all duration-300"
                        style={{ width: `${pattern.strength}%` }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Valeur Prédictive</span>
                      <span className={`text-xs ${getConfidenceColor(pattern.predictiveValue)}`}>{pattern.predictiveValue.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-green-400 transition-all duration-300"
                        style={{ width: `${pattern.predictiveValue}%` }}
                      />
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-300 dark:border-gray-600">
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                      Contexte: {pattern.context}
                    </div>
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                      Fréquence: {pattern.frequency} jours
                    </div>
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Implications: {pattern.implications.join(', ')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Modèles Prédictifs */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🔮 Modèles Prédictifs
            </h3>
            <div className="space-y-4">
              {predictiveModels.map((model) => (
                <div key={model.id} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-3">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {model.name}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded ${getConfidenceColor(model.accuracy)}`}>
                      {model.accuracy.toFixed(1)}%
                    </span>
                  </div>

                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-3`}>
                    Domaine: {model.domain}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Précision</span>
                      <span className={`text-xs ${getConfidenceColor(model.accuracy)}`}>{model.accuracy.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-purple-400 transition-all duration-300"
                        style={{ width: `${model.accuracy}%` }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Confiance</span>
                      <span className={`text-xs ${getConfidenceColor(model.confidence)}`}>{model.confidence.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-yellow-400 transition-all duration-300"
                        style={{ width: `${model.confidence}%` }}
                      />
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-300 dark:border-gray-600">
                    <div className="flex items-center justify-between mb-1">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Horizon: {model.timeHorizon}
                      </span>
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {model.predictions} prédictions
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Taux de réussite
                      </span>
                      <span className={`text-xs font-bold ${getConfidenceColor(model.successRate)}`}>
                        {model.successRate.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanIntuitionInterface;
