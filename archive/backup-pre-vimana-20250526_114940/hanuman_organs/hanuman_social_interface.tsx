import React, { useState, useEffect, useRef } from 'react';
import { Users, UserPlus, MessageSquare, Heart, Star, TrendingUp, Activity, Wifi, WifiOff, Clock, Target, BarChart3, Zap } from 'lucide-react';

// Types pour les relations sociales
interface UserProfile {
  userId: string;
  userName: string;
  avatar?: string;
  relationshipType: 'new' | 'regular' | 'vip' | 'friend' | 'collaborator';
  interactionCount: number;
  lastInteraction: Date;
  preferences: {
    communicationStyle: 'formal' | 'casual' | 'friendly' | 'professional';
    topics: string[];
    responseTime: 'immediate' | 'quick' | 'normal' | 'patient';
  };
  satisfactionScore: number;
  trustLevel: number;
}

interface Interaction {
  id: string;
  userId: string;
  userName: string;
  type: 'question' | 'conversation' | 'support' | 'feedback' | 'collaboration';
  content: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  duration: number;
  outcome: 'resolved' | 'ongoing' | 'escalated' | 'satisfied';
  timestamp: Date;
}

interface SocialRecommendation {
  id: string;
  userId: string;
  type: 'content' | 'connection' | 'activity' | 'improvement';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  confidence: number;
  timestamp: Date;
}

interface SocialMetrics {
  totalUsers: number;
  activeRelationships: number;
  averageSatisfaction: number;
  responseTime: number;
  engagementRate: number;
}

const HanumanSocialInterface = ({ darkMode = true }) => {
  const [userProfiles, setUserProfiles] = useState<UserProfile[]>([]);
  const [recentInteractions, setRecentInteractions] = useState<Interaction[]>([]);
  const [socialRecommendations, setSocialRecommendations] = useState<SocialRecommendation[]>([]);
  const [socialMetrics, setSocialMetrics] = useState<SocialMetrics>({
    totalUsers: 1247,
    activeRelationships: 89,
    averageSatisfaction: 92.4,
    responseTime: 1.8,
    engagementRate: 87.6
  });

  const [isConnected, setIsConnected] = useState(false);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [networkView, setNetworkView] = useState<'list' | 'graph'>('list');
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Connexion aux agents sociaux
    connectToSocialAgents();

    // Initialiser les données sociales
    initializeSocialData();

    // Simulation d'activité sociale
    const socialInterval = setInterval(() => {
      simulateSocialActivity();
    }, 4000);

    return () => {
      clearInterval(socialInterval);
      wsRef.current?.close();
    };
  }, []);

  const connectToSocialAgents = () => {
    try {
      wsRef.current = new WebSocket('ws://localhost:3001/social');

      wsRef.current.onopen = () => {
        console.log('🔗 Connexion établie avec les agents sociaux');
        setIsConnected(true);

        wsRef.current?.send(JSON.stringify({
          type: 'GET_SOCIAL_DATA',
          timestamp: Date.now()
        }));
      };

      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleSocialMessage(data);
      };

      wsRef.current.onclose = () => {
        console.log('❌ Connexion fermée avec les agents sociaux');
        setIsConnected(false);
        setTimeout(connectToSocialAgents, 5000);
      };

    } catch (error) {
      console.error('🚨 Erreur de connexion sociale:', error);
      setIsConnected(false);
    }
  };

  const handleSocialMessage = (data: any) => {
    switch (data.type) {
      case 'USER_PROFILE_UPDATE':
        updateUserProfile(data.profile);
        break;

      case 'NEW_INTERACTION':
        addInteraction(data.interaction);
        break;

      case 'PERSONALIZATION_SEGMENT':
        // Connexion avec Interface Personalization
        applyPersonalizationSegment(data.segment);
        break;

      case 'SOCIAL_RECOMMENDATION':
        addSocialRecommendation(data.recommendation);
        break;

      case 'METRICS_UPDATE':
        setSocialMetrics(data.metrics);
        break;

      default:
        console.log('📨 Message social non géré:', data);
    }
  };

  const initializeSocialData = () => {
    // Profils utilisateurs simulés
    const profiles: UserProfile[] = [
      {
        userId: 'user_001',
        userName: 'Alice Martin',
        relationshipType: 'vip',
        interactionCount: 47,
        lastInteraction: new Date(),
        preferences: {
          communicationStyle: 'professional',
          topics: ['IA', 'Technologie', 'Innovation'],
          responseTime: 'quick'
        },
        satisfactionScore: 96,
        trustLevel: 94
      },
      {
        userId: 'user_002',
        userName: 'Bob Dupont',
        relationshipType: 'regular',
        interactionCount: 23,
        lastInteraction: new Date(Date.now() - 3600000),
        preferences: {
          communicationStyle: 'casual',
          topics: ['Développement', 'Projets'],
          responseTime: 'normal'
        },
        satisfactionScore: 89,
        trustLevel: 87
      },
      {
        userId: 'user_003',
        userName: 'Claire Rousseau',
        relationshipType: 'friend',
        interactionCount: 65,
        lastInteraction: new Date(Date.now() - 1800000),
        preferences: {
          communicationStyle: 'friendly',
          topics: ['Créativité', 'Design', 'Art'],
          responseTime: 'immediate'
        },
        satisfactionScore: 98,
        trustLevel: 96
      }
    ];

    setUserProfiles(profiles);

    // Interactions récentes simulées
    const interactions: Interaction[] = [
      {
        id: 'int_001',
        userId: 'user_001',
        userName: 'Alice Martin',
        type: 'question',
        content: 'Comment optimiser les performances de l\'IA ?',
        sentiment: 'positive',
        duration: 12,
        outcome: 'resolved',
        timestamp: new Date()
      },
      {
        id: 'int_002',
        userId: 'user_003',
        userName: 'Claire Rousseau',
        type: 'conversation',
        content: 'Discussion sur les tendances créatives',
        sentiment: 'positive',
        duration: 25,
        outcome: 'satisfied',
        timestamp: new Date(Date.now() - 1800000)
      },
      {
        id: 'int_003',
        userId: 'user_002',
        userName: 'Bob Dupont',
        type: 'support',
        content: 'Aide pour l\'intégration API',
        sentiment: 'neutral',
        duration: 18,
        outcome: 'ongoing',
        timestamp: new Date(Date.now() - 3600000)
      }
    ];

    setRecentInteractions(interactions);

    // Recommandations sociales simulées
    const recommendations: SocialRecommendation[] = [
      {
        id: 'rec_001',
        userId: 'user_001',
        type: 'content',
        title: 'Article sur l\'IA générative',
        description: 'Basé sur ses intérêts pour l\'innovation technologique',
        priority: 'high',
        confidence: 92,
        timestamp: new Date()
      },
      {
        id: 'rec_002',
        userId: 'user_003',
        type: 'connection',
        title: 'Connexion avec des designers',
        description: 'Réseau de créatifs partageant ses intérêts',
        priority: 'medium',
        confidence: 85,
        timestamp: new Date()
      }
    ];

    setSocialRecommendations(recommendations);
  };

  const simulateSocialActivity = () => {
    const activities = [
      'Nouvelle interaction',
      'Mise à jour profil',
      'Recommandation générée',
      'Feedback reçu'
    ];

    const users = ['Alice Martin', 'Bob Dupont', 'Claire Rousseau', 'David Chen', 'Emma Wilson'];
    const randomActivity = activities[Math.floor(Math.random() * activities.length)];
    const randomUser = users[Math.floor(Math.random() * users.length)];

    if (randomActivity === 'Nouvelle interaction') {
      const newInteraction: Interaction = {
        id: `int_${Date.now()}`,
        userId: `user_${Math.random().toString(36).substr(2, 9)}`,
        userName: randomUser,
        type: ['question', 'conversation', 'support'][Math.floor(Math.random() * 3)] as any,
        content: `${randomActivity} de ${randomUser}`,
        sentiment: ['positive', 'neutral', 'negative'][Math.floor(Math.random() * 3)] as any,
        duration: 5 + Math.random() * 20,
        outcome: ['resolved', 'ongoing', 'satisfied'][Math.floor(Math.random() * 3)] as any,
        timestamp: new Date()
      };

      setRecentInteractions(prev => [newInteraction, ...prev.slice(0, 9)]);
    }
  };

  const updateUserProfile = (profile: UserProfile) => {
    setUserProfiles(prev =>
      prev.map(p => p.userId === profile.userId ? profile : p)
    );
  };

  const addInteraction = (interaction: Interaction) => {
    setRecentInteractions(prev => [interaction, ...prev.slice(0, 9)]);
  };

  const applyPersonalizationSegment = (segment: any) => {
    // Intégration avec Interface Personalization pour segmentation utilisateur
    console.log('🎯 Application segment personnalisation:', segment);
  };

  const addSocialRecommendation = (recommendation: SocialRecommendation) => {
    setSocialRecommendations(prev => [recommendation, ...prev.slice(0, 9)]);
  };

  const getRelationshipIcon = (type: string) => {
    switch (type) {
      case 'vip': return <Star className="text-yellow-400" size={16} />;
      case 'friend': return <Heart className="text-pink-400" size={16} />;
      case 'collaborator': return <Users className="text-blue-400" size={16} />;
      case 'regular': return <UserPlus className="text-green-400" size={16} />;
      case 'new': return <UserPlus className="text-gray-400" size={16} />;
      default: return <Users className="text-gray-400" size={16} />;
    }
  };

  const getRelationshipColor = (type: string) => {
    switch (type) {
      case 'vip': return 'text-yellow-400';
      case 'friend': return 'text-pink-400';
      case 'collaborator': return 'text-blue-400';
      case 'regular': return 'text-green-400';
      case 'new': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-400';
      case 'negative': return 'text-red-400';
      case 'neutral': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getOutcomeColor = (outcome: string) => {
    switch (outcome) {
      case 'resolved': case 'satisfied': return 'text-green-400';
      case 'ongoing': return 'text-yellow-400';
      case 'escalated': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getScoreColor = (score: number) => {
    if (score > 90) return 'text-green-400';
    if (score > 75) return 'text-yellow-400';
    if (score > 60) return 'text-orange-400';
    return 'text-red-400';
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">

        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-green-500 rounded-full flex items-center justify-center">
              <Users className="text-white" size={24} />
            </div>
            <div>
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Relations Sociales d'Hanuman
              </h1>
              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Réseau Social IA • Interface Personalization, Gestion Relations
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected
                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                {isConnected ? 'Connecté' : 'Déconnecté'}
              </span>
            </div>

            <button
              onClick={() => setNetworkView(networkView === 'list' ? 'graph' : 'list')}
              className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                darkMode ? 'bg-gray-800 text-white hover:bg-gray-700' : 'bg-white text-gray-900 hover:bg-gray-100'
              } border ${darkMode ? 'border-gray-700' : 'border-gray-300'}`}
            >
              Vue {networkView === 'list' ? 'Graphique' : 'Liste'}
            </button>
          </div>
        </div>

        {/* Métriques Sociales */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Métriques Sociales
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Users className="text-blue-400" size={20} />
              </div>
              <div className="text-3xl font-bold text-blue-400">
                {socialMetrics.totalUsers.toLocaleString()}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Utilisateurs Totaux
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Heart className="text-pink-400" size={20} />
              </div>
              <div className="text-3xl font-bold text-pink-400">
                {socialMetrics.activeRelationships}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Relations Actives
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Star className="text-yellow-400" size={20} />
              </div>
              <div className={`text-3xl font-bold ${getScoreColor(socialMetrics.averageSatisfaction)}`}>
                {socialMetrics.averageSatisfaction.toFixed(1)}%
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Satisfaction Moyenne
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Clock className="text-purple-400" size={20} />
              </div>
              <div className="text-3xl font-bold text-purple-400">
                {socialMetrics.responseTime.toFixed(1)}s
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Temps Réponse
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <TrendingUp className="text-green-400" size={20} />
              </div>
              <div className={`text-3xl font-bold ${getScoreColor(socialMetrics.engagementRate)}`}>
                {socialMetrics.engagementRate.toFixed(1)}%
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Taux Engagement
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">

          {/* Profils Utilisateurs */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              👥 Profils Utilisateurs
            </h3>
            <div className="space-y-3">
              {userProfiles.map((profile) => (
                <div
                  key={profile.userId}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedUser === profile.userId
                      ? 'bg-blue-100 dark:bg-blue-900'
                      : darkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200'
                  }`}
                  onClick={() => setSelectedUser(selectedUser === profile.userId ? null : profile.userId)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getRelationshipIcon(profile.relationshipType)}
                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {profile.userName}
                      </span>
                    </div>
                    <span className={`text-xs ${getRelationshipColor(profile.relationshipType)}`}>
                      {profile.relationshipType}
                    </span>
                  </div>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {profile.interactionCount} interactions
                    </span>
                    <span className={`text-xs ${getScoreColor(profile.satisfactionScore)}`}>
                      {profile.satisfactionScore}% satisfaction
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Style: {profile.preferences.communicationStyle}
                    </span>
                    <span className={`text-xs ${getScoreColor(profile.trustLevel)}`}>
                      {profile.trustLevel}% confiance
                    </span>
                  </div>
                  {selectedUser === profile.userId && (
                    <div className="mt-3 pt-3 border-t border-gray-300 dark:border-gray-600">
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`}>
                        Sujets préférés: {profile.preferences.topics.join(', ')}
                      </div>
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Dernière interaction: {profile.lastInteraction.toLocaleString()}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Interactions Récentes */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              💬 Interactions Récentes
            </h3>
            <div className="space-y-3">
              {recentInteractions.map((interaction) => (
                <div key={interaction.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <MessageSquare size={16} className="text-blue-400" />
                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {interaction.userName}
                      </span>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded ${
                      interaction.type === 'question' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      interaction.type === 'support' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                      interaction.type === 'conversation' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
                    }`}>
                      {interaction.type}
                    </span>
                  </div>
                  <div className={`text-sm ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                    {interaction.content}
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs ${getSentimentColor(interaction.sentiment)}`}>
                        {interaction.sentiment}
                      </span>
                      <span className={`text-xs ${getOutcomeColor(interaction.outcome)}`}>
                        {interaction.outcome}
                      </span>
                    </div>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {interaction.duration}min • {interaction.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recommandations Sociales */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🎯 Recommandations Sociales
            </h3>
            <div className="space-y-3">
              {socialRecommendations.map((recommendation) => (
                <div key={recommendation.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {recommendation.title}
                    </span>
                    <div className="flex items-center space-x-2">
                      <span className={`text-xs ${getPriorityColor(recommendation.priority)}`}>
                        {recommendation.priority}
                      </span>
                      <span className={`text-xs ${getScoreColor(recommendation.confidence)}`}>
                        {recommendation.confidence}%
                      </span>
                    </div>
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                    {recommendation.description}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className={`text-xs px-2 py-1 rounded ${
                      recommendation.type === 'content' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                      recommendation.type === 'connection' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      recommendation.type === 'activity' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200'
                    }`}>
                      {recommendation.type}
                    </span>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {recommendation.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanSocialInterface;
