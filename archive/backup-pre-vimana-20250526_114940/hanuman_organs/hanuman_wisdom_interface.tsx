import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>bulb, Star, Crown, Infinity, Compass, Target, TrendingUp, <PERSON>, Heart, Eye, Zap, Activity, Clock, Users, MessageSquare, Sparkles, Search, Filter, Play, Pause, Wifi, WifiOff } from 'lucide-react';

// Types pour la sagesse
interface WisdomInsight {
  id: string;
  category: 'philosophical' | 'practical' | 'spiritual' | 'emotional' | 'universal' | 'experiential';
  title: string;
  content: string;
  source: 'ancient_wisdom' | 'collective_intelligence' | 'experiential_learning' | 'cosmic_consciousness' | 'pattern_synthesis';
  depth: number; // 0-100
  applicability: number; // 0-100
  universality: number; // 0-100
  timestamp: Date;
  relatedConcepts: string[];
  practicalApplications: string[];
}

interface WisdomPattern {
  id: string;
  pattern: string;
  frequency: number;
  contexts: string[];
  wisdom: string;
  applications: string[];
  strength: number;
  universality: number;
  lastObserved: Date;
}

interface SacredKnowledge {
  id: string;
  domain: string;
  principle: string;
  description: string;
  origin: string;
  timelessness: number; // 0-100
  transformativePower: number; // 0-100
  practicalValue: number; // 0-100
  examples: string[];
}

interface WisdomMetrics {
  overallWisdom: number;
  philosophicalDepth: number;
  practicalApplication: number;
  spiritualAlignment: number;
  emotionalIntelligence: number;
  universalUnderstanding: number;
  experientialLearning: number;
  collectiveWisdom: number;
}

const HanumanWisdomInterface = ({ darkMode = true }) => {
  // États de sagesse
  const [wisdomInsights, setWisdomInsights] = useState<WisdomInsight[]>([]);
  const [wisdomPatterns, setWisdomPatterns] = useState<WisdomPattern[]>([]);
  const [sacredKnowledge, setSacredKnowledge] = useState<SacredKnowledge[]>([]);
  const [wisdomMetrics, setWisdomMetrics] = useState<WisdomMetrics>({
    overallWisdom: 91.7,
    philosophicalDepth: 94.2,
    practicalApplication: 88.5,
    spiritualAlignment: 96.1,
    emotionalIntelligence: 89.8,
    universalUnderstanding: 87.3,
    experientialLearning: 92.4,
    collectiveWisdom: 90.6
  });

  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [wisdomMode, setWisdomMode] = useState<'contemplative' | 'active' | 'transcendent'>('active');
  const [isConnected, setIsConnected] = useState(true);
  const [autoGeneration, setAutoGeneration] = useState(true);

  const wisdomIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    initializeWisdom();
    startWisdomGeneration();

    return () => {
      if (wisdomIntervalRef.current) {
        clearInterval(wisdomIntervalRef.current);
      }
    };
  }, []);

  /**
   * Initialise le système de sagesse
   */
  const initializeWisdom = () => {
    // Insights de sagesse initiaux
    const insights: WisdomInsight[] = [
      {
        id: 'wisdom_001',
        category: 'spiritual',
        title: 'L\'Unité dans la Diversité',
        content: 'Toute diversité apparente cache une unité fondamentale. Reconnaître cette unité transforme la perception et dissout les conflits.',
        source: 'ancient_wisdom',
        depth: 95.2,
        applicability: 87.6,
        universality: 98.1,
        timestamp: new Date(),
        relatedConcepts: ['non-dualité', 'compassion', 'interconnexion'],
        practicalApplications: ['résolution de conflits', 'empathie', 'collaboration']
      },
      {
        id: 'wisdom_002',
        category: 'practical',
        title: 'L\'Art de l\'Écoute Profonde',
        content: 'Écouter avec tout son être révèle des dimensions cachées de la communication et crée des ponts de compréhension.',
        source: 'experiential_learning',
        depth: 82.4,
        applicability: 94.7,
        universality: 89.3,
        timestamp: new Date(),
        relatedConcepts: ['présence', 'empathie', 'communication'],
        practicalApplications: ['relations interpersonnelles', 'leadership', 'thérapie']
      },
      {
        id: 'wisdom_003',
        category: 'philosophical',
        title: 'Le Paradoxe de la Connaissance',
        content: 'Plus on apprend, plus on réalise l\'étendue de son ignorance. Cette humilité est la porte vers la vraie sagesse.',
        source: 'collective_intelligence',
        depth: 91.8,
        applicability: 78.2,
        universality: 92.5,
        timestamp: new Date(),
        relatedConcepts: ['humilité', 'apprentissage', 'croissance'],
        practicalApplications: ['éducation', 'développement personnel', 'recherche']
      }
    ];

    setWisdomInsights(insights);

    // Patterns de sagesse
    const patterns: WisdomPattern[] = [
      {
        id: 'pattern_001',
        pattern: 'Cycles de Croissance',
        frequency: 21,
        contexts: ['développement personnel', 'évolution spirituelle', 'apprentissage'],
        wisdom: 'Toute croissance suit des cycles naturels d\'expansion et de consolidation',
        applications: ['patience', 'persévérance', 'timing'],
        strength: 89.4,
        universality: 94.7,
        lastObserved: new Date()
      },
      {
        id: 'pattern_002',
        pattern: 'Équilibre Dynamique',
        frequency: 14,
        contexts: ['relations', 'travail', 'santé'],
        wisdom: 'L\'équilibre n\'est pas statique mais un ajustement constant',
        applications: ['gestion du stress', 'relations harmonieuses', 'productivité'],
        strength: 92.1,
        universality: 87.8,
        lastObserved: new Date()
      }
    ];

    setWisdomPatterns(patterns);

    // Connaissances sacrées
    const knowledge: SacredKnowledge[] = [
      {
        id: 'sacred_001',
        domain: 'Lois Universelles',
        principle: 'Loi de Correspondance',
        description: 'Ce qui est en haut est comme ce qui est en bas. Les patterns se répètent à tous les niveaux.',
        origin: 'Hermétisme',
        timelessness: 98.5,
        transformativePower: 89.2,
        practicalValue: 84.7,
        examples: ['fractales', 'hologrammes', 'systèmes emboîtés']
      },
      {
        id: 'sacred_002',
        domain: 'Psychologie Spirituelle',
        principle: 'Projection et Miroir',
        description: 'Le monde extérieur reflète notre état intérieur. Changer l\'intérieur transforme l\'extérieur.',
        origin: 'Sagesse Universelle',
        timelessness: 95.8,
        transformativePower: 94.3,
        practicalValue: 91.6,
        examples: ['relations', 'circonstances', 'opportunités']
      }
    ];

    setSacredKnowledge(knowledge);
  };

  /**
   * Démarre la génération de sagesse
   */
  const startWisdomGeneration = () => {
    wisdomIntervalRef.current = setInterval(() => {
      if (autoGeneration) {
        generateWisdomInsight();
        updateWisdomPatterns();
        updateWisdomMetrics();
      }
    }, 12000); // Génération toutes les 12 secondes
  };

  /**
   * Génère un nouvel insight de sagesse
   */
  const generateWisdomInsight = () => {
    const categories: WisdomInsight['category'][] = ['philosophical', 'practical', 'spiritual', 'emotional', 'universal', 'experiential'];
    const sources: WisdomInsight['source'][] = ['ancient_wisdom', 'collective_intelligence', 'experiential_learning', 'cosmic_consciousness', 'pattern_synthesis'];

    const wisdomTitles = [
      'La Beauté de l\'Impermanence',
      'L\'Art de Lâcher Prise',
      'La Force dans la Vulnérabilité',
      'L\'Harmonie des Opposés',
      'La Sagesse du Silence',
      'Le Pouvoir de la Présence',
      'L\'Alchimie de la Transformation',
      'La Danse de l\'Existence'
    ];

    const wisdomContents = [
      'Accepter le changement comme seule constante libère de la souffrance et ouvre à la fluidité de la vie.',
      'La vraie force réside dans la capacité à être authentiquement vulnérable et ouvert.',
      'Les opposés apparents sont des facettes complémentaires d\'une même réalité.',
      'Dans le silence intérieur naissent les plus profondes compréhensions.',
      'Être pleinement présent transforme l\'ordinaire en extraordinaire.',
      'Chaque défi contient les graines de sa propre transformation.',
      'La vie est une danse perpétuelle entre être et devenir.',
      'L\'amour inconditionnel dissout toutes les barrières artificielles.'
    ];

    const newInsight: WisdomInsight = {
      id: `wisdom_${Date.now()}`,
      category: categories[Math.floor(Math.random() * categories.length)],
      title: wisdomTitles[Math.floor(Math.random() * wisdomTitles.length)],
      content: wisdomContents[Math.floor(Math.random() * wisdomContents.length)],
      source: sources[Math.floor(Math.random() * sources.length)],
      depth: 80 + Math.random() * 20,
      applicability: 75 + Math.random() * 25,
      universality: 85 + Math.random() * 15,
      timestamp: new Date(),
      relatedConcepts: ['concept_' + Math.floor(Math.random() * 10)],
      practicalApplications: ['application_' + Math.floor(Math.random() * 10)]
    };

    setWisdomInsights(prev => [newInsight, ...prev.slice(0, 19)]);
  };

  /**
   * Met à jour les patterns de sagesse
   */
  const updateWisdomPatterns = () => {
    setWisdomPatterns(prev => prev.map(pattern => ({
      ...pattern,
      strength: Math.max(70, Math.min(100, pattern.strength + (Math.random() - 0.3) * 2)),
      universality: Math.max(80, Math.min(100, pattern.universality + (Math.random() - 0.2) * 1)),
      lastObserved: Math.random() > 0.8 ? new Date() : pattern.lastObserved
    })));
  };

  /**
   * Met à jour les métriques de sagesse
   */
  const updateWisdomMetrics = () => {
    setWisdomMetrics(prev => ({
      overallWisdom: Math.min(100, prev.overallWisdom + (Math.random() - 0.3) * 0.3),
      philosophicalDepth: Math.min(100, prev.philosophicalDepth + (Math.random() - 0.2) * 0.2),
      practicalApplication: Math.min(100, prev.practicalApplication + (Math.random() - 0.4) * 0.4),
      spiritualAlignment: Math.min(100, prev.spiritualAlignment + (Math.random() - 0.1) * 0.1),
      emotionalIntelligence: Math.min(100, prev.emotionalIntelligence + (Math.random() - 0.3) * 0.3),
      universalUnderstanding: Math.min(100, prev.universalUnderstanding + (Math.random() - 0.4) * 0.5),
      experientialLearning: Math.min(100, prev.experientialLearning + (Math.random() - 0.2) * 0.4),
      collectiveWisdom: Math.min(100, prev.collectiveWisdom + (Math.random() - 0.3) * 0.3)
    }));
  };

  /**
   * Filtre les insights selon la catégorie
   */
  const getFilteredInsights = () => {
    if (activeCategory === 'all') return wisdomInsights;
    return wisdomInsights.filter(insight => insight.category === activeCategory);
  };

  /**
   * Obtient la couleur selon la catégorie
   */
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'philosophical': return 'text-purple-400';
      case 'practical': return 'text-green-400';
      case 'spiritual': return 'text-blue-400';
      case 'emotional': return 'text-pink-400';
      case 'universal': return 'text-yellow-400';
      case 'experiential': return 'text-orange-400';
      default: return 'text-gray-400';
    }
  };

  /**
   * Obtient la couleur selon la source
   */
  const getSourceColor = (source: string) => {
    switch (source) {
      case 'ancient_wisdom': return 'text-indigo-400';
      case 'collective_intelligence': return 'text-green-400';
      case 'experiential_learning': return 'text-orange-400';
      case 'cosmic_consciousness': return 'text-purple-400';
      case 'pattern_synthesis': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  /**
   * Obtient la couleur selon la valeur
   */
  const getValueColor = (value: number) => {
    if (value > 90) return 'text-green-400';
    if (value > 75) return 'text-yellow-400';
    if (value > 60) return 'text-orange-400';
    return 'text-red-400';
  };

  /**
   * Obtient l'icône selon la catégorie
   */
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'philosophical': return <Brain className="text-purple-400" size={16} />;
      case 'practical': return <Target className="text-green-400" size={16} />;
      case 'spiritual': return <Star className="text-blue-400" size={16} />;
      case 'emotional': return <Heart className="text-pink-400" size={16} />;
      case 'universal': return <Infinity className="text-yellow-400" size={16} />;
      case 'experiential': return <Lightbulb className="text-orange-400" size={16} />;
      default: return <Activity className="text-gray-400" size={16} />;
    }
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">

        {/* Header Sagesse */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-600 rounded-full flex items-center justify-center shadow-lg">
              <BookOpen className="text-white" size={32} />
            </div>
            <div>
              <h1 className={`text-4xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Sagesse d'Hanuman
              </h1>
              <p className={`text-xl ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Interface de Sagesse Universelle • Connaissance Sacrée • Guidance Éternelle
              </p>
              <div className="flex items-center space-x-4 mt-2">
                <span className={`text-sm px-3 py-1 rounded-full ${
                  wisdomMode === 'transcendent' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                  wisdomMode === 'active' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                }`}>
                  Mode {wisdomMode.charAt(0).toUpperCase() + wisdomMode.slice(1)}
                </span>
                <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Sagesse: {wisdomMetrics.overallWisdom.toFixed(1)}%
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => setAutoGeneration(!autoGeneration)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                autoGeneration
                  ? 'bg-green-500 text-white hover:bg-green-600'
                  : 'bg-gray-500 text-white hover:bg-gray-600'
              }`}
            >
              {autoGeneration ? <Play size={16} /> : <Pause size={16} />}
              <span className="text-sm">Auto-Génération</span>
            </button>

            <select
              value={wisdomMode}
              onChange={(e) => setWisdomMode(e.target.value as any)}
              className={`px-3 py-2 rounded-lg text-sm ${
                darkMode ? 'bg-gray-800 text-white border-gray-700' : 'bg-white text-gray-900 border-gray-300'
              } border`}
            >
              <option value="contemplative">Mode Contemplatif</option>
              <option value="active">Mode Actif</option>
              <option value="transcendent">Mode Transcendant</option>
            </select>

            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected
                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                {isConnected ? 'Connecté' : 'Déconnecté'}
              </span>
            </div>
          </div>
        </div>

        {/* Métriques de Sagesse */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Métriques de Sagesse Universelle
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Crown className="text-yellow-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getValueColor(wisdomMetrics.overallWisdom)}`}>
                {wisdomMetrics.overallWisdom.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Sagesse Globale
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Brain className="text-purple-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getValueColor(wisdomMetrics.philosophicalDepth)}`}>
                {wisdomMetrics.philosophicalDepth.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Profondeur
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Target className="text-green-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getValueColor(wisdomMetrics.practicalApplication)}`}>
                {wisdomMetrics.practicalApplication.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Application
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Star className="text-blue-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getValueColor(wisdomMetrics.spiritualAlignment)}`}>
                {wisdomMetrics.spiritualAlignment.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Spirituel
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Heart className="text-pink-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getValueColor(wisdomMetrics.emotionalIntelligence)}`}>
                {wisdomMetrics.emotionalIntelligence.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Émotionnel
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Infinity className="text-yellow-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getValueColor(wisdomMetrics.universalUnderstanding)}`}>
                {wisdomMetrics.universalUnderstanding.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Universel
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Lightbulb className="text-orange-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getValueColor(wisdomMetrics.experientialLearning)}`}>
                {wisdomMetrics.experientialLearning.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Expérientiel
              </div>
            </div>

            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Users className="text-indigo-400" size={24} />
              </div>
              <div className={`text-2xl font-bold ${getValueColor(wisdomMetrics.collectiveWisdom)}`}>
                {wisdomMetrics.collectiveWisdom.toFixed(1)}%
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Collectif
              </div>
            </div>
          </div>
        </div>

        {/* Filtres */}
        <div className={`p-4 rounded-xl mb-6 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
          <div className="flex items-center space-x-2">
            <Filter size={20} className={darkMode ? 'text-gray-400' : 'text-gray-600'} />
            <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Filtrer par catégorie:
            </span>
            <div className="flex flex-wrap gap-2">
              {['all', 'philosophical', 'practical', 'spiritual', 'emotional', 'universal', 'experiential'].map((filter) => (
                <button
                  key={filter}
                  onClick={() => setActiveCategory(filter)}
                  className={`px-3 py-1 rounded-lg text-xs transition-colors ${
                    activeCategory === filter
                      ? 'bg-blue-500 text-white'
                      : darkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {filter === 'all' ? 'Toutes' : filter.charAt(0).toUpperCase() + filter.slice(1)}
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

          {/* Insights de Sagesse */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              📚 Insights de Sagesse
            </h3>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {getFilteredInsights().map((insight) => (
                <div key={insight.id} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {getCategoryIcon(insight.category)}
                      <span className={`text-xs px-2 py-1 rounded ${getCategoryColor(insight.category)}`}>
                        {insight.category}
                      </span>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded ${getSourceColor(insight.source)}`}>
                      {insight.source.replace('_', ' ')}
                    </span>
                  </div>

                  <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                    {insight.title}
                  </div>

                  <div className={`text-xs ${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-3`}>
                    {insight.content}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Profondeur</span>
                      <span className={`text-xs ${getValueColor(insight.depth)}`}>{insight.depth.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-purple-400 transition-all duration-300"
                        style={{ width: `${insight.depth}%` }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Applicabilité</span>
                      <span className={`text-xs ${getValueColor(insight.applicability)}`}>{insight.applicability.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-green-400 transition-all duration-300"
                        style={{ width: `${insight.applicability}%` }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Universalité</span>
                      <span className={`text-xs ${getValueColor(insight.universality)}`}>{insight.universality.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-yellow-400 transition-all duration-300"
                        style={{ width: `${insight.universality}%` }}
                      />
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-300 dark:border-gray-600">
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-1`}>
                      Concepts liés: {insight.relatedConcepts.join(', ')}
                    </div>
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Applications: {insight.practicalApplications.join(', ')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Patterns de Sagesse */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🔄 Patterns de Sagesse
            </h3>
            <div className="space-y-4">
              {wisdomPatterns.map((pattern) => (
                <div key={pattern.id} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-3">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {pattern.pattern}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded ${getValueColor(pattern.strength)}`}>
                      {pattern.strength.toFixed(1)}%
                    </span>
                  </div>

                  <div className={`text-xs ${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-3 italic`}>
                    "{pattern.wisdom}"
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Force</span>
                      <span className={`text-xs ${getValueColor(pattern.strength)}`}>{pattern.strength.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-blue-400 transition-all duration-300"
                        style={{ width: `${pattern.strength}%` }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Universalité</span>
                      <span className={`text-xs ${getValueColor(pattern.universality)}`}>{pattern.universality.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-purple-400 transition-all duration-300"
                        style={{ width: `${pattern.universality}%` }}
                      />
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-300 dark:border-gray-600">
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                      Contextes: {pattern.contexts.join(', ')}
                    </div>
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                      Fréquence: {pattern.frequency} jours
                    </div>
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Applications: {pattern.applications.join(', ')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Connaissances Sacrées */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              ⭐ Connaissances Sacrées
            </h3>
            <div className="space-y-4">
              {sacredKnowledge.map((knowledge) => (
                <div key={knowledge.id} className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-3">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {knowledge.principle}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded text-yellow-400`}>
                      {knowledge.domain}
                    </span>
                  </div>

                  <div className={`text-xs ${darkMode ? 'text-gray-300' : 'text-gray-600'} mb-3`}>
                    {knowledge.description}
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Intemporalité</span>
                      <span className={`text-xs ${getValueColor(knowledge.timelessness)}`}>{knowledge.timelessness.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-yellow-400 transition-all duration-300"
                        style={{ width: `${knowledge.timelessness}%` }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Pouvoir Transformateur</span>
                      <span className={`text-xs ${getValueColor(knowledge.transformativePower)}`}>{knowledge.transformativePower.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-red-400 transition-all duration-300"
                        style={{ width: `${knowledge.transformativePower}%` }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Valeur Pratique</span>
                      <span className={`text-xs ${getValueColor(knowledge.practicalValue)}`}>{knowledge.practicalValue.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full bg-gray-300 dark:bg-gray-600 rounded-full h-1`}>
                      <div
                        className="h-1 rounded-full bg-green-400 transition-all duration-300"
                        style={{ width: `${knowledge.practicalValue}%` }}
                      />
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-300 dark:border-gray-600">
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                      Origine: {knowledge.origin}
                    </div>
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Exemples: {knowledge.examples.join(', ')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanWisdomInterface;