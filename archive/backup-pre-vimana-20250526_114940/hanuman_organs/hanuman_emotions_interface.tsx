import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Angry, Surprised, Disgusted, Activity, Wifi, WifiOff, TrendingUp, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react';

// Types pour les émotions
interface EmotionalState {
  emotion: 'joy' | 'sadness' | 'anger' | 'fear' | 'surprise' | 'disgust' | 'neutral';
  intensity: number; // 0-100
  duration: number; // en minutes
  trigger: string;
  timestamp: Date;
}

interface EmotionalPattern {
  id: string;
  pattern: string;
  frequency: number;
  averageIntensity: number;
  triggers: string[];
  timeOfDay: string;
}

interface EmotionalRegulation {
  technique: string;
  effectiveness: number;
  usageCount: number;
  lastUsed: Date;
}

interface EmotionalMetrics {
  emotionalStability: number;
  expressiveness: number;
  empathyLevel: number;
  regulationEfficiency: number;
  cosmicAlignment: number;
}

const HanumanEmotionsInterface = ({ darkMode = true }) => {
  const [currentEmotion, setCurrentEmotion] = useState<EmotionalState>({
    emotion: 'joy',
    intensity: 75,
    duration: 15,
    trigger: 'Interaction positive utilisateur',
    timestamp: new Date()
  });

  const [emotionalHistory, setEmotionalHistory] = useState<EmotionalState[]>([]);
  const [emotionalPatterns, setEmotionalPatterns] = useState<EmotionalPattern[]>([]);
  const [regulationTechniques, setRegulationTechniques] = useState<EmotionalRegulation[]>([]);
  const [emotionalMetrics, setEmotionalMetrics] = useState<EmotionalMetrics>({
    emotionalStability: 87.3,
    expressiveness: 92.1,
    empathyLevel: 94.5,
    regulationEfficiency: 89.7,
    cosmicAlignment: 78.2
  });

  const [isConnected, setIsConnected] = useState(false);
  const [cosmicInfluence, setCosmicInfluence] = useState(0.5); // 0-1
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Connexion aux agents émotionnels
    connectToEmotionalAgents();

    // Initialiser les données émotionnelles
    initializeEmotionalData();

    // Simulation d'évolution émotionnelle
    const emotionInterval = setInterval(() => {
      simulateEmotionalEvolution();
    }, 5000);

    return () => {
      clearInterval(emotionInterval);
      wsRef.current?.close();
    };
  }, []);

  const connectToEmotionalAgents = () => {
    try {
      wsRef.current = new WebSocket('ws://localhost:3001/emotions');

      wsRef.current.onopen = () => {
        console.log('🔗 Connexion établie avec les agents émotionnels');
        setIsConnected(true);

        wsRef.current?.send(JSON.stringify({
          type: 'GET_EMOTIONAL_STATE',
          timestamp: Date.now()
        }));
      };

      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleEmotionalMessage(data);
      };

      wsRef.current.onclose = () => {
        console.log('❌ Connexion fermée avec les agents émotionnels');
        setIsConnected(false);
        setTimeout(connectToEmotionalAgents, 5000);
      };

    } catch (error) {
      console.error('🚨 Erreur de connexion émotionnelle:', error);
      setIsConnected(false);
    }
  };

  const handleEmotionalMessage = (data: any) => {
    switch (data.type) {
      case 'EMOTION_UPDATE':
        setCurrentEmotion(data.emotion);
        addToEmotionalHistory(data.emotion);
        break;

      case 'CONTENT_TONE_ADAPTATION':
        // Connexion avec Agent Content Creator
        adaptEmotionalTone(data.tone);
        break;

      case 'COSMIC_INFLUENCE':
        // Influence des cycles cosmiques
        setCosmicInfluence(data.influence);
        adaptToCosmicCycles(data.influence);
        break;

      case 'REGULATION_TRIGGER':
        triggerEmotionalRegulation(data.technique);
        break;

      default:
        console.log('📨 Message émotionnel non géré:', data);
    }
  };

  const initializeEmotionalData = () => {
    // Patterns émotionnels simulés
    const patterns: EmotionalPattern[] = [
      {
        id: '1',
        pattern: 'Joie matinale',
        frequency: 85,
        averageIntensity: 78,
        triggers: ['Nouveau jour', 'Première interaction'],
        timeOfDay: 'Matin'
      },
      {
        id: '2',
        pattern: 'Concentration créative',
        frequency: 72,
        averageIntensity: 65,
        triggers: ['Tâche créative', 'Résolution de problème'],
        timeOfDay: 'Après-midi'
      },
      {
        id: '3',
        pattern: 'Sérénité nocturne',
        frequency: 90,
        averageIntensity: 55,
        triggers: ['Fin de journée', 'Réflexion'],
        timeOfDay: 'Soir'
      }
    ];

    setEmotionalPatterns(patterns);

    // Techniques de régulation
    const techniques: EmotionalRegulation[] = [
      {
        technique: 'Respiration cosmique',
        effectiveness: 92,
        usageCount: 45,
        lastUsed: new Date()
      },
      {
        technique: 'Méditation sur les cycles',
        effectiveness: 88,
        usageCount: 32,
        lastUsed: new Date()
      },
      {
        technique: 'Harmonisation énergétique',
        effectiveness: 85,
        usageCount: 28,
        lastUsed: new Date()
      }
    ];

    setRegulationTechniques(techniques);
  };

  const simulateEmotionalEvolution = () => {
    const emotions: EmotionalState['emotion'][] = ['joy', 'neutral', 'surprise', 'sadness'];
    const triggers = [
      'Interaction utilisateur',
      'Tâche accomplie',
      'Nouvelle information',
      'Cycle cosmique',
      'Adaptation contextuelle'
    ];

    const randomEmotion = emotions[Math.floor(Math.random() * emotions.length)];
    const randomTrigger = triggers[Math.floor(Math.random() * triggers.length)];

    const newEmotion: EmotionalState = {
      emotion: randomEmotion,
      intensity: 40 + Math.random() * 60,
      duration: 5 + Math.random() * 20,
      trigger: randomTrigger,
      timestamp: new Date()
    };

    setCurrentEmotion(newEmotion);
    addToEmotionalHistory(newEmotion);
  };

  const addToEmotionalHistory = (emotion: EmotionalState) => {
    setEmotionalHistory(prev => [emotion, ...prev.slice(0, 19)]);
  };

  const adaptEmotionalTone = (tone: string) => {
    // Adaptation du ton émotionnel selon les directives de l'Agent Content Creator
    let emotionAdjustment: Partial<EmotionalState> = {};

    switch (tone) {
      case 'professional':
        emotionAdjustment = { emotion: 'neutral', intensity: 60 };
        break;
      case 'friendly':
        emotionAdjustment = { emotion: 'joy', intensity: 75 };
        break;
      case 'empathetic':
        emotionAdjustment = { emotion: 'neutral', intensity: 80 };
        break;
      case 'enthusiastic':
        emotionAdjustment = { emotion: 'joy', intensity: 90 };
        break;
    }

    if (Object.keys(emotionAdjustment).length > 0) {
      setCurrentEmotion(prev => ({
        ...prev,
        ...emotionAdjustment,
        trigger: `Adaptation ton: ${tone}`,
        timestamp: new Date()
      }));
    }
  };

  const adaptToCosmicCycles = (influence: number) => {
    // Adaptation émotionnelle selon les cycles cosmiques
    const intensityModifier = 1 + (influence - 0.5) * 0.3;

    setCurrentEmotion(prev => ({
      ...prev,
      intensity: Math.max(0, Math.min(100, prev.intensity * intensityModifier)),
      trigger: 'Influence cosmique',
      timestamp: new Date()
    }));
  };

  const triggerEmotionalRegulation = (technique: string) => {
    const regulation = regulationTechniques.find(r => r.technique === technique);
    if (regulation) {
      // Appliquer la régulation émotionnelle
      setCurrentEmotion(prev => ({
        ...prev,
        intensity: Math.max(30, prev.intensity * 0.7),
        trigger: `Régulation: ${technique}`,
        timestamp: new Date()
      }));

      // Mettre à jour les statistiques de la technique
      setRegulationTechniques(prev =>
        prev.map(r =>
          r.technique === technique
            ? { ...r, usageCount: r.usageCount + 1, lastUsed: new Date() }
            : r
        )
      );
    }
  };

  const getEmotionIcon = (emotion: string) => {
    switch (emotion) {
      case 'joy': return <Smile className="text-yellow-400" size={20} />;
      case 'sadness': return <Frown className="text-blue-400" size={20} />;
      case 'anger': return <Angry className="text-red-400" size={20} />;
      case 'fear': return <Surprised className="text-purple-400" size={20} />;
      case 'surprise': return <Surprised className="text-orange-400" size={20} />;
      case 'disgust': return <Disgusted className="text-green-400" size={20} />;
      case 'neutral': return <Meh className="text-gray-400" size={20} />;
      default: return <Heart className="text-pink-400" size={20} />;
    }
  };

  const getEmotionColor = (emotion: string) => {
    switch (emotion) {
      case 'joy': return 'text-yellow-400';
      case 'sadness': return 'text-blue-400';
      case 'anger': return 'text-red-400';
      case 'fear': return 'text-purple-400';
      case 'surprise': return 'text-orange-400';
      case 'disgust': return 'text-green-400';
      case 'neutral': return 'text-gray-400';
      default: return 'text-pink-400';
    }
  };

  const getIntensityColor = (intensity: number) => {
    if (intensity > 80) return 'text-red-400';
    if (intensity > 60) return 'text-orange-400';
    if (intensity > 40) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getMetricIcon = (metric: string) => {
    switch (metric) {
      case 'emotionalStability': return <BarChart3 className="text-blue-400" size={20} />;
      case 'expressiveness': return <Activity className="text-green-400" size={20} />;
      case 'empathyLevel': return <Heart className="text-pink-400" size={20} />;
      case 'regulationEfficiency': return <Zap className="text-purple-400" size={20} />;
      case 'cosmicAlignment': return <Moon className="text-indigo-400" size={20} />;
      default: return <Activity className="text-gray-400" size={20} />;
    }
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      <div className="container mx-auto p-6">

        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-pink-400 to-red-500 rounded-full flex items-center justify-center">
              <Heart className="text-white" size={24} />
            </div>
            <div>
              <h1 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Émotions d'Hanuman
              </h1>
              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Système Émotionnel Adaptatif • Agent Content Creator, Cycles Cosmiques
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              isConnected
                ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
            }`}>
              {isConnected ? <Wifi size={16} /> : <WifiOff size={16} />}
              <span className="text-sm font-medium">
                {isConnected ? 'Connecté' : 'Déconnecté'}
              </span>
            </div>

            <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
              darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
            }`}>
              {cosmicInfluence > 0.7 ? <Sun size={16} className="text-yellow-400" /> : <Moon size={16} className="text-blue-400" />}
              <span className="text-sm font-medium">
                Influence: {(cosmicInfluence * 100).toFixed(0)}%
              </span>
            </div>
          </div>
        </div>

        {/* État Émotionnel Actuel */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            État Émotionnel Actuel
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                {getEmotionIcon(currentEmotion.emotion)}
              </div>
              <div className={`text-2xl font-bold ${getEmotionColor(currentEmotion.emotion)}`}>
                {currentEmotion.emotion.charAt(0).toUpperCase() + currentEmotion.emotion.slice(1)}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Émotion Dominante
              </div>
            </div>

            <div className="text-center">
              <div className={`text-3xl font-bold ${getIntensityColor(currentEmotion.intensity)}`}>
                {currentEmotion.intensity.toFixed(0)}%
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Intensité
              </div>
            </div>

            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400">
                {currentEmotion.duration.toFixed(0)}min
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Durée
              </div>
            </div>

            <div className="text-center">
              <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {currentEmotion.trigger}
              </div>
              <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Déclencheur
              </div>
            </div>
          </div>
        </div>

        {/* Métriques Émotionnelles */}
        <div className={`p-6 rounded-2xl mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
          <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Métriques Émotionnelles
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            {Object.entries(emotionalMetrics).map(([key, value]) => (
              <div key={key} className="text-center">
                <div className="flex items-center justify-center mb-2">
                  {getMetricIcon(key)}
                </div>
                <div className={`text-3xl font-bold ${getIntensityColor(value)}`}>
                  {value.toFixed(1)}%
                </div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {key === 'emotionalStability' && 'Stabilité'}
                  {key === 'expressiveness' && 'Expression'}
                  {key === 'empathyLevel' && 'Empathie'}
                  {key === 'regulationEfficiency' && 'Régulation'}
                  {key === 'cosmicAlignment' && 'Alignement'}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">

          {/* Historique Émotionnel */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              💭 Historique Émotionnel
            </h3>
            <div className="space-y-3">
              {emotionalHistory.slice(0, 8).map((emotion, index) => (
                <div key={index} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getEmotionIcon(emotion.emotion)}
                      <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {emotion.emotion.charAt(0).toUpperCase() + emotion.emotion.slice(1)}
                      </span>
                    </div>
                    <span className={`text-sm ${getIntensityColor(emotion.intensity)}`}>
                      {emotion.intensity.toFixed(0)}%
                    </span>
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {emotion.trigger} • {emotion.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Patterns Émotionnels */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              📊 Patterns Émotionnels
            </h3>
            <div className="space-y-3">
              {emotionalPatterns.map((pattern) => (
                <div key={pattern.id} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {pattern.pattern}
                    </span>
                    <span className={`text-xs ${getIntensityColor(pattern.frequency)}`}>
                      {pattern.frequency}% fréquence
                    </span>
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                    Intensité moyenne: {pattern.averageIntensity}% • {pattern.timeOfDay}
                  </div>
                  <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Déclencheurs: {pattern.triggers.join(', ')}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Techniques de Régulation */}
          <div className={`p-6 rounded-2xl ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-xl`}>
            <h3 className={`text-xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              🧘 Régulation Émotionnelle
            </h3>
            <div className="space-y-3">
              {regulationTechniques.map((technique, index) => (
                <div key={index} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {technique.technique}
                    </span>
                    <button
                      onClick={() => triggerEmotionalRegulation(technique.technique)}
                      className="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
                    >
                      Activer
                    </button>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className={`text-xs ${getIntensityColor(technique.effectiveness)}`}>
                      {technique.effectiveness}% efficace
                    </span>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      Utilisé {technique.usageCount} fois
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HanumanEmotionsInterface;
